/* 处理sourceType与 <OccurrenceOfIllness />组件中cards字段的映射关系 */
import { FormCategoryValues } from '@/constant';
interface IMap {
  key: FormCategoryValues;
  showList: number[];
  number: number;
}
const sourceTypeMap: IMap[] = [
  {
    key: 'admission_report',
    showList: [0, 3],
    number: 1,
  },
  {
    key: 'discharge_report',
    showList: [0, 3],
    number: 3,
  },
  {
    key: 'outpatient_record',
    showList: [1],
    number: 4,
  },
  {
    key: 'diagnose_report',
    showList: [0, 2, 3, 1],
    number: 6,
  },
  {
    key: 'outpatient_exam',
    showList: [1],
    number: 5,
  },
  {
    key: 'operation',
    showList: [0, 3],
    number: 2,
  },
];
export function getKeysByValue(value: number | number[], type = 'key') {
  const result: any = [];
  const isArray = Array.isArray(value);
  for (const item of sourceTypeMap) {
    if (!isArray && item.showList.includes(value)) {
      result.push(item[type]);
    }
    if (isArray && value.includes(item.number)) {
      result.push(item[type]);
    }
  }
  return result;
}
