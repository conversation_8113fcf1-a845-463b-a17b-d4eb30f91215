<template>
  <Dialog
    v-model:visible="props.visible"
    :title="title"
    ignore-max-height
    draggable
    :width="800"
    @update:visible="closeDialog"
  >
    <div class="py-16 px-24 h-700">
      <HeaderTitle is-ocr-module />
      <OccurrenceOfIllness
        :cards="getCards"
        :mode="props.sourceId ? 'view' : 'create'"
        :resource-id="props.sourceId"
        :is-batch="false"
        :source-type="sourceType"
        nav-prefix="result"
        :query-params="{
          all: {
            entry_task: true,
            sub_task_id:
              globalStore.currentRole === 4
                ? dStore.taskId
                : (props.taskId as number),
          },
          attachment: {
            entry_task: false,
            sourceId:
              globalStore.currentRole === 4 ? dStore.caseId : props.sourceId,
          },
        }"
        :additional-data="{
          all: {
            source_type: sourceType,
            entry_task: true,
            patient_id: globalStore.userId,
            sub_task_id:
              globalStore.currentRole === 4
                ? dStore.taskId
                : (props.taskId as number),
          },
        }"
      />
    </div>
    <template #footer>
      <div class="footer px-24 py-16">
        <div class="warning cursor-pointer">
          <span v-if="!isAcceptanceCheck" @click="hangleIgnoreResult">
            忽略本次结果
          </span>
        </div>
        <div class="flex items-center">
          <div class="tips">{{ tips }}</div>
          <div class="cancle-btn common" @click="hangleRejectTask">
            {{ isAcceptanceCheck ? '验收驳回' : '取消' }}
          </div>
          <div class="sure-btn common" @click="submit">
            {{ isAcceptanceCheck ? '验收通过' : '确定' }}
          </div>
        </div>
      </div>
    </template>
    <!-- 数据对比弹窗 -->
    <DataComparison
      v-model:visible="dataComparisonVisible"
      :source-type="sourceType"
      is-need-callback
      @handle-callback="handleCallback"
    >
      <template #old>
        <ShowData
          :source-type="sourceType"
          :source-id="taskIdInfo.oldTaskId"
          nav-prefix="oldTaskData"
        />
      </template>
      <template #new>
        <ShowData
          :source-type="sourceType"
          :source-id="taskIdInfo.newTaskId"
          :cards="getCards"
          :query-params="{
            all: {
              entry_task: true,
              sub_task_id: taskIdInfo.newTaskId,
            },
            attachment: {
              entry_task: false,
              sourceId: taskIdInfo.oldTaskId,
            },
          }"
          nav-prefix="newTaskData"
        />
      </template>
    </DataComparison>
  </Dialog>
</template>
<script setup lang="ts">
import Dialog from '@/components/Dialog/index.vue';
import { OccurrenceOfIllness } from '@/features';
import DataComparison from '@/features/DataComparison/index.vue';
import HeaderTitle from '@/features/PatientRecord/HeaderTitle.vue';
import { rejectTaskApi, completedTaskApi } from '@/api/task';
import { SourceTypeValues } from '@/constant';
import { getKeysByValue } from './hooks';
import bus from '@/lib/bus';
import useGlobal from '@/store/module/useGlobal';
const globalStore = useGlobal();
import useInternDrawer from '@/store/module/useInternDrawer';
const dStore = useInternDrawer();
import ShowData from './components/ShowData.vue';
import { useOcrScan } from '@/store/module/useOcrScan';
const useOcrScanInfo = useOcrScan();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  // 获取组件类型 0-住院 1-门诊 3-入组
  sourceType: {
    type: Number as () => SourceTypeValues,
    default: 0,
  },
  // 任务状态
  taskStatus: {
    type: Number,
    default: 0,
  },
  sourceId: {
    type: Number,
    default: 0,
  },
  title: {
    type: String,
    default: '审核批量OCR结果',
  },
  tips: {
    type: String,
    default: '确认后内容即刻生效，不可撤回',
  },
  isAcceptanceCheck: {
    type: Boolean,
    default: false,
  },
  taskId: {
    type: Number,
    default: 0,
  },
  taskMethod: {
    type: Number,
    default: -1,
  },
  taskSection: {
    type: String,
    default: '',
  },
});

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

// 忽略本次结果
const hangleIgnoreResult = () => {
  rejectTaskApi({ taskId: props.taskId as number }).then(res => {
    if (res.code === 'E000000') {
      ElMessage({
        message: '操作成功！',
        type: 'success',
      });
      closeDialog();
      bus.emit('batch-ocr-success-refresh');
      bus.emit('update-task-status');
    }
  });
};
// 驳回任务
const hangleRejectTask = () => {
  if (props.isAcceptanceCheck) {
    hangleIgnoreResult();
  } else {
    closeDialog();
  }
};

const closeDialog = () => {
  emits('update:visible', false);
  useOcrScanInfo.updateMedicalRecord(true);
};

const taskIdInfo = ref<{
  oldTaskId: number;
  newTaskId: number;
}>({
  oldTaskId: 0,
  newTaskId: 0,
});
const submit = () => {
  const newTaskId =
    globalStore.currentRole === 4 ? dStore.taskId : (props.taskId as number);

  const oldTaskId =
    globalStore.currentRole === 4 ? dStore.sourceId : props.sourceId;

  taskIdInfo.value = {
    oldTaskId,
    newTaskId,
  };
  dataComparisonVisible.value = true;
};

// 数据对比
const dataComparisonVisible = ref(false);
const handleCallback = () => {
  completedTaskApi({ taskId: props.taskId as number }).then(res => {
    if (res.code === 'E000000') {
      ElMessage({
        message: '操作成功！',
        type: 'success',
      });
      bus.emit('batch-ocr-success-refresh-data');
      bus.emit('batch-ocr-success-refresh');
      bus.emit('update-task-status');
      closeDialog();
    }
  });
};

const getCards = computed(() => {
  const taskSection = props.taskSection ? JSON.parse(props.taskSection) : [];
  return getKeysByValue(taskSection);
});
</script>
<style scoped lang="less">
.footer {
  font-size: 14px;
  box-shadow: 0px -1px 2px 0px rgba(58, 71, 98, 0.23);
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .warning {
    color: #e63746;
  }
  .tips {
    color: #939cae;
  }
  .common {
    width: 70px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 2px;
    margin-left: 16px;
  }
  .cancle-btn {
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #7a8599;
  }
  .sure-btn {
    background: #2e6be6;
    color: #ffffff;
  }
}
</style>
