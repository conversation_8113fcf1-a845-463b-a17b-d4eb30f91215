<template>
  <!-- 批量扫描 -->
  <Dialog
    v-model:visible="props.batchScanningVisible"
    title="查看图片"
    ignore-max-height
    draggable
    :width="800"
    @close="cancel()"
  >
    <div class="batch-scanning">
      <div class="tips pl-24 flex items-center">
        批量识别任务提交后将在后台执行，无需在页面等待；识别结果需经医生审核后方可生效。
      </div>
      <div class="check-box py-16 px-24">
        <el-checkbox
          v-model="checkAll"
          :disabled="allDiabled"
          @change="handleCheckAllChange"
        >
          <div class="check-all">
            全选
            <span>（已选中{{ getCheckNum }}张图片）</span>
          </div>
        </el-checkbox>
        <div class="image-box flex flex-wrap mt-8">
          <div
            v-for="item in imageList"
            :key="item.url"
            class="item-image w-80 h-80 mr-16 mb-16"
          >
            <el-checkbox
              :key="item.url"
              v-model="item.checked"
              :disabled="item.disabled"
              size="large"
              class="check-box-item"
              @change="handleCheckChange"
            />
            <ImgPreview
              class="upload-image"
              :width="80"
              :url="item.url"
              :type="imgPreviewType"
              :show-status-label="showStatusLabel"
              :show-status="showStatus"
              fixed
              :thumbnail="false"
              is-batch
            />
          </div>
        </div>
      </div>
      <div class="btns h-48 pt-16 px-24 flex justify-end">
        <div class="cancel common mr-16 w-76" @click="cancel()">取消</div>
        <el-button
          type="primary"
          class="submit common w-100"
          :loading="loading"
          @click="submit()"
        >
          提交识别
        </el-button>
      </div>
    </div>
  </Dialog>
</template>
<script setup lang="ts">
import Dialog from '@/components/Dialog/index.vue';
import ImgPreview from '@/components/ImgPreview/index.vue';
import { getMsgImageStatus } from '@/api/ocr';

interface BatchScanningProps {
  selfImgList: { name: string; url: string }[];
  showStatusLabel?: boolean;
  showStatus?: boolean;
  batchScanningVisible: boolean;
  loading?: boolean;
}
const props = withDefaults(defineProps<BatchScanningProps>(), {
  selfImgList: () => [],
  showStatus: false,
  batchScanningVisible: false,
  loading: false,
});
const emit = defineEmits(['close', 'submissionRecognition']);

interface ImageList {
  name: string;
  url: string;
  checked: boolean;
  disabled?: boolean;
}
const allDiabled = ref(false);
const imageList = ref<ImageList[]>([]);
const loading = ref(props.loading);

// 全选
const checkAll = ref<boolean>(false);
const handleCheckAllChange = () => {
  imageList.value.forEach(item => {
    if (!item.disabled) item.checked = checkAll.value;
  });
};

// 单选
const handleCheckChange = () => {
  const filteredItems = imageList.value.filter(item => !item.disabled);
  checkAll.value = filteredItems.every(item => item.checked);
};

// 获取已选图片数量
const getCheckNum = computed(() => {
  return imageList.value.filter(item => item.checked).length;
});

// 提交识别
const submit = () => {
  const flag = imageList.value.some(item => item.checked);
  if (!flag) {
    ElMessage.warning('请选择要识别的图片！');
  } else {
    loading.value = true;
    const list = imageList.value.reduce((acc: any, item) => {
      if (item.checked) acc.push(item.url);
      return acc;
    }, []);
    emit('submissionRecognition', list);
  }
};

// 取消
const cancel = () => {
  imageList.value.forEach(item => (item.checked = false));
  emit('close');
};

// 图片预览分组
const imgPreviewType = ref(Date.now() + '');

watch(
  () => props.batchScanningVisible,
  async () => {
    const val = props.selfImgList;
    const urls = val.map(item => item.url);
    let arr: ImageList[] = [];
    if (urls.length) {
      await getMsgImageStatus({ urls }).then(data => {
        if (Array.isArray(data)) {
          arr = val.map(item => {
            let info: ImageList = {
              name: item.name,
              url: item.url,
              checked: false,
            };
            data.forEach(ite => {
              if (ite.url === item.url) {
                info.disabled = ite.ocrStatus === 0;
              }
            });

            return info;
          });
        }
      });
    }
    allDiabled.value = arr.every(item => item.disabled);
    imageList.value = arr;

    loading.value = false;
    checkAll.value = false;
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<style scoped lang="less">
.batch-scanning {
  .tips {
    height: 44px;
    background: #e6eeff;
    font-size: 14px;
    color: #2e6be6;
    box-sizing: border-box;
  }
}
.check-box {
  .check-all {
    font-size: 14px;
    color: #3a4762;
    span {
      color: #7a8599;
    }
  }
  .image-box {
    .item-image {
      border-radius: 2px;
      position: relative;
      .check-box-item {
        position: absolute;
        left: 4px;
        bottom: 4px;
        height: 16px;
        width: 16px;
      }
    }
  }
}
.btns {
  border-top: 1px solid #e9e8eb;
  .common {
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    font-size: 14px;
    cursor: pointer;
  }
  .cancel {
    background: #ffffff;
    color: #7a8599;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  .submit {
    background: #2e6be6;
    color: #ffffff;
  }
}
</style>
