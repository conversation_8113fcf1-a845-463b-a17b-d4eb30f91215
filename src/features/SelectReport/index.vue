<!--其他报告-->
<template>
  <CheckList
    :special-config="specialConfig"
    :disease-list="checkList"
    :data-map="dataMap"
    :disease-data="{}"
    :num="0"
    class="check-list"
    @on-change="val => checkChange(val.ids, 1)"
    @on-special-change="val => checkChange(val, 2)"
  />
  <div class="check-btn-group">
    <el-button type="primary" @click="submit">确定</el-button>
    <el-button @click="emits('handle-close')">取消</el-button>
  </div>
</template>
<script setup lang="ts">
import { updateHospitalCheck } from '@/api/disease';
import CheckList from '@/components/DiseaseSelector/components/CheckList.vue';
import {
  IDiseaseItem,
  IDiseaseMap,
  ISpecialConfigItem,
} from '@/components/DiseaseSelector/type';
import { getDataMap } from '@/components/DiseaseSelector/util';
import { IApiPatientReportOperateListParams } from '@/interface/type';
import useGlobal from '@/store/module/useGlobal';
import { keyBy } from 'lodash-es';
import { SourceTypeValues } from '@/constant';
import { useExaminationList } from '@/hooks';
defineOptions({ name: 'SelectReport' });

const {
  sourceType = 0,
  sourceId,
  businessCallEnabled = true,
  filterKeys = [],
  additionalParams = {},
} = defineProps<{
  /** 4 = 手动添加 */
  sourceType?: SourceTypeValues | 4;
  /** 来源id (记录id) */
  sourceId?: number | string;
  /** 过滤指定key 选项 */
  filterKeys?: string[];
  /** 是否参与业务接口调用 */
  businessCallEnabled?: boolean;
  /** 其他参数 */
  additionalParams?: Record<string, any>;
}>();

const emits = defineEmits<{
  (e: 'handle-update', id: number): void;
  (e: 'handle-close'): void;
  (e: 'handle-checked', list: IApiPatientReportOperateListParams): void;
}>();

const gData = useGlobal();
const { processedData: checkList } = useExaminationList(filterKeys);

// 自定义检查
const specialConfig = [
  { id: 94, key: 'remark', maxLength: 50, rows: 2, required: true },
];

const dataMap = computed<IDiseaseMap>(() => getDataMap(checkList.value));

const updateOperateData = ref<IApiPatientReportOperateListParams>([]);

const checkChange = (value: number[] | { [key: string]: any }, type: 1 | 2) => {
  const configMap = keyBy(specialConfig, 'id');
  const checks = updateOperateData.value;
  const oldCheckMap = keyBy(checks, 'indexTermId');
  let res: IApiPatientReportOperateListParams = [];
  if (type === 1) {
    res = (value as number[]).map(v => {
      const item = dataMap.value[v] as IDiseaseItem & {
        reportSort: number;
      };
      const finalItem: any = {
        /** 手动添加场景 sourceType传3 由后端处理业务  */
        sourceType: sourceType === 4 ? 3 : sourceType,
        indexTermId: v,
        name: item?.diseaseName,
        reportSort: item?.reportSort,
        patientId: Number(gData.userId!),
        remark: oldCheckMap[v]?.remark ?? '',
        key: item?.key,
      };
      if (sourceId) finalItem.sourceId = Number(sourceId);
      return finalItem;
    });
  } else {
    res = [...checks];
    for (const v of checks) {
      const indexTermId = v.indexTermId;
      if (indexTermId && configMap[indexTermId]) {
        const key = configMap[indexTermId].key;
        v.remark = (value as { [key: string]: any })[key];
      }
    }
  }
  updateOperateData.value = res;
};

const checkConclusions = (specialConfig: ISpecialConfigItem[]) => {
  const specialMap = keyBy(specialConfig, 'id');
  const diseaseMap = getDataMap(checkList.value);
  for (const v of updateOperateData.value) {
    const item = specialMap[v.indexTermId];
    if (item?.required && !v.remark) {
      const diseaseName = diseaseMap[v.indexTermId].diseaseName;
      return { error: true, msg: `结论 -【${diseaseName}】未填写完整！` };
    }
  }
  return { error: false, msg: '' };
};

async function submit() {
  const res = checkConclusions(specialConfig);
  if (res.error) return ElMessage.error(res.msg);
  if (!updateOperateData.value?.length) return ElMessage.error('请选择内容！');
  emits('handle-checked', updateOperateData.value);

  if (businessCallEnabled) {
    let patientHistoryId: number | undefined = undefined;
    const params = updateOperateData.value.map(operate => ({
      ...operate,
      ...(additionalParams || {}),
    }));
    patientHistoryId = (await updateHospitalCheck(params))?.patientHistoryId;
    ElMessage.success('操作成功！');
    if (!!patientHistoryId) {
      emits('handle-update', patientHistoryId);
    }
  }
}
</script>
<style lang="less" scoped>
.check-btn-group {
  border-top: 1px solid #e9e8eb;
  padding: 24px 0 24px 106px;
  position: absolute;
  bottom: 0;
  z-index: 1;
  width: 100%;
  background: #fff;

  .el-button {
    width: 76px;
  }
}
</style>
