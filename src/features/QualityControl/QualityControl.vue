<template>
  <Drawer :title="title" :visible="visible" :modal="true" @close="closeHandler">
    <div class="flex px-16 items-center justify-end">
      <div v-if="activeBox === 0" class="flex-1">
        <el-button type="text" @click="handleRevampAll">全部整改</el-button>
        <el-button type="text" @click="handleIgnoreAll">
          <span class="text-[#E37221]">全部忽略</span>
        </el-button>
      </div>
      <el-button
        v-if="activeBox === 0"
        type="text"
        @click="
          () => {
            activeBox = 1;
            refreshHandler();
          }
        "
      >
        全部质控
        <el-icon size="12px">
          <i-ep-arrow-right color="#2E6BE6" />
        </el-icon>
      </el-button>
    </div>
    <div class="w-full relative overflow-hidden overflow-y-auto">
      <div
        class="flex w-[100%] transition-transform duration-300"
        :style="{ transform: `translateX(-${activeBox * 100}%)` }"
      >
        <div class="w-full h-full shrink-0">
          <div
            v-for="(item, index) in recordList"
            :key="index"
            v-infinite-scroll="loadMore"
            class="w-full mt-8 px-16 *:mb-12 *:last:mb-0"
            @click="handleClick(item)"
          >
            <QualityCard
              :data="item"
              @close="visible = false"
              @refresh="refreshHandler"
            />
          </div>
          <el-divider v-if="listLoading">
            <el-icon :size="18" class="text-[#2E6BE6]">
              <i-ep-loading />
            </el-icon>
          </el-divider>
          <el-divider v-if="recordList?.length === total && !listLoading">
            <div class="text-[#7a8599]">没有更多了</div>
          </el-divider>
        </div>
        <div
          class="w-full h-full shrink-0 flex flex-col items-start mt-8 px-16"
        >
          <SearchHeader @search="searchHandler" />
          <div
            v-for="(item, index) in recordList"
            :key="index"
            class="w-full *:mb-12 *:last:mb-0"
            @click="handleClick(item)"
          >
            <QualityCard
              :data="item"
              @close="visible = false"
              @refresh="refreshHandler"
            />
          </div>
          <el-divider v-if="listLoading">
            <el-icon :size="18" class="text-[#2E6BE6]">
              <i-ep-loading />
            </el-icon>
          </el-divider>
          <el-divider v-if="recordList?.length === total && !listLoading">
            <span class="text-[#7a8599]">没有更多了</span>
          </el-divider>
        </div>
      </div>
    </div>
  </Drawer>
</template>

<script setup lang="ts">
import { debounce } from 'lodash-es';

import Drawer from '@/components/Drawer/index.vue';
import SearchHeader from './components/SearchHeader.vue';
import QualityCard from './components/QualityCard.vue';
import useUserStore from '@/store/module/useUserStore';
import useGlobal from '@/store/module/useGlobal';

import {
  getUserRoles,
  dialogTip,
} from '@/pages/Workbench/Right/components/PatientTodo/index';

import {
  getQualityPointPersonPage,
  ignoreQualityRecord,
  revampQualityRecord,
} from '@/api/qualityControl';
import type { IApiQualityRecordQueryPageContents } from '@/interface/type';

import useQualityRecord from '@/store/module/useQualityRecord';

const props = defineProps<{
  visible: boolean;
}>();

const { visible } = toRefs(props);

const title = ref<string>('质控');

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
}>();

const activeBox = ref(1);

const userStore = useUserStore();
const globalStore = useGlobal();

const qualityRecordStore = useQualityRecord();

watch(
  () => props.visible,
  val => {
    if (val && qualityRecordStore.shielderNumber) {
      activeBox.value = 0;
    }
  }
);
let pageNumber = 1;
let total: number | undefined = 0;
const recordList = ref<IApiQualityRecordQueryPageContents[]>();

let cachedParams = null;

const searchHandler = data => {
  pageNumber = 1;
  cachedParams = data;
  getQualityRecordList(data);
};

const listLoading = ref(false);

const getQualityRecordList = data => {
  if (pageNumber === 1) recordList.value = [];
  listLoading.value = true;
  const params = {
    ...data,
    userId: userStore.accountId as number,
    userType: String(getUserRoles()),
    pageNumber,
    pageSize: 20,
    all: true,
  };
  if (activeBox.value === 0) {
    params.all = false;
    params.status = 1;
  }

  if (params.userId) {
    getQualityPointPersonPage(params)
      .then(res => {
        console.log('搜索结果', res);
        if (pageNumber === 1) {
          recordList.value = res.contents ?? [];
        } else {
          recordList.value = [
            ...(recordList.value ?? []),
            ...(res.contents ?? []),
          ];
        }
        total = res.total;
      })
      .finally(() => {
        listLoading.value = false;
      });
  }
};

const loadMore = debounce(() => {
  console.log('loadMore');
  if (
    Array.isArray(recordList.value) &&
    typeof total === 'number' &&
    recordList.value.length < total
  ) {
    pageNumber++;
    getQualityRecordList(cachedParams);
  }
}, 1000);

const refreshHandler = () => {
  pageNumber = 1;
  getQualityRecordList(cachedParams);

  const params = {
    userId: userStore.accountId,
    userType: userStore.userRoles[0],
  };
  qualityRecordStore.getQualityNum(params);
};

const handleRevampAll = () => {
  console.log('全部整改');
  if (recordList.value.length === 0) return;
  const params = {
    userId: userStore.accountId || 0,
    userType: userStore.userRoles[0],
    all: true,
  };
  revampQualityRecord(params as any)
    .then(res => {
      console.log('revamp', res);
      dialogTip('发起整改成功', 'success');
    })
    .finally(refreshHandler);
};
const handleIgnoreAll = () => {
  console.log('全部忽略');
  if (recordList.value.length === 0) return;
  const params = {
    userId: userStore.accountId,
    userType: userStore.userRoles[0],
    all: true,
  };
  ignoreQualityRecord(params as any)
    .then(res => {
      console.log('ignore', res);
      dialogTip('忽略成功', 'success');
    })
    .finally(refreshHandler);
};
const handleClick = item => {
  globalStore.setUserId(item.patientId);
};
const closeHandler = () => {
  emit('update:visible', false);
};
</script>

<style></style>
