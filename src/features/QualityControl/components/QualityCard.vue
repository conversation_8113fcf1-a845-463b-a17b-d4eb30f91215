<template>
  <div
    class="bg-[#F7F8FA] rounded-[4px] px-12 py-14 w-full text-sm"
    v-bind="$attrs"
  >
    <div class="flex items-center justify-between">
      <div class="flex items-center leading-0">
        <img
          v-if="data.description.includes('质量')"
          width="16"
          height="16"
          src="@/assets/imgs/qualityControl/quality.svg"
          alt=""
        />
        <img
          v-else
          width="16"
          height="16"
          src="@/assets/imgs/qualityControl/overtime.svg"
          alt=""
        />
        <h4 class="text-[#3A4762] ml-6 font-bold text-sm">
          {{ data.description }}
        </h4>
        <!-- 状态栏 -->
        <div class="ml-16 text-white *:py-2 *:px-4 *:rounded-[2px] text-xs">
          <span v-if="status === 1" class="bg-[#2E6BE6]">待处理</span>
          <span v-else-if="status === 2" class="bg-[#E37221]">已忽略</span>
          <span v-else-if="status === 3" class="bg-[#2E6BE6]">整改中</span>
          <span v-else-if="status === 4" class="bg-[#939CAE]">已整改</span>
          <span v-else-if="status === 5" class="bg-[#939CAE]">未整改</span>
          <span v-else-if="status === 6" class="bg-[#939CAE]">已超期</span>
        </div>
      </div>
      <!-- 操作栏 -->
      <div
        class="flex text-[#2E6BE6] *:mr-16 *:cursor-pointer active:opacity-85 text-sm"
      >
        <span
          v-if="status === 1"
          type="text"
          class="last:mr-0 text-[#e37221]"
          @click="handleIgnore(data)"
        >
          忽略
        </span>
        <span
          v-if="status === 1"
          type="text"
          class="last:mr-0 active:opacity-85"
          @click="handleRevamp"
        >
          发起整改
        </span>
        <span
          v-if="data.status === 3"
          type="text"
          class="last:mr-0 active:opacity-85"
          @click="handleRetract(data)"
        >
          撤回整改
        </span>
      </div>
      <!-- 理由? -->
      <span v-if="status === 4" class="block max-w-280 text-[#3A4762]">
        <Text>
          {{ data.revampContent || '--' }}
        </Text>
      </span>
    </div>
    <!-- 分割线上面内容 -->
    <div class="mt-12 px-22 border-b border-[#E8EAED] pb-8">
      <span class="text-[#E63746] font-bold">{{ data.timeoutContent }}</span>
      <ul
        class="text-[#7A8599] *:flex *:items-center *:before:block *:before:content-[''] *:before:w-5 *:before:h-5 *:before:bg-[#7A8599] *:before:rounded-full *:before:mr-6"
      >
        <li
          v-for="(item, index) in data.checkPoint"
          :key="index"
          class="mb-11 last:mb-0"
        >
          {{ item.desc }}
        </li>
      </ul>
      <div v-if="data.backlogResult" class="text-[#7A8599] font-bold mt-12">
        {{ data.backlogResult }}
      </div>
    </div>
    <!-- 分割线下面内容 -->
    <div class="mt-7 pl-22 grid grid-cols-2 gap-x-18 gap-y-12 text-sm">
      <div>
        <span class="text-[#7A8599]">
          {{
            status === 1 || status === 2 || status === 6
              ? '被质控人：'
              : '整改人：'
          }}
        </span>
        <span class="text-[#3A4762]">
          {{
            data[
              status === 1 || status === 2 || status === 6
                ? 'qualityName'
                : 'qualityName'
            ]
          }}
        </span>
      </div>
      <div v-if="status === 1 || status === 2 || status === 6">
        <span class="text-[#7A8599]">生成时间：</span>
        <span class="text-[#3A4762]">
          {{ dayjs(data.createTime).format('YYYY-MM-DD HH:mm:ss') }}
        </span>
      </div>
      <div v-if="status === 3 || status == 4 || status === 5">
        <span class="text-[#7A8599]">发起整改时间：</span>
        <span class="text-[#3A4762]">
          {{
            dayjs(data.revampTime || data.modifyTime).format(
              'YYYY-MM-DD HH:mm:ss'
            )
          }}
        </span>
      </div>
      <div v-if="status === 1 || status === 2 || status === 6">
        <span class="text-[#7A8599]">患者：</span>
        <span class="text-[#3A4762]">
          {{ data.patientName }}
        </span>
      </div>
      <div
        v-if="status === 3 || status === 4 || status === 5"
        class="flex items-center"
      >
        <span class="text-[#7A8599]">整改要求：</span>
        <span class="text-[#3A4762] block max-w-177">
          <Text>
            {{ data.content || '--' }}
          </Text>
        </span>
      </div>
      <div v-if="status === 4">
        <span class="text-[#7A8599]">完成整改时间：</span>
        <span class="text-[#3A4762]">
          {{ dayjs(data.modifyTime).format('YYYY-MM-DD HH:mm:ss') }}
        </span>
      </div>
      <div v-if="status === 1 || status === 2 || status === 6">
        <span class="text-[#7A8599]">工作室：</span>
        <span class="text-[#3A4762]">{{ data.groupName }}</span>
      </div>
    </div>
    <QualityHandle
      v-model:visible="visible"
      :row-data="data"
      :type="1"
      :append-to-body="true"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import type { IApiQualityRecordQueryPageContents } from '@/interface/type';
import QualityHandle from './QualityHandle.vue';
import Text from '@/components/Text/index.vue';
import dayjs from 'dayjs';
import { dialogTip } from '@/pages/Workbench/Right/components/PatientTodo/index';
import { useUserStore } from '@/store/module/useUserStore';
import {
  ignoreQualityRecord,
  revampQualityRecord,
  retractQualityRecord,
} from '@/api/qualityControl';

const visible = ref(false);
const emit = defineEmits(['refresh']);

const props = defineProps<{
  data: IApiQualityRecordQueryPageContents;
}>();
const userStore = useUserStore();
const status = computed(() => {
  return props.data.status || 1;
});
const refresh = () => {
  emit('refresh');
};
const handleIgnore = data => {
  console.log('ignore');
  const params = {
    userId: userStore.accountId,
    userType: userStore.userRoles[0],
    all: false,
    qualityRecordId: data.id,
    qualityPointId: data.qualityPointId,
    patientId: data.patientId,
    revampDemand: '',
  };
  ignoreQualityRecord(params)
    .then(res => {
      console.log('ignore', res);
      dialogTip('忽略成功', 'success');
    })
    .finally(refresh);
};

const handleRevamp = () => {
  visible.value = true;
};

const handleSubmit = remark => {
  console.log('handleSubmit', remark);
  const params = {
    userId: userStore.accountId,
    userType: userStore.userRoles[0],
    all: false,
    qualityRecordId: props.data.id,
    qualityPointId: props.data.qualityPointId,
    patientId: props.data.patientId,
    revampDemand: remark,
  };
  revampQualityRecord(params)
    .then(res => {
      console.log('revamp', res);
      dialogTip('发起整改成功', 'success');
    })
    .finally(refresh);
};

const handleRetract = data => {
  const params = {
    userId: userStore.accountId,
    userType: userStore.userRoles[0],
    all: false,
    qualityRecordId: data.id,
    qualityPointId: data.qualityPointId,
    patientId: data.patientId,
    revampDemand: '',
  };
  retractQualityRecord(params)
    .then(res => {
      console.log('retract', res);
      dialogTip('撤回整改成功', 'success');
    })
    .finally(refresh);
};
</script>

<style></style>
