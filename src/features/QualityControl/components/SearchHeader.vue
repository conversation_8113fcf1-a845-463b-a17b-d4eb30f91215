<template>
  <div class="search-header flex items-center flex-wrap gap-16 p-16">
    <Select
      v-model="searchParams.type"
      :options="qualityTypeOptions"
      placeholder="请选择质控类型"
      style="width: 240px"
      clearable
      mode="edit"
      @change="typeChangeHandle"
    />
    <Select
      v-model="searchParams.qualityPointIds"
      :options="checkPointsOptions"
      placeholder="请选择质控点"
      style="width: 240px"
      mode="edit"
      multiple
      filterable
      clearable
    />
    <Select
      v-model="searchParams.qualityId"
      :options="qualityRecordStore.qualityPointPerson"
      placeholder="请选择被质控人"
      style="width: 240px"
      mode="edit"
      clearable
      filterable
    />
    <el-date-picker
      v-model="dateRange"
      type="daterange"
      placeholder="请选择生成日期"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
      :disabled-date="datePickerOption"
    />
    <Select
      v-model="searchParams.status"
      :options="qualityStatusOptions"
      placeholder="请选择质控状态"
      style="width: 240px"
      mode="edit"
      clearable
    />
    <div
      class="flex items-center gap-4 cursor-pointer text-primary"
      @click="resetHandler"
    >
      <img
        width="14"
        height="14"
        src="@/assets/imgs/reminder/reset.png"
        alt=""
      />
      重置
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import Select from '@/components/FormItem/Select.vue';
import dayjs from 'dayjs';
import { useQualityRecord } from '@/store/module/useQualityRecord';

const qualityRecordStore = useQualityRecord();

// 选项数据

const checkPointsOptions = computed(() =>
  qualityRecordStore.qualityPoint.filter(
    it => searchParams.value.type === it.type || !searchParams.value.type
  )
);

const qualityTypeOptions = ref<any[]>([
  {
    label: '超时风险',
    value: 1,
  },
  {
    label: '质量风险',
    value: 2,
  },
]);
const qualityStatusOptions = ref<any[]>([
  {
    label: '全部',
    value: '',
  },
  {
    label: '待处理',
    value: 1,
  },
  {
    label: '整改中',
    value: 3,
  },
  {
    label: '已忽略',
    value: 2,
  },
  {
    label: '已整改',
    value: 4,
  },
  {
    label: '未整改',
    value: 5,
  },
  {
    label: '已超期',
    value: 6,
  },
]);

// 搜索参数

interface SearchParams {
  type: string;
  qualityPointIds: string[];
  qualityId: string;
  dayStart: string | number;
  dayEnd: string | number;
  status: string | number;
}

const searchParams = ref<SearchParams>({
  type: '', // 1超时2质量
  qualityPointIds: [],
  qualityId: '',
  dayStart: '',
  dayEnd: '',
  status: '',
});
// 查询
const emit = defineEmits(['search']);
const searchHandler = () => {
  emit('search', { ...searchParams.value });
};
watch(
  searchParams,
  val => {
    console.log('搜索参数', val);
    searchHandler();
  },
  {
    deep: true,
    immediate: true,
  }
);
const getQualityPointPerson = () => {
  qualityRecordStore.getQualityPointPerson({ code: 2 });
};
getQualityPointPerson();

// 日期区间
const dateRange = ref<string[]>([]);

// 监听日期区间变化，自动同步到 searchParams
watch(dateRange, val => {
  searchParams.value.dayStart = val?.[0]
    ? dayjs(val[0]).startOf('day').valueOf()
    : '';
  searchParams.value.dayEnd = val?.[1]
    ? dayjs(val[1]).startOf('day').valueOf()
    : '';
});
const datePickerOption = (time: Date) => {
  return time.getTime() > Date.now();
};
// 重置
const resetHandler = () => {
  searchParams.value = {
    type: '',
    qualityPointIds: [],
    qualityId: '',
    dayStart: '',
    dayEnd: '',
    status: '',
  };
  dateRange.value = [];
};

const typeChangeHandle = val => {
  if (val && searchParams.value.qualityPointIds.length)
    searchParams.value.qualityPointIds = [];
};
</script>

<style scoped lang="less">
:deep(.el-date-editor) {
  max-width: 240px;
  width: 240px;
  box-sizing: border-box;
  overflow: hidden;
}
</style>
