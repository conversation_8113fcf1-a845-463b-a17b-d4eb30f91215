<template>
  <Dialog width="420" :visible="visible" title="发起整改" @close="closeHandler">
    <div class="w-full h-full pt-14 px-24">
      <div class="bg-[#F7F8FA] rounded-[4px] px-16 py-14">
        <div class="flex items-center gap-6">
          <img
            v-if="rowData?.description?.includes('质量')"
            width="16"
            height="16"
            src="@/assets/imgs/qualityControl/quality.svg"
            alt=""
          />
          <img
            v-else
            width="16"
            height="16"
            src="@/assets/imgs/qualityControl/overtime.svg"
            alt=""
          />
          <h4 class="text-[#3A4762] font-bold">{{ rowData?.description }}</h4>
        </div>
        <ul
          class="ml-22 *:flex *:items-center *:before:block *:before:content-[''] *:before:w-5 *:before:h-5 *:before:bg-[#7A8599] *:before:rounded-full *:before:mr-6"
        >
          <li v-for="(item, index) in rowData?.checkPoint || []" :key="index">
            {{ typeof item === 'object' ? item.desc : item }}
          </li>
          <li v-if="!rowData?.checkPoint">
            {{ rowData?.timeoutContent }}
          </li>
        </ul>
      </div>
      <ul
        class="grid grid-cols-8 gap-x-2 gap-y-12 mt-16 text-[#7A8599] *:text-nowrap"
      >
        <li class="col-span-3 flex items-center">
          <div class="w-56">被质控人：</div>
          <div class="text-[#3A4762] font-normal ml-12 w-72">
            <Text>
              {{ rowData?.qualityName }}
            </Text>
          </div>
        </li>
        <li class="col-span-5 flex items-center">
          生成时间：
          <div
            class="flex-1 text-[#3A4762] font-normal ml-12 flex items-center"
          >
            <Text>
              {{ dayjs(rowData?.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </Text>
          </div>
        </li>
        <li class="col-span-3 flex items-center">
          <div class="w-56">患者：</div>
          <div
            class="flex-1 text-[#3A4762] font-normal ml-12 flex items-center"
          >
            <Text>
              {{ rowData?.patientName }}
            </Text>
          </div>
        </li>
        <li class="col-span-5 flex items-center">
          <div class="w-56">工作室：</div>
          <div
            class="flex-1 text-[#3A4762] font-normal ml-12 flex items-center"
          >
            <Text>
              {{ rowData?.groupName }}
            </Text>
          </div>
        </li>
      </ul>
      <h4 class="text-[#3A4762] mt-16">
        整改要求
        <span>（非必填）：</span>
      </h4>
      <textarea
        id="222"
        v-model="remark"
        name="111"
        maxlength="200"
        :class="textStr"
      ></textarea>
      <h4 class="text-[#7A8599] mt-8">以上要求将提交给被质控人。</h4>
    </div>
    <template #footer>
      <div class="text-center pb-24">
        <el-button type="primary" @click="handleSubmit">确定</el-button>
        <el-button @click="closeHandler">取消</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import Dialog from '@/components/Dialog/index.vue';
import Text from '@/components/Text/index.vue';
import type { IApiQualityRecordQueryPageContents } from '@/interface/type';
import dayjs from 'dayjs';
const textStr =
  'block  h-full border border-[#DCDFE6] border-solid rounded-[4px] w-full mt-8  px-12 py-6 focus:border-[#2E6BE6] focus:outline-none placeholder:text-[#BAC8D4]';

withDefaults(
  defineProps<{
    visible: boolean;
    type: number;
    rowData: IApiQualityRecordQueryPageContents | undefined;
  }>(),
  {
    visible: false,
    type: 0,
    rowData: undefined,
  }
);
const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'submit', value: string);
}>();

const remark = ref<string>('');

const closeHandler = () => {
  emits('update:visible', false);
};
const handleSubmit = () => {
  console.log('handleSubmit');
  emits('submit', remark.value);
  remark.value = '';
  closeHandler();
};
</script>

<style></style>
