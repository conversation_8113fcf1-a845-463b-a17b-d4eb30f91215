<template>
  <div class="acceptance-check flex items-center">
    <div
      class="status-btns w-50 h-24 flex items-center justify-center"
      :class="{
        'btns-primary': taskInfo.taskStatus === 0,
        'btns-success':
          taskInfo.taskStatus === 1 ||
          taskInfo.taskStatus === 2 ||
          taskInfo.taskStatus === 3 ||
          taskInfo.taskStatus === 5,
        'btns-warning': taskInfo.taskStatus === 6,
      }"
    >
      {{ getTaskStatus(taskInfo.taskStatus) }}
    </div>
    <div v-if="taskInfo.taskStatus !== 0" class="ml-16">
      {{ getTips(taskInfo) }}
    </div>
    <div
      v-if="taskInfo.taskStatus === 5"
      class="pass btn w-80 h-32 flex items-center justify-center ml-16"
      @click="immediateAcceptance"
    >
      立即验收
    </div>
    <div
      v-if="taskInfo.taskMethod === 0 && taskInfo.taskStatus === 0"
      class="revocation btn w-80 h-32 flex items-center justify-center ml-8"
      @click="handleCancelTask()"
    >
      撤销任务
    </div>
  </div>

  <!-- 任务验收 -->
  <OcrAuditResult
    v-model:visible="visible"
    :source-type="sourceType"
    :source-id="sourceId"
    :task-status="taskInfo.taskStatus"
    :task-id="taskInfo.taskId"
    :task-method="taskInfo.taskMethod"
    :task-section="taskInfo.taskSection"
    tips="验收通过后才能完成数据填写，请谨慎操作。"
    title="转录验收"
    is-acceptance-check
    @update:visible="visible = false"
  />
</template>
<script setup lang="ts">
import OcrAuditResult from '@/features/OcrAuditResult/index.vue';
import { getTaskStatus } from '@/pages/Workbench/Header/components/TaskAcceptanceDrawer/config';
import { SourceTypeValues } from '@/constant';
import { ICaseTaskApi } from '@/pages/Workbench/Header/components/TaskAcceptanceDrawer/type';
import { quashTaskApi, queryCaseTaskApi } from '@/api/task';
import { getTips } from '@/pages/Workbench/Header/components/TaskAcceptanceDrawer/config';
import bus from '@/lib/bus';

interface IProps {
  /* 任务状态 */
  sourceType: SourceTypeValues;
  /* 任务id */
  sourceId?: number;
  /* 任务信息 */
  taskInfos?: any;
}
const props = withDefaults(defineProps<IProps>(), {
  sourceType: 0,
  sourceId: 0,
  taskInfos: {},
});

// 立即验收
const visible = ref(false);
const immediateAcceptance = () => {
  visible.value = true;
};

// 获取任务状态
const getCaseTaskStatus = () => {
  const info: ICaseTaskApi = {
    sourceId: props.sourceId!,
    taskType: props.sourceType!,
    taskMethod: 0,
  };
  queryCaseTaskApi(info).then((res: any) => {
    const { data } = res;
    taskInfo.value = data;
  });
};

// 撤销任务
const handleCancelTask = () => {
  ElMessageBox.confirm(
    '<div class="ml-36">是否确认撤回转录任务?</div><div class="ml-36">转录任务撤回后，实习生端将不可继续进行转录</div>',
    '提示',
    {
      dangerouslyUseHTMLString: true,
    }
  )
    .then(() => {
      quashTaskApi({ taskId: taskInfo.value.taskId }).then(res => {
        const { code } = res;
        if (code === 'E000000') {
          ElMessage({
            type: 'success',
            message: '操作成功!',
          });
          bus.emit('updata-task-refresh');
          getCaseTaskStatus();
        }
      });
    })
    .catch(() => {});
};

const taskInfo = ref();
watch(
  () => props.taskInfos,
  data => {
    taskInfo.value = data;
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="less" scoped>
.acceptance-check {
  .btn {
    border-radius: 2px;
    border: 1px solid;
    font-size: 14px;
    box-sizing: border-box;
    cursor: pointer;
  }
  .pass {
    border-color: #2e6be6;
    color: #2e6be6;
  }
  .reject {
    border-color: #e63746;
    color: #e63746;
  }
  .revocation {
    border-color: #e37221;
    color: #e37221;
  }
  .status-btns {
    font-size: 14px;
  }
  .btns-primary {
    color: #2e6be6;
    background: #e6eeff;
  }
  .btns-success {
    color: #2fb324;
    background: #e2f5e1;
  }
  .btns-warning {
    color: #e63746;
    background: #ffe6e7;
  }
}
</style>
