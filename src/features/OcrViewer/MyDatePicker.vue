<template>
  <div class="date-box">
    <el-date-picker
      v-model="timeValue"
      type="date"
      placeholder="请选择日期"
      format="YYYY-MM-DD"
      v-bind="attrs"
      :disabled-date="disableFutureDate ? pickerOptions : null"
      value-format="x"
      @change="(value: any) => emit('update:time', value)"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'MyDatePicker',
});
interface TypeDate {
  disableFutureDate?: boolean;
  time: number | string;
}
const props = defineProps<TypeDate>();
const emit = defineEmits(['update:time']);
const attrs = useAttrs();
const timeValue = ref<string | number>('');

const pickerOptions = (time: { getTime: () => number }) => {
  return time.getTime() > Date.now();
};
watch(
  () => props.time,
  newValue => {
    timeValue.value = newValue;
  },
  { immediate: true }
);
</script>

<style scoped lang="less">
.date-box {
  :deep(.el-date-editor) {
    width: 240px;
    height: 34px !important;
  }
  :deep(.el-input__wrapper) {
    border-radius: 2px;
  }
}
</style>
