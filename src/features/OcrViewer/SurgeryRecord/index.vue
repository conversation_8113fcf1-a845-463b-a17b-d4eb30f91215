<template>
  <div>
    <SurgeryRecordHead
      v-if="!locatedPatientHistoryId"
      disable-future-date
      @get-record-params="getRecordParams"
    />
    <Surgery
      ref="surgeryRef"
      :schema="schema"
      :from="'ocr'"
      mode="edit"
      :form-data="formData"
    />
  </div>
</template>

<script setup lang="ts">
import SurgeryRecordHead from './components/SurgeryRecordHead.vue';
import { TypeHeadParmas } from './components/type';
import globalBus from '@/lib/bus';
import useGlobal from '@/store/module/useGlobal';
import { useOcrScan } from '@/store/module/useOcrScan';
// import SurgicalInformation from '@/features/PatientRecord/AddHospitalized/SurgicalInformation.vue';
import Surgery from '@/features/Surgery/index.vue';
import { useSaveOcrInfo } from '@/store/module/useSaveOcrInfo';
import { FormCategory } from '@/constant';
import { useDynamicSubForm } from '@/hooks';

const { schema } = useDynamicSubForm(FormCategory.OPERATION_RECORD);
const locatedPatientHistoryId = inject('locatedPatientHistoryId');
const globalData = useGlobal();
const saveOcrStore = useSaveOcrInfo();
const ocrStore = useOcrScan();
const formData = ref<any[]>([]);
const surgeryRef = shallowRef();

const recordParams = ref<TypeHeadParmas>({
  saveType: 0,
});

const getRecordParams = (value: {
  patientHistoryId: null | number;
  date: string;
  saveType: 0 | 1;
}) => {
  recordParams.value = value;
  saveOcrStore.updateCurrentDataTimeAndId(value);
};

const saveData = async () => {
  if (!recordParams.value.date && !locatedPatientHistoryId) {
    ElMessage.error('请选择入院时间!');
    return;
  }
  const extraParams: any = saveOcrStore.getExtraParams();
  const params = {
    orc: true,
    other_date: recordParams.value.date ?? null,
    source_id: locatedPatientHistoryId
      ? locatedPatientHistoryId
      : recordParams.value.patientHistoryId,
    source_type: 0,
    patient_id: globalData.userId as number,
    operation_accessory: ocrStore.globalImgInfo.currentImgUrl.url
      ? [ocrStore.globalImgInfo.currentImgUrl.url]
      : [],
    entry_task: extraParams.entry_task,
    sub_task_id: extraParams.sub_task_id,
  };
  return surgeryRef.value?.submit(params)?.then(res => {
    if (res) {
      globalBus.emit('close-ocr-dialog');
      globalBus.emit('saved-ocr-info');
      if (locatedPatientHistoryId || recordParams.value.patientHistoryId) {
        let id = locatedPatientHistoryId
          ? locatedPatientHistoryId
          : recordParams.value.patientHistoryId;

        globalBus.emit('refresh-attachment', id as number);
        globalBus.emit('refresh-record-data', id as number);
      } else {
        globalBus.emit('updata-review-list');
      }
      return true;
    }
  });
};

const setFormData = (outData = null) => {
  const data = ocrStore.aiResultData[FormCategory.OPERATION_RECORD];
  const finalData = outData ? outData : data;
  formData.value = finalData;
};

const getSaveData = () => {
  return {
    data: { ...formData.value },
    source_type: 0,
    key: FormCategory.DISCHARGE_REPORT,
  };
};
watch(
  () => ocrStore.aiResultData,
  () => {
    setFormData();
  },
  { deep: true }
);

defineExpose({
  saveData,
  setFormData,
  getSaveData,
});
onMounted(() => {
  setFormData();
});
defineOptions({
  name: 'SurgeryRecord',
});
</script>

<style scoped lang="less">
.select-box {
  :deep(.el-select) {
    width: 240px;
    border-radius: 2px;
  }
  :deep(.el-input__inner) {
    height: 32px;
  }
  :deep(.el-input__wrapper) {
    border-radius: 2px;
  }
}
:deep(.el-textarea__inner) {
  border-radius: 2px;
  font-size: 14px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  color: #203549;
  line-height: 20px;
  padding-bottom: 20px;
}
.form-label {
  font-size: 14px;
  font-weight: bold;
  color: #111111;
  .required-icon {
    color: #ea1212;
  }
}
.surgery-title {
  box-sizing: border-box;
  min-width: 112px;
  height: 40px;
  background: #f7f8fa;
  border: 1px solid #dcdee0;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: auto;
  user-select: none;
  padding: 0 20px;
}
.active {
  background-color: #ffffff;
  border-bottom: none;
}
.surgery-edit {
  background-color: #ffffff;
}
.add-btn {
  display: flex;
  align-items: center;
  margin-top: 8px;
  font-size: 14px;
  font-weight: 400;
  color: #2e6be6;
  line-height: 20px;
  cursor: pointer;
}
</style>
