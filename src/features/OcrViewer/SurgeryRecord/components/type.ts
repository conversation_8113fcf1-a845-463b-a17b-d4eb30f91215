//心率失常器械植入类型
export interface TypeManufacturer {
  type?: number;
  remark?: string;
}
export interface TypeResonseSegment {
  segmentId: number;
  alias: string;
  location: string;
  segmentName: string;
  degree?: number;
  support?: string;
  balloon?: number;
  check?: string | number;
  show?: boolean;
  manufacturer?: TypeManufacturer;
}

export interface TypeLesionParam {
  //病变类型
  type: number;
  //病变id
  lesionId: number[];
}

export interface PciType {
  name: string;
  value: 1 | 2 | 3;
}

export type PciTypeList = PciType[];

export interface TypeDegree {
  name: string;
  value: number;
}

export type TypeDegreeGuideList = TypeDegree[];

/**响应数据类型**/
export interface TypeResponseLesion {
  type: number;
  lesionId: number[];
}

export interface TypeOperationChild {
  childOperationId: number;
  childOperationName: string;
  manufacturer?: TypeManufacturer;
}

export interface TypeHeartNode {
  id?: string;
  pulseWidth?: number;
  threshold?: number;
  impedance?: number;
  perception?: number;
  part?: number;
  [propName: string]: any;
}

export interface TypeHeartInsert {
  operationId?: number;
  operationName?: string;
  manufacturer?: TypeManufacturer;
  type?: number[];
  node?: TypeHeartNode[];
  electricQuantity?: number;
  code?: number;
  imbeddingTime?: string;
  testInstrument?: string;
  electrodeType?: string;
  surgeryType?: string;
  electrodeLocation?: string;
  children?: [];
  finishMustValue?: boolean;
}

export interface TypeHeartInsertSurgery {
  operationId: number;
  operationName: string;
  data: TypeHeartInsert;
  children?: TypeHeartInsert[];
}

export interface TypeResponseSurgeryChild {
  operationId?: number;
  operationName?: string;
  operationType?: number | null;
  typeName?: string;
  data?: TypeResonseSegment[] | TypeOperationChild[] | TypeHeartInsert[];
  lesion?: TypeResponseLesion[];
  guide?: number | string;
}
export interface TypeSurgeryItem {
  surgeryId?: number;
  surgeryTime: string;
  conclusion: string;
  surgeryInfo: TypeResponseSurgeryChild[];
  accessory?: string[];
}

export interface TypeHeadParmas {
  date?: string;
  patientHistoryId?: null | number;
  saveType: number;
}
