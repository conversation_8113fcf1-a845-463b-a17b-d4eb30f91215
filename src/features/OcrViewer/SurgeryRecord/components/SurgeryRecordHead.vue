<template>
  <div class="form-label">
    <span class="required-icon">*</span>
    入院记录
  </div>
  <el-radio-group
    v-model="recordStatus"
    class="my-radio ml-4"
    @change="changeStatus"
  >
    <el-radio :label="1" size="large">已有记录</el-radio>
    <el-radio :label="0" size="large">
      <span>没有记录</span>
      <span class="radio-tips">（创建记录）</span>
    </el-radio>
  </el-radio-group>
  <div class="flex">
    <div v-if="recordStatus === 1">
      <div class="form-label mb-8">
        <span class="required-icon">*</span>
        记录日期
      </div>
      <div class="select-box">
        <el-select v-model="patientHistoryId" placeholder="请选择记录">
          <el-option
            v-for="(item, index) in timeOptions"
            :key="index"
            :label="item.name"
            :value="item.patientHistoryId"
          />
        </el-select>
      </div>
    </div>
    <div v-if="recordStatus === 0">
      <div class="form-label mb-8">
        <span class="required-icon">*</span>
        入院日期
      </div>
      <MyDatePicker
        v-model:time="date"
        :disable-future-date="disableFutureDate"
      />
    </div>
  </div>
  <div class="bottom-line"></div>
</template>
<script setup lang="ts">
import MyDatePicker from '../../MyDatePicker.vue';
import { getInHospitalRecords } from '@/api/ocr';
import { useOcrScan } from '@/store/module/useOcrScan';

import useGlobal from '@/store/module/useGlobal';

import dayjs from 'dayjs';

interface IProps {
  disableFutureDate?: boolean;
}
defineProps<IProps>();
type TypeRecordReqMap = {
  [key: number]: (data: any) => Promise<unknown>;
};

const globalData = useGlobal();

const ocrStore = useOcrScan();

const emit = defineEmits(['getRecordParams']);

interface TypeTimeOptions {
  name: string;
  patientHistoryId: number;
  inTime?: string | number;
}
const recordStatus = ref(0);

const timeOptions = ref<TypeTimeOptions[]>([]);

const patientHistoryId = ref<number | null>(null);

const date = ref<number | string>('');

const inTime = computed(() => {
  console.log('$debug:  timeOptions.value', timeOptions.value);
  if (patientHistoryId.value) {
    return timeOptions.value.find(
      item => item.patientHistoryId === patientHistoryId.value
    )?.inTime;
  } else {
    return null;
  }
});

const recordRequestMap: TypeRecordReqMap = {
  5: getInHospitalRecords,
};

const getRecordsList = () => {
  recordRequestMap[ocrStore.annexType]({
    userId: globalData.userId,
  }).then(data => {
    timeOptions.value = [];
    if ((data as any).historyInfo) {
      (data as any).historyInfo.forEach((item: any) => {
        const { patientHistoryId, inTime, isGroup } = item;
        timeOptions.value.push({
          patientHistoryId: patientHistoryId,
          inTime: inTime,
          name:
            dayjs(inTime).format('YYYY-MM-DD') +
            '' +
            (isGroup ? ' 入组' : ' 入院'),
        });
      });
    }
  });
};

const changeStatus = (value: number) => {
  if (value === 1) {
    date.value = '';
  }
  if (value === 0) {
    patientHistoryId.value = null;
  }
  getRecordsList();
};
const initDate = () => {
  try {
    if (ocrStore.ocrResultData?.inHospitalTime) {
      date.value = dayjs(ocrStore.ocrResultData.inHospitalTime).valueOf();
    }
  } catch {
    date.value = '';
  }
};
watch(
  [date, patientHistoryId, recordStatus],
  newValue => {
    emit('getRecordParams', {
      patientHistoryId: newValue[1],
      date: recordStatus.value === 0 ? newValue[0] : inTime.value,
      saveType: recordStatus.value,
    });
  },
  {
    immediate: true,
  }
);
onMounted(() => {
  getRecordsList();
  initDate();
});
</script>
<style scoped lang="less">
.my-radio {
  .radio-tips {
    color: #708293;
  }
  :deep(.el-radio__label) {
    font-size: 14px;
    font-weight: 400;
    color: #323233;
  }
  :deep(.el-radio__inner) {
    width: 16px;
    height: 16px;
    // 去掉默认的中心填充
    &::after {
      display: none;
      transition: none;
    }
  }
  :deep(.el-radio__input.is-checked) {
    .el-radio__inner {
      padding: 2px;
      background-color: #0a73e4;
      background-clip: content-box;
    }
  }
}
.select-box {
  :deep(.el-select) {
    width: 240px;
    border-radius: 2px;
  }
  :deep(.el-input__inner) {
    height: 32px;
  }
  :deep(.el-input__wrapper) {
    border-radius: 2px;
  }
}
.form-label {
  font-size: 14px;
  font-weight: bold;
  color: #111111;
  .required-icon {
    color: #ea1212;
  }
}
.bottom-line {
  padding-top: 14px;
  padding-bottom: 14px;
  border-bottom: 1px solid #dcdee0;
}
</style>
