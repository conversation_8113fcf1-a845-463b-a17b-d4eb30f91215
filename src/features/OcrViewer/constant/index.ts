import { SelectOptionMapType } from '@/features/OcrViewer/ConfirmScanDialog/type';

export const selectOptions: SelectOptionMapType = {
  HOSPITALIZATION_RECORDS: [
    {
      label: '病历报告',
      options: [
        {
          value: 1,
          label: '入院记录',
        },
        {
          value: 2,
          label: '出院记录',
        },
        {
          value: 5,
          label: '手术记录',
        },
      ],
    },
    {
      label: '检验报告',
      options: [
        {
          value: 3,
          label: '检验报告',
        },
      ],
    },
    {
      label: '检查报告',
      options: [
        {
          value: 6,
          label: '12导联心电图',
        },
        {
          value: 7,
          label: '动态心电图',
        },
        {
          value: 8,
          label: '心脏彩超',
        },
      ],
    },
  ],
  REVIEW_RECORDS: [
    {
      label: '检验报告',
      options: [
        {
          value: 3,
          label: '检验报告',
        },
      ],
    },
    {
      label: '检查报告',
      options: [
        {
          value: 6,
          label: '12导联心电图',
        },
        {
          value: 7,
          label: '动态心电图',
        },
        {
          value: 8,
          label: '心脏彩超',
        },
      ],
    },
  ],
  OUTPATIENT_RECORDS: [
    {
      label: '病历报告',
      options: [
        {
          value: 4,
          label: '门诊报告',
        },
        {
          value: 9,
          label: '门诊处方',
        },
      ],
    },
    {
      label: '检验报告',
      options: [
        {
          value: 3,
          label: '检验报告',
        },
      ],
    },
    {
      label: '检查报告',
      options: [
        {
          value: 6,
          label: '12导联心电图',
        },
        {
          value: 7,
          label: '动态心电图',
        },
        {
          value: 8,
          label: '心脏彩超',
        },
      ],
    },
  ],
  ALL_RECORDS: [
    {
      label: '病历报告',
      options: [
        {
          value: 1,
          label: '入院记录',
        },
        {
          value: 2,
          label: '出院记录',
        },
        {
          value: 4,
          label: '门诊报告',
        },
        {
          value: 9,
          label: '门诊处方',
        },
        {
          value: 5,
          label: '手术记录',
        },
      ],
    },
    {
      label: '检验报告',
      options: [
        {
          value: 3,
          label: '检验报告',
        },
      ],
    },
    {
      label: '检查报告',
      options: [
        {
          value: 6,
          label: '12导联心电图',
        },
        {
          value: 7,
          label: '动态心电图',
        },
        {
          value: 8,
          label: '心脏彩超',
        },
      ],
    },
  ],
};
