import { ref } from 'vue';
import {
  checkPointCross,
  checkPointConcave,
  processcoorDinates,
  convertParamsPointToPoint,
  dealOutBorder,
  dealGlobalCoverPoints,
} from '../utils';

import { useOcrScan } from '@/store/module/useOcrScan';

const ocrStore = useOcrScan();

import { debounce, cloneDeep, throttle } from 'lodash-es';
import { Point, listType } from '../type';
export const useimgEnhanced = () => {
  const imgW = ref<number>(0);
  const imgH = ref<number>(0);
  //图片父级的宽度也是图片旋转后的宽度
  const imgWrapperW = ref<number>(0);
  //图片父级的高度度也是图片旋转后的高度
  const imgWrapperH = ref<number>(0);
  const wrapperBoxW = ref<number>(0);
  const wrapperBoxH = ref<number>(0);
  const deg = ref<number>(0);
  const top = ref<number>(0);
  const left = ref<number>(0);
  const scale = ref<string>('');
  const size = ref<number>(0);
  const loading = ref<boolean>(false);
  const imgRealWidth = ref<number>(0);
  const imgRealHeight = ref<number>(0);
  const imgContent = ref<HTMLDivElement | null>(null);
  //绘制相关参数

  const imgWrap = ref();
  const img = ref();

  const canvas = ref();
  const canvasObj = ref<any>();
  //画图
  const openDraw = ref(true);
  const pointList = ref<Array<Point>>([]);
  const pointListData = ref<Array<Point>>([
    { x: 40, y: 40 },
    { x: 40, y: 80 },
    { x: 80, y: 80 },
    { x: 80, y: 40 },
  ]);

  const minPointNum = ref(4);
  const sweepList = ref<Array<Array<Point>>>([]);
  const delList = ref<Array<Array<Point>>>([]);
  const movePoint = ref({ x: 0, y: 0 });
  const lastPointList = ref<Array<Point>>([]);
  const transformedPointList = ref<Array<Point>>([]);
  let timer: NodeJS.Timeout;

  const handleRotate = async (
    degValue: number,
    boxW: number,
    boxH: number,
    url?: string
  ) => {
    deg.value += degValue;
    if (deg.value >= 360 || deg.value <= -360) {
      deg.value = 0;
    }
    size.value = 0;
    //如果图片时横向的,并且高大于款需要重新计算宽高
    if (
      deg.value === 90 ||
      deg.value === -90 ||
      deg.value === 270 ||
      deg.value === -270
    ) {
      // initImage(wrapperBoxH.value, wrapperBoxW.value);
      await initImage(boxH, boxW, url);
      // await initImage(boxW, boxH, url);
    } else {
      // initImage(wrapperBoxW.value, wrapperBoxH.value);
      await initImage(boxW, boxH, url);
    }
  };

  //初始化图片尺寸和位置
  const initImage = async (boxW: number, boxH: number, url?: string) => {
    if (url) {
      //url存在代表首次初始化
      loading.value = true;
      const { width, height } = await getImgSize(url);
      loading.value = false;
      imgRealWidth.value = width;
      imgRealHeight.value = height;
      wrapperBoxW.value = boxW;
      wrapperBoxH.value = boxH;
    }
    // 获取高宽比例
    //宽大于高的情况
    if (imgRealWidth.value >= imgRealHeight.value) {
      if (
        deg.value === 90 ||
        deg.value === -90 ||
        deg.value === 270 ||
        deg.value === -270
      ) {
        const whRatio = imgRealHeight.value / imgRealWidth.value;
        const hwRatio = imgRealWidth.value / imgRealHeight.value;

        imgH.value = whRatio * boxH;
        const nih = imgH.value;
        if (nih > boxW) {
          imgH.value = boxW;
          imgW.value = hwRatio * boxW;
        } else {
          imgW.value = boxH - 40;
          imgH.value = whRatio * (boxH - 40);
        }
      } else {
        const whRatio = imgRealWidth.value / imgRealHeight.value;
        const hwRatio = imgRealHeight.value / imgRealWidth.value;
        imgH.value = hwRatio * boxW;
        const nih = imgH.value;

        if (nih > boxH) {
          imgH.value = boxH;
          imgW.value = whRatio * boxH;
        } else {
          imgW.value = boxW - 40;
          imgH.value = hwRatio * (boxW - 40);
        }
      }
    } else {
      //宽小于高
      if (imgRealWidth.value >= boxW) {
        imgW.value = (boxH / imgRealHeight.value) * imgRealWidth.value;
        imgH.value = boxH;
      } else {
        imgW.value = (boxH / imgRealHeight.value) * imgRealWidth.value;
        imgH.value = boxH;
      }
    }

    //根据旋转的角度渲染img外部盒子的尺寸外部盒子的尺寸不旋转,Canvas也不旋转
    if (
      deg.value === 90 ||
      deg.value === -90 ||
      deg.value === 270 ||
      deg.value === -270
    ) {
      imgWrapperW.value = imgH.value;
      imgWrapperH.value = imgW.value;
    } else {
      imgWrapperW.value = imgW.value;
      imgWrapperH.value = imgH.value;
    }
    initDefaultPointList(ocrStore.cropperImgInfo.pointData);
    setCanvasSize(imgWrapperW.value, imgWrapperH.value);
    scale.value = `scale(1) rotate(${deg.value}deg)`;
  };
  const getImgSize = (url: string): any => {
    return new Promise(resolve => {
      const imgObj = new Image();
      imgObj.setAttribute('crossOrigin', 'anonymous');
      imgObj.src = url;
      imgObj.onload = () => {
        const canvas = document.createElement('canvas');
        //初始化
        canvas.width = imgObj.width;
        canvas.height = imgObj.height;
        const context = canvas.getContext('2d');
        context!.drawImage(imgObj, 0, 0, imgObj.width, imgObj.height);
        //这里的dataurl就是base64类型
        const dataURL = canvas.toDataURL('image/png', 0.5);
        //我们要的base64就拿到了
        resolve({
          width: imgObj.width,
          height: imgObj.height,
          baseUrl: dataURL,
        });
      };
    });
  };

  //绘制相关
  //设置canvas大小 使其和图片大小一致
  const setCanvasSize = (width: number, height: number) => {
    const canvasRef = canvas.value;
    canvasRef.setAttribute('width', width);
    canvasRef.setAttribute('height', height);
    canvasObj.value = canvasRef.getContext('2d');
    canvasObj.value.lineWidth = 1;
    canvasObj.value.strokeStyle = '#687072';

    initPointPosition(pointListData.value);
  };

  //绘制圆点
  const drawCircle = (left: number, top: number, color: string) => {
    const pointDom = document.createElement('div');
    pointDom.setAttribute('class', 'x-point');
    const style = `background-color:${color};
                   left:${left}px;
                   top:${top}px;
                   width: 40px;
                   height: 40px;
                   border-radius: 50%;
                   position: absolute;
                   touch-action: none;
                   z-index: 2;
                   cursor: pointer;
                   transform: translate(-50%, -50%);`;
    pointDom.setAttribute('style', style);

    const move = throttle((e: any) => {
      const oldLeft = +pointDom.style.left.slice(0, -2);
      const oldTop = +pointDom.style.top.slice(0, -2);
      const left = oldLeft - (movePoint.value.x - e.pageX);
      const top = oldTop - (movePoint.value.y - e.pageY);

      movePoint.value = {
        x: e.pageX,
        y: e.pageY,
      };

      pointDom.style.left = `${left}px`;
      pointDom.style.top = `${top}px`;

      const setPosition = (list: any) => {
        list.some((item: any) => {
          return item.some((it: any) => {
            const isX = ~~it.x <= ~~oldLeft + 3 && ~~it.x >= ~~oldLeft - 3;
            const isY = ~~it.y <= ~~oldTop + 3 && ~~it.y >= ~~oldTop - 3;
            if (isX && isY) {
              it.x = left;
              it.y = top;
              return true;
            }
            return false;
          });
        });
      };

      setPosition(sweepList.value);
      setPosition(delList.value);

      timer && clearTimeout(timer);
      timer = setTimeout(() => {
        drawList({ point: { x: 0, y: 0 }, resetPoint: false });
      });
      e.preventDefault();
      const res = checkIsValidPic(pointList.value[0], pointList.value);
      if (res === false) {
        imgWrap.value.removeEventListener('pointermove', move);
      }
    }, 16);

    pointDom.onpointerdown = (e: any) => {
      movePoint.value = {
        x: e.pageX,
        y: e.pageY,
      };
      lastPointList.value = cloneDeep(sweepList.value[0]);
      e.stopPropagation();
      if (openDraw.value) {
        if (pointList.value.length > 2) {
          closeFigure();
        }
        return;
      }
      imgWrap.value.addEventListener('pointermove', move);
    };

    pointDom.onpointerup = () => {
      if (!openDraw.value) {
        drawList();
      }
      //计算渲染坐标转接口参数坐标
      if (
        deg.value === 90 ||
        deg.value === -90 ||
        deg.value === 270 ||
        deg.value === -270
      ) {
        transformedPointList.value = processcoorDinates(
          imgRealHeight.value,
          imgWrapperW.value,
          sweepList.value[0],
          1
        );
      } else {
        transformedPointList.value = processcoorDinates(
          imgRealWidth.value,
          imgWrapperW.value,
          sweepList.value[0],
          1
        );
      }
      imgWrap.value.removeEventListener('pointermove', move);
    };

    // pointDom.onpointerleave = () => {
    //   pointDom.removeEventListener('pointermove', move);
    // };
    imgWrap.value.appendChild(pointDom);
  };

  //检查移动是否合法
  const checkIsValidPic = (item, list) => {
    if (!checkPointCross(item, list) || !checkPointConcave(item, list, true)) {
      ElMessage.error('图形出现凹多边形，请重新绘制！');
      reDrawPic();
      return false;
    } else {
      return true;
    }
  };

  //重新绘制
  const reDrawPic = () => {
    sweepList.value = [];
    pointList.value = [];
    canvasObj.value.clearRect(0, 0, 10000, 10000);
    const pointDoms = Array.from(document.getElementsByClassName('x-point'));
    pointDoms.forEach(item => {
      imgWrap.value.removeChild(item);
    });
    initPointPosition(lastPointList.value);
  };

  const closeFigure = () => {
    // 检查部分
    if (!checkPointCross(pointList.value[0], pointList.value)) {
      ElMessage.error('闭合图形时发生横穿线，请重新绘制！');
      //重置坐标
      const coverPoint = dealGlobalCoverPoints(
        imgWrapperW.value,
        imgWrapperH.value
      );
      resetPoint(coverPoint);
      return;
    }
    if (!checkPointConcave(pointList.value[0], pointList.value, true)) {
      ElMessage.error('闭合图形时出现凹多边形，请重新绘制！');
      //重置坐标
      const coverPoint = dealGlobalCoverPoints(
        imgWrapperW.value,
        imgWrapperH.value
      );
      resetPoint(coverPoint);
      return;
    }
    if (pointList.value.length >= minPointNum.value) {
      // 符合要求
      canvasObj.value.fillStyle = 'rgba(29,179,219,0.4)';
      for (let i = 0; i < pointList.value.length - 2; i++) {
        canvasObj.value.lineTo(pointList.value[i].x, pointList.value[i].y);
      }
      canvasObj.value.closePath();
      canvasObj.value.stroke();
      canvasObj.value.fill();
      sweepList.value.push(pointList.value);
      openDraw.value = false;
    } else {
      ElMessage.error('最低绘制3个点！');
    }
  };

  //
  const resetPoint = arr => {
    sweepList.value = [];
    pointList.value = [];
    canvasObj.value.clearRect(0, 0, 10000, 10000);
    const pointDoms = Array.from(document.getElementsByClassName('x-point'));
    pointDoms.forEach(item => {
      imgWrap.value.removeChild(item);
    });
    pointListData.value = arr;
    //计算渲染坐标转接口参数坐标
    if (
      deg.value === 90 ||
      deg.value === -90 ||
      deg.value === 270 ||
      deg.value === -270
    ) {
      transformedPointList.value = processcoorDinates(
        imgRealHeight.value,
        imgWrapperW.value,
        pointListData.value,
        1
      );
    } else {
      transformedPointList.value = processcoorDinates(
        imgRealWidth.value,
        imgWrapperW.value,
        pointListData.value,
        1
      );
    }
    initPointPosition(pointListData.value);
  };
  const drawList = (
    params: listType = { point: { x: 0, y: 0 }, resetPoint: true }
  ) => {
    if (params.resetPoint) {
      const pointDoms = Array.from(document.getElementsByClassName('x-point'));
      pointDoms.forEach(item => {
        imgWrap.value.removeChild(item);
      });
    }

    canvasObj.value.clearRect(0, 0, 10000, 10000);
    try {
      sweepList.value.forEach((item, i) => {
        drawPic(item, 'rgba(29,179,219,0.4)');
        if (
          params.point.x != 0 &&
          params.point.y != 0 &&
          canvasObj.value.isPointInPath(params.point.x, params.point.y)
        ) {
          if (!!delList.value.length) {
            sweepList.value.push(delList.value[0]);
          }
          delList.value = sweepList.value.splice(i, 1);
          throw new Error();
        }

        if (params.resetPoint) {
          item.forEach((subItem: Point) => {
            drawCircle(subItem.x, subItem.y, 'rgb(0,180,226)');
          });
        }
      });

      delList.value.forEach(item => {
        drawPic(item, 'rgba(233,79,79, 0.5)');
        if (
          params.point.x != 0 &&
          params.point.y != 0 &&
          canvasObj.value.isPointInPath(params.point.x, params.point.y)
        ) {
          const temp = { ...item };
          sweepList.value.push(temp);
          delList.value = [];
          throw new Error();
        }

        if (params.resetPoint) {
          item.forEach((subItem: Point) => {
            drawCircle(subItem.x, subItem.y, 'rgb(233,79,79)');
          });
        }
      });
    } catch (e) {
      drawList();
    }
  };

  const drawPic = (item: any, bgColor: string) => {
    canvasObj.value.fillStyle = bgColor;
    canvasObj.value.beginPath();
    canvasObj.value.moveTo(item[0].x, item[0].y);
    item.forEach((subItem: Point, index: number) => {
      if (index > 0) {
        canvasObj.value.lineTo(subItem.x, subItem.y);
        canvasObj.value.stroke();
      }
    });
    canvasObj.value.closePath();
    canvasObj.value.stroke();
    canvasObj.value.fill();
  };

  //根据坐标绘制图形初始化坐标
  const initPointPosition = (pointListData: any) => {
    const pointColor = 'rgba(0,180,226)';
    pointListData.forEach((item: Point) => {
      if (pointList.value.length === 0) {
        drawCircle(item.x, item.y, pointColor);
        canvasObj.value.beginPath();
        canvasObj.value.moveTo(item.x, item.y);
      } else {
        drawCircle(item.x, item.y, pointColor);
        // 已经有点了，连成线
        canvasObj.value.beginPath();
        const lastPoint = pointList.value.slice(-1)[0];
        canvasObj.value.moveTo(lastPoint.x, lastPoint.y);
        canvasObj.value.lineTo(item.x, item.y);
        canvasObj.value.stroke();
      }
      pointList.value.push({
        ...item,
      });
    });
    //绘制完成闭合图形
    closeFigure();
  };

  //初始化pointList 接口默认返回坐标

  const initDefaultPointList = arr => {
    const res = convertParamsPointToPoint(arr);
    transformedPointList.value = res;
    let originWidth: number;
    const realWidth: number = imgWrapperW.value;
    //计算接口参数转渲染坐标
    if (
      deg.value === 90 ||
      deg.value === -90 ||
      deg.value === 270 ||
      deg.value === -270
    ) {
      originWidth = imgRealHeight.value;
    } else {
      originWidth = imgRealWidth.value;
    }
    const pointArr = processcoorDinates(originWidth, realWidth, res, 0);
    pointListData.value = dealOutBorder(
      pointArr,
      imgWrapperW.value,
      imgWrapperH.value
    );
  };
  return {
    loading,
    imgW,
    imgH,
    imgWrapperW,
    imgWrapperH,
    deg,
    top,
    left,
    scale,
    size,
    imgContent,
    imgRealWidth,
    imgRealHeight,
    handleRotate,
    initImage,
    //绘制相关
    canvas,
    imgWrap,
    img,
    transformedPointList,
    initDefaultPointList,
  };
};
