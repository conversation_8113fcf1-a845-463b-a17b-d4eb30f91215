interface Point {
  x: number;
  y: number;
}

// /**
//  * 获取动态图片地址
//  * @param {url} string
//  * @returns {string}
//  */
// export const getImage = (url: string) => {
//   let path: string = `../assets/images/${url}.png`;
//   const modules: any = import.meta.globEager('../assets/images/**/**.png');
//   return modules[path].default;
// };

/**
 * 检查图形有没有横穿
 * @param point
 * @param pointList
 * @returns
 */
export function checkPointCross(point: Point, pointList: Array<Point>) {
  if (pointList.length < 3) {
    return true;
  }
  for (let i = 0; i < pointList.length - 2; ++i) {
    const re = isPointCross(
      pointList[i],
      pointList[i + 1],
      pointList[pointList.length - 1],
      point
    );
    if (re) {
      return false;
    }
  }
  return true;
}

/**
 * 检查是否是凹图形
 * @param point
 * @param pointList
 * @param isEnd
 * @returns
 */
export function checkPointConcave(
  point: Point,
  pointList: Array<Point>,
  isEnd: boolean
) {
  if (pointList.length < 3) {
    return true;
  }
  if (
    isPointConcave(
      pointList[pointList.length - 3],
      pointList[pointList.length - 2],
      pointList[pointList.length - 1],
      point
    )
  )
    return false;

  // 如果是闭合时，point为起始点，需要再判断最后两条线与第一条线是否形成凹图形
  if (isEnd) {
    if (
      isPointConcave(
        pointList[pointList.length - 2],
        pointList[pointList.length - 1],
        pointList[0],
        pointList[1]
      )
    )
      return false;
    if (
      isPointConcave(
        pointList[pointList.length - 1],
        pointList[0],
        pointList[1],
        pointList[2]
      )
    )
      return false;
  }
  return true;
}

/**
 * 检查点有没有与当前点位置太近，如果太近就不认为是一个点
 * @param point
 * @param pointList
 * @param minPointNum
 * @returns
 */
export function checkPointClose(
  point: Point,
  pointList: Array<Point>,
  minPointNum: number
) {
  for (let i = 0; i < pointList.length; ++i) {
    const distance = Math.sqrt(
      Math.abs(pointList[i].x - point.x) + Math.abs(pointList[i].y - point.y)
    );
    if (distance > 6) {
      continue;
    }
    // 如果是在第一个点附近点的，那就认为是在尝试闭合图形
    if (pointList.length >= minPointNum && i === 0) {
      return 'closeFirst';
    }
    return false;
  }
  return true;
}

/**
 * 辅助函数 检查两个线是否交叉
 * @param line1P1
 * @param line1P2
 * @param line2P1
 * @param line2P2
 * @returns
 */
export function isPointCross(
  line1P1: Point,
  line1P2: Point,
  line2P1: Point,
  line2P2: Point
) {
  const euqal =
    isEuqalPoint(line1P1, line2P1) ||
    isEuqalPoint(line1P1, line2P2) ||
    isEuqalPoint(line1P2, line2P1) ||
    isEuqalPoint(line1P2, line2P2);
  const re1 = isDirection(line1P1, line1P2, line2P1);
  const re2 = isDirection(line1P1, line1P2, line2P2);
  const re3 = isDirection(line2P1, line2P2, line1P1);
  const re4 = isDirection(line2P1, line2P2, line1P2);
  const re11 = re1 * re2;
  const re22 = re3 * re4;
  if (re11 < 0 && re22 < 0) return true;
  if (euqal) {
    if (re1 === 0 && re2 === 0 && re3 === 0 && re4 === 0) return true;
  } else {
    if (re11 * re22 === 0) return true;
  }
  return false;
}

/**
 * 辅助函数 检查三个线是否凹凸
 * @param point1
 * @param point2
 * @param point3
 * @param point4
 * @returns
 */
export function isPointConcave(
  point1: Point,
  point2: Point,
  point3: Point,
  point4: Point
) {
  const re1 = isDirection(point1, point2, point3);
  const re2 = isDirection(point2, point3, point4);
  if (re1 * re2 <= 0) return true;
  return false;
}

/**
 * 辅助函数 判断两个点是否是同一个
 * @param point1
 * @param point2
 * @returns
 */
export function isEuqalPoint(point1: Point, point2: Point) {
  if (point1.x == point2.x && point1.y == point2.y) {
    return true;
  }
}

/**
 * 辅助函数 检查第二条线的方向在第一条线的左还是右
 * @param point1
 * @param point2
 * @param point3
 * @returns
 */
export function isDirection(point1: Point, point2: Point, point3: Point) {
  // 假设point1是原点
  const p1 = getPointLine(point1, point2);
  const p2 = getPointLine(point1, point3);
  return crossLine(p1, p2);
}

/**
 * 辅助函数 获取以point1作为原点的线
 * @param point1
 * @param point2
 * @returns
 */
export function getPointLine(point1: Point, point2: Point) {
  const p1 = {
    x: point2.x - point1.x,
    y: point2.y - point1.y,
  };
  return p1;
}

/**
 * 辅助函数 两线叉乘 两线的起点必须一致
 * @param point1
 * @param point2
 * @returns
 */
export function crossLine(point1: Point, point2: Point) {
  return point1.x * point2.y - point2.x * point1.y;
}
/**
 * 通过渲染比例计算坐标
 * @param originWidth
 * @param realWidth
 * @param pointList
 *@param type 1转化为接口坐标,0转化为渲染坐标
 *
 */
export function processcoorDinates(
  originWidth: number,
  realWidth: number,
  pointList: Point[],
  type: number
) {
  // 实际尺寸比值
  const scaleFactor = originWidth / realWidth;
  const mappedCoordinates: Point[] = [];

  for (let i = 0; i < pointList.length; i++) {
    const originalX = pointList[i].x;
    const originalY = pointList[i].y;

    // 计算新的坐标值
    const newX = parseFloat(
      (type === 1 ? originalX * scaleFactor : originalX / scaleFactor).toFixed(
        5
      )
    );
    const newY = parseFloat(
      (type === 1 ? originalY * scaleFactor : originalY / scaleFactor).toFixed(
        5
      )
    );
    // 将映射后的坐标加入数组中
    mappedCoordinates.push({ x: newX, y: newY });
  }
  return mappedCoordinates;
}

//将返回的number [] [] 转化为[{x,y}]
export function convertParamsPointToPoint(arr) {
  return arr.map(([x, y]) => ({ x, y }));
}

export function convertPointToParamsPointList(array) {
  return array.map(({ x, y }) => [
    x < 0 ? 0 : Math.round(x),
    y < 0 ? 0 : Math.round(y),
  ]);
}

export function dealOutBorder(array, xSize, ySize) {
  array.forEach((item, index) => {
    //左上角点位
    if (index === 0) {
      if (item.x > xSize || item.x < 0) {
        item.x = 0;
      }
      if (item.y > ySize || item.y < 0) {
        item.y = 0;
      }
    }
    //右上角点位
    if (index === 1) {
      if (item.x > xSize || item.x < 0) {
        item.x = xSize;
      }
      if (item.y > ySize || item.y < 0) {
        item.y = 0;
      }
    }
    //右下角点位
    if (index === 2) {
      if (item.x > xSize || item.x < 0) {
        item.x = xSize;
      }
      if (item.y > ySize || item.y < 0) {
        item.y = ySize;
      }
    }
    //左下角点位
    if (index === 3) {
      if (item.x > xSize || item.x < 0) {
        item.x = 0;
      }
      if (item.y > ySize || item.y < 0) {
        item.y = ySize;
      }
    }
  });
  return array;
}

export function dealGlobalCoverPoints(xSize, ySize) {
  return [
    { x: xSize, y: 0 },
    { x: xSize, y: ySize },
    { x: 0, y: ySize },
    { x: 0, y: 0 },
  ];
}
