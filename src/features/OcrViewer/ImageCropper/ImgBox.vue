<template>
  <div ref="imgContent" class="img-wrapper">
    <div
      ref="imgWrap"
      class="modal-img-wrap"
      :style="{
        width: imgWrapperW + 'px',
        height: imgWrapperH + 'px',
      }"
    >
      <img
        :src="url"
        alt=""
        ref="img"
        :style="{
          width: imgW + 'px',
          height: imgH + 'px',
          userSelect: 'none',
          transform: scale,
          maxWidth: 'unset',
        }"
      />
      <canvas class="canvas" ref="canvas"></canvas>
    </div>
    <el-icon v-if="loading" :size="24" class="loading-icon">
      <i-ep-loading />
    </el-icon>
  </div>
</template>
<script setup lang="ts">
import { useimgEnhanced } from './hooks/useimgEnhanced';

interface Props {
  url: string;
  deg?: number;
}
const props = withDefaults(defineProps<Props>(), {
  deg: 90,
});
const emits = defineEmits(['get-point-list']);
const {
  loading,
  imgW,
  imgH,
  imgWrapperW,
  imgWrapperH,
  scale,
  handleRotate,
  initImage,
  canvas,
  imgWrap,
  img,
  transformedPointList,
} = useimgEnhanced();
const imgContent = ref<HTMLDivElement | null>(null);

const getBoxSize = async () => {
  //获取盒子的大小
  await nextTick();
  const boxW = imgContent.value!.clientWidth;
  const boxH = imgContent.value!.clientHeight;
  if (props.deg) {
    handleRotate(props.deg, boxW, boxH, props.url);
  } else {
    initImage(boxW, boxH, props.url);
  }
};

watch(
  () => transformedPointList.value,
  () => {
    emits('get-point-list', transformedPointList.value);
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  getBoxSize();
});
</script>
<style lang="less" scoped>
.img-wrapper {
  width: 100%;
  height: 100%;
  background-color: #fff;
  position: relative;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  touch-action: none;

  .modal-img-wrap {
    overflow: hidden;
    touch-action: none;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    left: 0;
    top: 0;
    .modal-img {
      position: absolute;
      touch-action: none;
      top: 0;
      left: 0;
    }
    .canvas {
      z-index: 2;
      position: absolute;
      touch-action: none;
      top: 0;
      left: 0;
    }
  }
}
.loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #0a73e4;
  animation: spin 1s infinite linear;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  } /* 初始状态，角度为0度 */
  100% {
    transform: rotate(360deg);
  } /* 结束状态，角度为360度（完全旋转一周）*/
}
</style>
