<template>
  <el-dialog
    :model-value="dialogCropperVisible"
    title="Shipping address"
    width="520"
    :show-close="false"
    :destroy-on-close="true"
    top="5vh"
    class="cropper-dialog"
    :append-to-body="true"
    :close-on-click-modal="false"
    @close="initDialogData"
  >
    <template #header>
      <div class="my-header">
        <div>第一步：框选识别区</div>
        <div>
          <el-icon :size="16" class="cursor-pointer" @click="closeDialog">
            <i-ep-close />
          </el-icon>
        </div>
      </div>
    </template>
    <div
      v-loading="ocrStore.cropperImgInfo.getEnhancedUrlLoading"
      class="content"
      element-loading-text="正在获取增强后图片..."
    >
      <div class="img-content">
        <ImgBox
          ref="imgEnhanceBox"
          :url="url"
          :deg="deg"
          @get-point-list="getTransformPointList"
        />
      </div>
      <div class="bottom-btn">
        <div class="btn-group">
          <div class="btn confirm" @click="nextStep">下一步:确认识别</div>
        </div>
      </div>
    </div>
    <ConfirmScanDialog
      v-if="dialogConfirmVisible"
      v-model:dialogConfirmVisible="dialogConfirmVisible"
    />
  </el-dialog>
</template>
<script lang="ts" setup>
import ImgBox from './ImgBox.vue';
import { Point, PointParams } from './type';
import { useOcrScan } from '@/store/module/useOcrScan';
import { convertPointToParamsPointList } from './utils';
import ConfirmScanDialog from '../ConfirmScanDialog/index.vue';
const ocrStore = useOcrScan();

interface Props {
  dialogCropperVisible: boolean;
  url: string;
  deg: number;
}

const imgEnhanceBox = ref();

const dialogConfirmVisible = computed(
  () => ocrStore.cropperImgInfo.showEnhancedDialog
);

const closeDialog = () => {
  ocrStore.cropperImgInfo.showDialogCropper = false;
};

const ponitParams = reactive<PointParams>({
  points: [],
});

const getTransformPointList = (transformedPointsList: Point[]) => {
  ponitParams.points = transformedPointsList;
  const corners = convertPointToParamsPointList(ponitParams.points);
  ocrStore.initAnnexTypeAndCorners(corners);
};

const initDialogData = () => {
  ponitParams.points = [];
};

const nextStep = () => {
  ocrStore.getImageEnhancedUrl();
};
defineProps<Props>();
</script>

<style scoped lang="less">
.my-header {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 8px;
  align-items: center;
  font-weight: bold;
  color: #111;
}
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.img-content {
  flex: 1;
}
.bottom-btn {
  background-color: white;
  padding: 12px 12px;
  box-sizing: border-box;
  border-top: 1px solid #e9e8eb;
  .btn-group {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.btn {
  box-sizing: border-box;
  padding: 6px 24px;
  border-radius: 2px;
  font-size: 14px;
  font-family:
    PingFangSC-Regular,
    PingFang SC;
  font-weight: 400;
  cursor: pointer;
  user-select: none;
}
.confirm {
  color: #ffffff;
  background-color: #0a73e4;
}
.notAllw {
  background-color: rgb(232, 234, 237);
  cursor: not-allowed;
}
.img-box {
  display: flex;
  justify-content: space-between;
  border-right: 1px solid #e9e8eb;
  box-sizing: border-box;
  padding: 0 24px;
  img {
    width: 14px;
    height: 14px;
    cursor: pointer !important;
  }
  img:not(:first-child) {
    margin-left: 16px;
  }
}
</style>
<style lang="less">
.cropper-dialog {
  padding: 0 !important;
  .el-dialog__header {
    padding: 0 !important;
    margin: 0;
    border-bottom: 1px solid #ebeef5;
  }
  .el-dialog__body {
    padding: 0;
    height: 736px;
  }
}
</style>
