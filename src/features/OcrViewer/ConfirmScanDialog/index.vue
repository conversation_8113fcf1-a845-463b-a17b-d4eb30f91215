<template>
  <el-dialog
    :model-value="dialogConfirmVisible"
    title="Shipping address"
    width="520"
    :show-close="false"
    :destroy-on-close="true"
    :modal="false"
    top="5vh"
    class="cropper-confirm-dialog"
    :close-on-click-modal="false"
    @close="initDialogData"
  >
    <template #header>
      <div class="my-header">
        <div>第二步：提交识别</div>
        <div>
          <el-icon :size="16" class="cursor-pointer" @click="closeDialog">
            <i-ep-close />
          </el-icon>
        </div>
      </div>
    </template>
    <div class="content">
      <div ref="maskBox" class="img-content" @mousedown="onmousedownHandle">
        <div
          v-if="ocrStore.loadingStatus"
          :class="['scan-contain', { active: ocrStore.loadingStatus }]"
        ></div>
        <img
          ref="img"
          :src="url as string"
          alt=""
          :style="{
            width: imgW,
            height: imgH,
            top: top + 'px',
            left: left + 'px',
            right: 0,
            bottom: 0,
            userSelect: 'none',
            transform: `scale(${scale}) rotateZ(${deg}deg)`,
            margin: 'auto',
          }"
        />
        <el-icon v-if="loading" :size="24" class="loading-icon">
          <i-ep-loading />
        </el-icon>
      </div>
      <div class="bottom-btn">
        <div class="btn-group">
          <div class="btn confirm" @click="closeDialog">返回上一步</div>
          <div>
            <el-select
              v-model="fileType"
              placeholder="请选择图片类型"
              style="width: 240px"
              @change="changeFileType"
            >
              <el-option-group
                v-for="group in options"
                :key="group.label"
                :label="group.label"
              >
                <el-option
                  v-for="item in group.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-option-group>
            </el-select>
          </div>
          <div
            :class="[
              'btn',
              fileType && !ocrStore.loadingStatus ? 'confirm' : 'notAllw',
            ]"
            @click="startOcrScan"
          >
            提交识别
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { PointParams } from '@/features/OcrViewer/ImageCropper/type';
import { useOcrScan } from '@/store/module/useOcrScan';
import { useImgPosition } from '@/features/OcrViewer/hooks/useImgPosition';
import { selectOptions } from '@/features/OcrViewer/constant';
import { ElMessage } from 'element-plus';

const {
  loading,
  imgW,
  imgH,
  maskBox,
  scale,
  deg,
  top,
  left,
  onmousedownHandle,
  addMouseWheeLevtEvent,
  unloadMouseWheeLevtEvent,
} = useImgPosition();

const ocrStore = useOcrScan();

interface Props {
  dialogConfirmVisible: boolean;
}

const componentLocation = inject('componentLocation');

const options = selectOptions[componentLocation];

const fileType = ref<number | null>(null);

const url = computed(() => ocrStore.cropperImgInfo.imageEnhancedUrl);

const ponitParams = reactive<PointParams>({
  points: [],
});

const initDialogData = () => {
  ponitParams.points = [];
};

//发起ocr识别
const startOcrScan = () => {
  if (!ocrStore.loadingStatus && fileType.value) {
    ocrStore.scanType = 1;
    ocrStore.startOcrScan();
  }
  // ocrStore.startOcrTestScan();
};

const changeFileType = () => {
  ocrStore.annexType = fileType.value ?? 1;
};

const closeDialog = () => {
  if (ocrStore.loadingStatus)
    return ElMessage({
      message: '正在识别图片,请勿关闭!',
      type: 'warning',
    });
  ocrStore.cropperImgInfo.showEnhancedDialog = false;
};

onMounted(() => {
  fileType.value = selectOptions[componentLocation][0].options[0].value;
  ocrStore.annexType = fileType.value ?? 1;
  addMouseWheeLevtEvent();
  ocrStore.loadingStatus = false;
});
onUnmounted(() => {
  unloadMouseWheeLevtEvent();
});
defineProps<Props>();
</script>

<style scoped lang="less">
:deep(.el-dialog) {
  padding: 0;
}
.my-header {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 8px;
  align-items: center;
  font-weight: bold;
  color: #111;
}
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.img-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  img {
    position: absolute;
    cursor: pointer;
  }
  .scan-contain {
    position: absolute;
    left: 0;
    right: 0;
    height: 100%;
    width: 100%;
    z-index: 6666;
    background: linear-gradient(180deg, rgba(0, 255, 51, 0) 60%, #1989fa 250%);
    border-bottom: 2px solid #03a9f4;
    &.active {
      animation: moveScan 3s ease-in-out infinite;
    }
  }
}
.bottom-btn {
  background-color: white;
  padding: 12px 12px;
  box-sizing: border-box;
  border-top: 1px solid #e9e8eb;
  .btn-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.btn {
  box-sizing: border-box;
  padding: 6px 24px;
  border-radius: 2px;
  font-size: 14px;
  font-family:
    PingFangSC-Regular,
    PingFang SC;
  font-weight: 400;
  cursor: pointer;
  user-select: none;
}
.confirm {
  color: #ffffff;
  background-color: #0a73e4;
}
.notAllw {
  background-color: rgb(232, 234, 237);
  cursor: not-allowed;
}
.img-box {
  display: flex;
  justify-content: space-between;
  border-right: 1px solid #e9e8eb;
  box-sizing: border-box;
  padding: 0 24px;
  img {
    width: 14px;
    height: 14px;
    cursor: pointer !important;
  }
  img:not(:first-child) {
    margin-left: 16px;
  }
}
.loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #0a73e4;
  animation: spin 1s infinite linear;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  } /* 初始状态，角度为0度 */
  100% {
    transform: rotate(360deg);
  } /* 结束状态，角度为360度（完全旋转一周）*/
}
</style>
<style lang="less">
.cropper-confirm-dialog {
  padding: 0 !important;
}
</style>
