<template>
  <div class="form-label">
    <span class="required-icon">*</span>
    {{ ocrStore.annexType === 4 ? '门诊记录' : '入院记录' }}
  </div>
  <el-radio-group
    v-model="recordStatus"
    class="my-radio ml-4"
    @change="changeStatus"
  >
    <div>
      <div>
        <el-radio :label="1" size="large">
          <span>存入已有记录</span>
          <span class="descriptions-box"
            >存入已有诊疗/复查/报告；如有数据将会进行覆盖。</span
          >
        </el-radio>
      </div>
      <div>
        <el-radio :label="0" size="large">
          <span>创建新记录</span>
          <span class="descriptions-box">创建并保存新的诊疗/复查/报告。</span>
        </el-radio>
      </div>
    </div>
  </el-radio-group>
  <div class="flex">
    <div v-if="recordStatus === 1">
      <div class="form-label mb-8">
        <span class="required-icon">*</span> 记录日期
      </div>
      <div class="select-box">
        <el-select v-model="patientHistoryId" placeholder="请选择记录" filter>
          <el-option
            v-for="(item, index) in timeOptions"
            :key="index"
            :label="item.name"
            :value="item.patientHistoryId"
          />
        </el-select>
      </div>
    </div>
    <div v-if="recordStatus === 0">
      <div class="form-label mb-8">
        <span class="required-icon">*</span
        >{{
          ocrStore.annexType === 4 || ocrStore.annexType === 9
            ? ' 门诊日期'
            : ' 入院日期'
        }}
      </div>
      <MyDatePicker v-model:time="date" />
    </div>
  </div>
  <div class="bottom-line"></div>
</template>
<script setup lang="ts">
import MyDatePicker from '../MyDatePicker.vue';
import { getInHospitalRecords, getOutPatientHospitalRecords } from '@/api/ocr';

import dayjs from 'dayjs';

import { TypeRecordReqMap } from './type';

import { useOcrScan } from '@/store/module/useOcrScan';

import useGlobal from '@/store/module/useGlobal';

const globalData = useGlobal();

const ocrStore = useOcrScan();

const emit = defineEmits(['getRecordParams']);

interface TypeTimeOptions {
  name: string;
  patientHistoryId: number;
  inTime: number;
}
const recordStatus = ref(0);

const timeOptions = ref<TypeTimeOptions[]>([]);

const patientHistoryId = ref<number | null>(null);

const date = ref('');

const inTime = computed(() => {
  if (patientHistoryId.value) {
    return timeOptions.value.find(
      item => item.patientHistoryId === patientHistoryId.value
    )?.inTime;
  } else {
    return null;
  }
});
const recordRequestMap: TypeRecordReqMap = {
  1: getInHospitalRecords,
  2: getInHospitalRecords,
  4: getOutPatientHospitalRecords,
  9: getOutPatientHospitalRecords,
};

const getRecordsList = () => {
  recordRequestMap[ocrStore.annexType]({
    userId: globalData.userId,
  }).then(data => {
    timeOptions.value = [];
    if ((data as any).historyInfo) {
      if (ocrStore.annexType === 1 || ocrStore.annexType === 2) {
        (data as any).historyInfo.forEach((item: any) => {
          const { patientHistoryId, inTime, isGroup } = item;
          timeOptions.value.push({
            patientHistoryId: patientHistoryId,
            inTime: inTime,
            name:
              dayjs(inTime).format('YYYY-MM-DD') +
              '' +
              (isGroup ? ' 入组' : ' 入院'),
          });
        });
      }

      if (ocrStore.annexType === 4 || ocrStore.annexType === 9) {
        (data as any).historyInfo.forEach((item: any) => {
          const { patientHistoryId, inTime } = item;
          timeOptions.value.push({
            patientHistoryId: patientHistoryId,
            inTime: inTime,
            name: dayjs(inTime).format('YYYY-MM-DD') + '' + ' 门诊',
          });
        });
      }
    }
  });
};

const changeStatus = (value: number) => {
  if (value === 1) {
    date.value = '';
  }
  if (value === 0) {
    patientHistoryId.value = null;
  }
  getRecordsList();
};
const initDate = () => {
  try {
    if (ocrStore.aiResultData!.inHospitalTime?.length) {
      date.value = dayjs(
        ocrStore.aiResultData!.inHospitalTime?.[0].key
      ).valueOf();
    }
  } catch {
    date.value = null;
  }
};
watch([date, patientHistoryId, recordStatus], newValue => {
  emit('getRecordParams', {
    patientHistoryId: newValue[1],
    date: recordStatus.value === 0 ? newValue[0] : inTime.value,
    saveType: recordStatus.value,
  });
});
onMounted(() => {
  getRecordsList();
  initDate();
});
</script>
<style scoped lang="less">
.my-radio {
  :deep(.el-radio__label) {
    font-size: 14px;
    font-weight: 400;
    color: #323233;
    position: relative;
  }
  :deep(.el-radio__inner) {
    width: 16px;
    height: 16px;
    // 去掉默认的中心填充
    &::after {
      display: none;
      transition: none;
    }
  }
  :deep(.el-radio__input.is-checked) {
    .el-radio__inner {
      padding: 2px;
      background-color: #0a73e4;
      background-clip: content-box;
    }
    .el-radio__label {
      color: #323233;
    }
  }
}
.descriptions-box {
  color: #708293;
  margin-left: 16px;
}
.select-box {
  :deep(.el-select) {
    width: 240px;
    border-radius: 2px;
    color: #111111;
  }
  :deep(.el-input__inner) {
    height: 32px;
  }
  :deep(.el-input__wrapper) {
    border-radius: 2px;
  }
}
.form-label {
  font-size: 14px;
  font-weight: bold;
  color: #111111;
  .required-icon {
    color: #ea1212;
  }
}
.bottom-line {
  padding-top: 14px;
  border-bottom: 1px solid #dcdee0;
}
</style>
