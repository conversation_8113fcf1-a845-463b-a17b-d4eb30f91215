/** 格式化检验检查项返回数据-数据回显 */
export function handleFormatDiagnoseReportData(data: any) {
  const res = data?.map((item: any) => {
    const keys = Object.keys(item);
    if (item.diagnose_report) {
      return {
        ...item.diagnose_report,
        [item.diagnose_report?.['key']]: item.diagnose_report?.items,
      };
    }
    const specialKeys = keys.filter(v => v !== 'diagnose_report');
    let res;
    specialKeys.forEach(key => {
      res = res || item[key];
    });
    return res;
  });
  return res?.filter(Boolean) ?? [];
}
