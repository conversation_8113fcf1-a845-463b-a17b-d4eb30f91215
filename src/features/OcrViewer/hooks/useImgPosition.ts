import { ref, nextTick } from 'vue';
import OCR_DIALOG_SIZES from '@/features/OcrViewer/constant/ocrDialogSize';
import { useOcrScan } from '@/store/module/useOcrScan';
const ocrStore = useOcrScan();
export const useImgPosition = (): any => {
  const imgW = ref<string>('auto');
  const imgH = ref<string>('auto');
  const deg = ref<number>(0);
  const top = ref<number>(0);
  const left = ref<number>(0);
  const scale = ref<number>(1);
  const size = ref<number>(0);
  const loading = ref<boolean>(false);
  const imgRealWidth = ref<number>(0);
  const imgRealHeight = ref<number>(0);
  const mouseWheeLevt = ref<string>('');
  const maskBox = ref<HTMLDivElement | null>(null);

  const isFullScreen = ref<boolean>(false);

  const showAllContent = ref<boolean>(true);

  const dialogWidth = ref<string | number>(OCR_DIALOG_SIZES.INIT_SIZE);

  //给图片父元素添加滚动事件 在mounted中调用
  const addMouseWheeLevtEvent = async (): Promise<void> => {
    await nextTick();
    mouseWheeLevt.value = /Firefox/i.test(navigator.userAgent)
      ? 'DOMMouseScroll'
      : 'mousewheel';
    maskBox.value?.addEventListener(mouseWheeLevt.value, wheelHandle, {
      passive: true,
    });
  };
  //卸载鼠标滚轮事件
  const unloadMouseWheeLevtEvent = (): void => {
    maskBox.value?.removeEventListener(mouseWheeLevt.value, wheelHandle);
  };

  /**
   * 鼠标滚动 实现放大缩小
   */
  const wheelHandle = (e: any) => {
    const ev = e || window.event; // 兼容性处理 => 火狐浏览器判断滚轮的方向是属性 detail，谷歌和ie浏览器判断滚轮滚动的方向是属性 wheelDelta
    // dir = -dir; // dir > 0 => 表示的滚轮是向上滚动，否则是向下滚动 => 范围 (-120 ~ 120)
    const dir = ev.detail ? ev.detail * -120 : ev.wheelDelta;
    //滚动的数值 / 2000 => 表示滚动的比例，用此比例作为图片缩放的比例
    imgScaleHandle(Number(dir / 2000));
  };
  /**
   * 通过鼠标滚轮更改图片大小
   */
  const imgScaleHandle = (zoom: number) => {
    size.value += zoom;
    if (size.value < -0.5) {
      size.value = -0.5;
    }
    scale.value = 1 + size.value;
  };
  /**
   * 旋转图片
   */
  const handleRotate = (degValue: number) => {
    deg.value += degValue;
    if (Math.abs(deg.value) === 360) {
      deg.value = 0;
    }
    size.value = 0;
    scale.value = 1;
    changImgWidthAndHeight();
  };
  const initImgPosition = () => {
    imgW.value = 'auto';
    imgH.value = 'auto';
    top.value = 0;
    left.value = 0;
    deg.value = 0;
    size.value = 0;
    scale.value = 1;
  };
  const changImgWidthAndHeight = async () => {
    const ocrCurrentImg = document.getElementById('ocrCurrentImg');
    const ocrEnhanceImg = document.getElementById('ocrEnhanceImg');
    const boxW = maskBox.value!.clientWidth;
    const boxH = maskBox.value!.clientHeight;
    await nextTick();
    if (ocrCurrentImg && ocrCurrentImg.complete) {
      const realWidth = ocrCurrentImg.naturalWidth;
      const realHeight = ocrCurrentImg.naturalHeight;
      const aspectRatio = realWidth / realHeight;
      if (aspectRatio < 1) {
        if (Math.abs(deg.value) === 90 || Math.abs(deg.value) === 270) {
          if (isFullScreen.value) {
            if (!ocrStore.hasContent) {
              imgW.value = boxH + 'px';
              imgH.value = 'auto';
            } else {
              imgH.value = boxW + 'px';
              imgW.value = 'auto';
            }
          } else {
            imgH.value = boxW + 'px';
            imgW.value = 'auto';
          }
        } else {
          if (isFullScreen.value) {
            if (!ocrStore.hasContent) {
              imgH.value = boxH + 'px';
            } else {
              imgH.value = 'auto';
            }
            imgW.value = 'auto';
          } else {
            imgH.value = 'auto';
            imgW.value = 'auto';
          }
        }
      }
      if (aspectRatio > 1) {
        if (Math.abs(deg.value) === 90 || Math.abs(deg.value) === 270) {
          if (isFullScreen.value) {
            imgW.value = boxH + 'px';
            imgH.value = 'auto';
          } else {
            imgW.value = boxW + 'px';
            imgH.value = 'auto';
          }
        } else {
          if (isFullScreen.value) {
            if (!ocrStore.hasContent) {
              imgH.value = boxH + 'px';
              imgW.value = 'auto';
            } else {
              imgW.value = boxW + 'px';
              imgH.value = 'auto';
            }
          } else {
            imgW.value = boxW + 'px';
            imgH.value = 'auto';
          }
        }
      }
    }
    if (ocrEnhanceImg && ocrEnhanceImg.complete) {
      const realWidth = ocrEnhanceImg.naturalWidth;
      const realHeight = ocrEnhanceImg.naturalHeight;
      // 获取高宽比例
      const whRatio = realWidth / realHeight;
      const hwRatio = realHeight / realWidth;
      if (realWidth >= realHeight) {
        imgH.value = hwRatio * boxW + 'px';
        const nih = imgH.value;
        if (nih > boxH) {
          imgH.value = boxH + 'px';
          imgW.value = whRatio * boxH + 'px';
        } else {
          imgW.value = boxW - 40 + 'px';
          imgH.value = hwRatio * (boxW - 40) + 'px';
        }
        top.value = (boxH - imgH.value) / 2;
        left.value = (boxW - imgW.value) / 2;
      } else {
        if (realWidth >= boxW) {
          imgW.value = ((boxH - 40) / realHeight) * realWidth - 40 + 'px';
          imgH.value = boxH - 80 + 'px';
        } else {
          imgW.value = ((boxH - 40) / realHeight) * realWidth + 'px';
          imgH.value = boxH - 40 + 'px';
        }
        left.value = (boxW - imgW.value) / 2;
        top.value = (boxH - imgH.value) / 2;
      }
    }
  };
  /**
   * 图片拖动
   */
  const onmousedownHandle = (e: any) => {
    maskBox.value!.onmousemove = function (el: any) {
      const ev = el || window.event; // 阻止默认事件
      ev.preventDefault();
      left.value += ev.movementX;
      top.value += ev.movementY;
    };
    maskBox.value!.onmouseup = function () {
      // 鼠标抬起时将操作区域的鼠标按下和抬起事件置为null 并初始化
      maskBox.value!.onmousemove = null;
      maskBox.value!.onmouseup = null;
    };
    if (e.preventDefault) {
      e.preventDefault();
    } else {
      return false;
    }
  };
  //根据扫描结果初始化内容
  const changDialogWidthByScanRes = async (): Promise<void> => {
    if (isFullScreen.value) {
      if (ocrStore.hasContent) {
        const leftContent = document.querySelector('.left-content');
        (leftContent as HTMLDivElement).style.width =
          OCR_DIALOG_SIZES.INIT_SIZE + 'px';
      }
    } else {
      if (ocrStore.hasContent) {
        if (showAllContent.value) {
          dialogWidth.value = OCR_DIALOG_SIZES.EXPANDED_ALL_SIZE;
        } else {
          dialogWidth.value = OCR_DIALOG_SIZES.EXPANDED_SIZE;
        }
      } else {
        dialogWidth.value = OCR_DIALOG_SIZES.INIT_SIZE;
      }
    }
  };

  //展开原文内容
  const setShowAllContent = (value): void => {
    showAllContent.value = value;
  };
  //发起全屏
  const openFullScreen = async (): Promise<void> => {
    isFullScreen.value = !isFullScreen.value;
    initImgPosition();
    await nextTick();

    const dialogBody = document.querySelector('.ocr-dialog .el-dialog__body');
    const leftContent = document.querySelector('.left-content');
    const dialogHeader = document.querySelector(
      '.ocr-dialog .el-dialog__header'
    );

    if (isFullScreen.value) {
      //有内容的情况下
      if (ocrStore.hasContent) {
        (leftContent as HTMLDivElement).style.width =
          OCR_DIALOG_SIZES.INIT_SIZE + 'px';
        changImgWidthAndHeight();
      } else {
        (leftContent as HTMLDivElement).style.width = '100%';
        imgH.value = '100%';
      }
      (dialogBody as HTMLDivElement).style.height = `calc(100% - ${
        (dialogHeader as HTMLDivElement).offsetHeight
      }px)`;
    } else {
      (dialogBody as HTMLDivElement).style.height =
        OCR_DIALOG_SIZES.INIT_BODY_SIZE + 'px';

      (leftContent as HTMLDivElement).style.width =
        OCR_DIALOG_SIZES.INIT_SIZE + 'px';
      //非全屏有内容
      if (ocrStore.hasContent) {
        if (showAllContent.value) {
          dialogWidth.value = OCR_DIALOG_SIZES.EXPANDED_ALL_SIZE;
        } else {
          dialogWidth.value = OCR_DIALOG_SIZES.EXPANDED_SIZE;
        }
      } else {
        dialogWidth.value = OCR_DIALOG_SIZES.INIT_SIZE;
      }
      // imgH.value = 'auto';
      changImgWidthAndHeight();
    }
  };
  return {
    loading,
    imgW,
    imgH,
    deg,
    top,
    left,
    scale,
    size,
    imgRealWidth,
    imgRealHeight,
    maskBox,
    handleRotate,
    onmousedownHandle,
    imgScaleHandle,
    unloadMouseWheeLevtEvent,
    addMouseWheeLevtEvent,
    isFullScreen,
    showAllContent,
    dialogWidth,
    setShowAllContent,
    openFullScreen,
    changDialogWidthByScanRes,
    initImgPosition,
  };
};
