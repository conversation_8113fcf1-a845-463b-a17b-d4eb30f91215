<template>
  <CardWrapper
    class="outpatient-record-box mb-0"
    title="手术记录"
    :header-show="from !== 'ocr'"
  >
    <EditTitle
      v-if="msgStatus === 'view'"
      :edit-info="editInfo"
      :readonly="readonly"
      @edit-msg="editMsg"
    />
    <div class="edit-content">
      <div class="edit-item mb-24 flex">
        <div class="item-content">
          <div class="sd-box mb-16">
            <div
              v-for="(surgery, index) in surgeryRecords"
              :key="surgery.operation_id"
              class="content-box mb-14"
            >
              <div class="title">
                <span>手术{{ toChineseNumber(index + 1) }}</span>
              </div>
              <template v-if="msgStatus === 'view'">
                <OutpatientStyle title="附件记录" :title-top="0">
                  <UploadImages
                    v-model:img-list="surgery.operation_accessory"
                    is-view
                  />
                </OutpatientStyle>
                <OutpatientStyle title="手术日期" :title-top="0">
                  <div class="query-style">{{ surgery.operation_date }}</div>
                </OutpatientStyle>
                <OutpatientStyle title="手术结论" :title-top="0">
                  <div class="query-style">
                    <div>{{ surgery.conclusion_text || '--' }}</div>
                    <div class="tag">
                      {{ surgery.operation_diagnosis.label }}
                    </div>
                  </div>
                </OutpatientStyle>
              </template>
              <div v-else class="box mt-8">
                <div class="total-label margin-b flex justify-between">
                  <span
                    class="deconste-btn cursor-pointer"
                    @click="deconsteSurgery(surgery, index)"
                  >
                    删除
                  </span>
                </div>
                <div v-if="from !== 'ocr'" class="row-item">
                  <div class="row-label">附件记录：</div>
                  <div>
                    <UploadImages
                      v-model:img-list="surgery.operation_accessory"
                      :is-view="false"
                      :disabled="false"
                    />
                  </div>
                </div>
                <div class="row-item flex-v-center">
                  <div class="row-label">手术时间：</div>
                  <div class="date-box" :style="{ width: '360px' }">
                    <el-date-picker
                      v-model="surgery.operation_date"
                      type="date"
                      placeholder="选择日期"
                      class="datepicker"
                      style="width: 100%"
                      value-format="x"
                      format="YYYY-MM-DD"
                      :clearable="false"
                      :disabled-date="pickerOptions"
                      @change="changeTime"
                    />
                    <img
                      :src="changeTimeIng"
                      alt=""
                      class="w-14 h-14 change-time-icon"
                    />
                  </div>
                </div>
                <div class="row-item">
                  <div class="row-label">手术结论：</div>
                  <div v-loading="conclusionLoading" class="flex-1 relative">
                    <el-input
                      v-model="surgery.conclusion_text"
                      :rows="4"
                      maxlength="1000"
                      type="textarea"
                    />
                    <div
                      v-if="surgery.conclusion_text"
                      class="struct-btn"
                      @click="() => textToOcr(surgery)"
                    >
                      结构化
                    </div>
                    <div
                      v-if="surgery.operation_diagnosis.label"
                      class="tag cursor-pointer"
                      @click="chooseSurgery(index)"
                    >
                      {{ surgery.operation_diagnosis.label }}
                    </div>
                    <div
                      v-if="!surgery.operation_diagnosis.label"
                      class="choose-text cursor-pointer mt-4"
                      @click="chooseSurgery(index)"
                    >
                      {{ surgery.operation_diagnosis.label ? '编辑' : '新增+' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="msgStatus !== 'view'"
              class="add-btn cursor-pointer mt-30 w-200"
              @click="beforeAddEmptySurgery"
            >
              +新增手术记录
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="msgStatus !== 'view' && from !== 'ocr'" class="btn-group">
      <el-button :loading="loading" type="primary" @click="saveInfo">
        保存
      </el-button>
      <el-button
        v-if="msgStatus === 'edit'"
        :loading="loading"
        @click="cancelEdit"
      >
        取消
      </el-button>
    </div>
    <SurgeryDialog
      :menu-keys="DIALOG_KEYS"
      :schema-map="dialogSchema"
      :value="choosedSurgeryInfo"
      :choose-surgery-visible="chooseSurgeryVisible"
      @close-surgery-visible="closeSurgeryVisible"
      @confirm="confirmSurgery"
    />
  </CardWrapper>
</template>
<script setup lang="ts">
import { checkTaskContent } from '@/api';
import {
  addDiagnosisSurgeryApi,
  deleteOperationApi,
  queryOperationDetailsApi,
} from '@/api/review';
import changeTimeIng from '@/assets/imgs/callCenter/change-time.png';
import CardWrapper from '@/components/CardWrapper/index.vue';
import UploadImages from '@/components/UploadImages/index.vue';
import {
  FormCategory,
  FormCategoryValues,
  RoleEnum,
  SourceType,
  SourceTypeValues,
} from '@/constant';
import bus from '@/lib/bus';
import store from '@/store';
import useInternDrawer from '@/store/module/useInternDrawer';
import { toChineseNumber } from '@/utils';
import EditTitle from '../PatientRecord/EditTitle.vue';
import {
  dialogTip,
  timestampToDate,
  timestampToDateTime,
} from '../PatientRecord/hooks';
import OutpatientStyle from '../PatientRecord/OutpatientStyle.vue';
import SurgeryDialog from './SurgeryDialog.vue';
import { FormModeValues } from '@/constant';
import { cloneDeep, keyBy } from 'lodash-es';
import { ocrTextToData } from '@/api/ocr';

interface ISurgeryInfo {
  operation_id: string;
  operation_date: string;
  operation_diagnosis: {
    label: string;
    operation_conclusion: any[];
  };
  operation_accessory: any;
  visible: boolean;
  conclusion_text: string;
}

const DIALOG_KEYS = ['CAG_PTCA_PCI', 'AVR', 'RFCA', 'OT', 'PI'];
const useGlobalInfo = store.useGlobal();
const internDrawer = useInternDrawer();

const {
  sourceId = 0,
  sourceType = SourceType.HOSPITAL,
  from = '',
  readonly = false,
  additionalData = {},
  queryParams = {},
  schema,
  formData,
  mode,
} = defineProps<{
  schema: any;
  mode: FormModeValues;
  formData: any[];
  /** 操作类型 */
  actionType?: string;
  /** 病例🆔 */
  sourceId?: number;
  /** 病例类型 */
  sourceType?: SourceTypeValues;
  /** 来源 */
  from?: 'ocr' | string;
  readonly?: boolean;
  /** 额外数据，用于保存模块数据时携带 */
  additionalData?: Partial<
    Record<FormCategoryValues | 'all', Record<string, any>>
  >;
  /** 额外数据，用于查询数据时携带 */
  queryParams?: Record<'all' | string, Record<string, any>>;
}>();
const defaultRecord = {
  operation_id: '',
  operation_date: '',
  conclusion_text: '',
  operation_diagnosis: {
    label: '',
    operation_conclusion: [],
  },
  visible: false,
  operation_accessory: [],
};
const loading = ref(false);
const conclusionLoading = ref(false);
const surgeryRecords = ref<ISurgeryInfo[]>([cloneDeep(defaultRecord)]);
const currentSurgeryIndex = ref(0);
const choosedSurgeryInfo = ref<any>([]);
const chooseSurgeryVisible = ref(false);
const patientHistoryId = ref<number>(0);
const msgStatus = ref(mode);
// 编辑人/时间
const editInfo = ref({
  name: '',
  time: '',
});
const emits = defineEmits<{
  (e: 'mode-change', data: any): void;
  (e: 'update-source-id', id: number): void;
}>();

const dialogSchema = computed(() => {
  const items = schema?.items ?? [];
  return keyBy(
    items.filter(v => DIALOG_KEYS.includes(v.key)),
    'key'
  );
});

const editMsg = () => {
  msgStatus.value = 'edit';
  emits('mode-change', msgStatus.value);
};

const confirmSurgery = ({ data, conclusion = '' }) => {
  surgeryRecords.value[
    currentSurgeryIndex.value
  ].operation_diagnosis.operation_conclusion = data;
  surgeryRecords.value[currentSurgeryIndex.value].operation_diagnosis.label =
    conclusion;
};
const closeSurgeryVisible = () => {
  chooseSurgeryVisible.value = false;
};

const deconsteSurgery = (item, index: any) => {
  ElMessageBox.confirm('是否删除本条手术记录?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      if (item.operation_id) {
        deleteOperationApi({ surgeryId: item.operation_id }).then(res => {
          if (res.code === 'E000000') {
            dialogTip('删除成功！', 'success');
            surgeryRecords.value.splice(index, 1);
          }
        });
      } else {
        dialogTip('删除成功！', 'success');
        surgeryRecords.value.splice(index, 1);
      }
    })
    .catch(() => {});
};

const chooseSurgery = (index: any) => {
  currentSurgeryIndex.value = index;
  const surgeryInfo =
    surgeryRecords.value[currentSurgeryIndex.value]?.operation_diagnosis;
  if (surgeryInfo.label) {
    choosedSurgeryInfo.value = surgeryInfo.operation_conclusion;
  } else {
    choosedSurgeryInfo.value = [];
  }
  chooseSurgeryVisible.value = true;
};

// 手术时间
const changeTime = () => {};
const pickerOptions = (time: { getTime: () => number }) => {
  return time.getTime() > Date.now();
};
const checkLastRecord = () => {
  const lastItem = surgeryRecords.value[surgeryRecords.value.length - 1];
  return lastItem.operation_date && lastItem.operation_diagnosis.label;
};
// 新增手术记录
const beforeAddEmptySurgery = () => {
  if (surgeryRecords.value.length === 0) {
    addEmptySurgery();
  } else {
    if (checkLastRecord()) {
      addEmptySurgery();
    } else {
      ElMessage({
        message: '请填写完成上一条手术记录!',
        type: 'warning',
      });
    }
  }
};
const addEmptySurgery = () => {
  surgeryRecords.value.push(cloneDeep(defaultRecord));
};
// 保存
const getSaveParams = () => {
  if (!surgeryRecords.value.length) {
    ElMessage({
      message: '暂无要提交的手术，请添加!',
      type: 'warning',
    });
    return;
  }
  if (checkLastRecord()) {
    const operation_record = cloneDeep(surgeryRecords.value);

    /** 患者🆔，实习生端和其他端数据来源不同 */
    const patientId =
      useGlobalInfo.currentRole === RoleEnum.INTERN
        ? internDrawer.patientId
        : useGlobalInfo.userId;
    return {
      group: sourceType === 3 ? 1 : 0,
      source_type: sourceType === 3 ? 0 : sourceType,
      patient_id: patientId,
      source_id: sourceId,
      operation_record,
    };
  } else {
    ElMessage({
      message: '请填写完所有手术记录!',
      type: 'warning',
    });
  }
  return false;
};
const submit = async params => {
  loading.value = true;
  return addDiagnosisSurgeryApi({
    ...(params || {}),
    ...(additionalData?.[FormCategory.OPERATION_RECORD] || {}),
    ...(additionalData?.all || {}),
  })
    .then((res: any) => {
      if (res.code === 'E000000') {
        patientHistoryId.value = res.data;
        ElMessage({
          message: '保存成功',
          type: 'success',
        });
        if (from !== 'ocr') {
          msgStatus.value = 'view';
          emits('update-source-id', res.data);
          bus.emit('updata-review-list');
          bus.emit('refresh-attachment', res.data);
          emits('mode-change', msgStatus.value);
          getOnlySurgery(res.data);
        }
        return true;
      }
    })
    .catch((err: { msg: any }) => {
      ElMessage.error(`添加失败！${err.msg}`);
    })
    .finally(() => {
      loading.value = false;
    });
};
const saveInfo = () => {
  const res = getSaveParams();
  if (!res) return;
  submit(res);
};

const ocrSaveInfo = (params: any) => {
  const res: any = getSaveParams();
  if (!res) return;
  const operation_record = res?.operation_record.map(v => ({
    ...v,
    operation_accessory: params.operation_accessory,
  }));
  const mergeData = { ...params, operation_record };
  delete mergeData.accessory;
  return submit(mergeData);
};
const setFormData = (data: any) => {
  surgeryRecords.value = data;
};

/**
 * 获取手术详情
 * @param source_id 病例🆔
 */
const getOnlySurgery = async (source_id: number) => {
  /** 额外的参数 */
  const extraParams = {
    ...(queryParams?.[FormCategory.OPERATION_RECORD] || {}),
    ...(queryParams?.all || {}),
  };
  /**
   * 业务逻辑判断
   * 如果是实习生端并且传入了sub_task_id，entry_task的值由接口返回的结果确定
   */
  if (
    extraParams?.['sub_task_id'] &&
    useGlobalInfo.currentRole === RoleEnum.INTERN
  ) {
    extraParams['entry_task'] =
      (await checkTaskContent(extraParams?.['sub_task_id'])) ??
      extraParams?.['entry_task'];
  }

  queryOperationDetailsApi({
    source_id,
    ...extraParams,
  })
    .then((res: any) => {
      if (res.data?.operation_record?.length) {
        const { operation_record } = res.data;
        const arr = operation_record.map(
          (item: Omit<ISurgeryInfo, 'visible'>) => {
            return {
              operation_id: item.operation_id,
              operation_date: item.operation_date
                ? timestampToDate(item.operation_date)
                : '--',
              conclusion_text: item.conclusion_text,
              operation_diagnosis: item.operation_diagnosis,
              operation_accessory: item.operation_accessory,
            };
          }
        );
        // 上次编辑人和时间
        editInfo.value = {
          name: res.data.userName,
          time: res.data.modifyTime
            ? timestampToDateTime(res.data.modifyTime)
            : '--',
        };
        surgeryRecords.value = arr;
      } else {
        surgeryRecords.value = [];
      }
    })
    .catch(() => {});
};

const textToOcr = (surgery: ISurgeryInfo) => {
  let params = {
    sentence: surgery.conclusion_text,
    sessionName: 'operation_record',
  };
  conclusionLoading.value = true;
  ocrTextToData(params)
    .then(data => {
      if (data.resultStatus === 1) {
        const result = JSON.parse(data.aiParseData ?? '{}');
        surgery.operation_diagnosis = {
          label: result.label,
          operation_conclusion: result.operationConclusion?.map(c => ({
            en_session: c.enSession,
            label: c.label,
          })),
        };
      } else {
        ElMessage({
          message: '识别失败,请重新识别!',
          type: 'error',
        });
      }
    })
    .catch(err => {
      ElMessage({
        message: `识别失败,${err.message}!`,
        type: 'error',
      });
    })
    .finally(() => {
      conclusionLoading.value = false;
    });
};

// 取消保存
const cancelEdit = () => {
  if (patientHistoryId.value || sessionStorage.getItem('patientHistoryId')) {
    const id = patientHistoryId.value
      ? patientHistoryId.value
      : sessionStorage.getItem('patientHistoryId');
    getOnlySurgery(id as number);
  } else {
    surgeryRecords.value = [];
    addEmptySurgery();
  }
  msgStatus.value = 'view';
  emits('mode-change', msgStatus.value);
};

/**
 * 刷新数据
 */
const refresh = () => {
  const sourceId =
    patientHistoryId.value || sessionStorage.getItem('patientHistoryId');
  if (sourceId) {
    getOnlySurgery(Number(sourceId));
  }
};

defineExpose({
  submit: ocrSaveInfo,
  setFormData,
  getSaveData: getSaveParams,
  /** 刷新数据 */
  refresh,
  getData: () => surgeryRecords.value,
});
watch(
  () => formData,
  val => {
    if (val && from === 'ocr') {
      surgeryRecords.value = val;
    }
  },
  {
    immediate: true,
  }
);

onMounted(() => {
  if (sourceId && from !== 'ocr') {
    getOnlySurgery(sourceId);
    patientHistoryId.value = sourceId;
  }
});
</script>
<style scoped lang="less">
.outpatient-record-box {
  position: relative;
  .edit-box {
    position: absolute;
    right: 16px;
    top: 18px;
  }
}
.edit-content {
  .edit-item {
    .item-content {
      flex: 1;
      .sd-box {
        .content-box {
          .query-style {
            font-size: 14px;
            color: #3a4762;
          }
          .title {
            font-size: 16px;
            font-weight: 600;
            color: #15233f;
          }
          .box {
            background: #f7f8fa;
            border-radius: 4px;
            padding: 12px;
            box-sizing: border-box;
          }
          .total-label {
            font-size: 16px;
            font-weight: bold;
            color: #111111;
            .deconste-btn {
              color: #de4747;
              font-size: 14px;
            }
          }
          .row-item {
            display: flex;
            margin-bottom: 16px;
            .date-box {
              position: relative;
              :deep(.datepicker) {
                .el-input__prefix {
                  display: none;
                }
              }
              .change-time-icon {
                position: absolute;
                top: 8px;
                right: 14px;
              }
            }
            .row-label {
              font-size: 14px;
              font-weight: 700;
              color: #111111;
              margin-right: 16px;
              width: 92px;
              text-align: right;
            }
            .choose-text {
              font-size: 14px;
              font-weight: 400;
              color: #0a73e4;
              cursor: pointer;
            }
            :deep(.input-box) {
              flex: 1;
              box-sizing: border-box;
              min-height: 96px;
              padding: 6px 16px;
              border: 1px solid #dcdfe6;
              font-size: 14px;
              font-family:
                PingFangSC-Regular,
                PingFang SC,
                'sans-serif';
              font-weight: 400;
              color: #203549;
              background-color: #efefef;
              border-radius: 2px;
              line-height: 18px;
              .con-item:not(:first-child) {
                margin-top: 8px;
              }
            }
          }
          .margin-b {
            margin-bottom: 16px;
          }
          .flex-v-center {
            align-items: center;
          }
        }
      }
    }
  }
}
.btn-group {
  margin-left: 116px;
  display: flex;
  margin-bottom: 20px;
  > button {
    width: 76px;
  }
}
.add-btn {
  font-size: 14px;
  color: #1c81dc;
}
.tag {
  margin-top: 8px;
  display: inline-block;
  padding: 2px 4px;
  font-size: 14px;
  font-weight: 400;
  color: #0a73e4;
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid #dcdee0;
}
.struct-btn {
  position: absolute;
  right: 12px;
  top: 60px;
  font-size: 12px;
  font-weight: 400;
  color: #fff;
  box-sizing: border-box;
  padding: 4px 12px;
  background-color: #0a73e4;
  border-radius: 2px;
  cursor: pointer;
}
</style>
