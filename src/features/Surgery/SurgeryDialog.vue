<template>
  <el-dialog
    :model-value="chooseSurgeryVisible"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :modal="false"
    width="1160"
    draggable
    class="operation-dialog-wrap"
    @open="openHandler"
    @close="closeHandler"
  >
    <template #header="{ titleId, titleClass }">
      <div :id="titleId" :class="titleClass">
        <div class="label">选择手术</div>
      </div>
    </template>
    <div class="content">
      <div class="main-content">
        <div class="left">
          <div class="left-content">
            <div
              v-for="val in menuKeys"
              :key="val"
              :class="{ 'tab-item': true, active: activeMenu === val }"
              @click="activeMenu = val"
            >
              {{ schemaMap[val].value }}
            </div>
          </div>
        </div>
        <div class="right">
          <div class="right-content">
            <CheckList
              :num="0"
              :data-map="dataMap"
              :disease-data="selectedData"
              :disease-list="dataList"
              :enable-filter="false"
              radio-clearable
              hide-tags
              @on-change="checkListChange"
            />
            <FormList
              v-if="tableSchema"
              :key="String(chooseSurgeryVisible)"
              :mode="'edit'"
              :form-config="tableSchema"
              :form-value="formValue"
              :block-label="true"
              @formdata-change="formDataChange"
            />
          </div>
          <div class="footer">
            <el-button @click="closeHandler">取消</el-button>
            <el-button type="primary" @click="submitHandler">确定</el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import FormList from '@/components/FormList/index.vue';
import CheckList from '@/components/StructuredSelector/components/CheckList.vue';
import { getDataMap, idsTransform } from '@/components/StructuredSelector/util';
import { transformList } from '@/components/FormItem/utils';
import { chunk, cloneDeep, forEach } from 'lodash-es';
import dayjs from 'dayjs';

interface IProps {
  menuKeys: string[];
  schemaMap: any;
  value: any;
  chooseSurgeryVisible: boolean;
}
const CHECK_LIST_KEY = 'checklist';

const emits = defineEmits(['closeSurgeryVisible', 'confirm']);
const { menuKeys, schemaMap, chooseSurgeryVisible, value } =
  defineProps<IProps>();

const activeMenu = ref(menuKeys?.[0]);
const innerValue = ref({});
const innerSchemaMap = ref({});

const schema = computed(() => innerSchemaMap.value[activeMenu.value]);
const treeSchema = computed(() =>
  schema.value.items.filter(v => !['add-table', 'table'].includes(v.uiMethod))
);
const tableSchema = computed(() => {
  const newSchema = cloneDeep(schema.value);
  newSchema.items = newSchema.items.filter(v =>
    ['add-table', 'table'].includes(v.uiMethod)
  );
  return newSchema;
});
const dataList = computed(() => transformList(treeSchema.value ?? [], 'value'));
const dataMap = computed(() => getDataMap(dataList.value));
const selectedData = computed(() => {
  const curIds = getFlatCheckList(
    innerValue.value[activeMenu.value]?.[CHECK_LIST_KEY]
  ).map(v => v.id);
  const { fullIds } = idsTransform(curIds, dataMap.value) as any;
  return { diseaseIds: fullIds };
});
const formValue = computed(() => {
  return innerValue.value[activeMenu.value];
});
const getFlatCheckList = treeList => {
  if (!treeList) return [];
  const res: any[] = [];
  for (const v of treeList) {
    res.push({ id: v.id, key: v.key, value: v.diseaseName });
    if (v.children) {
      const child = getFlatCheckList(v.children);
      res.push(...child);
    }
  }
  return res;
};
const setInnerValue = (val: any) => {
  if (innerValue.value[activeMenu.value]) {
    innerValue.value[activeMenu.value] = {
      ...innerValue.value[activeMenu.value],
      ...val,
    };
  } else {
    innerValue.value[activeMenu.value] = { ...val };
  }
};
const checkListChange = params => {
  const { resultTreeData } = params;
  setInnerValue({ [CHECK_LIST_KEY]: resultTreeData });
};
const openHandler = () => {
  activeMenu.value = menuKeys[0];
  init();
};
const closeHandler = () => {
  innerValue.value = [];
  emits('closeSurgeryVisible', false);
};
const formDataChange = val => {
  setInnerValue(val);
};
const getTableResult = key => {
  const keys = Object.keys(innerValue.value[key]);
  const tableKeys = keys.filter(v => v.startsWith('table_'));
  const res = tableKeys
    .map(v => {
      let items = innerValue.value[key][v];
      const curSchemas = innerSchemaMap.value[key];
      const tableItems = curSchemas.items.filter(v =>
        v.key.startsWith('table_')
      );
      const curSchema = tableItems.find(val => val.key === v);
      if (curSchema.uiMethod === 'table') {
        const valueKey = curSchema.columns[1].key;
        // 普通table
        items = items.map(data => [
          // { key: data.key, value: data.key },
          { key: data.key, value: data[valueKey] },
        ]);
      }
      return items.flat();
    })
    .flat()
    .filter(v => v.key !== '');
  return res;
};
const formatValue = (key, val, items) => {
  const index = items.findIndex(val => val.key === key);
  const item = items[index + 1];
  if (item?.uiMethod === 'date') {
    const rules = JSON.parse(item?.uiRules ?? '{}');
    const dateFormat = rules.dateFormat || 'YYYY-MM-DD';
    return ':' + dayjs(val).format(dateFormat);
  }
  if (typeof val === 'boolean') {
    return '';
  }
  return ':' + val;
};
const getTableConclusion = (key: string) => {
  let text = '';
  const curValue = innerValue.value[key];
  const curTableSchema = innerSchemaMap.value[key].items.filter(v =>
    v.key.startsWith('table_')
  );
  curTableSchema.forEach(item => {
    const { key, columns, uiMethod, items } = item;
    if (text?.length) {
      text += '，';
    }
    if (uiMethod === 'table') {
      const valueKey = item.columns[1].key;
      const normalTable =
        curValue[key]
          ?.map(v => {
            if (v[valueKey] !== '' && v[valueKey] !== void 0) {
              return (
                items.find(j => j.key === v.key).value +
                formatValue(v.key, v[valueKey], items)
              );
            }
            return '';
          })
          .filter(v => !!v) ?? [];
      text += normalTable.join('、');
    } else {
      const addTable =
        curValue[key]?.map(val =>
          val
            .map((v, i) => {
              if (v.value !== '' && v.value !== false) {
                return i === 0
                  ? items.find(j => j.key === v.key).value
                  : columns[i].value + formatValue(v.key, v.value, items);
              }
              return '';
            })
            .filter(v => !!v)
            .join('、')
        ) ?? [];
      text += addTable.join('，');
    }
  });
  return text;
};
const getConclution = () => {
  const keys = Object.keys(innerValue.value);
  let text = '';
  keys.forEach(key => {
    const checkListData = innerValue.value[key][CHECK_LIST_KEY];
    const result = getFlatCheckList(checkListData);
    const ids = result.map(v => v.id);
    const curSchema = innerSchemaMap.value[key];
    const treeFlatList = transformList(
      curSchema.items.filter(
        v => !['add-table', 'table'].includes(v.uiMethod)
      ) ?? [],
      'value'
    );
    const listMap = getDataMap(treeFlatList);
    const { showNameList } = idsTransform(ids, listMap);
    const checkListConclusion = showNameList.map(v => v.text).join('、');
    const tableConclusion = getTableConclusion(key);
    if (checkListConclusion || tableConclusion) {
      text += curSchema.value + '：';
      text += [checkListConclusion, tableConclusion]
        .filter(v => !!v)
        .join('，');
      text += '；';
    }
  });
  return text;
};
const submitHandler = () => {
  const emitResult: any[] = [];
  const keys = Object.keys(innerValue.value);
  keys.forEach(key => {
    const checkListData = innerValue.value[key][CHECK_LIST_KEY];
    const result = getFlatCheckList(checkListData);
    const tableResult = getTableResult(key);
    const mergeRes = [...result, ...tableResult];
    emitResult.push({ en_session: key, label: mergeRes });
  });
  emits('confirm', { data: emitResult, conclusion: getConclution() });
  closeHandler();
};
const formatCurValue = (key: string, curValue: any) => {
  const curSchema = innerSchemaMap.value[key];
  const tableItems = curSchema.items.filter(v => v.key.startsWith('table_'));
  const treeSchema =
    curSchema.items.filter(v => !['add-table', 'table'].includes(v.uiMethod)) ??
    [];
  const treeFlatList = transformList(treeSchema, 'value');
  const listMap = getDataMap(treeFlatList);
  const listKeyMap = getDataMap(treeFlatList, 'key');
  const ids = curValue
    .filter(v => listKeyMap[v.key])
    .map(v => listKeyMap[v.key].id);
  const { resultTreeData } = idsTransform(ids, listMap);
  const res = { [CHECK_LIST_KEY]: resultTreeData };
  tableItems.forEach(t => {
    const list = curValue.filter(v => t.items.some(item => item.key === v.key));
    if (list.length) {
      list.forEach((val, i) => {
        if (
          typeof val.value === 'string' &&
          t.items?.[i]?.uiMethod === 'checkbox'
        ) {
          val.value = val.value === 'true';
        }
      });
      const splitLength =
        t.uiMethod === 'table' ? t.columns.length - 1 : t.columns.length;
      res[t.key] = chunk(list, splitLength);
      if (t.uiMethod === 'table') {
        const valueKey = t.columns[1].key;
        res[t.key] = res[t.key].map(v => ({
          key: v[0].key,
          [valueKey]: v[0].value,
        }));
      }
    }
  });
  return res;
};
const init = () => {
  setTimeout(() => {
    const res = value.reduce((pre, cur) => {
      const data = formatCurValue(cur.en_session, cur.label);
      return { ...pre, [cur.en_session]: data };
    }, {});
    innerValue.value = res;
  }, 200);
};
const formatSchemaMap = () => {
  innerSchemaMap.value = cloneDeep(schemaMap);
  forEach(innerSchemaMap.value, (val: any) => {
    const tableItems = val.items.filter(v =>
      ['table', 'add-table'].includes(v.uiMethod)
    );
    tableItems.forEach((v, i) => {
      v.key = 'table_' + i;
    });
  });
};
watch(
  () => value,
  val => {
    if (val) {
      // init();
    }
  },
  { immediate: true }
);
watch(
  () => schemaMap,
  () => formatSchemaMap(),
  { immediate: true }
);
defineOptions({
  name: 'SurgeryDialog',
});
</script>
<style lang="less">
.operation-dialog-wrap {
  .el-dialog__header {
    font-weight: 700;
    font-size: 16px;
    border-bottom: 1px solid #ccc;
  }
  .el-dialog__body {
    padding: 16px 0 0 0;
    max-height: 620px;
  }
}
</style>
<style scoped lang="less">
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  .head {
    box-sizing: border-box;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e9e8eb;
    .label {
      font-size: 16px;
      font-weight: bold;
      color: #101b25;
    }
    .close-icon {
      font-size: 18px;
      font-weight: bold;
    }
  }
  .main-content {
    box-sizing: border-box;
    display: flex;
    min-height: 420px;
    max-height: 600px;
    .left {
      height: 100%;
      width: 140px;
      background-color: #fff;
      .left-content {
        width: 100%;
        background-color: #f5f6f8;
        .tab-item {
          box-sizing: border-box;
          padding: 16px 0 16px 24px;
          cursor: pointer;
        }
        .active {
          background-color: #ffffff;
          color: #0a73e4;
        }
      }
    }
    .right {
      flex: 1;
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
      .right-content {
        background-color: #fff;
        flex: 1;
        box-sizing: border-box;
        padding: 0 16px;
        overflow-y: auto;
        :deep(.nopx-container) {
          height: auto;
          .content-wrapper {
            height: 100%;
          }
          .content {
            max-height: unset;
          }
        }
      }
    }
  }
}
.footer {
  padding: 16px 0 0 0;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e9e8eb;
}
</style>
