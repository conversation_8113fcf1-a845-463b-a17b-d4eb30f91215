<template>
  <div class="top flex items-center justify-between">
    <div class="base-msg flex items-center justify-center">
      <div class="avatar flex items-center justify-center">
        {{ baseInfo.patientName?.slice(0, 1) }}
      </div>
      <div class="base-box ml-8">
        <span class="name">{{ baseInfo.patientName }}</span>
        <el-divider direction="vertical" />
        <span class="sex">
          {{
            baseInfo.gender === 1 ? '男' : baseInfo.gender === 2 ? '女' : '未知'
          }}
        </span>
        <el-divider direction="vertical" />
        <span class="sex">{{ baseInfo.age }}岁</span>
      </div>
    </div>
    <div class="transcribe-box flex items-center">
      <template v-if="!isOcrModule">
        <TaskAcceptanceHeader
          v-if="typeof taskStatus === 'number' && taskInfo?.taskMethod === 0"
          :source-type="sourceType"
          :task-infos="taskInfo"
          :source-id="Number(sourceId)"
        />
        <div
          v-if="
            props.sourceType === 3 && baseInfo.inGroup !== 1 && sureJoinGroup
          "
          class="transcription-request flex items-center justify-center cursor-pointer ml-16"
          @click="sure"
        >
          确认入组
        </div>
        <div
          v-if="sourceId"
          class="transcription-request flex items-center justify-center cursor-pointer ml-16"
          @click="request"
        >
          转录申请
        </div>
      </template>
      <div v-else class="tips">
        {{ tabAction.data?.tabName }}
      </div>
    </div>
  </div>

  <!-- 转录申请 -->
  <Transcribe
    v-model:visible="visible"
    :source-type="sourceType"
    :source-id="sourceId"
  />
  <PlanStep
    :title="'确认入组'"
    :visible="planVisible"
    :ok-text="'确认入组'"
    :ok-callback="planConfirm"
    @close="planVisible = false"
  />
</template>
<script setup lang="ts">
import Transcribe from './Transcribe.vue';
import useGlobal from '@/store/module/useGlobal';
let useGlobalInfo = useGlobal();
import { sureInGroupApi } from '@/api/review';
import { dialogTip } from './hooks';
import { getPatientInfoBase } from '@/api/overview';
import store from '@/store';
import PlanStep from '@/components/PlanStep/index.vue';
const globalStore = store.useGlobal();
import { useComponentsTabAction } from '@/store/module/useComponentsTabAction';
const tabAction = useComponentsTabAction();
import TaskAcceptanceHeader from '@/features/TaskAcceptanceHeader/index.vue';
import { IRecordSourceType } from '@/store/module/useComponentsTabAction';
import { queryCaseTaskApi } from '@/api/task';
import { ICaseTaskApi } from '@/pages/Workbench/Header/components/TaskAcceptanceDrawer/type';
import useInternDrawer from '@/store/module/useInternDrawer';
const dStore = useInternDrawer();
import bus from '@/lib/bus';

// 头部信息
const props = defineProps({
  title: {
    default: '',
    type: String,
  },
  sourceType: {
    default: 0,
    type: Number as PropType<IRecordSourceType>,
  },
  sourceId: {
    type: [Number, String],
    default: 0,
  },
  isOcrModule: {
    default: false,
    type: Boolean,
  },
});

const planVisible = ref(false);
// 转录申请
const visible = ref(false);
const request = () => {
  visible.value = true;
};

// 获取基本信息
const baseInfo = ref({
  patientName: '',
  gender: 1,
  age: 0,
  inGroup: 0,
});

watchEffect(() => {
  const {
    inGroup = 0,
    patientName = '',
    gender,
    age = '--',
  } = (useGlobalInfo.userInfo || {}) as any;
  baseInfo.value = {
    inGroup,
    patientName,
    gender,
    age,
  };
});

onMounted(async () => {
  const patientId =
    globalStore.currentRole === 4
      ? dStore.patientId
      : (useGlobalInfo.userId as number);
  const res: any = await getPatientInfoBase({
    patientId,
  });
  globalStore.setUserInfo(res);

  if (props.sourceId) getCaseTaskStatus();
});

// 确认入组
const sureJoinGroup = ref(true);
const sure = () => {
  planVisible.value = true;
};
const planConfirm = () => {
  return sureInGroupApi({ patientId: useGlobalInfo.userId }).then(res => {
    if (res.code === 'E000000') {
      dialogTip('入组成功！', 'success');
      sureJoinGroup.value = false;
    }
  });
};

const taskStatus = ref<number | null>(0);
const taskInfo = ref();
const getCaseTaskStatus = () => {
  const info: ICaseTaskApi = {
    sourceId: props.sourceId! as number,
    taskType: props.sourceType!,
    taskMethod: 0,
  };

  queryCaseTaskApi(info).then((res: any) => {
    const { data } = res;
    taskInfo.value = data;
    taskStatus.value = data ? data.taskStatus : null;
    bus.off('update-task-status', getCaseTaskStatus);
  });
};

onMounted(() => {
  bus.on('update-task-status', () => {
    if (props.sourceId) getCaseTaskStatus();
  });
});
</script>
<style scoped lang="less">
.top {
  .base-msg {
    .avatar {
      width: 30px;
      height: 30px;
      background: #ffffff;
      border: 1px solid #efefef;
      font-size: 16px;
      font-weight: 600;
      color: #2e6be6;
      border-radius: 50%;
    }
    .base-box {
      :deep(.el-divider--vertical) {
        height: 12px;
      }
      .name {
        font-size: 14px;
        font-weight: 600;
        color: #3a4762;
      }
      .sex {
        font-size: 14px;
        color: #7a8599;
      }
    }
  }
  .transcribe-box {
    .title {
      font-size: 14px;
    }
    .transcription-request {
      width: 80px;
      height: 32px;
      background: #2e6be6;
      border-radius: 2px;
      font-size: 14px;
      color: #ffffff;
    }
    .acceptance-check {
      .btn {
        border-radius: 2px;
        border: 1px solid;
        font-size: 14px;
        box-sizing: border-box;
        cursor: pointer;
      }
      .pass {
        border-color: #2e6be6;
        color: #2e6be6;
      }
      .reject {
        border-color: #e63746;
        color: #e63746;
      }
      .revocation {
        border-color: #e37221;
        color: #e37221;
      }
      .status-btns {
        font-size: 14px;
      }
      .status1 {
        color: #2e6be6;
        background: #e6eeff;
      }
      .status2 {
        color: #2fb324;
        background: #e2f5e1;
      }
      .status3 {
        color: #e63746;
        background: #ffe6e7;
      }
    }
    .tips {
      color: #15233f;
    }
  }
}
</style>
