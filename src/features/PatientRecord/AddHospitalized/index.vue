<template>
  <div class="w-full h-full relative">
    <OccurrenceOfIllness
      ref="surgicalInformation"
      :key="renderKey"
      :cards="hospitalizedCards"
      :mode="mode"
      :resource-id="id"
      :api-url-middleware="apiUrlMiddleware"
      :source-type="sourceType"
      :additional-data="additionalData"
      :in-time-keys="[FormCategory.ADMISSION_REPORT, 'admission_date']"
      :before-submit-callback="beforeSubmitHandler"
      @resource-id-change="updateSourceId"
      @mode-change="updateMode"
      @update-in-time="updateInTime"
    >
      <template #header>
        <HeaderTitle
          v-if="!isSceneTypeOne"
          :title="data?.tabName"
          :source-type="sourceType"
          :source-id="id"
        />
      </template>
      <template #footer>
        <div
          v-if="showDeleteButton"
          class="py-sm text-center bg-[#F6F8FB] text-[#DC0101]"
        >
          <el-button type="danger" link @click="handleRemove">
            <Delete class="mr-3xs w-16" />
            删除住院
          </el-button>
        </div>
      </template>
    </OccurrenceOfIllness>
  </div>
</template>

<script setup lang="ts">
import { delPatientHistory } from '@/api/disease';
import { FormCategory, FormCategoryValues } from '@/constant';
import { OccurrenceOfIllness } from '@/features';
import { useHandleData } from '@/hooks';
import { useRenderRefresh } from '@/hooks/useRefresh';
import bus from '@/lib/bus';
import { IRecordProps } from '../hooks';
import store from '@/store';
import { Delete } from '@element-plus/icons-vue';
import { cloneDeep } from 'lodash-es';
import HeaderTitle from '../HeaderTitle.vue';
import { timestampToDate } from '../hooks';
import { useConfirmMessagebox } from '@/hooks/useHandleData';

defineOptions({ name: 'AddHospitalized' });

// Props 定义
const props = defineProps<IRecordProps>();
const { data } = toRefs(props);

// 计算属性
const id = computed(() => data?.value?.id);
const sourceType = computed(() => data?.value?.recordActionType?.sourceType);
const mode = computed(() => (id.value ? 'view' : 'create'));
const isSceneTypeOne = computed(() => sourceType.value === 1);
const showDeleteButton = computed(
  () => id.value && sourceType.value === 0 && !isSceneTypeOne.value
);
// 需要展示的卡片
const hospitalizedCards = [
  FormCategory.ADMISSION_REPORT,
  FormCategory.OPERATION_RECORD,
  FormCategory.DISCHARGE_REPORT,
  FormCategory.DIAGNOSE_REPORT,
];

// 状态管理
const { renderKey } = useRenderRefresh(data);
const inTime = ref('');
const useGlobalInfo = store.useGlobal();

// 计算属性
const locationStr = computed(() => {
  if (!id.value) return '';
  if (sourceType.value === 3) {
    return props.data?.tabName || '入组';
  }
  return `${timestampToDate(inTime.value)} 住院`;
});

const imgViewerProvideData = computed(() => ({
  sourceId: id.value,
  sourceType: sourceType.value,
  locationStr: locationStr.value,
}));

/** 提交时额外携带的参数 */
const additionalData = computed(() => ({
  admission_report: {
    group: sourceType.value === 3 ? 1 : 0,
    source_type: sourceType.value === 3 ? 0 : sourceType.value,
    patient_id: useGlobalInfo.userId,
  },
  all: {
    group: sourceType.value === 3 ? 1 : 0,
    source_type: sourceType.value === 3 ? 0 : sourceType.value,
    patient_id: useGlobalInfo.userId,
  },
  [FormCategory.DISCHARGE_REPORT]: {
    patient_history_id: id.value,
  },
}));

// 提供注入
provide('imgViewerData', imgViewerProvideData);

const updateInTime = time => {
  inTime.value = time;
};
const beforeSubmitHandler = async (category: FormCategoryValues) => {
  if (
    category === 'admission_report' &&
    useGlobalInfo.diseaseType === 'HEART_FAILURE'
  ) {
    return await useConfirmMessagebox({
      message: '提示',
      description:
        '此次住院记录会变更易损期，后续随访复查将按照新周期重新下发，请确认是否保存?',
    });
  }
  return true;
};

// 方法定义
function apiUrlMiddleware(key: string): string {
  return `/api/case/history/${key}`;
}

function updateSourceId(id: string | number, disabledRefresh?: boolean): void {
  if (!id || data?.value?.id) return;

  if (!disabledRefresh) {
    renderKey.value += 1;
  }

  const curData = cloneDeep(data.value) || {};
  curData.id = id;
  curData.recordActionType.actionType = 'view';
  props.updateTabItem?.({
    data: curData,
  });
}

async function handleRemove(): Promise<void> {
  await useHandleData(
    delPatientHistory,
    { patientHistoryId: id.value, caseType: 0 },
    '是否确认删除住院记录？',
    '删除后不可撤回'
  );

  props.updateTabItem?.({ type: 'view' });
  props.deleteTabItem?.();
  bus.emit('updata-review-list');
}

function updateMode(mode: string) {
  props.updateTabItem?.({
    type: mode,
  });
}

const surgicalInformationRef = useTemplateRef('surgicalInformation');
const updata = () => {
  surgicalInformationRef.value?.refresh?.();
};

onMounted(() => {
  bus.on('batch-ocr-success-refresh-data', updata);
});
onUnmounted(() => {
  bus.off('batch-ocr-success-refresh-data', updata);
});
</script>
