<template>
  <OccurrenceOfIllness
    :key="renderKey"
    :cards="[
      FormCategory.OUTPATIENT_RECORD,
      FormCategory.OUTPATIENT_PRESCRIPTION,
      FormCategory.DIAGNOSE_REPORT,
    ]"
    :mode="id ? 'view' : 'create'"
    :resource-id="id"
    :api-url-middleware="apiUrlMiddleware"
    :source-type="sourceType"
    :additional-data="{
      admission_report: {
        group: sourceType === 3 ? 1 : 0,
        // 来源类型
        source_type: sourceType === 3 ? 0 : sourceType,
        // 病人ID
        patient_id: useGlobalInfo.userId,
      },
      all: {
        group: sourceType === 3 ? 1 : 0,
        // 来源类型
        source_type: sourceType === 3 ? 0 : sourceType,
        // 病人ID
        patient_id: useGlobalInfo.userId,
      },
      [FormCategory.DISCHARGE_REPORT]: {
        patient_history_id: id,
      },
    }"
    :in-time-keys="[FormCategory.OUTPATIENT_RECORD, 'outpatient_date']"
    @resource-id-change="updateSourceId"
    @mode-change="updateMode"
    @update-in-time="updateInTime"
  >
    <template #header>
      <HeaderTitle
        v-if="sceneType !== 1"
        :title="data?.tabName"
        :source-type="sourceType"
        :source-id="id"
      />
    </template>
    <template #footer>
      <div v-if="id && sceneType !== 1" class="py-sm text-center bg-[#F6F8FB]">
        <el-button type="danger" class="text-sm" link @click="handleRemove">
          <Delete class="mr-3xs w-16" />
          删除诊疗记录
        </el-button>
      </div>
    </template>
  </OccurrenceOfIllness>
</template>
<script setup lang="ts">
import { Delete } from '@element-plus/icons-vue';
import { FormCategory } from '@/constant';
import { OccurrenceOfIllness } from '@/features';
import store from '@/store';
import HeaderTitle from '../HeaderTitle.vue';
import { useHandleData } from '@/hooks';
import { delPatientHistory } from '@/api/disease';
import { cloneDeep } from 'lodash-es';
import bus from '@/lib/bus';
import { IRecordProps } from '../hooks';
import { useRenderRefresh } from '@/hooks/useRefresh';
import { timestampToDate } from '../hooks';

defineOptions({ name: 'AddOutpatientRecord' });

const props = defineProps<IRecordProps>();
const { data } = toRefs(props);
const id = computed(() => data?.value?.id);
const sourceType = computed(() => data?.value?.recordActionType?.sourceType);
const { renderKey } = useRenderRefresh(data);
const inTime = ref('');

const useGlobalInfo = store.useGlobal();

const updateSourceId = (id: string | number, disabledRefresh?: boolean) => {
  if (!id) return;
  if (!data?.value?.id) {
    if (!disabledRefresh) {
      renderKey.value += 1;
    }
    const curData = cloneDeep(data.value) || {};
    curData.id = id;
    curData.recordActionType.actionType = 'view';
    props.updateTabItem?.({
      data: curData,
    });
  }
};

const updateInTime = time => {
  inTime.value = time;
};

function apiUrlMiddleware(key: string) {
  return `/api/case/history/${key}`;
}

const handleRemove = async () => {
  await useHandleData(
    delPatientHistory,
    { patientHistoryId: id.value, caseType: 1 },
    '是否确认删除门诊记录？',
    '删除后不可撤回'
  );
  props.updateTabItem?.({ type: 'view' });
  props.deleteTabItem?.();
  bus.emit('updata-review-list');
};
const locationStr = computed(() => {
  if (!id.value) return '';
  return timestampToDate(inTime.value) + ' 门诊';
});
const imgViewerProvideData = computed(() => {
  return {
    sourceId: id.value,
    sourceType: sourceType.value,
    locationStr: locationStr.value,
  };
});
provide('imgViewerData', imgViewerProvideData);

function updateMode(mode: string) {
  props.updateTabItem?.({ type: mode });
}
</script>
<style scoped lang="less">
.outpatient-page {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.header {
  background: #fff;
  padding: 16px;
  box-sizing: border-box;
  .procedure-box {
    .item {
      font-size: 14px;
      color: #7a8599;
      position: relative;
      .separator {
        top: -4px;
        right: -24px;
        position: absolute;
      }
    }
    .itemActive {
      font-size: 14px;
      font-weight: 600;
      color: #2e6be6;
    }
  }
  .boxShadow {
    box-shadow: 0 0 2px 0 rgba(186, 200, 212, 0.5);
  }
}
.outpatient-box {
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 0; /* 滚动条的宽度 */
  }
}
</style>
