<!--复查-->
<template>
  <div class="reexamination relative">
    <template v-if="sceneType !== 1">
      <div class="sticky top-0 z-10">
        <div class="bg-white p-xs pb-16">
          <HeaderTitle
            :title="data?.tabName"
            :source-type="sourceType"
            :source-id="id"
          />
          <Attachment
            :source-type="sourceType"
            :source-id="id"
            @refresh="refreshHandler"
          />
        </div>
        <div class="reexamination-header">
          <div class="flex items-baseline">
            <el-tag
              effect="light"
              :type="stateMap[summary.status || '-1'].type"
            >
              {{ stateMap[summary.status || '-1'].label }}
            </el-tag>
            <div class="text-sub-text text-sm ml-sm">
              应查{{ reportTotal }}个项目
            </div>
          </div>
          <el-button
            v-if="summary.times === 0"
            class="text-sm"
            type="danger"
            link
            @click="handleRemove"
          >
            <i-ep-delete class="mr-3xs" />
            删除自定义复查
          </el-button>
        </div>
      </div>
    </template>
    <CardWrapper
      class="reexamination-card-wrapper"
      title="复查总结"
      :tools-tips="`上次编辑人员/时间：${summary.userName || '--'}：${summary.modifyTime || '--'}`"
    >
      <template #tools>
        <el-button
          v-if="summaryActionType === 'view'"
          link
          type="primary"
          @click="handleSummaryAction('edit')"
        >
          <i-ep-edit-pen class="mr-3xs" />
          编辑
        </el-button>
      </template>

      <template v-if="summaryActionType !== 'view'">
        <div class="edit-title flex justify-between">
          <span>复查结论</span>
          <el-button type="primary" @click="createSummary">
            {{ !isEditSummary ? '生成模板' : '刷新模版' }}
          </el-button>
        </div>
        <el-input
          v-model="summary.conclusion"
          :rows="3"
          type="textarea"
          maxlength="1000"
          placeholder="请输入，不超过1000个字符"
          @change="changeSummary"
        />
        <div class="edit-title mt-2xs">医生建议</div>
        <el-input
          v-model="summary.doctorOpinion"
          :rows="3"
          type="textarea"
          maxlength="1000"
          placeholder="请输入，不超过1000个字符"
        />
        <div class="h-32 flex items-center">
          <span class="mr-8 edit-title derate-money">
            是否符合减免续费金额：
          </span>
          <el-radio-group v-model="isConfirmDeposit" :disabled="userStatus">
            <el-radio
              :value="true"
              :disabled="policyStartDate !== policyEndDate"
            >
              是
            </el-radio>
            <el-radio :value="false">否</el-radio>
          </el-radio-group>
          <span v-if="policyStartDate" class="ml-16">
            两次减免复查须间隔2月以上
          </span>
          <span v-if="policyEndDate" class="ml-16">
            当前患者不满足续费减免政策
          </span>
        </div>
        <div class="btn-group">
          <el-button type="primary" @click="save">确定</el-button>
          <el-button
            v-if="summaryActionType !== 'add'"
            @click="handleSummaryAction('view')"
          >
            取消
          </el-button>
        </div>
      </template>
      <template v-else>
        <div class="reexamination-view">
          <div class="text-sub-text flex-shrink-0">复查结论：</div>
          <div class="content break-all">{{ summary.conclusion }}</div>
        </div>
        <div class="reexamination-view">
          <div class="text-sub-text flex-shrink-0">医生建议：</div>
          <div class="content break-all">{{ summary.doctorOpinion }}</div>
        </div>
        <div class="reexamination-view">
          <div class="text-sub-text flex-shrink-0 leading-8">
            是否符合减免续费金额：
          </div>
          <div class="content break-all ml-4">
            <el-radio-group v-model="isConfirmDeposit" disabled>
              <el-radio :value="true">是</el-radio>
              <el-radio :value="false">否</el-radio>
            </el-radio-group>
            <span v-if="policyStartDate" class="ml-16">
              两次减免复查须间隔2月以上
            </span>
            <span v-if="policyEndDate" class="ml-16">
              当前患者不满足续费减免政策
            </span>
          </div>
        </div>
      </template>
    </CardWrapper>
    <Attachment
      v-if="sceneType === 1"
      :source-type="sourceType"
      :source-id="id"
    />
    <div :class="{ 'custom-inspection': sceneType === 1 }">
      <Inspection
        ref="inspectionRef"
        :render-key="renderKey"
        :source-id="id"
        :source-type="sourceType"
        :action-type="actionType"
        @update-total="(count: number) => (reportTotal = count)"
        @on-status-change="
          (cType: IRecordActionType) => changeContentStatus('check-item', cType)
        "
        @get-data-list="getDataList"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { cloneDeep, every, values } from 'lodash-es';
import Inspection from '../Inspection/index.vue';
import CardWrapper from '@/components/CardWrapper/index.vue';
import { useHandleData } from '@/hooks';
import store from '@/store';
import { IRecordActionType } from '@/store/module/useComponentsTabAction';
import {
  delReview,
  getReviewDetail,
  updateReviewConclusion,
} from '@/api/reexamination';
import { IApiPatientReviewDetail } from '@/interface/type';
import dayjs from 'dayjs';
import bus from '@/lib/bus';
import { useRenderRefresh } from '@/hooks/useRefresh';
import { IRecordProps } from '@/features/PatientRecord/hooks';
import HeaderTitle from '@/features/PatientRecord/HeaderTitle.vue';
import Attachment from '@/components/Attachment/index.vue';
import useGlobal from '@/store/module/useGlobal';
import {
  createSummaryTemplate,
  queryReviewReductionMsgApi,
} from '@/api/reexamination';

import { renderElMessage } from '@/hooks/useHandleData';
import AddReport from '@/pages/Workbench/Main/Management/StructuredReport/AddReport/index.vue';
defineOptions({ name: 'Reexamination' });

const emits = defineEmits<{ (e: 'statusChange', value: boolean): void }>();

const inspectionRef = ref();
const globalStore = useGlobal();
const props = defineProps<IRecordProps>();
const { data } = toRefs(props);
const reportTotal = ref(0);
const globalData = store.useGlobal();
const tabStore = store.useTabs();
const id = computed(() => props?.data?.id);
const sourceType = computed(() => props?.data?.recordActionType?.sourceType);
const actionType = computed(() => props?.data?.recordActionType?.actionType);
const summaryActionType = ref(actionType.value);
const summary = ref<IApiPatientReviewDetail>({
  status: -1,
  conclusion: '',
  doctorOpinion: '',
});
const reviewDate = ref('');

const { renderKey } = useRenderRefresh(data);

const reviewTime = ref<any>(null);
const stateMap: { [key: string]: { type: string; label: string } } = {
  1: { type: 'info', label: '未开始' },
  32: { type: 'danger', label: '未上传' },
  64: { type: 'success', label: '已上传' },
  128: { type: 'info', label: '已完成' },
  '-1': { type: 'info', label: '已失效' },
};

// 内容状态
const contentStatus = ref({
  summary: actionType.value,
  'check-item': actionType.value,
});

const refreshHandler = (id: string | number) => {
  if (!id) return;
  if (!data?.value?.id) {
    renderKey.value += 1;
    const curData = cloneDeep(data.value) || {};
    curData.id = id;
    curData.recordActionType.actionType = 'view';
    props.updateTabItem?.({
      data: curData,
    });
  }
  getDetail();
};
const changeContentStatus = (id: string, actionType: IRecordActionType) => {
  // 使用场景为实习生端时，emit组件状态
  if (props.sceneType === 1 && id === 'check-item') {
    emits('statusChange', actionType === 'view');
    return;
  }

  if (!props.updateTabItem) return;
  if (contentStatus.value[id]) contentStatus.value[id] = actionType;
  if (every(values(contentStatus.value), item => item === 'view')) {
    props.updateTabItem({ type: 'view' });
  } else {
    props.updateTabItem({ type: 'edit' });
  }
  getDetail();
};

const handleRemove = async () => {
  await useHandleData(
    delReview,
    { reviewId: id.value },
    '是否确认删除复查？',
    '删除后不可撤回'
  );
  props.updateTabItem?.({ type: 'view' });
  props.deleteTabItem?.();
  bus.emit('updata-review-list');
};

const handleSummaryAction = async (type: IRecordActionType) => {
  summaryActionType.value = type;
  changeContentStatus('summary', type);
};

const getDetail = async () => {
  const res = await getReviewDetail({ reviewId: Number(id.value) });
  reviewTime.value = res.date;
  isConfirmDeposit.value = res.isConfirmDeposit ?? false;
  res.modifyTime = res.modifyTime
    ? dayjs(res.modifyTime).format('YYYY-MM-DD HH:mm:ss')
    : '--';
  reviewDate.value = res.date ?? '';
  if (res.status) summary.value = res;
};

// 患者管理情况主页跳转
const jumpManage = () => {
  const reportTheme = props.data?.tabName || '';
  tabStore.mainActiveTab = 4;
  const fn = () => {
    tabStore.addTab({
      name: '阶段性总结报告',
      group: 'ManagementSituation',
      component: AddReport,
      disableCache: true,
      mainTabCode: 4,
      key: reportTheme,
      data: {
        reportTheme: reportTheme?.split(' ')[1] || reportTheme,
      },
    });
  };
  if (globalData.manageTabReady) {
    fn();
  } else {
    globalData.manageTabReadyCallback = fn;
  }
};

const save = async () => {
  const { conclusion, doctorOpinion } = summary.value;
  if (!conclusion || !doctorOpinion) return ElMessage.error('值不能为空！');
  ElMessageBox({
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    message: renderElMessage(
      '是否生成阶段性总结报告？',
      '默认包含基本信息、手术信息、出院诊断、当前用药、指标（可选时间段）、检查、复查结论、医生建议',
      true
    ),
    showCancelButton: true,
    customClass: 're-confirm-el-message-box',
    beforeClose: async (action, instance, done) => {
      await updateReviewConclusion({
        conclusion,
        doctorOpinion,
        isConfirmDeposit: isConfirmDeposit.value,
        userId: globalData.userId,
        userType: String(globalData.currentRole),
        reviewId: Number(id.value),
      })
        .then(res => {
          const { code } = res;
          if (code === 'E050201' || code === 'E000000') {
            handleSummaryAction('view');
            ElMessage.success('更新成功！');
            bus.emit('updata-review-list');

            if (action === 'confirm') {
              jumpManage();
            }

            done();
          }
        })
        .catch(() => {});
    },
  });
};

//生成复查结论

const isEditSummary = ref<boolean>(false);

const indexFormData = ref([]);

const getDataList = value => {
  indexFormData.value = value;
};

const createSummary = () => {
  isEditSummary.value = true;
  if (isEditSummary.value) {
    ElMessageBox.confirm('刷新模版会覆盖当前编辑内容，是否确认刷新', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        const data = inspectionRef.value?.dealReviewConclusionParams();
        let params = {
          patientId: globalStore.userId,
          reviewTime: reviewTime.value,
          ...data,
        };
        createSummaryTemplate(params).then(res => {
          summary.value.conclusion = res as string;
        });
      })
      .catch(() => {});
  }
};

const changeSummary = () => {
  isEditSummary.value = true;
};

const locationStr = computed(() => {
  if (!id.value) return '';
  let title = props.data?.tabName;
  if (!title) {
    title = '复查记录';
  }
  return title + ' ' + stateMap[summary.value.status || '-1'].label;
});
const imgViewerProvideData = computed(() => {
  return {
    sourceId: id.value,
    sourceType: sourceType.value,
    locationStr: locationStr.value,
  };
});
provide('imgViewerData', imgViewerProvideData);

// 是否符合续费减免金额
const isConfirmDeposit = ref<boolean>(false);
const policyStartDate = ref<boolean>(false);
const policyEndDate = ref<boolean>(true);
const userStatus = ref<boolean>(false);
const queryReviewReductionMsg = () => {
  const params = {
    reviewId: id.value,
    patientId: globalStore.userId,
  };
  queryReviewReductionMsgApi(params).then((res: any) => {
    policyStartDate.value = res?.reviewMsg;
    policyEndDate.value = res?.renewMsg;
    userStatus.value = res?.userStatus;
  });
};

watch(
  id,
  reviewId => {
    if (reviewId) {
      getDetail();
      queryReviewReductionMsg();
    }
  },
  { immediate: true }
);
onMounted(() => {
  bus.on('batch-ocr-success-refresh-data', getDetail);
});
onUnmounted(() => {
  bus.off('batch-ocr-success-refresh-data', getDetail);
});
</script>

<style scoped lang="less">
.reexamination {
  font-size: 14px;

  .reexamination-header {
    border-top: 8px solid #f6f8fb;
    padding: 16px;
    background: #fff;
    position: relative;
    display: flex;
    justify-content: space-between;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 16px;
      height: 1px;
      width: calc(100% - 32px);
      background: #e9e8eb;
    }
  }

  .edit-title {
    padding-bottom: 8px;
    font-weight: 600;
    color: var(--color-primary-text);
  }
  .derate-money {
    padding-bottom: 0;
  }

  .reexamination-view {
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;

    .content {
      flex: 1;
      color: #3a4762;
    }
  }

  .btn-group {
    padding: 24px 0 0 106px;
    .el-button {
      width: 76px;
    }
  }

  .custom-inspection {
    margin-top: 16px;
    &::-webkit-scrollbar {
      width: 0;
    }
  }
}

:deep(.reexamination-card-wrapper) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  margin-top: 0;
}
</style>
