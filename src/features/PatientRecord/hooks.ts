import { ITabProps } from '@/store/module/useTabs';
import { type Component } from 'vue';
import { IRecordActionType } from '@/store/module/useComponentsTabAction';

export const medicineTimeList = [
  {
    value: 1,
    label: '早上',
  },
  {
    value: 2,
    label: '中午',
  },
  {
    value: 8,
    label: '下午',
  },
  {
    value: 3,
    label: '晚上',
  },
  {
    value: 4,
    label: '早中晚',
  },
  {
    value: 5,
    label: '早晚',
  },
  {
    value: 6,
    label: '午晚',
  },
  {
    value: 7,
    label: '早午',
  },
];

export const unitList = [
  {
    label: '小时',
  },
  {
    label: '天',
  },
  {
    label: '周',
  },
  {
    label: '月',
  },
  {
    label: '年',
  },
];

export const pacemakerJson = [
  {
    label: '起搏器类型',
    type: 'checkbox-group',
    key: 'type',
    options: [
      { value: 1, label: '单腔' },
      { value: 2, label: '双腔' },
      { value: 3, label: '无导线起搏器' },
      { value: 4, label: 'ICD' },
      { value: 5, label: 'CRTP' },
      { value: 6, label: 'CRTD' },
    ],
  },
  {
    label: '起搏器厂商',
    type: 'select',
    key: 'manufacturer',
    otherKey: 'manufacturer_other',
    hasOther: true,
    otherValue: 4,
    otherMaxLength: 10,
    options: [
      { value: 1, label: '美敦力' },
      { value: 2, label: '波科' },
      { value: 3, label: '雅培' },
      { value: 5, label: '百多力' },
      { value: 4, label: '其他' },
    ],
  },
  {
    label: '起搏器编码',
    key: 'code',
    type: 'select',
    options: [
      { value: 1, label: 'DDD' },
      { value: 2, label: 'VVI' },
      { value: 3, label: 'DVI' },
      { value: 4, label: 'AAI' },
      { value: 5, label: 'VDD' },
    ],
  },
  {
    label: '置入时间',
    key: 'insertTime',
    type: 'date',
    dateType: 'datetime',
    valueFormat: 'yyyy/MM/dd HH:mm:ss',
    pickerOptions: {
      disabledDate: date => {
        return date.getTime() > Date.now();
      },
    },
    block: 'inline',
  },
  {
    label: '剩余电量',
    type: 'input',
    key: 'electricQuantity',
    block: 'inline',
    valueType: 'number',
    precision: 0,
    unit: '%',
    format: val => {
      if (val < 1) return 1;
      if (val > 100) return 100;
      return val;
    },
  },
  {
    label: '心室起搏比例（VP）',
    block: 'inline',
    type: 'input',
    valueType: 'number',
    key: 'vp',
    precision: 3,
    unit: '%',
    width: '400px',
  },
  {
    label: '心房起搏比例（AP）',
    type: 'input',
    key: 'ap',
    block: 'inline',
    valueType: 'number',
    precision: 3,
    unit: '%',
  },
  {
    label: '房速/房颤（AT/AF）',
    key: ['at', 'af'],
    type: 'between',
    valueType: 'number',
    precision: 3,
  },
  {
    label: '房速/房颤时间(Time in AT/AF)',
    key: ['timeInAt', 'timeInAf'],
    type: 'between',
    valueType: 'number',
    precision: 3,
    unit: 'hr/day(%)',
  },
  {
    label: '心房高频事件（Atrial arrhythmia burden)',
    type: 'input',
    key: 'atrialEvent',
    valueType: 'number',
    block: 'inline',
    precision: 3,
    unit: '%',
  },
  {
    label: '持续时间',
    type: 'input',
    key: 'duration',
    block: 'inline',
    valueType: 'number',
    precision: 0,
    unit: 'h',
  },
];

// 弹窗提醒
export const dialogTip = (msg: string, type = '') => {
  return ElMessage({
    message: msg || '请稍等！',
    type: type || 'warning',
    showClose: true,
  } as any);
};

//处理药品规格
export function getDrugSpecStr(obj: {
  ingredients: any;
  contentUnit: any;
  packageNum: string;
  unit: string;
  packageUnit: string;
}) {
  const ingredients = obj.ingredients + obj.contentUnit;
  const packageContent = obj.packageNum ? '*' + obj.packageNum + obj.unit : '';
  const packageUnit = obj.packageUnit ? '/' + obj.packageUnit : '';
  return ingredients + packageContent + packageUnit;
}

// 将时间戳转为年月日
export function timestampToDate(timestamp: string | number | Date) {
  if (!timestamp) return '--';

  const date = new Date(timestamp); // JavaScript Date对象需要的毫秒级时间戳
  const year = date.getFullYear();
  let month: any = date.getMonth() + 1; // getMonth()返回的月份从0开始，所以要+1
  let day: any = date.getDate();

  // 如果月份或日期小于10，前面补0
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;

  const dateStr = year + '-' + month + '-' + day;
  return dateStr;
}

// 编辑时间
export function timestampToDateTime(timestamp: string | number | Date) {
  if (!timestamp) return '--';

  // 创建一个新的Date对象
  const date = new Date(timestamp);

  // 获取年、月、日、时、分、秒
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // 月份是从0开始的，所以要加1
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();

  // 为了确保月份、日期、小时、分钟和秒都是两位数，我们使用padStart方法
  const paddedMonth = month.toString().padStart(2, '0');
  const paddedDay = day.toString().padStart(2, '0');
  const paddedHours = hours.toString().padStart(2, '0');
  const paddedMinutes = minutes.toString().padStart(2, '0');
  const paddedSeconds = seconds.toString().padStart(2, '0');

  // 返回日期和时间字符串
  return `${year}-${paddedMonth}-${paddedDay} ${paddedHours}:${paddedMinutes}:${paddedSeconds}`;
}

// 全量病种树形数据新增 chooseType
export const transformDiseaseTList = (data: any) => {
  const chidrenIsRadio = [
    36, 46, 37, 41, 48, 52, 58, 62, 67, 70, 74, 78, 82, 86, 90, 94, 98, 102,
    106, 114, 119, 124, 129, 135, 139, 142, 145, 149, 152, 155, 156, 157, 158,
    159, 160, 161, 165, 168, 171, 174, 209,
  ];
  const fn = (node: any) => {
    if (!node) return;
    if (Array.isArray(node)) {
      for (const v of node) {
        fn(v);
      }
    } else {
      if (chidrenIsRadio.includes(node.pId)) {
        node.chooseType = 'radio';
      } else {
        node.chooseType = 'checkbox';
      }
      fn(node.children);
    }
  };
  fn(data);
  const hasChild = data.filter(v => v.children.length || v.diseaseId === 213);
  const hasNoChild = data.filter(
    v => !v.children.length && v.diseaseId !== 213
  );
  const sortData = [...hasNoChild, ...hasChild];
  return sortData;
};

// BMI计算
export function BMINum(weight: number, height: number) {
  const heightSquared = (height * height) / 10000;
  const BMI = (weight / heightSquared).toFixed(1);
  if (isNaN(Number(BMI))) {
    return '--';
  } else {
    return Number(BMI);
  }
}

/** 场景类型 0: 无（默认） 1: 实习生端 */
export type IRecordSceneType = 0 | 1;

/** 门诊 ｜ 住院 ｜ 复查 组件props */
export interface IRecordProps extends Partial<ITabProps> {
  sceneType?: IRecordSceneType;
}

/** 对应模块段落类型
 * 1:入院记录 2:手术记录 3:出院信息 4:门诊记录 5:门诊处方 6:检查项
 */
export type ModulesType = 1 | 2 | 3 | 4 | 5 | 6;

/** 段落 */
export interface IProcedureItem {
  title: string;
  id: string;
  type: ModulesType;
  component: Component;
  actionType: IRecordActionType;
}
