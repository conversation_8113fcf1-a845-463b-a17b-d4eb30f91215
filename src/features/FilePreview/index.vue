<template>
  <div class="h-file-preview">
    <template v-if="props.size === 'small'">
      <template v-for="item in files" :key="item.key">
        <template v-if="FILE_CATEGORY[item.type]">
          <div class="relative cursor-pointer" :title="item.name">
            <ImgPreview
              v-if="FILE_CATEGORY[item.type].isImg"
              :key="item.url || item.fileUrl"
              :url="item.url || item.fileUrl"
              :width="22"
              :height="18"
              disable-scanning
              :show-status-label="false"
              :show-status="false"
              :fixed="true"
              :type="'doubt-detail'"
              :lazy="true"
              class="mark"
            />
            <img
              :class="[
                'h-file-preview-item',
                'h-file-preview-item--small',
                FILE_CATEGORY[item.type].iconClass,
              ]"
              :src="FILE_CATEGORY[item.type].iconSrc"
              @click="handlePreview(item)"
            />
          </div>
        </template>
        <img
          v-else
          class="h-file-preview-item h-file-preview-item--small h-file-preview-other"
          :src="fileIcon"
          :title="item.name"
          @click="handlePreview(item)"
        />
      </template>
    </template>
    <template v-else>
      <div
        v-for="item in files"
        :key="item.key"
        class="h-file-preview-item h-file-preview-item--default"
        :title="item.name"
        @click="handlePreview(item)"
      >
        <div class="h-file-preview-item--default_top">
          <template v-if="FILE_CATEGORY[item.type]">
            <!-- <img
              v-if="FILE_CATEGORY[item.type].isImg"
              :src="item.url || item.fileUrl"
              :style="{
                width: '100%',
                height: '100%',
                borderRadius: 'inherit',
              }"
            /> -->
            <ImgPreview
              v-if="FILE_CATEGORY[item.type].isImg"
              :key="item.url || item.fileUrl"
              :url="item.url || item.fileUrl"
              :width="58"
              disable-scanning
              :show-status-label="false"
              :show-status="false"
              :fixed="true"
              :type="'doubt-detail'"
              :lazy="true"
            />
            <img
              v-else
              :class="FILE_CATEGORY[item.type].iconClass"
              :src="FILE_CATEGORY[item.type].iconSrc"
            />
          </template>
          <img v-else class="h-file-preview-other" :src="fileIcon" />
        </div>
        <div class="h-file-preview-item--default_bottom">{{ item.name }}</div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { getFileType, getFileName, getUuid } from '@/utils';
import imgIcon from '@/assets/icons/<EMAIL>';
import wordIcon from '@/assets/icons/<EMAIL>';
import pdfIcon from '@/assets/icons/<EMAIL>';
import excelIcon from '@/assets/icons/<EMAIL>';
import fileIcon from '@/assets/icons/<EMAIL>';
import ImgPreview from '@/components/ImgPreview/index.vue';

interface IPropsFileItem {
  url?: string;
  fileUrl?: string;
  name?: string;
  fileName?: string;
  type?: string;
  key?: string;
}
type IFileItem = Required<IPropsFileItem>;
type ISize = 'small' | 'default';
interface IProps {
  fileList: IPropsFileItem[];
  size?: ISize;
}

const FILE_CATEGORY = {
  jpg: {
    iconSrc: imgIcon,
    iconClass: 'h-file-preview-img',
    isImg: true,
  },
  jpeg: {
    iconSrc: imgIcon,
    iconClass: 'h-file-preview-img',
    isImg: true,
  },
  png: {
    iconSrc: imgIcon,
    iconClass: 'h-file-preview-img',
    isImg: true,
  },
  doc: {
    iconSrc: wordIcon,
    iconClass: 'h-file-preview-other',
    isImg: false,
  },
  docx: {
    iconSrc: wordIcon,
    iconClass: 'h-file-preview-other',
    isImg: false,
  },
  pdf: {
    iconSrc: pdfIcon,
    iconClass: 'h-file-preview-other',
    isImg: false,
  },
  xlsx: {
    iconSrc: excelIcon,
    iconClass: 'h-file-preview-other',
    isImg: false,
  },
  xls: {
    iconSrc: excelIcon,
    iconClass: 'h-file-preview-other',
    isImg: false,
  },
};
const props = withDefaults(defineProps<IProps>(), {
  fileList: () => [],
  size: 'default',
});
const transFiles = (fileParams: IPropsFileItem[]): IFileItem[] => {
  const newFiles = fileParams.map(item => {
    const newItem: IFileItem = { ...item, key: getUuid() } as IFileItem;

    if (item.name) {
      newItem.name = decodeURIComponent(newItem.name || newItem.fileName);
    } else {
      newItem.name = decodeURIComponent(
        getFileName((item.url || item.fileUrl) as string)
      );
    }
    if (!item.type)
      newItem.type = getFileType((item.url || item.fileUrl) as string);

    return newItem;
  });

  return newFiles;
};
const files = ref<IFileItem[]>(transFiles(props.fileList));

watch(
  () => props.fileList,
  newValues => {
    files.value = transFiles(newValues);
  }
);

const handlePreview = (item: IPropsFileItem) => {
  window.open(item.url || item.fileUrl, '_blank');
};
</script>

<style lang="less" scoped>
.h-file-preview {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: -10px;
  .h-file-preview-item {
    margin-right: 15px;
    margin-bottom: 10px;
    cursor: pointer;
  }
  .h-file-preview-item--default {
    width: 60px;
    .h-file-preview-item--default_top {
      width: 100%;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f9f9fb;
      border: 1px solid #e9e8eb;
      border-radius: 2px;
    }
    .h-file-preview-item--default_bottom {
      font-size: 12px;
      line-height: 16px;
      margin-top: 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .h-file-preview-img {
    width: 22px;
    height: 18px;
  }
  .h-file-preview-other {
    width: 18px;
    height: 20px;
  }
}
.mark {
  position: absolute;
  opacity: 0;
  left: 0;
  top: 0;
}
</style>
