/**
 * 获取数组中 item 类型
 */
export type ItemType<T> = T extends Array<infer U> ? U : never;

/**
 * undefined
 */
export interface IApiCaseHistoryDynamicKeyParamsKey {}

/**
 * 保存通用指标 - body 请求参数
 */
export interface IApiCaseHistoryDynamicKeyParams {
  key?: IApiCaseHistoryDynamicKeyParamsKey;
}

/**
 * 保存通用指标
 */
export type IApiCaseHistoryDynamicKey = undefined;

/**
 *
 */
export interface IApiCaseHistoryEcg_12ParamsDiagnosisItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryEcg_12ParamsDiagnosis {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryEcg_12ParamsDiagnosisItems[];
}

/**
 *
 */
export interface IApiCaseHistoryEcg_12ParamsTable {
  key?: string;
  result?: string;
}

/**
 * 新增12导联心电图 - body 请求参数
 */
export interface IApiCaseHistoryEcg_12Params {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  examination_date: number;
  examination_accessory?: string[];
  diagnosis?: IApiCaseHistoryEcg_12ParamsDiagnosis[];
  table?: IApiCaseHistoryEcg_12ParamsTable[];
}

/**
 * 新增12导联心电图
 */
export type IApiCaseHistoryEcg_12 = undefined;

/**
 *
 */
export interface IApiCaseHistoryWalkTestParamsTime {
  ago?: string;
  after?: string;
}

/**
 *
 */
export interface IApiCaseHistoryWalkTestParamsHeart_rate {
  ago?: number;
  after?: number;
}

/**
 *
 */
export interface IApiCaseHistoryWalkTestParamsBlood_pressureAgo {
  systolic?: number;
  diastolic?: number;
}

/**
 *
 */
export interface IApiCaseHistoryWalkTestParamsBlood_pressureAfter {
  systolic?: number;
  diastolic?: number;
}

/**
 *
 */
export interface IApiCaseHistoryWalkTestParamsBlood_pressure {
  ago?: IApiCaseHistoryWalkTestParamsBlood_pressureAgo;
  after?: IApiCaseHistoryWalkTestParamsBlood_pressureAfter;
}

/**
 *
 */
export interface IApiCaseHistoryWalkTestParamsSaturation {
  ago?: number;
  after?: number;
}

/**
 *
 */
export interface IApiCaseHistoryWalkTestParamsScore {
  ago?: number;
  after?: number;
}

/**
 *
 */
export interface IApiCaseHistoryWalkTestParamsSuspend {
  remark?: string;
  is_suspend?: number;
}

/**
 *
 */
export interface IApiCaseHistoryWalkTestParamsTermination {
  remark?: string;
  is_termination?: number;
}

/**
 * 新增6分钟步行试验 - body 请求参数
 */
export interface IApiCaseHistoryWalkTestParams {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  test_id?: number;
  test_time: number;
  distance?: number;
  time?: IApiCaseHistoryWalkTestParamsTime;
  heart_rate?: IApiCaseHistoryWalkTestParamsHeart_rate;
  blood_pressure?: IApiCaseHistoryWalkTestParamsBlood_pressure;
  saturation?: IApiCaseHistoryWalkTestParamsSaturation;
  score?: IApiCaseHistoryWalkTestParamsScore;
  suspend: IApiCaseHistoryWalkTestParamsSuspend;
  termination: IApiCaseHistoryWalkTestParamsTermination;
  in_symptom?: number[];
  end_symptom?: number[];
}

/**
 * 新增6分钟步行试验
 */
export type IApiCaseHistoryWalkTest = undefined;

/**
 *
 */
export interface IApiCaseHistoryAortaParamsAll_cta {
  lm?: number;
  lad?: number;
  circumflex_stenosis?: number;
  rca?: number;
  stent?: number;
  stents?: number;
  debakey?: number;
  imh?: number;
  taa?: number;
  aaa?: number;
  pau?: number;
  ulp?: number;
  aortic?: number;
  other?: string;
  aortic_direct?: number;
  thrombosis?: number;
  dominance?: number;
  brachiocephalic?: number;
  left_carotid?: number;
  left_subclavian_artery?: number;
  celiac_trunk?: number;
  coronary_artery?: number;
  left_renal_artery?: number;
  right_renal_artery?: number;
  inferior_mesenteric_artery?: number;
  left_common?: number;
  left_external?: number;
  left_internal?: number;
  right_common?: number;
  right_external?: number;
  right_internal?: number;
  cyst_of_liver?: number;
  renal_cyst?: number;
  pleural_effusion?: number;
  hydropericardium?: number;
  mediastinum?: number;
  aortic_variation?: number;
  aortic_remark?: string;
}

/**
 * 新增主动脉CTA - body 请求参数
 */
export interface IApiCaseHistoryAortaParams {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  check_time: number;
  cta_type: number;
  all_cta?: IApiCaseHistoryAortaParamsAll_cta;
}

/**
 * 新增主动脉CTA
 */
export type IApiCaseHistoryAorta = undefined;

/**
 *
 */
export interface IApiCaseHistoryCoronaryParamsAll_cta {
  lm?: number;
  lad?: number;
  circumflex_stenosis?: number;
  rca?: number;
  stent?: number;
  stents?: number;
  debakey?: number;
  imh?: number;
  taa?: number;
  aaa?: number;
  pau?: number;
  ulp?: number;
  aortic?: number;
  other?: string;
  aortic_direct?: number;
  thrombosis?: number;
  dominance?: number;
  brachiocephalic?: number;
  left_carotid?: number;
  left_subclavian_artery?: number;
  celiac_trunk?: number;
  coronary_artery?: number;
  left_renal_artery?: number;
  right_renal_artery?: number;
  inferior_mesenteric_artery?: number;
  left_common?: number;
  left_external?: number;
  left_internal?: number;
  right_common?: number;
  right_external?: number;
  right_internal?: number;
  cyst_of_liver?: number;
  renal_cyst?: number;
  pleural_effusion?: number;
  hydropericardium?: number;
  mediastinum?: number;
  aortic_variation?: number;
  aortic_remark?: string;
}

/**
 * 新增冠脉CTA - body 请求参数
 */
export interface IApiCaseHistoryCoronaryParams {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  check_time: number;
  cta_type: number;
  all_cta?: IApiCaseHistoryCoronaryParamsAll_cta;
}

/**
 * 新增冠脉CTA
 */
export type IApiCaseHistoryCoronary = undefined;

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsTotal_analysis_duration {
  hour?: number;
  minute?: number;
  second?: number;
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsHr {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsSvr {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsVrLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsVrLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryEcg_dynamicParamsVrLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsVrVr {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsVr {
  label?: IApiCaseHistoryEcg_dynamicParamsVrLabel[];
  vr?: IApiCaseHistoryEcg_dynamicParamsVrVr[];
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsAf_afl_analysis {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsPacing_analysis {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsHrv {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsDiagnosisItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryEcg_dynamicParamsDiagnosis {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryEcg_dynamicParamsDiagnosisItems[];
}

/**
 * 新增动态心电图 - body 请求参数
 */
export interface IApiCaseHistoryEcg_dynamicParams {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  examination_date: number;
  analysis_start_time?: number;
  total_analysis_duration?: IApiCaseHistoryEcg_dynamicParamsTotal_analysis_duration;
  examination_accessory?: string[];
  hr?: IApiCaseHistoryEcg_dynamicParamsHr[];
  svr?: IApiCaseHistoryEcg_dynamicParamsSvr[];
  vr?: IApiCaseHistoryEcg_dynamicParamsVr;
  af_afl_analysis?: IApiCaseHistoryEcg_dynamicParamsAf_afl_analysis[];
  pacing_analysis?: IApiCaseHistoryEcg_dynamicParamsPacing_analysis[];
  hrv?: IApiCaseHistoryEcg_dynamicParamsHrv[];
  diagnosis?: IApiCaseHistoryEcg_dynamicParamsDiagnosis[];
}

/**
 * 新增动态心电图
 */
export type IApiCaseHistoryEcg_dynamic = undefined;

/**
 *
 */
export interface IApiCaseHistoryCeptParamsValue_list {
  field_id?: number;
  measured_value?: number;
  predictive_value?: number;
  pred?: number;
  peek?: number;
}

/**
 * 新增心肺运动 - body 请求参数
 */
export interface IApiCaseHistoryCeptParams {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  check_time: number;
  value_list?: IApiCaseHistoryCeptParamsValue_list[];
}

/**
 * 新增心肺运动
 */
export type IApiCaseHistoryCept = undefined;

/**
 *
 */
export interface IApiCaseHistoryEchocardiogramParamsM_type {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryEchocardiogramParamsSystolic_function {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryEchocardiogramParamsTissue_doppler_measurements {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryEchocardiogramParamsDiagnosisItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryEchocardiogramParamsDiagnosis {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryEchocardiogramParamsDiagnosisItems[];
}

/**
 * 新增心脏彩超 - body 请求参数
 */
export interface IApiCaseHistoryEchocardiogramParams {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  examination_date: number;
  m_type?: IApiCaseHistoryEchocardiogramParamsM_type[];
  systolic_function?: IApiCaseHistoryEchocardiogramParamsSystolic_function[];
  tissue_doppler_measurements?: IApiCaseHistoryEchocardiogramParamsTissue_doppler_measurements[];
  examination_accessory?: string[];
  diagnosis?: IApiCaseHistoryEchocardiogramParamsDiagnosis[];
}

/**
 * 新增心脏彩超
 */
export type IApiCaseHistoryEchocardiogram = undefined;

/**
 *
 */
export interface IApiCaseHistoryPacemakerParamsIndex_info {
  type?: string;
  code?: number;
  manufacturer?: string;
  insert_time?: number;
  at?: number;
  af?: number;
  time_in_at?: number;
  time_in_af?: number;
  electric_quantity?: number;
  atrial_event?: number;
  duration?: number;
  implantation_point?: string;
  vp?: string;
  ap?: string;
  av_interval?: number;
  heart_sensitivity?: string;
  perception_of_room_interval?: number;
}

/**
 * 新增起搏器程控 - body 请求参数
 */
export interface IApiCaseHistoryPacemakerParams {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  pacemaker_id?: number;
  check_time?: number;
  index_info?: IApiCaseHistoryPacemakerParamsIndex_info;
}

/**
 * 新增起搏器程控
 */
export type IApiCaseHistoryPacemaker = undefined;

/**
 * 查询全部报告列表 - body 请求参数
 */
export interface IApiCaseHistoryReportsParams {
  nonAiReport?: boolean;
}

/**
 *
 */
export interface IApiCaseHistoryReportsItem {
  indexTermId?: number;
  reportSort?: number;
  reportName?: string;
  enSession?: string;
}

/**
 * 查询全部报告列表
 */
export type IApiCaseHistoryReports = IApiCaseHistoryReportsItem[];

/**
 * 查询检查报告列表 - query 请求参数
 */
export interface IApiCaseHistoryDiagnose_reportQuery {
  source_id?: string;
  source_type?: string;
  entry_task?: string;
  sub_task_id?: string;
}

/**
 * 查询检查报告列表 - body 请求参数
 */
export interface IApiCaseHistoryDiagnose_reportParams {
  data: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicTotal_analysis_duration {
  hour?: number;
  minute?: number;
  second?: number;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicHr {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicSvr {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicVrLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicVrLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicVrLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicVrVr {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicVr {
  label?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicVrLabel[];
  vr?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicVrVr[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicAf_afl_analysis {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicPacing_analysis {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicHrv {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicDiagnosisItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamicDiagnosis {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicDiagnosisItems[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg_dynamic {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  examination_date: number;
  analysis_start_time?: number;
  total_analysis_duration?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicTotal_analysis_duration;
  examination_accessory?: string[];
  hr?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicHr[];
  svr?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicSvr[];
  vr?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicVr;
  af_afl_analysis?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicAf_afl_analysis[];
  pacing_analysis?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicPacing_analysis[];
  hrv?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicHrv[];
  diagnosis?: IApiCaseHistoryDiagnose_reportItemEcg_dynamicDiagnosis[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg12DiagnosisItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg12Diagnosis {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryDiagnose_reportItemEcg12DiagnosisItems[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg12Table {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEcg12 {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  examination_date: number;
  examination_accessory?: string[];
  diagnosis?: IApiCaseHistoryDiagnose_reportItemEcg12Diagnosis[];
  table?: IApiCaseHistoryDiagnose_reportItemEcg12Table[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEchocardiogramM_type {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEchocardiogramSystolic_function {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEchocardiogramTissue_doppler_measurements {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEchocardiogramDiagnosisItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEchocardiogramDiagnosis {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryDiagnose_reportItemEchocardiogramDiagnosisItems[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemEchocardiogram {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  examination_date: number;
  m_type?: IApiCaseHistoryDiagnose_reportItemEchocardiogramM_type[];
  systolic_function?: IApiCaseHistoryDiagnose_reportItemEchocardiogramSystolic_function[];
  tissue_doppler_measurements?: IApiCaseHistoryDiagnose_reportItemEchocardiogramTissue_doppler_measurements[];
  examination_accessory?: string[];
  diagnosis?: IApiCaseHistoryDiagnose_reportItemEchocardiogramDiagnosis[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemDiagnose_reportItems {
  key?: string;
  result?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItemDiagnose_report {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  report_id?: number;
  index_term_id?: number;
  sort?: number;
  name?: string;
  key?: string;
  items?: IApiCaseHistoryDiagnose_reportItemDiagnose_reportItems[];
}

/**
 *
 */
export interface IApiCaseHistoryDiagnose_reportItem {
  ecg_dynamic?: IApiCaseHistoryDiagnose_reportItemEcg_dynamic;
  ecg12?: IApiCaseHistoryDiagnose_reportItemEcg12;
  echocardiogram?: IApiCaseHistoryDiagnose_reportItemEchocardiogram;
  diagnose_report?: IApiCaseHistoryDiagnose_reportItemDiagnose_report;
}

/**
 * 查询检查报告列表
 */
export type IApiCaseHistoryDiagnose_report =
  IApiCaseHistoryDiagnose_reportItem[];

/**
 * 获取检查项目信息列表 - body 请求参数
 */
export interface IApiCaseHistoryMedicineExaminationKeyListParams {
  non_ai_report?: boolean;
}

/**
 *
 */
export interface IApiCaseHistoryMedicineExaminationKeyListItem {
  index_term_id?: number;
  key?: string;
  name?: string;
  report_type?: number;
  report_sort?: number;
}

/**
 * 获取检查项目信息列表
 */
export type IApiCaseHistoryMedicineExaminationKeyList =
  IApiCaseHistoryMedicineExaminationKeyListItem[];

/**
 *
 */
export interface IApiCaseHistoryBatchSaveExaminationParamsItem {
  patient_id: number;
  index_term_id: number;
  remark?: string;
  name?: string;
  report_sort?: number;
  source_id?: number;
  source_type: number;
  user_id?: number;
  user_type?: string;
  entry_task?: boolean;
  sub_task_id?: number;
  key?: string;
}

/**
 * 转录批量保存检查项目-空数据 - body 请求参数
 */
export type IApiCaseHistoryBatchSaveExaminationParams =
  IApiCaseHistoryBatchSaveExaminationParamsItem[];

/**
 * 转录批量保存检查项目-空数据
 */
export type IApiCaseHistoryBatchSaveExamination = undefined;

/**
 * 定向提交 - body 请求参数
 */
export interface IApiResearchDoubtSubmitParams {
  /** 创建人 */
  operatorId: number;
  /** 操作人类型 */
  operatorType: number;
  /** 创建名称 */
  operatorName: string;
  /** 项目id */
  projectId?: number;
  /** 患者id */
  patientId?: number;
  /** 质疑id */
  doubtId?: number;
  /** 保存数据 */
  data?: string;
  /** 数据类型，入组、入院记录、用药、指标等字典表Id或者类型 */
  type?: string;
}

/**
 * 定向提交
 */
export type IApiResearchDoubtSubmit = undefined;

/**
 * 拒绝质疑 - body 请求参数
 */
export interface IApiResearchDoubtRejectParams {
  /** 创建人 */
  operatorId: number;
  /** 操作人类型 */
  operatorType: number;
  /** 创建名称 */
  operatorName: string;
  /** 项目id */
  doubtId: number;
}

/**
 * 拒绝质疑
 */
export type IApiResearchDoubtReject = undefined;

/**
 * 接受质疑 - body 请求参数
 */
export interface IApiResearchDoubtReceiveParams {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  /** 项目id */
  doubtId: number;
}

/**
 * 接受质疑
 */
export type IApiResearchDoubtReceive = undefined;

/**
 * 撤回质疑 - body 请求参数
 */
export interface IApiResearchDoubtWithdrawParams {
  /** 创建人 */
  operatorId: number;
  /** 操作人类型 */
  operatorType: number;
  /** 创建名称 */
  operatorName: string;
  /** 项目id */
  doubtId: number;
}

/**
 * 撤回质疑
 */
export type IApiResearchDoubtWithdraw = undefined;

/**
 * 获取上一条或者下一条质疑 - body 请求参数
 */
export interface IApiResearchDoubtAnotherParams {
  /** 质疑id */
  doubtId: number;
  /** 患者id */
  patientId: number;
  /** 类型 */
  type: string;
  /** 质疑状态 */
  doubtStatuses?: string[];
}

/**
 * 质疑单元
 */
export interface IApiResearchDoubtAnotherDoubtUnites {
  /** 单元id */
  unitId?: number;
  /** 单元名称 */
  unitName?: string;
  /** 单元类型 */
  unitType?: string;
}

/**
 * 质疑回复
 */
export interface IApiResearchDoubtAnotherDoubtReplies {
  /** 创建人id
TODO 待对接 */
  createUserId?: number;
  /** 创建人名称 */
  createUserName?: string;
  /** 质疑状态 */
  doubtStatus?: string;
  /** 动作 */
  replyAction?: string;
  /** 创建时间 */
  createTime?: number;
  /** 描述 */
  remark?: string;
  /** 附件 */
  accessories?: string[];
}

/**
 * 单元缺少列表
 */
export interface IApiResearchDoubtAnotherFaultUnits {
  /** 单元id */
  unitId?: number;
  /** 单元名称 */
  unitName?: string;
  /** 单元类型 */
  unitType?: string;
  /** 是否缺失 */
  fault?: boolean;
}

/**
 * 获取上一条或者下一条质疑
 */
export interface IApiResearchDoubtAnother {
  /** 质疑id */
  doubtId?: number;
  /** 项目id */
  projectId?: number;
  /** 项目名称 */
  projectName?: string;
  /** 患者id */
  patientId?: number;
  /** 负责人id */
  directorId?: number;
  /** 负责人名称 */
  directorName?: string;
  /** 负责人类型 */
  directorType?: number;
  /** 质疑编码 */
  doubtCode?: string;
  /** 质疑类型---CHEME_INSPECT(方案审查)、DATA_INSPECT(数据审查)、AUDIT_INSPECT(审计审查)、OTHER(其他) */
  doubtType?: string;
  /** 优先级---HIGH(高)、MID(中)、LOW(低) */
  doubtPriority?: string;
  /** 质疑状态---INIT(初始化)、WAIT_RECEIVE(待接收)、INSPECTING(调查中)、WAIT_AUDIT(待审查)、CLOSED(已关闭)、COMPLETED(已归档) */
  doubtStatus?: string;
  /** 单元数量 */
  unitCount?: number;
  /** 记录单元数量 */
  recordUnitCount?: number;
  /** 解决时间 */
  solveTime?: number;
  /** 题目 */
  doubtTitle?: string;
  /** 内容 */
  content?: string;
  /** 创建人名称 */
  createName?: string;
  /** 创建时间 */
  createTime?: number;
  /** 记录id */
  projectRecordId?: number;
  /** 记录名称 */
  doubtRecordName?: string;
  /** 单元名称 */
  doubtUnitName?: string;
  /** 质疑单元 */
  doubtUnites?: IApiResearchDoubtAnotherDoubtUnites[];
  /** 质疑回复 */
  doubtReplies?: IApiResearchDoubtAnotherDoubtReplies[];
  /** 附件url */
  accessories?: string[];
  /** 单元缺少列表 */
  faultUnits?: IApiResearchDoubtAnotherFaultUnits[];
}

/**
 * 获取质疑信息 - body 请求参数
 */
export interface IApiResearchDoubtInfoParams {
  /** 项目id */
  doubtId: number;
}

/**
 * 质疑单元
 */
export interface IApiResearchDoubtInfoDoubtUnites {
  /** 单元id */
  unitId?: number;
  /** 单元名称 */
  unitName?: string;
  /** 单元类型 */
  unitType?: string;
}

/**
 * 质疑回复
 */
export interface IApiResearchDoubtInfoDoubtReplies {
  /** 创建人id
TODO 待对接 */
  createUserId?: number;
  /** 创建人名称 */
  createUserName?: string;
  /** 质疑状态 */
  doubtStatus?: string;
  /** 动作 */
  replyAction?: string;
  /** 创建时间 */
  createTime?: number;
  /** 描述 */
  remark?: string;
  /** 附件 */
  accessories?: string[];
}

/**
 * 单元缺少列表
 */
export interface IApiResearchDoubtInfoFaultUnits {
  /** 单元id */
  unitId?: number;
  /** 单元名称 */
  unitName?: string;
  /** 单元类型 */
  unitType?: string;
  /** 是否缺失 */
  fault?: boolean;
}

/**
 * 获取质疑信息
 */
export interface IApiResearchDoubtInfo {
  /** 质疑id */
  doubtId?: number;
  /** 项目id */
  projectId?: number;
  /** 项目名称 */
  projectName?: string;
  /** 患者id */
  patientId?: number;
  /** 负责人id */
  directorId?: number;
  /** 负责人名称 */
  directorName?: string;
  /** 负责人类型 */
  directorType?: number;
  /** 质疑编码 */
  doubtCode?: string;
  /** 质疑类型---CHEME_INSPECT(方案审查)、DATA_INSPECT(数据审查)、AUDIT_INSPECT(审计审查)、OTHER(其他) */
  doubtType?: string;
  /** 优先级---HIGH(高)、MID(中)、LOW(低) */
  doubtPriority?: string;
  /** 质疑状态---INIT(初始化)、WAIT_RECEIVE(待接收)、INSPECTING(调查中)、WAIT_AUDIT(待审查)、CLOSED(已关闭)、COMPLETED(已归档) */
  doubtStatus?: string;
  /** 单元数量 */
  unitCount?: number;
  /** 记录单元数量 */
  recordUnitCount?: number;
  /** 解决时间 */
  solveTime?: number;
  /** 题目 */
  doubtTitle?: string;
  /** 内容 */
  content?: string;
  /** 创建人名称 */
  createName?: string;
  /** 创建时间 */
  createTime?: number;
  /** 记录id */
  projectRecordId?: number;
  /** 记录名称 */
  doubtRecordName?: string;
  /** 单元名称 */
  doubtUnitName?: string;
  /** 质疑单元 */
  doubtUnites?: IApiResearchDoubtInfoDoubtUnites[];
  /** 质疑回复 */
  doubtReplies?: IApiResearchDoubtInfoDoubtReplies[];
  /** 附件url */
  accessories?: string[];
  /** 单元缺少列表 */
  faultUnits?: IApiResearchDoubtInfoFaultUnits[];
}

/**
 * 获取质疑定位信息 - body 请求参数
 */
export interface IApiResearchDoubtLocationParams {
  /** 项目id */
  doubtId: number;
}

/**
 *
 */
export interface IApiResearchDoubtLocationItem {
  /** 单元类型 */
  unitType?: string;
  /** 患者id */
  patientId?: number;
  /** 数据来源id---定位到医患平台的病例id */
  sourceId?: number;
  /** 数据来源类型---定位到医患平台的来源类型 */
  sourceType?: number;
  /** 医患key */
  healthCenterKey?: string;
  /** 发生时间 */
  occurredTime?: number;
  /** 质疑id */
  doubtId?: number;
}

/**
 * 获取质疑定位信息
 */
export type IApiResearchDoubtLocation = IApiResearchDoubtLocationItem[];

/**
 * 获取质疑快照 - body 请求参数
 */
export interface IApiResearchDoubtSnapshotCompareParams {
  /** 质疑回复id */
  doubtReplyId: number;
}

/**
 * 获取质疑快照
 */
export type IApiResearchDoubtSnapshotCompare = string[];

/**
 * 质疑情况说明提交 - body 请求参数
 */
export interface IApiResearchDoubtReplyParams {
  /** 创建人 */
  operatorId: number;
  /** 操作人类型 */
  operatorType: number;
  /** 创建名称 */
  operatorName: string;
  /** 质疑id */
  doubtId: number;
  /** 质疑编码 */
  remark?: string;
  /** 附件 */
  accessories?: string[];
}

/**
 * 质疑情况说明提交
 */
export type IApiResearchDoubtReply = undefined;

/**
 *
 */
export interface IApiOrprojectQuerySimpleItem {
  /** 项目id */
  projectId?: number;
  /** 项目名称 */
  projectName?: string;
}

/**
 * 查询用户的科研项目信息
 */
export type IApiOrprojectQuerySimple = IApiOrprojectQuerySimpleItem[];

/**
 * 容联呼叫中心回调 - body 请求参数
 */
export interface IApiAddressCallDialoutMoorParams {
  /** 坐席工号 */
  fromExten?: string;
  /** 被叫号码 */
  exten?: string;
  /** 被叫类型
Local/sip/gateway */
  extenType?: string;
  /** 唯一字符串
请求id */
  actionID?: string;
  /** 自定义字符串 */
  dialoutStrVar?: string;
  /** 指定外呼外显号码 */
  outShow?: string;
}

/**
 * 容联呼叫中心回调
 */
export interface IApiAddressCallDialoutMoor {
  /** 响应 */
  response?: string;
  /** actionID */
  actionID?: string;
  /** 是否成功 */
  succeed?: boolean;
  /** 消息 */
  message?: string;
}

/**
 * 容联呼叫中心离线接听签入 - body 请求参数
 */
export interface IApiAddressCallSignInMoorParams {
  /** 坐席号码 */
  cno?: string;
}

/**
 * 容联呼叫中心离线接听签入
 */
export interface IApiAddressCallSignInMoor {
  /** 响应 */
  response?: string;
  /** actionID */
  actionID?: string;
  /** 是否成功 */
  succeed?: boolean;
  /** 消息 */
  message?: string;
}

/**
 * 查询线路登陆信息 - body 请求参数
 */
export interface IApiAddressCallLoginInfoQueryParams {
  accountId?: string;
  channel?: string;
}

/**
 * 查询线路登陆信息
 */
export interface IApiAddressCallLoginInfoQuery {
  cno?: string;
  loginPassword?: string;
}

/**
 * 查询线路登陆账号 - body 请求参数
 */
export interface IApiAddressCallSeatQueryParams {
  accountId?: number;
  employeeType?: string;
  channel?: string;
}

/**
 * 查询线路登陆账号
 */
export interface IApiAddressCallSeatQuery {
  cno?: string;
  loginPassword?: string;
  /** 账号id
该账号id是容联侧id */
  accountId?: string;
}

/**
 * 分页查询 - body 请求参数
 */
export interface IApiQualityRecordQueryPageParams {
  pageNumber?: number;
  pageSize?: number;
  /** 用户ID = managerId 质控人 */
  userId: number;
  /** 用户类型 */
  userType: string;
  /** 全部质控 */
  all?: boolean;
  /** 被质控人id */
  qualityId?: string;
  /** 被质控人姓名 */
  qualityName?: string;
  /** 质控名称 */
  description?: string;
  /** 质控类型：具体待办/咨询接诊/未接回拨 字典表type */
  dicType?: number;
  /** 质控点id，由description、dicType 计算得来 */
  qualityPointIds?: number[];
  /** 质控方向：1完成时效/2完成质量 */
  type?: number;
  /** 质控状态 */
  status?: number;
  /** 质控生成开始日期 */
  dayStart?: number;
  /** 质控生成结束日期 */
  dayEnd?: number;
}

/**
 *
 */
export interface IApiQualityRecordQueryPageContents {
  /** id */
  id?: number;
  /** 质控点id */
  qualityPointId?: number;
  /** 患者id */
  patientId?: number;
  /** 工作室id */
  groupId?: number;
  /** 被质控人id */
  qualityId?: number;
  /** 被质控人类型: 0待办责任人/1医生/2健康管理师/3运动康复师 */
  qualityRole?: number;
  /** 被质控人名称 */
  qualityName?: string;
  /** 质控管理人id */
  managerId?: number;
  /** 质控管理人类型: 0待办责任人/1医生/2健康管理师/3运动康复师 */
  managerRole?: number;
  /** 质控管理人名称 */
  managerName?: string;
  /** 整改要求 */
  content?: string;
  /** 质控状态: 1未处理/2已忽略/3整改中/4已整改/5未整改/6已超期 */
  status?: number;
  /** 创建时间 */
  createTime?: number;
  /** 更新时间 */
  modifyTime?: number;
  /** 是否删除 */
  deleted?: boolean;
  /** 质控描述/质控点名称 */
  description?: string;
  /** 工作室名称 */
  groupName?: string;
  /** 整改描述 */
  revampContent?: string;
  /** 质控超时描述 */
  timeoutContent?: string;
  /** 质控检查点 */
  checkPoint?: string[];
}

/**
 * 分页查询
 */
export interface IApiQualityRecordQueryPage {
  total?: number;
  contents?: IApiQualityRecordQueryPageContents[];
}

/**
 * 创建质量质控记录 - body 请求参数
 */
export interface IApiQualityRecordCreateParams {
  /** 质控来源id */
  sourceId?: number;
  /** 患者id */
  patientId?: number;
  /** 负责人id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
  /** 工作室id */
  groupId?: number;
  /** 质控点来源：2待办事项/5咨询接诊/6未接回拨 字典表code */
  sourceCode?: number;
  /** 质控点业务类型 */
  dicType?: number;
}

/**
 * 创建质量质控记录
 */
export type IApiQualityRecordCreate = boolean;

/**
 * 忽略/全部忽略 - body 请求参数
 */
export interface IApiQualityRecordIgnoreParams {
  /** 用户ID = managerId 质控人 */
  userId: number;
  /** 用户类型 */
  userType: string;
  /** 全部质控
all = true 则忽略下面参数，处理该账号下的全部未处理质控记录 */
  all?: boolean;
  /** 质控记录id */
  qualityRecordId?: number;
  /** 质控点id */
  qualityPointId?: number;
  /** 患者id */
  patientId?: number;
  /** 质控整改要求 */
  revampDemand?: string;
}

/**
 * 忽略/全部忽略
 */
export type IApiQualityRecordIgnore = boolean;

/**
 * 撤回质控整改 - body 请求参数
 */
export interface IApiQualityRecordRetractParams {
  /** 用户ID = managerId 质控人 */
  userId: number;
  /** 用户类型 */
  userType: string;
  /** 全部质控
all = true 则忽略下面参数，处理该账号下的全部未处理质控记录 */
  all?: boolean;
  /** 质控记录id */
  qualityRecordId?: number;
  /** 质控点id */
  qualityPointId?: number;
  /** 患者id */
  patientId?: number;
  /** 质控整改要求 */
  revampDemand?: string;
}

/**
 * 撤回质控整改
 */
export type IApiQualityRecordRetract = boolean;

/**
 * 整改/全部整改 - body 请求参数
 */
export interface IApiQualityRecordRevampParams {
  /** 用户ID = managerId 质控人 */
  userId: number;
  /** 用户类型 */
  userType: string;
  /** 全部质控
all = true 则忽略下面参数，处理该账号下的全部未处理质控记录 */
  all?: boolean;
  /** 质控记录id */
  qualityRecordId?: number;
  /** 质控点id */
  qualityPointId?: number;
  /** 患者id */
  patientId?: number;
  /** 质控整改要求 */
  revampDemand?: string;
}

/**
 * 整改/全部整改
 */
export type IApiQualityRecordRevamp = boolean;

/**
 * 查询当前用户未处理质控数量 - body 请求参数
 */
export interface IApiQualityRecordQueryUnprocessedCountParams {
  pageNumber?: number;
  pageSize?: number;
  /** 用户ID = managerId 质控人 */
  userId: number;
  /** 用户类型 */
  userType: string;
  /** 全部质控 */
  all?: boolean;
  /** 被质控人id */
  qualityId?: string;
  /** 被质控人姓名 */
  qualityName?: string;
  /** 质控名称 */
  description?: string;
  /** 质控类型：具体待办/咨询接诊/未接回拨 字典表type */
  dicType?: number;
  /** 质控点id，由description、dicType 计算得来 */
  qualityPointIds?: number[];
  /** 质控方向：1完成时效/2完成质量 */
  type?: number;
  /** 质控状态 */
  status?: number;
  /** 质控生成开始日期 */
  dayStart?: number;
  /** 质控生成结束日期 */
  dayEnd?: number;
}

/**
 * 查询当前用户未处理质控数量
 */
export type IApiQualityRecordQueryUnprocessedCount = number;

/**
 * 查询待办的质量检查点 - body 请求参数
 */
export interface IApiQualityRecordCheckPointParams {
  /** 用户ID = managerId 质控人 */
  userId: number;
  /** 用户类型 */
  userType: string;
  /** 患者id */
  patientId?: number;
  /** 待办id */
  backlogId?: number;
  /** 待办类型 */
  dicType?: number;
  /** 质控点来源：2待办事项/5咨询接诊/6未接回拨 字典表code */
  source?: number;
}

/**
 *
 */
export interface IApiQualityRecordCheckPointItem {
  /** 质量检查点 code */
  code?: number;
  /** 质量检查点简介 */
  desc?: string;
  /** 是否完成 */
  complete?: boolean;
}

/**
 * 查询待办的质量检查点
 */
export type IApiQualityRecordCheckPoint = IApiQualityRecordCheckPointItem[];

/**
 * 查询质控点/被质控人 - body 请求参数
 */
export interface IApiQualityRecordPointPersonParams {
  data: string;
}

/**
 * 全部质控点
 */
export interface IApiQualityRecordPointPersonPoints {
  id?: number;
  description?: string;
  synopsis?: string;
}

/**
 * 全部被质控人
 */
export interface IApiQualityRecordPointPersonPersons {
  qualityId?: number;
  qualityRole?: number;
  qualityName?: string;
}

/**
 * 查询质控点/被质控人
 */
export interface IApiQualityRecordPointPerson {
  /** 全部质控点 */
  points?: IApiQualityRecordPointPersonPoints[];
  /** 全部被质控人 */
  persons?: IApiQualityRecordPointPersonPersons[];
}

/**
 * 查询外显号码 - body 请求参数
 */
export interface IApiCallCenterCallShowListParams {
  phone: string;
  source: string;
  patientId?: number;
}

/**
 *
 */
export interface IApiCallCenterCallShowListItem {
  phone?: string;
  province?: string;
  city?: string;
  selected?: boolean;
  tags?: string[];
  userName?: string;
  callTime?: number;
  callStatus?: string;
  sort?: number;
}

/**
 * 查询外显号码
 */
export type IApiCallCenterCallShowList = IApiCallCenterCallShowListItem[];

/**
 * 查询患者亲情账号列表 - body 请求参数
 */
export interface IApiCallCenterPatientUseropenTagListParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiCallCenterPatientUseropenTagListItem {
  /** 手机号 */
  phone?: string;
  /** 姓名 */
  name?: string;
  /** 关系 */
  relation?: string;
  /** 标签 */
  tags?: string[];
}

/**
 * 查询患者亲情账号列表
 */
export type IApiCallCenterPatientUseropenTagList =
  IApiCallCenterPatientUseropenTagListItem[];

/**
 * 查询患者通讯录列表 - body 请求参数
 */
export interface IApiCallCenterPatientAddressbookTagListParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiCallCenterPatientAddressbookTagListItem {
  /** 手机号 */
  phone?: string;
  /** 姓名 */
  name?: string;
  /** 关系 */
  relation?: string;
  /** id */
  addressBookId?: number;
  /** 标签 */
  tags?: string[];
}

/**
 * 查询患者通讯录列表
 */
export type IApiCallCenterPatientAddressbookTagList =
  IApiCallCenterPatientAddressbookTagListItem[];

/**
 * 查询被叫号码归属地 - body 请求参数
 */
export interface IApiCallCenterAreaGetParams {
  phone: string;
}

/**
 * 查询被叫号码归属地
 */
export interface IApiCallCenterAreaGet {
  province?: string;
  city?: string;
  source?: string;
}

/**
 * 未接来电数量统计
 */
export type IApiCallCenterMissedCallTotal = number;

/**
 * 通话记录已读标记 - body 请求参数
 */
export interface IApiCallCenterMissedCallReadParams {
  uniqueId: string;
}

/**
 * 通话记录已读标记
 */
export type IApiCallCenterMissedCallRead = boolean;

/**
 * 新增实质性事务数据 - body 请求参数
 */
export interface IApiSubmitHandleRecordSubmitParams {
  /** 处理用户id */
  userId: number;
  /** 处理用户类型(5：医生，3：健管师，9：康复师) */
  userType: number;
  /** 患者id */
  patientId: number;
  /** 记录时间   格式为 20240304 */
  recordTime: number;
  /** 是否删除 */
  deleted?: boolean;
}

/**
 * 新增实质性事务数据
 */
export type IApiSubmitHandleRecordSubmit = number;

/**
 * 新增搜索排序数据 - body 请求参数
 */
export interface IApiSubmitSortEventSubmitParams {
  /** 患者id */
  patientId: number;
  /** 事件类型事件类型（1：订单新购，2：订单续费，3：搜索置顶，4：聊天置顶） */
  eventType: number;
}

/**
 * 新增搜索排序数据
 */
export type IApiSubmitSortEventSubmit = number;

/**
 * 获取吸烟、饮酒、bmi、运动记录列表 - body 请求参数
 */
export interface IApiFollowStatisticsNumberQuestionParams {
  /** 患者id */
  patientId: number;
  /** 类型 1:吸烟，2:饮酒，3:bmi，4:运动 */
  type: number;
}

/**
 * 随访列表
 */
export interface IApiFollowStatisticsNumberQuestionFollowList {
  /** 随访日期 */
  date?: string;
  /** 数量 */
  number?: number;
}

/**
 * 获取吸烟、饮酒、bmi、运动记录列表
 */
export interface IApiFollowStatisticsNumberQuestion {
  /** 随访列表 */
  followList?: IApiFollowStatisticsNumberQuestionFollowList[];
}

/**
 * 获取饮食记录列表 - body 请求参数
 */
export interface IApiFollowStatisticsDietParams {
  /** 患者id */
  patientId: number;
}

/**
 * 随访列表
 */
export interface IApiFollowStatisticsDietFollowList {
  /** 随访日期 */
  date?: number;
  /** 饮食选择项目 preferVegetable：蔬菜，preferFruit：水果，preferFatty：肥肉、油炸食品、动物内脏等，preferSweet：较甜，preferSalt：较咸 */
  dietList?: string[];
}

/**
 * 获取饮食记录列表
 */
export interface IApiFollowStatisticsDiet {
  /** 随访列表 */
  followList?: IApiFollowStatisticsDietFollowList[];
}

/**
 * 系统指标数据 - body 请求参数
 */
export interface IApiStructuredReportingAllIndexParams {
  /** 患者id */
  patientId: number;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
}

/**
 *
 */
export interface IApiStructuredReportingAllIndexItem {
  /** 指标项id */
  indexTermId?: number;
  /** 检查项 */
  checkType?: number;
  /** 名称 */
  name?: string;
  /** 别名 */
  alias?: string;
  /** 单位 */
  unit?: string;
  /** 检查小项 */
  indexType?: number;
  /** 是否可选状态   0可选 1不可选 */
  chooseStatus?: number;
}

/**
 * 系统指标数据
 */
export type IApiStructuredReportingAllIndex =
  IApiStructuredReportingAllIndexItem[];

/**
 * 结构化报告推送 - body 请求参数
 */
export interface IApiStructuredReportingPushPatientWxParams {
  /** 患者id */
  patientId?: number;
  /** 报告名称 */
  reportName?: string;
  /** 报告生成时间 */
  reportGenerateTime?: number;
}

/**
 * 结构化报告推送
 */
export type IApiStructuredReportingPushPatientWx = boolean;

/**
 * 患者血压、心率阈值查询 - body 请求参数
 */
export interface IApiStructuredReportingPatientThresholdParams {
  /** 患者id */
  patientId: number;
}

/**
 * 血压
 */
export interface IApiStructuredReportingPatientThresholdBlood {
  /** 收缩压高 */
  systolicHigh?: number;
  /** 收缩压低 */
  systolicLow?: number;
  /** 舒张压高 */
  diastolicHigh?: number;
  /** 舒张压低 */
  diastolicLow?: number;
}

/**
 * 心率
 */
export interface IApiStructuredReportingPatientThresholdHeart {
  /** 心率高 */
  heartHigh?: number;
  /** 心率低 */
  heartLow?: number;
}

/**
 * 患者血压、心率阈值查询
 */
export interface IApiStructuredReportingPatientThreshold {
  /** 血压 */
  blood?: IApiStructuredReportingPatientThresholdBlood;
  /** 心率 */
  heart?: IApiStructuredReportingPatientThresholdHeart;
}

/**
 * 结构化报告列表查询 - body 请求参数
 */
export interface IApiStructuredReportingListParams {
  /** 患者id */
  patientId: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 主题模糊匹配 */
  theme?: string;
  /** 当前页 */
  pageNumber: number;
  /** 页大小 */
  pageSize: number;
}

/**
 *
 */
export interface IApiStructuredReportingListContents {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  /** 结构化报告id */
  id?: number;
  /** 报告名称 */
  reportName?: string;
  /** 报告生成时间 */
  generatedTime?: string;
}

/**
 * 结构化报告列表查询
 */
export interface IApiStructuredReportingList {
  total?: number;
  contents?: IApiStructuredReportingListContents[];
}

/**
 * 用户拥有群聊查询 - body 请求参数
 */
export interface IApiStructuredReportingGroupChatParams {
  /** 患者id */
  patientId: number;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: string;
}

/**
 * 群聊用户列表
 */
export interface IApiStructuredReportingGroupChatTeamListUserList {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  /** 角色名称 */
  roleName?: string;
}

/**
 * 群聊列表
 */
export interface IApiStructuredReportingGroupChatTeamList {
  /** 群聊号 */
  teamNumber?: string;
  /** 群聊类型 1为医生-专家群聊；2为医生-患者-健康管理师-运动康复师群聊；3为患者-专家群聊；4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 群聊名称 */
  teamName?: string;
  /** 群聊用户列表 */
  userList?: IApiStructuredReportingGroupChatTeamListUserList[];
}

/**
 * 用户拥有群聊查询
 */
export interface IApiStructuredReportingGroupChat {
  /** 群聊列表 */
  teamList?: IApiStructuredReportingGroupChatTeamList[];
}

/**
 * 结构化报告患者信息 - body 请求参数
 */
export interface IApiStructuredReportingPatientInfoParams {
  /** 患者id */
  patientId: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
}

/**
 * 结构化报告患者信息
 */
export interface IApiStructuredReportingPatientInfo {
  /** 患者姓名 */
  patientName?: string;
  /** 性别 */
  gender?: number;
  /** 年龄 */
  age?: number;
  /** 手术时间 */
  surgeryDate?: number;
  /** 手术结论 */
  surgeryCon?: string;
  /** 诊断 */
  diagnosis?: string[];
}

/**
 * 结构化报告详情查询 - body 请求参数
 */
export interface IApiStructuredReportingDetailParams {
  /** 结构化报告id */
  structuredId: number;
}

/**
 * 模块信息
 */
export interface IApiStructuredReportingDetailOperation {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 心电图
 */
export interface IApiStructuredReportingDetailEcgDataValue {
  /** 报告项id */
  indexTermId?: number;
  /** 报告名称 */
  reportName?: string;
  /** 可选状态 0 可选 1不可选 */
  chooseStatus?: number;
}

/**
 * 心电图信息
 */
export interface IApiStructuredReportingDetailEcgData {
  /** 是否勾选 */
  checkType?: boolean;
  /** 心电图 */
  value?: IApiStructuredReportingDetailEcgDataValue[];
}

/**
 *
 */
export interface IApiStructuredReportingDetailPatientIndexValue {
  /** 指标项id */
  indexTermId?: number;
  /** 检查项 */
  checkType?: number;
  /** 检查小项 */
  indexType?: number;
  /** 指标名称 */
  indexName?: string;
  /** 单位 */
  unit?: string;
}

/**
 * 指标信息
 */
export interface IApiStructuredReportingDetailPatientIndex {
  /** 检查是否选中 */
  checked?: boolean;
  value?: IApiStructuredReportingDetailPatientIndexValue[];
}

/**
 * 规格
 */
export interface IApiStructuredReportingDetailPatientDrugDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 单次用量
 */
export interface IApiStructuredReportingDetailPatientDrugDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
}

/**
 * 用药信息
 */
export interface IApiStructuredReportingDetailPatientDrug {
  /** 药品id */
  drugInfoId?: number;
  /** 药品名称 */
  drugName?: string;
  /** 用法(频率) */
  drugUsage?: string;
  /** 规格 */
  drugSpec?: IApiStructuredReportingDetailPatientDrugDrugSpec;
  /** 用药方式 */
  drugMode?: string;
  /** 单次用量 */
  drugAmount?: IApiStructuredReportingDetailPatientDrugDrugAmount;
  /** 服药时间1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
  /** 通用名称 */
  commonName?: string;
}

/**
 * 复查结论
 */
export interface IApiStructuredReportingDetailReviewConclusions {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 医生建议
 */
export interface IApiStructuredReportingDetailDoctorAdvice {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 出院诊断
 */
export interface IApiStructuredReportingDetailDiagnose {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 问题
 */
export interface IApiStructuredReportingDetailProblem {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 结构化报告详情查询
 */
export interface IApiStructuredReportingDetail {
  /** 患者id */
  patientId?: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 模块信息 */
  operation?: IApiStructuredReportingDetailOperation;
  /** 心电图信息 */
  ecgData?: IApiStructuredReportingDetailEcgData;
  /** 指标信息 */
  patientIndex?: IApiStructuredReportingDetailPatientIndex;
  /** 用药信息 */
  patientDrug?: IApiStructuredReportingDetailPatientDrug[];
  /** 附件 */
  accessory?: string[];
  /** 复查结论 */
  reviewConclusions?: IApiStructuredReportingDetailReviewConclusions;
  /** 医生建议 */
  doctorAdvice?: IApiStructuredReportingDetailDoctorAdvice;
  /** 出院诊断 */
  diagnose?: IApiStructuredReportingDetailDiagnose;
  /** 问题 */
  problem?: IApiStructuredReportingDetailProblem;
  /** 主题 */
  theme?: string;
  /** 是否勾选用药 */
  drugCheck?: boolean;
  /** 是否勾选图片档案 */
  imageCheck?: boolean;
}

/**
 * 具体指标数据 - body 请求参数
 */
export interface IApiStructuredReportingIndexDetailParams {
  /** 患者id */
  patientId: number;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
  /** 报告项id */
  indexTermId: number;
  /** 检查项 */
  checkType?: number;
  /** 指标项 */
  indexType?: number;
}

/**
 *
 */
export interface IApiStructuredReportingIndexDetailItem {
  /** 检查时间 */
  checkTime?: number;
  /** 舒张压 */
  systolicHigh?: number;
  /** 收缩压 */
  diastolicHigh?: number;
  /** 其他类型指标数据 */
  otherData?: number;
}

/**
 * 具体指标数据
 */
export type IApiStructuredReportingIndexDetail =
  IApiStructuredReportingIndexDetailItem[];

/**
 * 心电图 - body 请求参数
 */
export interface IApiStructuredReportingEcgDataParams {
  /** 患者id */
  patientId: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
}

/**
 *
 */
export interface IApiStructuredReportingEcgDataItem {
  /** 报告项id */
  indexTermId?: number;
  /** 报告名称 */
  reportName?: string;
  /** 可选状态 0 可选 1不可选 */
  chooseStatus?: number;
}

/**
 * 心电图
 */
export type IApiStructuredReportingEcgData =
  IApiStructuredReportingEcgDataItem[];

/**
 * 心电图结论及时间 - body 请求参数
 */
export interface IApiStructuredReportingEcgDetailParams {
  /** 患者id */
  patientId: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 报告项id */
  indexTermId?: number;
}

/**
 * 结论
 */
export interface IApiStructuredReportingEcgDetailItemConclusions {
  /** 结论id */
  conclusionId?: number;
  /** 结论类型 */
  type?: number;
  /** 选择类型 */
  chooseType?: number;
  /** 上级id */
  pid?: number;
  /** 备注 */
  remark?: string;
  /** 结论名称 */
  name?: string;
}

/**
 *
 */
export interface IApiStructuredReportingEcgDetailItem {
  /** 检查时间 */
  checkTime?: number;
  /** 结论 */
  conclusions?: IApiStructuredReportingEcgDetailItemConclusions[];
}

/**
 * 心电图结论及时间
 */
export type IApiStructuredReportingEcgDetail =
  IApiStructuredReportingEcgDetailItem[];

/**
 * 主题
 */
export interface IApiStructuredReportingSaveStructuredParamsTheme {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 手术
 */
export interface IApiStructuredReportingSaveStructuredParamsOperation {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 *
 */
export interface IApiStructuredReportingSaveStructuredParamsPatientIndexValue {
  /** 是否勾选 */
  checked?: boolean;
  /** 指标项id */
  indexTermId?: number;
  /** 检查项 */
  checkType?: number;
  /** 检查小项 */
  indexType?: number;
  /** 指标名称 */
  indexName?: string;
  /** 单位 */
  unit?: string;
}

/**
 * 指标信息
 */
export interface IApiStructuredReportingSaveStructuredParamsPatientIndex {
  /** 检查是否选中 */
  checked?: boolean;
  value?: IApiStructuredReportingSaveStructuredParamsPatientIndexValue[];
}

/**
 * 心电图
 */
export interface IApiStructuredReportingSaveStructuredParamsEcgDataValue {
  /** 是否勾选 */
  checked?: boolean;
  /** 报告项id */
  indexTermId?: number;
  /** 报告名称 */
  reportName?: string;
}

/**
 * 心电图
 */
export interface IApiStructuredReportingSaveStructuredParamsEcgData {
  /** 是否勾选 */
  checkType?: boolean;
  /** 心电图 */
  value?: IApiStructuredReportingSaveStructuredParamsEcgDataValue[];
}

/**
 * 规格
 */
export interface IApiStructuredReportingSaveStructuredParamsPatientDrugDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 单次用量
 */
export interface IApiStructuredReportingSaveStructuredParamsPatientDrugDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
}

/**
 * 用药信息
 */
export interface IApiStructuredReportingSaveStructuredParamsPatientDrug {
  /** 药品id */
  drugInfoId?: number;
  /** 药品名称 */
  drugName?: string;
  /** 用法(频率) */
  drugUsage?: string;
  /** 规格 */
  drugSpec?: IApiStructuredReportingSaveStructuredParamsPatientDrugDrugSpec;
  /** 用药方式 */
  drugMode?: string;
  /** 单次用量 */
  drugAmount?: IApiStructuredReportingSaveStructuredParamsPatientDrugDrugAmount;
  /** 服药时间1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
  /** 通用名称 */
  commonName?: string;
}

/**
 * 复查结论
 */
export interface IApiStructuredReportingSaveStructuredParamsReviewCon {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 建议
 */
export interface IApiStructuredReportingSaveStructuredParamsSuggest {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 概述
 */
export interface IApiStructuredReportingSaveStructuredParamsDiagnose {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 需确认问题
 */
export interface IApiStructuredReportingSaveStructuredParamsIssue {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 保存结构化报告 - body 请求参数
 */
export interface IApiStructuredReportingSaveStructuredParams {
  /** 结构化报告id */
  id?: number;
  /** 患者id */
  patientId?: number;
  /** 主题 */
  theme?: IApiStructuredReportingSaveStructuredParamsTheme;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 手术 */
  operation?: IApiStructuredReportingSaveStructuredParamsOperation;
  /** 指标信息 */
  patientIndex?: IApiStructuredReportingSaveStructuredParamsPatientIndex;
  /** 心电图 */
  ecgData?: IApiStructuredReportingSaveStructuredParamsEcgData;
  /** 用药信息 */
  patientDrug?: IApiStructuredReportingSaveStructuredParamsPatientDrug[];
  /** 复查结论 */
  reviewCon?: IApiStructuredReportingSaveStructuredParamsReviewCon;
  /** 建议 */
  suggest?: IApiStructuredReportingSaveStructuredParamsSuggest;
  /** 概述 */
  diagnose?: IApiStructuredReportingSaveStructuredParamsDiagnose;
  /** 需确认问题 */
  issue?: IApiStructuredReportingSaveStructuredParamsIssue;
  /** 附件 */
  accessory?: string[];
  /** 用药是否勾选 */
  drugCheck?: boolean;
  /** 图片档案是否勾选 */
  imageCheck?: boolean;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
}

/**
 * 模块信息
 */
export interface IApiStructuredReportingSaveStructuredOperation {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 心电图
 */
export interface IApiStructuredReportingSaveStructuredEcgDataValue {
  /** 报告项id */
  indexTermId?: number;
  /** 报告名称 */
  reportName?: string;
  /** 可选状态 0 可选 1不可选 */
  chooseStatus?: number;
}

/**
 * 心电图信息
 */
export interface IApiStructuredReportingSaveStructuredEcgData {
  /** 是否勾选 */
  checkType?: boolean;
  /** 心电图 */
  value?: IApiStructuredReportingSaveStructuredEcgDataValue[];
}

/**
 *
 */
export interface IApiStructuredReportingSaveStructuredPatientIndexValue {
  /** 指标项id */
  indexTermId?: number;
  /** 检查项 */
  checkType?: number;
  /** 检查小项 */
  indexType?: number;
  /** 指标名称 */
  indexName?: string;
  /** 单位 */
  unit?: string;
}

/**
 * 指标信息
 */
export interface IApiStructuredReportingSaveStructuredPatientIndex {
  /** 检查是否选中 */
  checked?: boolean;
  value?: IApiStructuredReportingSaveStructuredPatientIndexValue[];
}

/**
 * 规格
 */
export interface IApiStructuredReportingSaveStructuredPatientDrugDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 单次用量
 */
export interface IApiStructuredReportingSaveStructuredPatientDrugDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
}

/**
 * 用药信息
 */
export interface IApiStructuredReportingSaveStructuredPatientDrug {
  /** 药品id */
  drugInfoId?: number;
  /** 药品名称 */
  drugName?: string;
  /** 用法(频率) */
  drugUsage?: string;
  /** 规格 */
  drugSpec?: IApiStructuredReportingSaveStructuredPatientDrugDrugSpec;
  /** 用药方式 */
  drugMode?: string;
  /** 单次用量 */
  drugAmount?: IApiStructuredReportingSaveStructuredPatientDrugDrugAmount;
  /** 服药时间1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
  /** 通用名称 */
  commonName?: string;
}

/**
 * 复查结论
 */
export interface IApiStructuredReportingSaveStructuredReviewConclusions {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 医生建议
 */
export interface IApiStructuredReportingSaveStructuredDoctorAdvice {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 出院诊断
 */
export interface IApiStructuredReportingSaveStructuredDiagnose {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 问题
 */
export interface IApiStructuredReportingSaveStructuredProblem {
  /** 是否勾选 */
  checked?: boolean;
  /** 具体值 */
  value?: string;
}

/**
 * 保存结构化报告
 */
export interface IApiStructuredReportingSaveStructured {
  /** 患者id */
  patientId?: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 模块信息 */
  operation?: IApiStructuredReportingSaveStructuredOperation;
  /** 心电图信息 */
  ecgData?: IApiStructuredReportingSaveStructuredEcgData;
  /** 指标信息 */
  patientIndex?: IApiStructuredReportingSaveStructuredPatientIndex;
  /** 用药信息 */
  patientDrug?: IApiStructuredReportingSaveStructuredPatientDrug[];
  /** 附件 */
  accessory?: string[];
  /** 复查结论 */
  reviewConclusions?: IApiStructuredReportingSaveStructuredReviewConclusions;
  /** 医生建议 */
  doctorAdvice?: IApiStructuredReportingSaveStructuredDoctorAdvice;
  /** 出院诊断 */
  diagnose?: IApiStructuredReportingSaveStructuredDiagnose;
  /** 问题 */
  problem?: IApiStructuredReportingSaveStructuredProblem;
  /** 主题 */
  theme?: string;
  /** 是否勾选用药 */
  drugCheck?: boolean;
  /** 是否勾选图片档案 */
  imageCheck?: boolean;
}

/**
 * 指标信息
 */
export interface IApiStructuredReportingUpdateStructuredParamsPatientIndex {
  /** 指标项id */
  indexTermId?: number;
  /** 检查项 */
  checkType?: number;
  /** 检查小项 */
  indexType?: number;
  /** 指标名称 */
  indexName?: string;
}

/**
 * 心电图
 */
export interface IApiStructuredReportingUpdateStructuredParamsEcgData {
  /** 报告项id */
  indexTermId?: number;
  /** 报告名称 */
  reportName?: string;
}

/**
 * 规格
 */
export interface IApiStructuredReportingUpdateStructuredParamsPatientDrugDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 单次用量
 */
export interface IApiStructuredReportingUpdateStructuredParamsPatientDrugDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
}

/**
 * 用药信息
 */
export interface IApiStructuredReportingUpdateStructuredParamsPatientDrug {
  /** 药品id */
  drugInfoId?: number;
  /** 药品名称 */
  drugName?: string;
  /** 用法(频率) */
  drugUsage?: string;
  /** 规格 */
  drugSpec?: IApiStructuredReportingUpdateStructuredParamsPatientDrugDrugSpec;
  /** 用药方式 */
  drugMode?: string;
  /** 单次用量 */
  drugAmount?: IApiStructuredReportingUpdateStructuredParamsPatientDrugDrugAmount;
  /** 服药时间1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
  /** 通用名称 */
  commonName?: string;
}

/**
 * 修改结构化报告 - body 请求参数
 */
export interface IApiStructuredReportingUpdateStructuredParams {
  /** 结构化报告id */
  id?: number;
  /** 患者id */
  patientId?: number;
  /** 主题 */
  theme?: string;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 模块信息 */
  module?: number[];
  /** 指标信息 */
  patientIndex?: IApiStructuredReportingUpdateStructuredParamsPatientIndex[];
  /** 心电图 */
  ecgData?: IApiStructuredReportingUpdateStructuredParamsEcgData[];
  /** 用药信息 */
  patientDrug?: IApiStructuredReportingUpdateStructuredParamsPatientDrug[];
  /** 复查结论 */
  reviewCon?: string;
  /** 建议 */
  suggest?: string;
  /** 概述 */
  overview?: string;
  /** 需确认问题 */
  issue?: string;
  /** 附件 */
  accessory?: string[];
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
}

/**
 * 修改结构化报告
 */
export type IApiStructuredReportingUpdateStructured = boolean;

/**
 * 获取ASCVD总体风险 - body 请求参数
 */
export interface IApiRiskStatisticsAscvdParams {
  /** 患者id */
  patientId: number;
}

/**
 * 视图
 */
export interface IApiRiskStatisticsAscvdView {
  dwAscvdRiskId?: number;
  /** 患者id */
  userId?: number;
  /** 风险类别 */
  riskType?: number;
  /** 风险程度 */
  riskLevel?: string;
  /** 等级 */
  level?: number;
  /** ASCVD事件 */
  ascvd?: string[];
  /** 高危因素事件 */
  highRiskFactor?: string[];
  /** 危险因素事件 */
  riskFactor?: string[];
  /** 日期 */
  dt?: number;
}

/**
 * 风险数据列表
 */
export interface IApiRiskStatisticsAscvdRiskDataList {
  dwAscvdRiskId?: number;
  /** 患者id */
  userId?: number;
  /** 风险类别 */
  riskType?: number;
  /** 风险程度 */
  riskLevel?: string;
  /** 等级 */
  level?: number;
  /** ASCVD事件 */
  ascvd?: string[];
  /** 高危因素事件 */
  highRiskFactor?: string[];
  /** 危险因素事件 */
  riskFactor?: string[];
  /** 日期 */
  dt?: number;
}

/**
 * 获取ASCVD总体风险
 */
export interface IApiRiskStatisticsAscvd {
  /** 视图 */
  view?: IApiRiskStatisticsAscvdView[];
  /** 风险数据列表 */
  riskDataList?: IApiRiskStatisticsAscvdRiskDataList[];
}

/**
 * 获取出血风险-ACS出血风险统计 - body 请求参数
 */
export interface IApiRiskStatisticsAciParams {
  /** 患者id */
  patientId: number;
}

/**
 * 视图
 */
export interface IApiRiskStatisticsAciView {
  dwAcuityRiskId?: number;
  /** 患者id */
  userId?: number;
  /** 风险类别 */
  riskType?: number;
  /** 分值 */
  score?: number;
  /** 风险程度 */
  riskLevel?: string;
  /** 年龄 */
  age?: string;
  /** 性别 */
  gender?: string;
  /** 肌酐 */
  creatinine?: string;
  /** 白细胞计数 */
  leukocyte?: string;
  /** 血红蛋白 */
  hemoglobin?: string;
  /** 临床诊断 */
  clinicalDiagnosis?: string;
  /** 用药信息 */
  medicate?: string;
  /** 日期 */
  dt?: number;
}

/**
 * 其他信息
 */
export interface IApiRiskStatisticsAciRiskDataListData {
  /** 值 */
  value?: string;
  /** 分数 */
  score?: number;
}

/**
 * 风险数据列表
 */
export interface IApiRiskStatisticsAciRiskDataList {
  /** 分数 */
  score?: number;
  /** 风险等级 */
  riskLevel?: string;
  /** 时间 */
  dt?: number;
  /** 其他信息 */
  data?: IApiRiskStatisticsAciRiskDataListData[];
}

/**
 * 获取出血风险-ACS出血风险统计
 */
export interface IApiRiskStatisticsAci {
  /** 视图 */
  view?: IApiRiskStatisticsAciView[];
  /** 风险数据列表 */
  riskDataList?: IApiRiskStatisticsAciRiskDataList[];
}

/**
 * 获取出血风险-房颤抗凝出血风险统计 - body 请求参数
 */
export interface IApiRiskStatisticsBledParams {
  /** 患者id */
  patientId: number;
}

/**
 * 视图
 */
export interface IApiRiskStatisticsBledView {
  dwBledRiskId?: number;
  /** 患者id */
  userId?: number;
  /** 风险类别 */
  riskType?: number;
  /** 分值 */
  score?: number;
  /** 风险程度 */
  riskLevel?: string;
  /** 年龄 */
  age?: string;
  /** 凝血 */
  inr?: string;
  /** 饮酒过量 */
  tipple?: string;
  /** 既往史 */
  previousHistory?: string;
  /** 临床诊断 */
  clinicalDiagnosis?: string;
  /** 用药信息 */
  medicate?: string;
  /** 日期 */
  dt?: number;
}

/**
 * 其他信息
 */
export interface IApiRiskStatisticsBledRiskDataListData {
  /** 值 */
  value?: string;
  /** 分数 */
  score?: number;
}

/**
 * 风险数据列表
 */
export interface IApiRiskStatisticsBledRiskDataList {
  /** 分数 */
  score?: number;
  /** 风险等级 */
  riskLevel?: string;
  /** 时间 */
  dt?: number;
  /** 其他信息 */
  data?: IApiRiskStatisticsBledRiskDataListData[];
}

/**
 * 获取出血风险-房颤抗凝出血风险统计
 */
export interface IApiRiskStatisticsBled {
  /** 视图 */
  view?: IApiRiskStatisticsBledView[];
  /** 风险数据列表 */
  riskDataList?: IApiRiskStatisticsBledRiskDataList[];
}

/**
 * 获取猝死风险 - body 请求参数
 */
export interface IApiRiskStatisticsDeathParams {
  /** 患者id */
  patientId: number;
}

/**
 * 视图
 */
export interface IApiRiskStatisticsDeathView {
  dwDeathRiskId?: number;
  /** 患者id */
  userId?: number;
  /** 风险类别 */
  riskType?: number;
  /** 风险程度 */
  riskLevel?: string;
  /** 临床诊断 */
  clinicalDiagnosis?: string;
  /** 心电图 */
  ecg?: string;
  /** 日期 */
  dt?: number;
}

/**
 * 风险数据列表
 */
export interface IApiRiskStatisticsDeathRiskDataList {
  dwDeathRiskId?: number;
  /** 患者id */
  userId?: number;
  /** 风险类别 */
  riskType?: number;
  /** 风险程度 */
  riskLevel?: string;
  /** 临床诊断 */
  clinicalDiagnosis?: string;
  /** 心电图 */
  ecg?: string;
  /** 日期 */
  dt?: number;
}

/**
 * 获取猝死风险
 */
export interface IApiRiskStatisticsDeath {
  /** 视图 */
  view?: IApiRiskStatisticsDeathView[];
  /** 风险数据列表 */
  riskDataList?: IApiRiskStatisticsDeathRiskDataList[];
}

/**
 * 获取预后风险-复杂ACS风险统计 - body 请求参数
 */
export interface IApiRiskStatisticsAcsSynParams {
  /** 患者id */
  patientId: number;
}

/**
 * 视图
 */
export interface IApiRiskStatisticsAcsSynView {
  dwRiskId?: number;
  /** 患者id */
  userId?: number;
  /** 风险类别 */
  riskType?: number;
  /** 分值 */
  score?: number;
  /** 风险程度 */
  riskLevel?: string;
  /** 性别 */
  gender?: string;
  /** 年龄 */
  age?: string;
  /** 肌酐清除率 */
  crcl?: string;
  /** 左室收缩功能 */
  lvef?: string;
  /** 节段选择勾选“左主干” */
  leftMain?: string;
  /** 临床诊断 */
  clinicalDiagnosis?: string;
  /** 肌酐清除率 */
  segmentInfo?: string;
  /** 日期 */
  dt?: number;
  /** 片段 */
  segments?: string[];
}

/**
 * 其他信息
 */
export interface IApiRiskStatisticsAcsSynRiskDataListData {
  /** 值 */
  value?: string;
  /** 分数 */
  score?: number;
}

/**
 * 风险数据列表
 */
export interface IApiRiskStatisticsAcsSynRiskDataList {
  /** 分数 */
  score?: number;
  /** 风险等级 */
  riskLevel?: string;
  /** 时间 */
  dt?: number;
  /** 其他信息 */
  data?: IApiRiskStatisticsAcsSynRiskDataListData[];
}

/**
 * 获取预后风险-复杂ACS风险统计
 */
export interface IApiRiskStatisticsAcsSyn {
  /** 视图 */
  view?: IApiRiskStatisticsAcsSynView[];
  /** 风险数据列表 */
  riskDataList?: IApiRiskStatisticsAcsSynRiskDataList[];
}

/**
 * 获取预后风险-急性ACS风险统计 - body 请求参数
 */
export interface IApiRiskStatisticsAcsAcuteParams {
  /** 患者id */
  patientId: number;
}

/**
 * 视图
 */
export interface IApiRiskStatisticsAcsAcuteView {
  dwGraceRiskId?: number;
  /** 患者id */
  userId?: number;
  /** 风险类别 */
  riskType?: number;
  /** 分值 */
  score?: number;
  /** 风险程度 */
  riskLevel?: string;
  /** 年龄 */
  age?: string;
  /** KILLIP分级 */
  killip?: string;
  /** 收缩压 */
  highPressure?: string;
  /** 心率 */
  heartRate?: string;
  /** 肌酐 */
  creatinine?: string;
  /** 危险因素 */
  dangerousFactor?: string;
  /** 日期 */
  dt?: number;
}

/**
 * 其他信息
 */
export interface IApiRiskStatisticsAcsAcuteRiskDataListData {
  /** 值 */
  value?: string;
  /** 分数 */
  score?: number;
}

/**
 * 风险数据列表
 */
export interface IApiRiskStatisticsAcsAcuteRiskDataList {
  /** 分数 */
  score?: number;
  /** 风险等级 */
  riskLevel?: string;
  /** 时间 */
  dt?: number;
  /** 其他信息 */
  data?: IApiRiskStatisticsAcsAcuteRiskDataListData[];
}

/**
 * 获取预后风险-急性ACS风险统计
 */
export interface IApiRiskStatisticsAcsAcute {
  /** 视图 */
  view?: IApiRiskStatisticsAcsAcuteView[];
  /** 风险数据列表 */
  riskDataList?: IApiRiskStatisticsAcsAcuteRiskDataList[];
}

/**
 * 获取预后风险-非瓣膜性房颤脑卒中风险统计 - body 请求参数
 */
export interface IApiRiskStatisticsAcsChaParams {
  /** 患者id */
  patientId: number;
}

/**
 * 视图
 */
export interface IApiRiskStatisticsAcsChaView {
  dwChaVascRiskId?: number;
  /** 患者id */
  userId?: number;
  /** 风险类别 */
  riskType?: number;
  /** 分值 */
  score?: number;
  /** 风险程度 */
  riskLevel?: string;
  /** 年龄 */
  age?: string;
  /** 性别 */
  gender?: string;
  /** 既往史 */
  previousHistory?: string;
  /** 临床诊断 */
  clinicalDiagnosis?: string;
  /** 日期 */
  dt?: number;
}

/**
 * 其他信息
 */
export interface IApiRiskStatisticsAcsChaRiskDataListData {
  /** 值 */
  value?: string;
  /** 分数 */
  score?: number;
}

/**
 * 风险数据列表
 */
export interface IApiRiskStatisticsAcsChaRiskDataList {
  /** 分数 */
  score?: number;
  /** 风险等级 */
  riskLevel?: string;
  /** 时间 */
  dt?: number;
  /** 其他信息 */
  data?: IApiRiskStatisticsAcsChaRiskDataListData[];
}

/**
 * 获取预后风险-非瓣膜性房颤脑卒中风险统计
 */
export interface IApiRiskStatisticsAcsCha {
  /** 视图 */
  view?: IApiRiskStatisticsAcsChaView[];
  /** 风险数据列表 */
  riskDataList?: IApiRiskStatisticsAcsChaRiskDataList[];
}

/**
 * 获取风险等级 - body 请求参数
 */
export interface IApiRiskStatisticsRiskLevelParams {
  /** 患者id */
  patientId: number;
}

/**
 * 获取风险等级
 */
export interface IApiRiskStatisticsRiskLevel {
  /** 总体风险 */
  ascvd?: number;
  /** 预后风险 */
  grace?: number;
  /** 出血风险 */
  bleed?: number;
  /** 猝死风险 */
  death?: number;
}

/**
 * 硬件订单备注信息
 */
export interface IApiOrderCreateParamsOrderDeviceRemark {
  /** 设备编号 */
  deviceSoNo?: string;
  /** 设备类型
BPG: 台式血压计
WATCH:智能手表
WS:体重秤 */
  deviceType?: string;
}

/**
 * 补差价订单备注信息
 */
export interface IApiOrderCreateParamsOrderReplaceRemark {
  /** 退费流程id */
  refundProcessId?: number;
  /** 退费类型
PACKAGE: 服务包
HARDWARE:硬件
REPLACE:补差价 */
  refundType?: string;
}

/**
 * 创建硬件、补差价订单 - body 请求参数
 */
export interface IApiOrderCreateParams {
  /** 患者ID */
  patientId: number;
  /** 产品ID */
  productId: number;
  /** 服务包类型
PACKAGE：服务包
HARDWARE：硬件
REPLACE：补差价 */
  orderType: string;
  /** 下单人ID */
  creatorId: number;
  /** 下单人类型（区分下单人的类型：健康顾问、医生、专家、患者）
SELLER：健康顾问
DOCTOR：医生
EXPERT：专家
PATIENT：患者
COMPANY：公司 */
  creatorType: string;
  /** 硬件订单备注信息 */
  orderDeviceRemark?: IApiOrderCreateParamsOrderDeviceRemark;
  /** 补差价订单备注信息 */
  orderReplaceRemark?: IApiOrderCreateParamsOrderReplaceRemark;
  /** 微信支付方式
WX_JSAPI:微信支付
WX_NATIVE:扫码支付 */
  wxPayType: string;
}

/**
 * 创建硬件、补差价订单
 */
export interface IApiOrderCreate {
  /** 微信jsapi支付返回参数appId */
  appId?: string;
  /** 微信jsapi支付返回参数timeStamp */
  timeStamp?: string;
  /** 微信jsapi支付返回参数nonceStr */
  nonceStr?: string;
  /** 微信jsapi支付返回参数packageValue */
  packageValue?: string;
  /** 微信jsapi支付返回参数signType */
  signType?: string;
  /** 微信jsapi支付返回参数paySign */
  paySign?: string;
  /** 微信native支付返回参数 */
  wxPayQrCodeUrl?: string;
  /** 订单id */
  orderId?: number;
}

/**
 * 医生线下支付订单续费 - body 请求参数
 */
export interface IApiOrderAsstOfflineRenewParams {
  /** 订单id */
  orderId?: number;
  /** 患者id */
  patientId?: number;
  /** 服务包id */
  productId?: number;
  /** 支付类型 */
  payType?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 医生线下支付订单续费
 */
export interface IApiOrderAsstOfflineRenew {}

/**
 * 医生订单续费 - body 请求参数
 */
export interface IApiOrderAsstRenewParams {
  /** 订单id */
  orderId?: number;
  /** 患者id */
  patientId?: number;
  /** 服务包id */
  productId?: number;
  /** 支付类型 */
  payType?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 医生订单续费
 */
export interface IApiOrderAsstRenew {}

/**
 * 取消订单 - body 请求参数
 */
export interface IApiOrderCancelParams {
  /** 订单id */
  orderId: number;
  /** 订单类型 */
  orderType: string;
}

/**
 * 取消订单
 */
export type IApiOrderCancel = boolean;

/**
 * 推送设备邮寄地址 - body 请求参数
 */
export interface IApiOrderPushAddressParams {
  businessId: number;
}

/**
 * 推送设备邮寄地址
 */
export type IApiOrderPushAddress = boolean;

/**
 * 是否需要打折 - body 请求参数
 */
export interface IApiOrderWhetherDiscountParams {
  /** 患者id */
  patientId: number;
}

/**
 * 是否需要打折
 */
export type IApiOrderWhetherDiscount = boolean;

/**
 * 查询订单减免列表 - body 请求参数
 */
export interface IApiOrderSelectReductionListParams {
  /** 患者id */
  patientId: number;
  /** 查询有效数据（true：有效，false：所有） */
  queryEffectiveData: boolean;
}

/**
 *
 */
export interface IApiOrderSelectReductionListItem {
  /** 主键id */
  id?: number;
  /** 患者id */
  patientId?: number;
  /** 申请日期 */
  applyTime?: number;
  /** 使用日期 */
  userTime?: number;
  /** 使用订单编号 */
  useOrderNo?: string;
  /** 使用订单类型 */
  useOrderType?: string;
  /** 减免类型（REVIEW） */
  reductionType?: string;
  /** 减免id */
  reductionId?: number;
  /** 减免金额 */
  reductionAmount?: number;
  /** 减免描述 */
  reductionDesc?: string;
  /** 是否使用 */
  used?: boolean;
}

/**
 * 查询订单减免列表
 */
export type IApiOrderSelectReductionList = IApiOrderSelectReductionListItem[];

/**
 * 查询订单收款二维码 - body 请求参数
 */
export interface IApiOrderQrCodeParams {
  /** 订单id */
  orderId: number;
}

/**
 * 查询订单收款二维码
 */
export type IApiOrderQrCode = string;

/**
 * 查询患者最近一条订单信息 - body 请求参数
 */
export interface IApiPatientOrderLastInfoParams {
  /** 患者id */
  patientId: number;
}

/**
 * 查询患者最近一条订单信息
 */
export interface IApiPatientOrderLastInfo {
  /** 续费和购买等
1 购买
2续费
4预约服务
5咨询
6门诊 */
  goal?: number;
  /** 订单支付时间 */
  payTime?: string;
  /** 支付价格 */
  payPrice?: number;
  /** 订单id */
  orderId?: number;
  /** 退费备注信息 */
  refundRemarks?: string;
  /** 订单创建时间 */
  createTime?: string;
  /** 工作室id */
  groupId?: number;
  /** 订单发起人(发起人类型)
1.健康顾问发起
2.患者发起
3.医助发起
4兼职医学顾问发起 */
  initiatorType?: number;
  /** 服务包id */
  productId?: number;
  /** 患者id */
  patientId?: number;
  /** 订单有效时间 */
  invalidTime?: string;
  /** 退款时间 */
  refundTime?: string;
  /** 支付类型
1现金
2微信
3支付宝
4免费会员 */
  payType?: number;
  /** 支付对象
1.患者缴费
2.健康顾问缴费
3.公司账号缴费
4.兼职医学顾问缴费 */
  payObject?: number;
  /** 订单状态
100：成功
0：待支付
1：已取消
2：已退款 */
  status?: number;
  /** 订单编号 */
  orderNo?: string;
  /** 健康顾问id */
  sellerId?: number;
  /** 备注信息 */
  remarks?: string;
  /** 1代表退款 */
  refund?: number;
  /** 服务包名称 */
  productName?: string;
}

/**
 * 查询患者订单服务信息 - body 请求参数
 */
export interface IApiPatientOrderServiceInfoParams {
  /** 订单id */
  orderId: number;
}

/**
 * 查询患者订单服务信息
 */
export interface IApiPatientOrderServiceInfo {
  /** 医生姓名 */
  assistantName?: string;
  /** 健管师姓名 */
  customerName?: string;
  /** 康复师姓名 */
  rehabName?: string;
  /** 专家姓名 */
  doctorName?: string;
  /** 健康顾问姓名 */
  sellerName?: string;
  /** 健康顾问姓名 */
  sellerPhone?: string;
}

/**
 * 查询患者订单管理周期 - body 请求参数
 */
export interface IApiPatientOrderManagerPeriodParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiPatientOrderManagerPeriodItem {
  /** 订单id */
  orderId?: number;
  /** 续费和购买等区别
1购买
2续费
6门诊
5咨询
4预约服务 */
  type?: number;
  /** 开始管理周期 */
  startDate?: number;
  /** 截止管理周期 */
  endDate?: number;
  /** 产品名称 */
  productName?: string;
}

/**
 * 查询患者订单管理周期
 */
export type IApiPatientOrderManagerPeriod = IApiPatientOrderManagerPeriodItem[];

/**
 * 查询患者订单购买记录 - body 请求参数
 */
export interface IApiPatientOrderRecordListParams {
  pageNumber?: number;
  pageSize?: number;
  /** 患者id */
  patientId: number;
  /** 订单状态
100：成功
0：待支付
1：已取消
2：已退款 */
  status?: number;
  /** 退费  1代表退款 */
  refund?: number;
  /** 退费状态
0暂存
1退款中
2已驳回
3已撤回
4已退款
5退款失败 */
  refundStatus?: number;
}

/**
 *
 */
export interface IApiPatientOrderRecordListContents {
  /** 订单编号 */
  orderNo?: string;
  /** 实际支付价格 */
  actualPrice?: number;
  /** 支付价格 */
  payPrice?: number;
  /** 续费和购买等
1购买
2续费
4预约服务
5咨询
6门诊 */
  goal?: number;
  /** 订单类型 */
  orderType?: string;
  /** 订单创建时间 */
  createTime?: number;
  /** 退费备注信息 */
  refundRemarks?: string;
  /** 订单支付时间 */
  payTime?: number;
  /** 订单发起人(发起人类型)
1.健康顾问发起
2.患者发起
3.医助发起
4.兼职医学顾问发起 */
  initiatorType?: number;
  /** 工作室id */
  groupId?: number;
  /** 患者id */
  patientId?: number;
  /** 退款时间 */
  refundTime?: number;
  /** 服务包时间 */
  productId?: number;
  /** 支付对象
1.患者缴费
2.健康顾问缴费
3.公司账号缴费
4.兼职医学顾问缴费 */
  payObject?: number;
  /** 订单有效时间 */
  invalidTime?: number;
  /** 支付类型
1现金
2微信
3支付宝
4免费会员 */
  payType?: number;
  /** 订单id */
  orderId?: number;
  /** 健康顾问id */
  sellerId?: number;
  /** 备注信息 */
  remarks?: string;
  /** 订单状态
100：成功
0：待支付
1：已取消
2：已退款 */
  status?: number;
  /** 1代表退款 */
  refund?: number;
  /** 服务包名称 */
  productName?: string;
  /** 服务包类型 */
  productType?: number;
  /** 退款状态
0暂存
1退款中
2已驳回
3已撤回
4已退款
5退款失败 */
  refundStatus?: number;
}

/**
 * 查询患者订单购买记录
 */
export interface IApiPatientOrderRecordList {
  total?: number;
  contents?: IApiPatientOrderRecordListContents[];
}

/**
 * 给患者公众号推送订单信息 - body 请求参数
 */
export interface IApiPatientOrderNotifyParams {
  /** 订单id */
  orderId: number;
}

/**
 * 给患者公众号推送订单信息
 */
export type IApiPatientOrderNotify = boolean;

/**
 * 获取患者会员到期时间 - body 请求参数
 */
export interface IApiPatientOrderExpirationDateParams {
  /** 患者id */
  patientId: number;
}

/**
 * 获取患者会员到期时间
 */
export interface IApiPatientOrderExpirationDate {
  /** 患者id */
  patientId?: number;
  /** 会员状态
0：非会员
1：会员 */
  vipType?: number;
  /** 过期时间 */
  expirationDate?: number;
}

/**
 * 获取患者绑定工作室的产品列表 - body 请求参数
 */
export interface IApiPatientOrderProductParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiPatientOrderProductItem {
  /** 产品id */
  productId?: number;
  /** 服务包id */
  packageId?: number;
  /** 产品名称 */
  productName?: string;
  /** 服务包价格 */
  price?: number;
  /** 最低订单价格 */
  lowestPrice?: number;
  /** 服务包类型 */
  productType?: number;
}

/**
 * 获取患者绑定工作室的产品列表
 */
export type IApiPatientOrderProduct = IApiPatientOrderProductItem[];

/**
 * 获取公司列表 - body 请求参数
 */
export interface IApiOrderCompanyListParams {
  data: string;
}

/**
 *
 */
export interface IApiOrderCompanyListItem {
  /** 公司id */
  companyId?: number;
  /** 公司名称 */
  companyName?: string;
}

/**
 * 获取公司列表
 */
export type IApiOrderCompanyList = IApiOrderCompanyListItem[];

/**
 * 查询患者的生活方式问卷分数折线图 - body 请求参数
 */
export interface IApiUserQuestionnaireScoreParams {
  /** 患者id */
  patientId?: number;
  /** 问卷名称 （睡眠质量调查问卷、抑郁评估调查问卷、焦虑评估调查问卷） */
  questionnaireName?: string;
}

/**
 *
 */
export interface IApiUserQuestionnaireScoreItem {
  /** 问卷日期（yyyy-MM） */
  date?: string;
  /** 问卷分数 */
  score?: number;
}

/**
 * 查询患者的生活方式问卷分数折线图
 */
export type IApiUserQuestionnaireScore = IApiUserQuestionnaireScoreItem[];

/**
 * 查询患者的生活质量问卷分数数据 - body 请求参数
 */
export interface IApiUserQuestionnaireLifeQualityScoreParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiUserQuestionnaireLifeQualityScoreItem {
  /** 问卷id */
  userQuestionnaireId?: number;
  /** 问卷开始时间 (yyyy-MM) */
  startTime?: string;
  /** 生理机能 */
  physicalFunctioning?: string;
  /** 生理职能 */
  rolePhysical?: string;
  /** 躯体疼痛 */
  bodilyPain?: string;
  /** 一般健康状况 */
  generalHealth?: string;
  /** 精力 */
  vitality?: string;
  /** 社会功能 */
  socialFunctioning?: string;
  /** 情感职能 */
  roleEmotional?: string;
  /** 精神健康 */
  mentalHealth?: string;
  /** 健康变化 */
  reportedHealthTransition?: string;
}

/**
 * 查询患者的生活质量问卷分数数据
 */
export type IApiUserQuestionnaireLifeQualityScore =
  IApiUserQuestionnaireLifeQualityScoreItem[];

/**
 * 查询患者运动状态变更历史及详情 - body 请求参数
 */
export interface IApiRehabManageMotionHistoryParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiRehabManageMotionHistoryItem {
  /** 运动状态id */
  motionStateId?: number;
  /** 患者id */
  patientId?: number;
  /** 运动康复师id */
  rehabId?: number;
  /** 运动康复师姓名 */
  rehabName?: string;
  /** 绝对禁忌症 */
  absoluteContraindication?: string;
  /** 相对禁忌症 */
  relativeContraindication?: string;
  /** 备注 */
  notes?: string;
  /** 评估结论 */
  conclusion?: string;
  /** 评估建议 */
  suggestion?: string;
  /** 评估时间 */
  evaluationTime?: string;
  /** 变更内容 */
  changes?: string;
}

/**
 * 查询患者运动状态变更历史及详情
 */
export type IApiRehabManageMotionHistory = IApiRehabManageMotionHistoryItem[];

/**
 * 查询患者风险评估变更历史及详情 - body 请求参数
 */
export interface IApiRehabManageRiskHistoryParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiRehabManageRiskHistoryItem {
  /** 风险评估id */
  riskEstimateId?: number;
  /** 患者id */
  patientId?: number;
  /** 运动康复师id */
  rehabId?: number;
  /** 运动康复师姓名 */
  rehabName?: string;
  /** 评估内容 */
  estimateContent?: string;
  /** 低危（废弃） */
  lowRisk?: string;
  /** 中危（废弃） */
  moderateRisk?: string;
  /** 高危（废弃） */
  highRisk?: string;
  /** 备注 */
  notes?: string;
  /** 评估结论 */
  conclusion?: string;
  /** 评估建议 */
  suggestion?: string;
  /** 评估时间 */
  evaluationTime?: string;
  /** 变更内容 */
  changes?: string;
}

/**
 * 查询患者风险评估变更历史及详情
 */
export type IApiRehabManageRiskHistory = IApiRehabManageRiskHistoryItem[];

/**
 * 编辑患者运动状态 - body 请求参数
 */
export interface IApiRehabManageEditMotionParams {
  /** 患者id */
  patientId: number;
  /** 运动康复师id */
  rehabId?: number;
  /** 绝对禁忌症 */
  absoluteContraindication?: string;
  /** 相对禁忌症 */
  relativeContraindication?: string;
  /** 备注 */
  notes?: string;
  /** 评估结论 */
  conclusion: string;
  /** 评估建议 */
  suggestion: string;
  /** 变更内容 */
  changes?: string;
}

/**
 * 编辑患者运动状态
 */
export type IApiRehabManageEditMotion = boolean;

/**
 * 编辑患者风险评估 - body 请求参数
 */
export interface IApiRehabManageEditRiskParams {
  /** 患者id */
  patientId: number;
  /** 运动康复师id */
  rehabId?: number;
  /** 评估内容 */
  estimateContent?: string;
  /** 低危（废弃） */
  lowRisk?: string;
  /** 中危（废弃） */
  moderateRisk?: string;
  /** 高危（废弃） */
  highRisk?: string;
  /** 备注 */
  notes?: string;
  /** 评估结论 */
  conclusion: string;
  /** 评估建议 */
  suggestion: string;
  /** 变更内容 */
  changes?: string;
}

/**
 * 编辑患者风险评估
 */
export type IApiRehabManageEditRisk = boolean;

/**
 * 患者管理效果数据 - body 请求参数
 */
export interface IApiManageInfoEffectParams {
  /** 患者id */
  patientId: number;
}

/**
 * 患者管理效果数据
 */
export interface IApiManageInfoEffect {
  /** 血压达标率 */
  bloodPressure?: string;
  /** 血糖达标率 */
  bloodSugarRate?: string;
  /** 心率达标率 */
  heartRate?: string;
  /** 血脂达标率 */
  bloodFatRate?: string;
  /** 临床事件次数 */
  clinicalNum?: number;
}

/**
 * 患者行为数据 - body 请求参数
 */
export interface IApiManageInfoBehaviorParams {
  /** 患者id */
  patientId: number;
}

/**
 * 患者行为数据
 */
export interface IApiManageInfoBehavior {
  /** 随访平均用时 */
  followUpAvg?: string;
  /** 随访次数 */
  followUpNum?: number;
  /** 复查平均用时 */
  reviewAvg?: string;
  /** 复查次数 */
  reviewNum?: number;
  /** 用药调整数 */
  drugAdjust?: number;
}

/**
 * 患者随访复查完成率 - body 请求参数
 */
export interface IApiManageInfoCompleteRateParams {
  /** 患者id */
  patientId: number;
}

/**
 * 患者随访复查完成率
 */
export interface IApiManageInfoCompleteRate {
  /** 随访完成率 */
  followUpRate?: string;
  /** 症状随访完成率 */
  symptomRate?: string;
  /** 生活方式随访完成率 */
  lifeStyleRate?: string;
  /** 复查完成率 */
  reviewRate?: string;
}

/**
 * 血压测量统计次数--折线图 - body 请求参数
 */
export interface IApiManageInfoPressureNumParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiManageInfoPressureNumItem {
  /** 月份 */
  month?: number;
  /** 数量 */
  num?: number;
}

/**
 * 血压测量统计次数--折线图
 */
export type IApiManageInfoPressureNum = IApiManageInfoPressureNumItem[];

/**
 * 血压达标率--折线图 - body 请求参数
 */
export interface IApiManageInfoPressureRateParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiManageInfoPressureRateItem {
  /** 达标率 */
  complianceRate?: number;
  /** 达标次数 */
  complianceNum?: number;
  /** 时间 */
  timeSector?: string;
}

/**
 * 血压达标率--折线图
 */
export type IApiManageInfoPressureRate = IApiManageInfoPressureRateItem[];

/**
 * 问诊时间段分布--玫瑰图 - body 请求参数
 */
export interface IApiManageInfoTimeSlotParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiManageInfoTimeSlotItem {
  /** 百分比 */
  rate?: string;
  /** 时间段 */
  time?: string;
  /** 问诊总次数 */
  totalNum?: number;
}

/**
 * 问诊时间段分布--玫瑰图
 */
export type IApiManageInfoTimeSlot = IApiManageInfoTimeSlotItem[];

/**
 * 问诊统计次数--折线图 - body 请求参数
 */
export interface IApiManageInfoConsultNumParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiManageInfoConsultNumItem {
  /** 月份 */
  month?: number;
  /** 数量 */
  num?: number;
}

/**
 * 问诊统计次数--折线图
 */
export type IApiManageInfoConsultNum = IApiManageInfoConsultNumItem[];

/**
 * TEST - body 请求参数
 */
export interface IApiWebSocketTestParams {
  /** 消息id */
  msgId?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
  /** 消息类型 */
  msgType?: string;
  /** 消息状态 */
  msgStatus?: string;
  /** 是否是系统消息 */
  sysMsg?: boolean;
  /** 最新一次更新时间 */
  latestTime?: number;
  /** 消息标题 */
  msgTitle?: string;
  /** 消息内容 */
  msgSimpleContent?: string;
  /** 来源id */
  sourceId?: string;
  /** 二级来源 */
  secondSourceId?: string;
}

/**
 * TEST
 */
export type IApiWebSocketTest = boolean;

/**
 * TEST1 - body 请求参数
 */
export interface IApiWebSocketCompleteParams {
  msgType: string;
  sourceId: string;
  patientId: number;
  patientName: string;
  occurredTime: number;
  content: string;
}

/**
 * TEST1
 */
export type IApiWebSocketComplete = boolean;

/**
 * TEST2 - body 请求参数
 */
export interface IApiWebSocketRiskParams {
  msgType: string;
  sourceId: string;
  patientId: number;
  patientName: string;
  content: string;
}

/**
 * TEST2
 */
export type IApiWebSocketRisk = boolean;

/**
 * TEST3 - body 请求参数
 */
export interface IApiWebSocketEnrollmentParams {
  msgType: string;
  sourceId: string;
  patientId: number;
  patientName: string;
  gender: number;
  age: number;
  groupName: string;
  groupId: number;
}

/**
 * TEST3
 */
export type IApiWebSocketEnrollment = boolean;

/**
 * TEST4 - body 请求参数
 */
export interface IApiWebSocketChatParams {
  chatId: string;
  sendUserId: number;
  sendUserName: string;
  sendUserType: string;
  recordType: string;
  record: string;
  recordTime: number;
  conversationId: string;
}

/**
 * TEST4
 */
export type IApiWebSocketChat = boolean;

/**
 * TEST Operate - body 请求参数
 */
export interface IApiMessageCenterTestOperateParams {
  /** 操作类型 */
  opType?: string;
}

/**
 * TEST Operate
 */
export type IApiMessageCenterTestOperate = boolean;

/**
 * TEST notification - body 请求参数
 */
export interface IApiMessageCenterTestNotificationParams {
  /** 消息类型 */
  msgType?: string;
  /** 消息标题 */
  msgTitle?: string;
  /** 消息内容 */
  msgSimpleContent?: string;
  /** 来源id */
  sourceId?: string;
  /** 二级来源 */
  secondSourceId?: string;
}

/**
 * TEST notification
 */
export type IApiMessageCenterTestNotification = boolean;

/**
 * 任务列表查询 - body 请求参数
 */
export interface IApiInternTaskListParams {
  /** 患者姓名 */
  patientName?: string;
  /** 性别 */
  sex?: number;
  /** 工作室id */
  workRoomId?: number;
  /** 医生id */
  doctorId?: number;
  /** 任务类型 */
  taskType?: number[];
  /** 最后提交开始时间 */
  endSubmitStartTime?: number;
  /** 最后提交结束时间 */
  endSubmitEndTime?: number;
  /** 任务状态 0初始化 1已锁单 2已完成 */
  taskStatus?: number;
  /** 当前页 */
  page: number;
  /** 页大小 */
  pageSize: number;
  /** 患者类型 没选则传null */
  patientIds?: number[];
}

/**
 * 子任务
 */
export interface IApiInternTaskListContentsSubtaskList {
  /** 患者id */
  patientId?: number;
  /** 子任务id */
  subtaskId?: number;
  /** 模块 */
  moduleList?: number[];
  /** 任务原因 */
  reason?: number[];
  /** 备注 */
  remark?: string;
  /** 任务状态 0初始化 1已锁单 2已完成 3处理中 4已释放 5已提交 6已驳回 */
  taskStatus?: number;
  /** 生成人 */
  generateName?: string;
  /** 生成时间 */
  generateTime?: number;
  /** 提交人 */
  submitName?: string;
  /** 提交时间 */
  submitTime?: number;
  /** 任务提交内容 */
  content?: string;
}

/**
 *
 */
export interface IApiInternTaskListContents {
  /** 主任务id */
  mainTaskId?: number;
  /** 来源id */
  sourceId?: number;
  /** 性别 */
  gender?: number;
  /** 来源时间 */
  sourceTime?: number;
  /** 患者id */
  patientId?: number;
  /** 患者姓名 */
  patientName?: string;
  /** 年龄 */
  age?: number;
  /** 任务类型 0住院 1门诊 2复查 3入组 */
  taskType?: number;
  /** 工作室名称 */
  workRoomName?: string;
  /** 医生名称 */
  doctorName?: string;
  /** 最后提交时间 */
  endSubmitTime?: number;
  /** 任务状态 0待接单 1进行中 2已完成 */
  taskStatus?: number;
  /** 子任务 */
  subtaskList?: IApiInternTaskListContentsSubtaskList[];
}

/**
 * 任务列表查询
 */
export interface IApiInternTaskList {
  total?: number;
  contents?: IApiInternTaskListContents[];
}

/**
 * 医生及工作室查询 - body 请求参数
 */
export interface IApiInternExistGroupDoctorListParams {
  data: string;
}

/**
 * 医生集合
 */
export interface IApiInternExistGroupDoctorListDoctorList {
  /** 医生id */
  doctorId?: number;
  /** 医生姓名 */
  doctorName?: string;
}

/**
 * 工作室集合
 */
export interface IApiInternExistGroupDoctorListWorkRoomList {
  /** 工作室id */
  workRoomId?: number;
  /** 工作室名称 */
  workRoomName?: string;
}

/**
 * 医生及工作室查询
 */
export interface IApiInternExistGroupDoctorList {
  /** 医生集合 */
  doctorList?: IApiInternExistGroupDoctorListDoctorList[];
  /** 工作室集合 */
  workRoomList?: IApiInternExistGroupDoctorListWorkRoomList[];
}

/**
 * 医生查询 - body 请求参数
 */
export interface IApiInternInfoListParams {
  data: string;
}

/**
 * 医生集合
 */
export interface IApiInternInfoListDoctorList {
  /** 医生id */
  doctorId?: number;
  /** 医生姓名 */
  doctorName?: string;
}

/**
 * 工作室集合
 */
export interface IApiInternInfoListWorkRoomList {
  /** 工作室id */
  workRoomId?: number;
  /** 工作室名称 */
  workRoomName?: string;
}

/**
 * 医生查询
 */
export interface IApiInternInfoList {
  /** 医生集合 */
  doctorList?: IApiInternInfoListDoctorList[];
  /** 工作室集合 */
  workRoomList?: IApiInternInfoListWorkRoomList[];
}

/**
 * 患者类型筛选条件查询 - body 请求参数
 */
export interface IApiInternTaskPatientTypeParams {
  data: string;
}

/**
 *
 */
export interface IApiInternTaskPatientTypeItem {
  /** 患者类型 1 会员 2 干预组 3 对照组 0为其他 */
  typeName?: string;
  /** 患者集合 列表传参时如果没选则传null */
  patientIds?: number[];
}

/**
 * 患者类型筛选条件查询
 */
export type IApiInternTaskPatientType = IApiInternTaskPatientTypeItem[];

/**
 * 生成转录任务 - body 请求参数
 */
export interface IApiInternTranscriptionParams {
  /** 患者id */
  patientId: number;
  /** 患者姓名 */
  patientName: string;
  /** 来源id */
  sourceId: number;
  /** 任务类型 0:住院 1:门诊 2:复查 3:入组 */
  taskType: number;
  /** 任务段落 1:入院记录 2:手术记录 3出院记录 4 门诊记录 5门诊处方 6 检查项 */
  taskSection?: number[];
  /** 申请理由 0:入组记录新增 1:原有内容错误 2:资料补充 -1:其他 */
  reason?: number[];
  /** 其他备注 */
  remark?: string;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userRole?: string;
  /** 录入方式 任务病历数据录入方式(0-手动录入，1-自动录入) */
  taskMethod: number;
  /** 来源数据类型(0-病历，1-病历录入任务) */
  sourceType: number;
}

/**
 * 生成转录任务
 */
export type IApiInternTranscription = undefined;

/**
 * 确认完成 - body 请求参数
 */
export interface IApiInternTaskCompleteParams {
  /** 任务id */
  taskId: number;
  /** 消息发生时间 */
  sourceTime?: number;
  /** 来源id */
  sourceId?: number;
}

/**
 * 确认完成
 */
export type IApiInternTaskComplete = undefined;

/**
 * 资料提交 - body 请求参数
 */
export interface IApiInternTaskSubmitParams {
  /** 任务id */
  taskId: number;
}

/**
 * 资料提交
 */
export type IApiInternTaskSubmit = boolean;

/**
 * 释放任务 - body 请求参数
 */
export interface IApiInternReleaseParams {
  /** 任务id */
  mainTaskId: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userRole?: string;
}

/**
 * 释放任务
 */
export type IApiInternRelease = undefined;

/**
 * 锁单 - body 请求参数
 */
export interface IApiInternTaskLockParams {
  /** 任务id */
  taskId: number;
}

/**
 * 锁单
 */
export type IApiInternTaskLock = undefined;

/**
 * 分页查询推荐 - body 请求参数
 */
export interface IApiRecommendConversationQueryPageParams {
  pageNumber?: number;
  pageSize?: number;
  /** 患者ID */
  patientId?: number;
  /** 对话ID */
  conversationNo?: string;
}

/**
 * 患者信息
 */
export interface IApiRecommendConversationQueryPageContentsPatient {
  /** 患者id */
  id?: number;
  /** 患者姓名 */
  name?: string;
  /** 患者性别 1男 2女 */
  gender?: number;
  /** 患者年龄 */
  age?: number;
}

/**
 * 推荐内容
 */
export interface IApiRecommendConversationQueryPageContentsRecommends {
  /** 推荐ID */
  recommendId?: number;
  /** 患者ID */
  patientId?: number;
  /** 对话ID */
  conversationNo?: string;
  /** 推荐号 */
  recommendNo?: string;
  /** 意图 */
  intention?: string;
  /** 推荐语料 */
  sentences?: string[];
  /** 消息提交时间 */
  sentenceTime?: number;
  /** 推荐回复文案 */
  outputDialogue?: string;
  /** 推荐文案的时间 */
  outputTime?: number;
}

/**
 *
 */
export interface IApiRecommendConversationQueryPageContents {
  /** 患者信息 */
  patient?: IApiRecommendConversationQueryPageContentsPatient;
  /** 推荐内容 */
  recommends?: IApiRecommendConversationQueryPageContentsRecommends[];
}

/**
 * 分页查询推荐
 */
export interface IApiRecommendConversationQueryPage {
  total?: number;
  contents?: IApiRecommendConversationQueryPageContents[];
}

/**
 * 反馈推荐 - body 请求参数
 */
export interface IApiRecommendConversationFeedbackParams {
  /** 推荐主键ID */
  id: number;
  /** 反馈 */
  feedback: string;
}

/**
 * 反馈推荐
 */
export type IApiRecommendConversationFeedback = boolean;

/**
 * 批量更新患者推荐 - body 请求参数
 */
export interface IApiRecommendConversationBatchConfirmParams {
  /** 患者ID */
  patientId: number;
  /** 推荐类型 */
  confirmType: string;
}

/**
 * 批量更新患者推荐
 */
export type IApiRecommendConversationBatchConfirm = boolean;

/**
 * 最近一次推荐 - body 请求参数
 */
export interface IApiRecommendConversationLatestParams {
  patientId: number;
}

/**
 * 图节点
 */
export interface IApiRecommendConversationLatestNodes {
  /** 节点ID */
  id?: number;
  /** 节点标签 */
  label?: string;
  /** 节点类型 - 0：病人节点 - 1：推理节点 */
  type?: number;
  /** 节点名称 */
  attribute?: string;
}

/**
 * 图关系
 */
export interface IApiRecommendConversationLatestRelations {
  /** 起始节点ID */
  source?: number;
  /** 目标节点ID */
  target?: number;
  /** 关系真实名称 */
  label?: string;
}

/**
 * 最近一次推荐
 */
export interface IApiRecommendConversationLatest {
  /** 根节点IDS */
  roots?: number[];
  /** 叶子结点IDS */
  leafs?: number[];
  /** 图节点 */
  nodes?: IApiRecommendConversationLatestNodes[];
  /** 图关系 */
  relations?: IApiRecommendConversationLatestRelations[];
}

/**
 * 最近一次推荐图 - body 请求参数
 */
export interface IApiRecommendConversationLatestGraphParams {
  patientId: number;
}

/**
 * 图节点
 */
export interface IApiRecommendConversationLatestGraphNodes {
  /** 节点ID */
  id?: string;
  /** 节点标签 */
  label?: string;
  /** 节点类型 - 0：病人节点 - 1：推理节点 */
  type?: number;
  /** 节点名称 */
  attribute?: string;
}

/**
 * 图关系
 */
export interface IApiRecommendConversationLatestGraphRelations {
  /** 起始节点ID */
  source?: string;
  /** 目标节点ID */
  target?: string;
  /** 关系真实名称 */
  label?: string;
}

/**
 * 最近一次推荐图
 */
export interface IApiRecommendConversationLatestGraph {
  /** 根节点IDS */
  roots?: string[];
  /** 叶子结点IDS */
  leafs?: string[];
  /** 图节点 */
  nodes?: IApiRecommendConversationLatestGraphNodes[];
  /** 图关系 */
  relations?: IApiRecommendConversationLatestGraphRelations[];
}

/**
 * 最近一次推荐对话 - body 请求参数
 */
export interface IApiRecommendConversationLatestContentParams {
  patientId: number;
}

/**
 * 最近一次推荐对话
 */
export interface IApiRecommendConversationLatestContent {
  /** 推荐ID */
  recommendId?: number;
  /** 推荐回复文案 */
  outputDialogue?: string;
  /** 推荐文案的时间 */
  outputTime?: number;
}

/**
 * 确认使用推荐 - body 请求参数
 */
export interface IApiRecommendConversationConfirmParams {
  /** 推荐主键ID */
  id: number;
  /** 推荐类型 */
  confirmType: string;
}

/**
 * 确认使用推荐
 */
export type IApiRecommendConversationConfirm = boolean;

/**
 * 获取推荐详情 - body 请求参数
 */
export interface IApiRecommendConversationDetailParams {
  /** 主键 */
  id: number;
}

/**
 * 患者信息
 */
export interface IApiRecommendConversationDetailPatient {
  /** 患者id */
  id?: number;
  /** 患者姓名 */
  name?: string;
  /** 患者性别 1男 2女 */
  gender?: number;
  /** 患者年龄 */
  age?: number;
}

/**
 * 图节点
 */
export interface IApiRecommendConversationDetailCurrentGraphNodes {
  /** 节点ID */
  id?: string;
  /** 节点标签 */
  label?: string;
  /** 节点类型 - 0：病人节点 - 1：推理节点 */
  type?: number;
  /** 节点名称 */
  attribute?: string;
}

/**
 * 图关系
 */
export interface IApiRecommendConversationDetailCurrentGraphRelations {
  /** 起始节点ID */
  source?: string;
  /** 目标节点ID */
  target?: string;
  /** 关系真实名称 */
  label?: string;
}

/**
 * 当前子图
 */
export interface IApiRecommendConversationDetailCurrentGraph {
  /** 根节点IDS */
  roots?: string[];
  /** 叶子结点IDS */
  leafs?: string[];
  /** 图节点 */
  nodes?: IApiRecommendConversationDetailCurrentGraphNodes[];
  /** 图关系 */
  relations?: IApiRecommendConversationDetailCurrentGraphRelations[];
}

/**
 * 反馈
 */
export interface IApiRecommendConversationDetailFeedback {
  /** 主键ID */
  id?: number;
  /** 患者ID */
  recommendId?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
  /** 是否使用 */
  used?: boolean;
  /** 使用时间 */
  usedTime?: number;
  /** 反馈 */
  feedback?: string;
  /** 反馈时间 */
  feedbackTime?: number;
  /** 忽略时间 */
  ignored?: boolean;
  /** 忽略时间 */
  ignoreTime?: number;
}

/**
 * 获取推荐详情
 */
export interface IApiRecommendConversationDetail {
  /** 主键ID */
  recommendId?: number;
  /** 患者ID */
  patientId?: number;
  /** 对话ID */
  conversationNo?: string;
  /** 推荐语料 */
  sentences?: string[];
  /** 消息提交时间 */
  sentenceTime?: number;
  /** 推荐回复文案 */
  outputDialogue?: string;
  /** 推荐文案的时间 */
  outputTime?: number;
  /** 详情 */
  description?: string;
  /** 患者信息 */
  patient?: IApiRecommendConversationDetailPatient;
  /** 当前子图 */
  currentGraph?: IApiRecommendConversationDetailCurrentGraph;
  /** 反馈 */
  feedback?: IApiRecommendConversationDetailFeedback;
}

/**
 * 获取患者工作室变更信息 - body 请求参数
 */
export interface IApiPatientGroupChangeRecordParams {
  businessId: number;
}

/**
 * 获取患者工作室变更信息
 */
export interface IApiPatientGroupChangeRecord {
  /** 患者姓名 */
  patientName?: string;
  /** 变更申请时间 */
  applyTime?: number;
  /** 转出工作室名称 */
  sourceGroupName?: string;
  /** 转入工作室名称 */
  targetGroupName?: string;
  /** 变更原因 */
  changeReason?: string;
}

/**
 * 微信申请退款 - body 请求参数
 */
export interface IApiOrderRefundInitiateWxParams {
  orderId: number;
  orderType: string;
}

/**
 * 微信申请退款
 */
export type IApiOrderRefundInitiateWx = string;

/**
 *
 */
export interface IApiOrderRefundSubmitDeviceApplyParamsDeviceList {
  deviceSoNo?: string;
  returnDeviceStatus?: boolean;
  deviceType?: string;
}

/**
 * 提交服务包、硬件退款申请 - body 请求参数
 */
export interface IApiOrderRefundSubmitDeviceApplyParams {
  refundProcessId?: number;
  orderId?: number;
  orderType?: string;
  refundDate?: number;
  deviceList?: IApiOrderRefundSubmitDeviceApplyParamsDeviceList[];
  noReturnReason?: string;
  companyId?: number;
  returnReason?: string;
  returnReasonDetails?: string;
  refundMoney?: number;
  refundType?: number;
  payeeName?: string;
  proceedsAccount?: string;
  bankOfDeposit?: string;
  isInvoicing?: number;
  status?: number;
  dingTalkProcessId?: string;
  payObject?: number;
  deductDeviceMoney?: number;
  proposeRefundPictureList?: string[];
  returnDevicePictureList?: string[];
  invoicingPictureList?: string[];
  applyId?: number;
  applyType?: string;
}

/**
 * 提交服务包、硬件退款申请
 */
export type IApiOrderRefundSubmitDeviceApply = boolean;

/**
 * 撤销审批 - body 请求参数
 */
export interface IApiOrderRefundProcessInstancesTerminateParams {
  /** 钉钉审批id */
  processInstanceId?: string;
}

/**
 * 撤销审批
 */
export interface IApiOrderRefundProcessInstancesTerminate {}

/**
 * 查询服务包、硬件退款申请页面数据 - body 请求参数
 */
export interface IApiOrderRefundQueryApplyInfoParams {
  /** 订单id */
  orderId: number;
  /** 订单类型
PACKAGE：服务包
HARDWARE：硬件
REPLACE：补差价 */
  orderType: string;
}

/**
 * 订单信息
 */
export interface IApiOrderRefundQueryApplyInfoOrderInfo {
  /** 订单id */
  orderId?: number;
  /** 订单编号 */
  orderNo?: string;
  /** 订单支付时间 */
  payTime?: number;
  /** 订单状态
100 ：成功
0：待支付
1：已取消
2：已退款 */
  status?: number;
  /** 订单类型 */
  orderType?: string;
  /** 订单类型：续费和购买等
1购买
2续费
4预约服务
5咨询
6门诊 */
  goal?: number;
  /** 支付类型
2微信
1现金
3支付宝
4免费会员 */
  payType?: number;
  /** 订单发起人(发起人类型)
1.健康顾问发起
2.患者发起
3.医助发起
4.兼职医学顾问发起 */
  initiatorType?: number;
  /** 支付价格 */
  payPrice?: number;
  /** 订单创建时间 */
  createTime?: number;
  /** 订单有效时间 */
  invalidTime?: number;
  /** 支付对象
1.患者缴费
2.健康顾问缴费
3.公司账号缴费
4.兼职医学顾问缴费 */
  payObject?: number;
  /** 患者姓名 */
  patientName?: string;
  /** 患者身份证号 */
  patientCardNo?: string;
  /** 患者性别
1是男
2是女 */
  patientGender?: number;
  /** 服务包名称 */
  productName?: string;
  /** 服务包类型 */
  productType?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 医生id */
  assistantId?: number;
  /** 医生姓名 */
  assistantName?: string;
  /** 健康顾问id */
  sellerId?: number;
  /** 健康顾问姓名 */
  sellerName?: string;
  /** 专家姓名 */
  doctorName?: string;
  /** 公司账户id */
  companyId?: number;
  /** 退款状态
0暂存
1退款中
2已驳回
3已撤回
4已退款
5退款失败 */
  refundStatus?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 患者id */
  patientId?: number;
  /** 加入时间 */
  joinDate?: number;
}

/**
 * 退费信息
 */
export interface IApiOrderRefundQueryApplyInfoRefundInfo {
  /** 退费主键id */
  refundProcessId?: number;
  /** 订单id */
  orderId?: number;
  /** 提出退款日期(患者提出退款时间) */
  refundDate?: number;
  /** 医生id */
  assistantId?: number;
  /** 健康顾问id */
  sellerId?: number;
  /** 是否退回血压计
是、否 */
  returnBpg?: boolean;
  /** 血压计编号 */
  bpgSoNo?: string;
  /** 是否退回智能手表
是、否 */
  returnWatch?: boolean;
  /** 智能手表编号 */
  watchSoNo?: string;
  /** 是否退回体重秤
是、否 */
  returnWs?: boolean;
  /** 体重秤编号 */
  wsSoNo?: string;
  /** 不退回原因 */
  noReturnReason?: string;
  /** 公司id */
  companyId?: number;
  /** 退款原因
1系统复杂,不好操作
2血压计问题
3对管理服务不满意
4自己去医院复查,认为没有管理必要
5不认可第三方平台
6患者不配合
7家属不同意
8患者去世
9经济困难
10价格太高
11家属或亲戚朋友为医生
12对医院不信任
13其他 */
  returnReason?: string;
  /** 详细退款原因 */
  returnReasonDetails?: string;
  /** 实退金额（元） */
  refundMoney?: number;
  /** 退款方式
1原路退回
2退回指定账户 */
  refundType?: number;
  /** 收款人姓名 */
  payeeName?: string;
  /** 收款账号 */
  proceedsAccount?: string;
  /** 收款开户行 */
  bankOfDeposit?: string;
  /** 是否已经开具发票
1是
0否 */
  isInvoicing?: number;
  /** 退款状态
0暂存
1退款中
2已驳回
3已撤回
4已退款
5退款失败 */
  status?: number;
  /** 创建时间 */
  createTime?: number;
  /** 更新时间 */
  updateTime?: number;
  /** 钉钉流程id */
  dingTalkProcessId?: string;
  /** 退费发起人类型
1健康顾问
2医助 */
  initiatorType?: number;
  /** 发起人类型
1.健康顾问发起
2.患者发起
3.医助发起 */
  payObject?: number;
  /** 是否扣除血压计费用
1是
0否 */
  deductDeviceMoney?: number;
  /** 提出退款证明 */
  proposeRefundPictureList?: string[];
  /** 退回血压计实物图 */
  returnDevicePictureList?: string[];
  /** 附件 */
  invoicingPictureList?: string[];
}

/**
 * 退费设备信息
 */
export interface IApiOrderRefundQueryApplyInfoRefundDeviceInfo {
  /** 设备编号 */
  deviceNo?: string;
  /** 设备类型 */
  deviceType?: string;
  /** 是否退回
true:退回
false: */
  isReturn?: boolean;
}

/**
 * 查询服务包、硬件退款申请页面数据
 */
export interface IApiOrderRefundQueryApplyInfo {
  /** 订单信息 */
  orderInfo?: IApiOrderRefundQueryApplyInfoOrderInfo;
  /** 退费信息 */
  refundInfo?: IApiOrderRefundQueryApplyInfoRefundInfo;
  /** 退费设备信息 */
  refundDeviceInfo?: IApiOrderRefundQueryApplyInfoRefundDeviceInfo[];
}

/**
 * 查询退费流程对应的待支付补差价订单信息 - body 请求参数
 */
export interface IApiOrderRefundQueryOrderByRefundParams {
  refundProcessId: number;
  orderType: string;
}

/**
 * 查询退费流程对应的待支付补差价订单信息
 */
export interface IApiOrderRefundQueryOrderByRefund {
  orderId?: number;
  orderNo?: string;
  productId?: number;
  productName?: string;
  patientId?: number;
  orderType?: string;
  orderSubtype?: string;
  orderStatus?: string;
  payType?: string;
  tarFee?: number;
  tarvFee?: number;
  creatorId?: number;
  creatorType?: string;
  payerId?: number;
  payerType?: string;
  performanceOwnerId?: number;
  performanceOwnerType?: string;
  groupId?: number;
  groupName?: string;
  companyId?: number;
  companyName?: string;
  hospitalId?: number;
  hospitalName?: string;
  sellerName?: string;
  doctorName?: string;
  expertName?: string;
  initTime?: number;
  payTime?: number;
  payInvalidTime?: number;
  invalidTime?: number;
  endTime?: number;
  refund?: boolean;
  refundTime?: number;
  refundRemarks?: string;
  remarks?: string;
  completed?: boolean;
  version?: number;
}

/**
 * 审核任务列表 - body 请求参数
 */
export interface IApiAuditTaskPageParams {
  pageNumber?: number;
  pageSize?: number;
  /** 录入方式
0-转录任务 1-OCR任务 */
  taskMethod?: number;
  /** 任务状态
0 待接单 1已接单 2已完成 3处理中 4已释放 5已提交 6已驳回 */
  taskStatus?: number;
  /** 完成开始时间 */
  completeStartTime?: number;
  /** 完成结束时间 */
  completeEndTime?: number;
  /** 当前用户ID */
  userId?: number;
  /** 当前用户类型 */
  userType?: string;
}

/**
 * 患者ID
 */
export interface IApiAuditTaskPageContentsPatientInfo {
  /** 患者ID */
  patientId?: number;
  /** 患者姓名 */
  patientName?: string;
  /** 患者性别 */
  patientSex?: number;
  /** 患者年龄 */
  patientAge?: number;
}

/**
 *
 */
export interface IApiAuditTaskPageContents {
  /** 任务ID */
  taskId?: number;
  /** 患者ID */
  patientInfo?: IApiAuditTaskPageContentsPatientInfo;
  /** 任务类型 0住院 1门诊 2复查 3入组 */
  taskType?: number;
  /** 任务状态 0待接单 1进行中 2已完成 3处理中 4已释放 5已提交 6已驳回 */
  taskStatus?: number;
  /** 状态变更时间 */
  statusTime?: number;
  /** 来源时间 */
  sourceTime?: number;
  /** 来源id */
  sourceId?: number;
  /** 录入方式 0手动 1自动 */
  taskMethod?: number;
  /** 生成人id */
  generatedName?: string;
  /** 实习生 */
  internName?: string;
  /** 状态变更时间 */
  statusModifyTime?: number;
  /** 创建时间 */
  createTime?: number;
  /** 图片数量 */
  imageCount?: number;
}

/**
 * 审核任务列表
 */
export interface IApiAuditTaskPage {
  total?: number;
  contents?: IApiAuditTaskPageContents[];
}

/**
 * 待审核任务数量查询 - body 请求参数
 */
export interface IApiAuditTaskWaitAuditTaskNumParams {
  data: string;
}

/**
 * 待审核任务数量查询
 */
export type IApiAuditTaskWaitAuditTaskNum = number;

/**
 * 撤销任务 - body 请求参数
 */
export interface IApiAuditTaskQuashTaskParams {
  /** 任务ID */
  taskId: number;
  /** 用户ID */
  userId?: number;
  /** 用户类型 */
  userType?: string;
}

/**
 * 撤销任务
 */
export type IApiAuditTaskQuashTask = boolean;

/**
 * 检验content - body 请求参数
 */
export interface IApiAuditTaskCheckTaskContentParams {
  taskId?: number;
}

/**
 * 检验content
 */
export type IApiAuditTaskCheckTaskContent = boolean;

/**
 * 病历任务查询 - body 请求参数
 */
export interface IApiAuditTaskCaseTaskStatusParams {
  /** 来源ID */
  sourceId: number;
  /** 任务类型
0住院 1门诊 2复查 3入组 */
  taskType: number;
  /** 任务方式 0手动 1自动 */
  taskMethod: number;
  /** 任务ID 实习生必传 */
  taskId?: number;
  /** 用户ID */
  userId?: number;
  /** 用户类型 */
  userType?: string;
}

/**
 * 病历任务查询
 */
export interface IApiAuditTaskCaseTaskStatus {
  /** 任务id */
  taskId?: number;
  /** 主任务id */
  mainId?: number;
  /** 任务段落 */
  taskSection?: string;
  /** 任务原因 */
  reason?: string;
  /** 备注 */
  remark?: string;
  /** 任务状态 0待接单 1进行中 2已完成 3处理中 4已释放 5已提交 6已驳回 */
  taskStatus?: number;
  /** 任务方式 */
  taskMethod?: number;
  /** 状态变更时间 */
  statusTime?: number;
  /** 生成人名称 */
  generatedName?: string;
  /** 实习生名称 */
  internName?: string;
  /** 提交时间 */
  submitTime?: number;
  /** 创建时间 */
  createTime?: number;
}

/**
 * 驳回任务 - body 请求参数
 */
export interface IApiAuditTaskRejectTaskParams {
  /** 任务ID */
  taskId: number;
  /** 用户ID */
  userId?: number;
  /** 用户类型 */
  userType?: string;
}

/**
 * 驳回任务
 */
export type IApiAuditTaskRejectTask = boolean;

/**
 * 验收通过 - body 请求参数
 */
export interface IApiAuditTaskCompletedAuditTaskParams {
  /** 任务ID */
  taskId: number;
  /** 用户ID */
  userId?: number;
  /** 用户类型 */
  userType?: string;
}

/**
 * 验收通过
 */
export type IApiAuditTaskCompletedAuditTask = boolean;

/**
 * 条件分页查询医生工作台今日列表 - body 请求参数
 */
export interface IApiAssistantCurrentPatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要危险因素 */
  isNeedDangerFactor?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要未入组 */
  isNeedUnAccess?: boolean;
  /** 是否需要未联系 */
  isNeedUnContact?: boolean;
  /** 是否需要续费 */
  isNeedRenew?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要超过30天未测血压 */
  isNeedBloodPressure?: boolean;
  /** 是否需要心衰易损期 */
  isNeedHeartFailurePeriod?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 患者标签
 */
export interface IApiAssistantCurrentPatientDataTagList {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 最新一次聊天信息
 */
export interface IApiAssistantCurrentPatientDataPatientChat {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 医生工作台今日列表数据
 */
export interface IApiAssistantCurrentPatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 关注状态 */
  isMarkPatient: boolean;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 患者标签 */
  tagList: IApiAssistantCurrentPatientDataTagList[];
  /** 是否确认入组 */
  isConfirmEnrollment: boolean;
  /** 是否易损期 */
  isVulnerablePhase?: boolean;
  /** 服务病种 */
  serviceDiseaseType?: string;
  /** 心衰等级 */
  heartFailureLevel?: string;
  /** 过期天数 */
  expireDays?: number;
  /** 最新一次聊天信息 */
  patientChat?: IApiAssistantCurrentPatientDataPatientChat;
}

/**
 * 条件分页查询医生工作台今日列表
 */
export interface IApiAssistantCurrentPatient {
  /** 数据总条数 */
  totals: number;
  /** 医生工作台今日列表数据 */
  data: IApiAssistantCurrentPatientData[];
}

/**
 * 条件分页查询医生工作台管理中列表 - body 请求参数
 */
export interface IApiAssistantManagePatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要危险因素 */
  isNeedDangerFactor?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要未入组 */
  isNeedUnAccess?: boolean;
  /** 是否需要未联系 */
  isNeedUnContact?: boolean;
  /** 是否需要续费 */
  isNeedRenew?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要超过30天未测血压 */
  isNeedBloodPressure?: boolean;
  /** 是否需要心衰易损期 */
  isNeedHeartFailurePeriod?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 患者标签
 */
export interface IApiAssistantManagePatientDataTagList {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 最新一次聊天信息
 */
export interface IApiAssistantManagePatientDataPatientChat {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 医生工作台今日列表数据
 */
export interface IApiAssistantManagePatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 关注状态 */
  isMarkPatient: boolean;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 患者标签 */
  tagList: IApiAssistantManagePatientDataTagList[];
  /** 是否确认入组 */
  isConfirmEnrollment: boolean;
  /** 是否易损期 */
  isVulnerablePhase?: boolean;
  /** 服务病种 */
  serviceDiseaseType?: string;
  /** 心衰等级 */
  heartFailureLevel?: string;
  /** 过期天数 */
  expireDays?: number;
  /** 最新一次聊天信息 */
  patientChat?: IApiAssistantManagePatientDataPatientChat;
}

/**
 * 条件分页查询医生工作台管理中列表
 */
export interface IApiAssistantManagePatient {
  /** 数据总条数 */
  totals: number;
  /** 医生工作台今日列表数据 */
  data: IApiAssistantManagePatientData[];
}

/**
 * 医生患者列表请求参数
 */
export interface IApiAssistantStatisticsNumParamsAsstParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要危险因素 */
  isNeedDangerFactor?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要未入组 */
  isNeedUnAccess?: boolean;
  /** 是否需要未联系 */
  isNeedUnContact?: boolean;
  /** 是否需要续费 */
  isNeedRenew?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要超过30天未测血压 */
  isNeedBloodPressure?: boolean;
  /** 是否需要心衰易损期 */
  isNeedHeartFailurePeriod?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 健管师患者列表请求参数
 */
export interface IApiAssistantStatisticsNumParamsCusParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要心衰易损期 */
  isNeedHeartFailurePeriod?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 康复师患者列表请求参数
 */
export interface IApiAssistantStatisticsNumParamsRehabParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要康复师管理状态 */
  isNeedRehabManageStatus?: boolean;
  /** 是否需要入组日期 */
  isNeedEnrollmentDate?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 康复师管理状态（1 待管理、2 管理中、3 结束管理、4 管理中断） */
  manageStatus?: number;
  /** 开始入组日期 */
  enrollmentStartDate?: number;
  /** 结束入组日期 */
  enrollmentEndDate?: number;
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 管理评估列表请求参数
 */
export interface IApiAssistantStatisticsNumParamsMaParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要康复师风险等级 */
  isNeedRehabRiskLevel?: boolean;
  /** 是否需要康复师管理评估日期 */
  isNeedAssessmentDate?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 风险等级（0 未知、1 低危、2 中危、3 高危） */
  riskLevel?: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 随访列表请求参数
 */
export interface IApiAssistantStatisticsNumParamsFuParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要七日内过期 */
  isNeedAboutExpire?: boolean;
  /** 是否需要科研对照 */
  isNeedScientificCompare?: boolean;
  /** 是否需要到期续管 */
  isNeedExpireRenew?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要症状随访 */
  isNeedSymptomFollowUp?: boolean;
  /** 是否需要生活方式随访 */
  isNeedLifestyleFollowUp?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 复查列表请求参数
 */
export interface IApiAssistantStatisticsNumParamsReviewParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要未上传 */
  isNeedNotUploaded?: boolean;
  /** 是否需要已上传 */
  isNeedUploaded?: boolean;
  /** 是否需要将失效 */
  isNeedExpire?: boolean;
  /** 是否需要科研对照 */
  isNeedScientificCompare?: boolean;
  /** 是否需要到期续管 */
  isNeedExpireRenew?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型(5：医生，3：健管师，9：康复师) */
  userType?: number;
}

/**
 * 统计医生患者列表和复查列表总数 - body 请求参数
 */
export interface IApiAssistantStatisticsNumParams {
  /** 医生患者列表请求参数 */
  asstParam?: IApiAssistantStatisticsNumParamsAsstParam;
  /** 健管师患者列表请求参数 */
  cusParam?: IApiAssistantStatisticsNumParamsCusParam;
  /** 康复师患者列表请求参数 */
  rehabParam?: IApiAssistantStatisticsNumParamsRehabParam;
  /** 管理评估列表请求参数 */
  maParam?: IApiAssistantStatisticsNumParamsMaParam;
  /** 随访列表请求参数 */
  fuParam?: IApiAssistantStatisticsNumParamsFuParam;
  /** 复查列表请求参数 */
  reviewParam?: IApiAssistantStatisticsNumParamsReviewParam;
}

/**
 * 统计医生患者列表和复查列表总数
 */
export interface IApiAssistantStatisticsNum {
  /** 患者列表统计总数 */
  patientListNum?: number;
  /** 复查列表统计总数 */
  reviewListNum?: number;
  /** 随访列表统计总数 */
  followUpListNum?: number;
  /** 管理评估列表统计总数 */
  manageAssessmentListNum?: number;
}

/**
 * 根据群聊编号和模糊文本查询聊天列表详情 - body 请求参数
 */
export interface IApiPatientChatListDetailParams {
  /** 群聊编号 */
  teamNumber: string;
  /** 模糊文本 */
  patientChat?: string;
  /** 消息类型
文本消息：TEXT
图片消息：PICTURE
语音消息：AUDIO
视频消息：VIDEO
地理位置消息：LOCATION
群通知消息，如群资料更新通知、群解散通知等：NOTIFICATION
文件消息：FILE
提示消息：TIPS
自定义消息：CUSTOM */
  recordTypes?: string[];
  /** 页码 */
  pageNumber?: number;
  /** 页大小 */
  pageSize?: number;
}

/**
 * 患者聊天相关信息
 */
export interface IApiPatientChatListDetailPatientChats {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 根据群聊编号和模糊文本查询聊天列表详情
 */
export interface IApiPatientChatListDetail {
  /** 患者id */
  patientId?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 专家名字 */
  doctorName?: string;
  /** 总聊天数 */
  totalPatientChats?: number;
  /** 患者聊天相关信息 */
  patientChats?: IApiPatientChatListDetailPatientChats[];
}

/**
 * 模糊分页查询患者姓名 - body 请求参数
 */
export interface IApiPatientInfoParams {
  pageNumber?: number;
  pageSize?: number;
  /** 患者姓名 */
  patientName?: string;
}

/**
 * 患者姓名
 */
export interface IApiPatientInfoData {
  /** 患者id */
  patientId?: number;
  /** 查询类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 医患群聊编号 */
  teamNumber: string;
}

/**
 * 模糊分页查询患者姓名
 */
export interface IApiPatientInfo {
  /** 数据总条数 */
  totals: number;
  /** 患者姓名 */
  data: IApiPatientInfoData[];
}

/**
 * 模糊分页查询患者标签 - body 请求参数
 */
export interface IApiPatientTagParams {
  pageNumber?: number;
  pageSize?: number;
  /** 患者标签 */
  patientTag?: string;
}

/**
 * 患者标签列表
 */
export interface IApiPatientTagDataPatientTags {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 患者标签信息
 */
export interface IApiPatientTagData {
  /** 患者id */
  patientId?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 患者标签列表 */
  patientTags?: IApiPatientTagDataPatientTags[];
}

/**
 * 模糊分页查询患者标签
 */
export interface IApiPatientTag {
  /** 数据总条数 */
  totals: number;
  /** 患者标签信息 */
  data: IApiPatientTagData[];
}

/**
 * 模糊分页查询患者电话号码 - body 请求参数
 */
export interface IApiPatientPhoneParams {
  pageNumber?: number;
  pageSize?: number;
  /** 患者手机号 */
  patientPhone?: string;
}

/**
 * 患者电话号码
 */
export interface IApiPatientPhoneDataPatientPhones {
  /** 匹配患者姓名 */
  name?: string;
  /** 匹配患者手机号 */
  phone?: string;
  /** 患者关系 */
  relation?: string;
}

/**
 * 患者电话号码
 */
export interface IApiPatientPhoneData {
  /** 患者id */
  patientId?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 患者电话号码 */
  patientPhones: IApiPatientPhoneDataPatientPhones[];
}

/**
 * 模糊分页查询患者电话号码
 */
export interface IApiPatientPhone {
  /** 数据总条数 */
  totals: number;
  /** 患者电话号码 */
  data: IApiPatientPhoneData[];
}

/**
 * 模糊分页查询患者聊天记录 - body 请求参数
 */
export interface IApiPatientChatParams {
  pageNumber?: number;
  pageSize?: number;
  /** 患者聊天记录 */
  patientChat?: string;
}

/**
 * 患者聊天相关信息
 */
export interface IApiPatientChatDataPatientChats {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 患者聊天记录
 */
export interface IApiPatientChatData {
  /** 患者id */
  patientId?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 专家名字 */
  doctorName?: string;
  /** 总聊天数 */
  totalPatientChats?: number;
  /** 患者聊天相关信息 */
  patientChats?: IApiPatientChatDataPatientChats[];
}

/**
 * 模糊分页查询患者聊天记录
 */
export interface IApiPatientChat {
  /** 数据总条数 */
  totals: number;
  /** 患者聊天记录 */
  data: IApiPatientChatData[];
}

/**
 * 模糊查询患者姓名、标签、聊天记录、电话号码 - body 请求参数
 */
export interface IApiPatientFullParams {
  pageNumber?: number;
  pageSize?: number;
  /** 查询文本 */
  queryText?: string;
}

/**
 * 患者姓名
 */
export interface IApiPatientFullInfoData {
  /** 患者id */
  patientId?: number;
  /** 查询类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 医患群聊编号 */
  teamNumber: string;
}

/**
 * 患者姓名
 */
export interface IApiPatientFullInfo {
  /** 数据总条数 */
  totals: number;
  /** 患者姓名 */
  data: IApiPatientFullInfoData[];
}

/**
 * 患者电话号码
 */
export interface IApiPatientFullPhoneDataPatientPhones {
  /** 匹配患者姓名 */
  name?: string;
  /** 匹配患者手机号 */
  phone?: string;
  /** 患者关系 */
  relation?: string;
}

/**
 * 患者电话号码
 */
export interface IApiPatientFullPhoneData {
  /** 患者id */
  patientId?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 患者电话号码 */
  patientPhones: IApiPatientFullPhoneDataPatientPhones[];
}

/**
 * 患者电话号码
 */
export interface IApiPatientFullPhone {
  /** 数据总条数 */
  totals: number;
  /** 患者电话号码 */
  data: IApiPatientFullPhoneData[];
}

/**
 * 患者标签列表
 */
export interface IApiPatientFullTagDataPatientTags {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 患者标签信息
 */
export interface IApiPatientFullTagData {
  /** 患者id */
  patientId?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 患者标签列表 */
  patientTags?: IApiPatientFullTagDataPatientTags[];
}

/**
 * 患者标签
 */
export interface IApiPatientFullTag {
  /** 数据总条数 */
  totals: number;
  /** 患者标签信息 */
  data: IApiPatientFullTagData[];
}

/**
 * 患者聊天相关信息
 */
export interface IApiPatientFullChatDataPatientChats {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 患者聊天记录
 */
export interface IApiPatientFullChatData {
  /** 患者id */
  patientId?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 专家名字 */
  doctorName?: string;
  /** 总聊天数 */
  totalPatientChats?: number;
  /** 患者聊天相关信息 */
  patientChats?: IApiPatientFullChatDataPatientChats[];
}

/**
 * 患者聊天记录
 */
export interface IApiPatientFullChat {
  /** 数据总条数 */
  totals: number;
  /** 患者聊天记录 */
  data: IApiPatientFullChatData[];
}

/**
 * 模糊查询患者姓名、标签、聊天记录、电话号码
 */
export interface IApiPatientFull {
  /** 患者姓名 */
  info: IApiPatientFullInfo;
  /** 患者电话号码 */
  phone: IApiPatientFullPhone;
  /** 患者标签 */
  tag: IApiPatientFullTag;
  /** 患者聊天记录 */
  chat: IApiPatientFullChat;
}

/**
 * 条件分页查询健管师工作台今日列表 - body 请求参数
 */
export interface IApiCustomerCurrentPatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要心衰易损期 */
  isNeedHeartFailurePeriod?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
  /** 是否需要超过30天未测血压 */
  isNeedBloodPressure?: boolean;
  /** 是否需要未联系 */
  isNeedUnContact?: boolean;
}

/**
 * 患者群聊信息
 */
export interface IApiCustomerCurrentPatientDataTeamBaseInfos {
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
}

/**
 * 患者标签
 */
export interface IApiCustomerCurrentPatientDataTagList {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 最新一次聊天信息
 */
export interface IApiCustomerCurrentPatientDataPatientChat {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 健管师患者列表数据
 */
export interface IApiCustomerCurrentPatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 服务病种 */
  serviceDiseaseType?: string;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 患者群聊信息 */
  teamBaseInfos?: IApiCustomerCurrentPatientDataTeamBaseInfos[];
  /** 关注状态 */
  isMarkPatient: boolean;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 是否易损期 */
  isVulnerablePhase?: boolean;
  /** 心衰等级 */
  heartFailureLevel?: string;
  /** 患者标签 */
  tagList: IApiCustomerCurrentPatientDataTagList[];
  /** 最新一次聊天信息 */
  patientChat?: IApiCustomerCurrentPatientDataPatientChat;
}

/**
 * 条件分页查询健管师工作台今日列表
 */
export interface IApiCustomerCurrentPatient {
  /** 数据总条数 */
  totals: number;
  /** 健管师患者列表数据 */
  data: IApiCustomerCurrentPatientData[];
}

/**
 * 条件分页查询健管师工作台团队列表 - body 请求参数
 */
export interface IApiCustomerManagePatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要心衰易损期 */
  isNeedHeartFailurePeriod?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
  /** 是否需要超过30天未测血压 */
  isNeedBloodPressure?: boolean;
  /** 是否需要未联系 */
  isNeedUnContact?: boolean;
}

/**
 * 患者群聊信息
 */
export interface IApiCustomerManagePatientDataTeamBaseInfos {
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
}

/**
 * 患者标签
 */
export interface IApiCustomerManagePatientDataTagList {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 最新一次聊天信息
 */
export interface IApiCustomerManagePatientDataPatientChat {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 健管师患者列表数据
 */
export interface IApiCustomerManagePatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 服务病种 */
  serviceDiseaseType?: string;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 患者群聊信息 */
  teamBaseInfos?: IApiCustomerManagePatientDataTeamBaseInfos[];
  /** 关注状态 */
  isMarkPatient: boolean;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 是否易损期 */
  isVulnerablePhase?: boolean;
  /** 心衰等级 */
  heartFailureLevel?: string;
  /** 患者标签 */
  tagList: IApiCustomerManagePatientDataTagList[];
  /** 最新一次聊天信息 */
  patientChat?: IApiCustomerManagePatientDataPatientChat;
}

/**
 * 条件分页查询健管师工作台团队列表
 */
export interface IApiCustomerManagePatient {
  /** 数据总条数 */
  totals: number;
  /** 健管师患者列表数据 */
  data: IApiCustomerManagePatientData[];
}

/**
 * 医生患者列表请求参数
 */
export interface IApiCustomerStatisticsNumParamsAsstParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要危险因素 */
  isNeedDangerFactor?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要未入组 */
  isNeedUnAccess?: boolean;
  /** 是否需要未联系 */
  isNeedUnContact?: boolean;
  /** 是否需要续费 */
  isNeedRenew?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要超过30天未测血压 */
  isNeedBloodPressure?: boolean;
  /** 是否需要心衰易损期 */
  isNeedHeartFailurePeriod?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 健管师患者列表请求参数
 */
export interface IApiCustomerStatisticsNumParamsCusParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要心衰易损期 */
  isNeedHeartFailurePeriod?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
  /** 是否需要超过30天未测血压 */
  isNeedBloodPressure?: boolean;
  /** 是否需要未联系 */
  isNeedUnContact?: boolean;
}

/**
 * 康复师患者列表请求参数
 */
export interface IApiCustomerStatisticsNumParamsRehabParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要康复师管理状态 */
  isNeedRehabManageStatus?: boolean;
  /** 是否需要入组日期 */
  isNeedEnrollmentDate?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 康复师管理状态（1 待管理、2 管理中、3 结束管理、4 管理中断） */
  manageStatus?: number;
  /** 开始入组日期 */
  enrollmentStartDate?: number;
  /** 结束入组日期 */
  enrollmentEndDate?: number;
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 管理评估列表请求参数
 */
export interface IApiCustomerStatisticsNumParamsMaParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要康复师风险等级 */
  isNeedRehabRiskLevel?: boolean;
  /** 是否需要康复师管理评估日期 */
  isNeedAssessmentDate?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 风险等级（0 未知、1 低危、2 中危、3 高危） */
  riskLevel?: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 随访列表请求参数
 */
export interface IApiCustomerStatisticsNumParamsFuParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要七日内过期 */
  isNeedAboutExpire?: boolean;
  /** 是否需要科研对照 */
  isNeedScientificCompare?: boolean;
  /** 是否需要到期续管 */
  isNeedExpireRenew?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要症状随访 */
  isNeedSymptomFollowUp?: boolean;
  /** 是否需要生活方式随访 */
  isNeedLifestyleFollowUp?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 复查列表请求参数
 */
export interface IApiCustomerStatisticsNumParamsReviewParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要未上传 */
  isNeedNotUploaded?: boolean;
  /** 是否需要已上传 */
  isNeedUploaded?: boolean;
  /** 是否需要将失效 */
  isNeedExpire?: boolean;
  /** 是否需要科研对照 */
  isNeedScientificCompare?: boolean;
  /** 是否需要到期续管 */
  isNeedExpireRenew?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型(5：医生，3：健管师，9：康复师) */
  userType?: number;
}

/**
 * 统计健管师患者列表和随访列表总数 - body 请求参数
 */
export interface IApiCustomerStatisticsNumParams {
  /** 医生患者列表请求参数 */
  asstParam?: IApiCustomerStatisticsNumParamsAsstParam;
  /** 健管师患者列表请求参数 */
  cusParam?: IApiCustomerStatisticsNumParamsCusParam;
  /** 康复师患者列表请求参数 */
  rehabParam?: IApiCustomerStatisticsNumParamsRehabParam;
  /** 管理评估列表请求参数 */
  maParam?: IApiCustomerStatisticsNumParamsMaParam;
  /** 随访列表请求参数 */
  fuParam?: IApiCustomerStatisticsNumParamsFuParam;
  /** 复查列表请求参数 */
  reviewParam?: IApiCustomerStatisticsNumParamsReviewParam;
}

/**
 * 统计健管师患者列表和随访列表总数
 */
export interface IApiCustomerStatisticsNum {
  /** 患者列表统计总数 */
  patientListNum?: number;
  /** 复查列表统计总数 */
  reviewListNum?: number;
  /** 随访列表统计总数 */
  followUpListNum?: number;
  /** 管理评估列表统计总数 */
  manageAssessmentListNum?: number;
}

/**
 * 条件分页查询康复师工作台今日列表 - body 请求参数
 */
export interface IApiRehabilitationCurrentPatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要康复师管理状态 */
  isNeedRehabManageStatus?: boolean;
  /** 是否需要入组日期 */
  isNeedEnrollmentDate?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 康复师管理状态（1 待管理、2 管理中、3 结束管理、4 管理中断） */
  manageStatus?: number;
  /** 开始入组日期 */
  enrollmentStartDate?: number;
  /** 结束入组日期 */
  enrollmentEndDate?: number;
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 患者标签
 */
export interface IApiRehabilitationCurrentPatientDataTagList {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 最新一次聊天信息
 */
export interface IApiRehabilitationCurrentPatientDataPatientChat {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 康复师患者列表数据
 */
export interface IApiRehabilitationCurrentPatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 关注状态 */
  isMarkPatient: boolean;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 患者标签 */
  tagList: IApiRehabilitationCurrentPatientDataTagList[];
  /** 最新一次聊天信息 */
  patientChat?: IApiRehabilitationCurrentPatientDataPatientChat;
  /** 管理状态 （1 待管理、2 管理中、3 结束管理、4 管理中断） */
  manageStatus: number;
  /** 风险等级 （0 未知、1 低危、2 中危、3 高危） */
  riskLevel: number;
}

/**
 * 条件分页查询康复师工作台今日列表
 */
export interface IApiRehabilitationCurrentPatient {
  /** 数据总条数 */
  totals: number;
  /** 康复师患者列表数据 */
  data: IApiRehabilitationCurrentPatientData[];
}

/**
 * 条件分页查询康复师工作台团队列表 - body 请求参数
 */
export interface IApiRehabilitationManagePatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要康复师管理状态 */
  isNeedRehabManageStatus?: boolean;
  /** 是否需要入组日期 */
  isNeedEnrollmentDate?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 康复师管理状态（1 待管理、2 管理中、3 结束管理、4 管理中断） */
  manageStatus?: number;
  /** 开始入组日期 */
  enrollmentStartDate?: number;
  /** 结束入组日期 */
  enrollmentEndDate?: number;
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 患者标签
 */
export interface IApiRehabilitationManagePatientDataTagList {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 最新一次聊天信息
 */
export interface IApiRehabilitationManagePatientDataPatientChat {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 康复师患者列表数据
 */
export interface IApiRehabilitationManagePatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 关注状态 */
  isMarkPatient: boolean;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 患者标签 */
  tagList: IApiRehabilitationManagePatientDataTagList[];
  /** 最新一次聊天信息 */
  patientChat?: IApiRehabilitationManagePatientDataPatientChat;
  /** 管理状态 （1 待管理、2 管理中、3 结束管理、4 管理中断） */
  manageStatus: number;
  /** 风险等级 （0 未知、1 低危、2 中危、3 高危） */
  riskLevel: number;
}

/**
 * 条件分页查询康复师工作台团队列表
 */
export interface IApiRehabilitationManagePatient {
  /** 数据总条数 */
  totals: number;
  /** 康复师患者列表数据 */
  data: IApiRehabilitationManagePatientData[];
}

/**
 * 条件分页查询康复师工作台管理评估列表 - body 请求参数
 */
export interface IApiRehabilitationManageAssessmentPatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要康复师风险等级 */
  isNeedRehabRiskLevel?: boolean;
  /** 是否需要康复师管理评估日期 */
  isNeedAssessmentDate?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 风险等级（0 未知、1 低危、2 中危、3 高危） */
  riskLevel?: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 患者标签
 */
export interface IApiRehabilitationManageAssessmentPatientDataTagList {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 管理评估列表数据
 */
export interface IApiRehabilitationManageAssessmentPatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 患者标签 */
  tagList?: IApiRehabilitationManageAssessmentPatientDataTagList[];
  /** 风险等级 （0 未知、1 低危、2 中危、3 高危） */
  riskLevel: number;
  /** 管理状态 （1 待管理、2 管理中、3 结束管理、4 管理中断） */
  manageStatus: number;
}

/**
 * 条件分页查询康复师工作台管理评估列表
 */
export interface IApiRehabilitationManageAssessmentPatient {
  /** 数据总条数 */
  totals: number;
  /** 管理评估列表数据 */
  data: IApiRehabilitationManageAssessmentPatientData[];
}

/**
 * 查询患者运动康复管理状态、风险等级 - body 请求参数
 */
export interface IApiRehabilitationManageStatusParams {
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userType?: number;
  /** 患者id列表 */
  patientIds: number[];
}

/**
 *
 */
export interface IApiRehabilitationManageStatusItem {
  /** 患者id */
  patientId?: number;
  /** 管理状态 （1 待管理、2 管理中、3 结束管理、4 管理中断） */
  manageStatus?: number;
  /** 风险等级 （0 未知、1 低危、2 中危、3 高危） */
  riskLevel?: number;
}

/**
 * 查询患者运动康复管理状态、风险等级
 */
export type IApiRehabilitationManageStatus =
  IApiRehabilitationManageStatusItem[];

/**
 * 医生患者列表请求参数
 */
export interface IApiRehabilitationStatisticsNumParamsAsstParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要危险因素 */
  isNeedDangerFactor?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要未入组 */
  isNeedUnAccess?: boolean;
  /** 是否需要未联系 */
  isNeedUnContact?: boolean;
  /** 是否需要续费 */
  isNeedRenew?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要超过30天未测血压 */
  isNeedBloodPressure?: boolean;
  /** 是否需要心衰易损期 */
  isNeedHeartFailurePeriod?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 健管师患者列表请求参数
 */
export interface IApiRehabilitationStatisticsNumParamsCusParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要心衰易损期 */
  isNeedHeartFailurePeriod?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 康复师患者列表请求参数
 */
export interface IApiRehabilitationStatisticsNumParamsRehabParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要今日待办 */
  isNeedCurrentTodo?: boolean;
  /** 是否需要重点关注 */
  isNeedMarkPatient?: boolean;
  /** 是否需要待续费 */
  isNeedPendingFee?: boolean;
  /** 是否需要康复师管理状态 */
  isNeedRehabManageStatus?: boolean;
  /** 是否需要入组日期 */
  isNeedEnrollmentDate?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 康复师管理状态（1 待管理、2 管理中、3 结束管理、4 管理中断） */
  manageStatus?: number;
  /** 开始入组日期 */
  enrollmentStartDate?: number;
  /** 结束入组日期 */
  enrollmentEndDate?: number;
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 管理评估列表请求参数
 */
export interface IApiRehabilitationStatisticsNumParamsMaParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要康复师风险等级 */
  isNeedRehabRiskLevel?: boolean;
  /** 是否需要康复师管理评估日期 */
  isNeedAssessmentDate?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 风险等级（0 未知、1 低危、2 中危、3 高危） */
  riskLevel?: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 随访列表请求参数
 */
export interface IApiRehabilitationStatisticsNumParamsFuParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要七日内过期 */
  isNeedAboutExpire?: boolean;
  /** 是否需要科研对照 */
  isNeedScientificCompare?: boolean;
  /** 是否需要到期续管 */
  isNeedExpireRenew?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要症状随访 */
  isNeedSymptomFollowUp?: boolean;
  /** 是否需要生活方式随访 */
  isNeedLifestyleFollowUp?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 复查列表请求参数
 */
export interface IApiRehabilitationStatisticsNumParamsReviewParam {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要未上传 */
  isNeedNotUploaded?: boolean;
  /** 是否需要已上传 */
  isNeedUploaded?: boolean;
  /** 是否需要将失效 */
  isNeedExpire?: boolean;
  /** 是否需要科研对照 */
  isNeedScientificCompare?: boolean;
  /** 是否需要到期续管 */
  isNeedExpireRenew?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型(5：医生，3：健管师，9：康复师) */
  userType?: number;
}

/**
 * 统计康复师患者列表和管理评估列表总数 - body 请求参数
 */
export interface IApiRehabilitationStatisticsNumParams {
  /** 医生患者列表请求参数 */
  asstParam?: IApiRehabilitationStatisticsNumParamsAsstParam;
  /** 健管师患者列表请求参数 */
  cusParam?: IApiRehabilitationStatisticsNumParamsCusParam;
  /** 康复师患者列表请求参数 */
  rehabParam?: IApiRehabilitationStatisticsNumParamsRehabParam;
  /** 管理评估列表请求参数 */
  maParam?: IApiRehabilitationStatisticsNumParamsMaParam;
  /** 随访列表请求参数 */
  fuParam?: IApiRehabilitationStatisticsNumParamsFuParam;
  /** 复查列表请求参数 */
  reviewParam?: IApiRehabilitationStatisticsNumParamsReviewParam;
}

/**
 * 统计康复师患者列表和管理评估列表总数
 */
export interface IApiRehabilitationStatisticsNum {
  /** 患者列表统计总数 */
  patientListNum?: number;
  /** 复查列表统计总数 */
  reviewListNum?: number;
  /** 随访列表统计总数 */
  followUpListNum?: number;
  /** 管理评估列表统计总数 */
  manageAssessmentListNum?: number;
}

/**
 * 条件分页查询健管师工作台近期随访已催办列表 - body 请求参数
 */
export interface IApiFollowUpUrgedPatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要七日内过期 */
  isNeedAboutExpire?: boolean;
  /** 是否需要科研对照 */
  isNeedScientificCompare?: boolean;
  /** 是否需要到期续管 */
  isNeedExpireRenew?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要症状随访 */
  isNeedSymptomFollowUp?: boolean;
  /** 是否需要生活方式随访 */
  isNeedLifestyleFollowUp?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 患者随访信息
 */
export interface IApiFollowUpUrgedPatientDataPatientFollowUp {
  /** 随访id */
  followUpId?: number;
  /** 随访日期 */
  followUpDate?: number;
  /** 随访类型
1：症状随访
2：生活方式随访 */
  followUpType?: number;
}

/**
 * 患者标签
 */
export interface IApiFollowUpUrgedPatientDataTagList {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 最新一次聊天信息
 */
export interface IApiFollowUpUrgedPatientDataPatientChat {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 近期随访数据
 */
export interface IApiFollowUpUrgedPatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 关注状态 */
  isMarkPatient: boolean;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 患者随访信息 */
  patientFollowUp: IApiFollowUpUrgedPatientDataPatientFollowUp;
  /** 患者标签 */
  tagList: IApiFollowUpUrgedPatientDataTagList[];
  /** 最新一次聊天信息 */
  patientChat?: IApiFollowUpUrgedPatientDataPatientChat;
  /** 剩余有效天数 */
  remainValidDays?: number;
}

/**
 * 条件分页查询健管师工作台近期随访已催办列表
 */
export interface IApiFollowUpUrgedPatient {
  /** 数据总条数 */
  totals: number;
  /** 近期随访数据 */
  data: IApiFollowUpUrgedPatientData[];
}

/**
 * 条件分页查询健管师工作台近期随访未催办列表 - body 请求参数
 */
export interface IApiFollowUpNotUrgedPatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要七日内过期 */
  isNeedAboutExpire?: boolean;
  /** 是否需要科研对照 */
  isNeedScientificCompare?: boolean;
  /** 是否需要到期续管 */
  isNeedExpireRenew?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 是否需要症状随访 */
  isNeedSymptomFollowUp?: boolean;
  /** 是否需要生活方式随访 */
  isNeedLifestyleFollowUp?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
}

/**
 * 患者随访信息
 */
export interface IApiFollowUpNotUrgedPatientDataPatientFollowUp {
  /** 随访id */
  followUpId?: number;
  /** 随访日期 */
  followUpDate?: number;
  /** 随访类型
1：症状随访
2：生活方式随访 */
  followUpType?: number;
}

/**
 * 患者标签
 */
export interface IApiFollowUpNotUrgedPatientDataTagList {
  /** 标签id */
  tagId?: number;
  /** 标签名称 */
  tagName?: string;
}

/**
 * 最新一次聊天信息
 */
export interface IApiFollowUpNotUrgedPatientDataPatientChat {
  /** 聊天详情信息 */
  chatDetail?: string;
  /** 聊天记录类型 */
  chatType?: string;
  /** 发送人名称 */
  sendUserName?: string;
  /** 发送人类型
 患者: PATIENT
 医生：HRT_EXPERT
 医助：HRT_DOCTOR
 健康管理师：HRT_HMD
 运动康复师：HRT_PT
 实习生：HRT_MEDICAL_INTERN */
  sendUserType?: string;
  /** 聊天消息id */
  chatId?: string;
  /** 聊天时间 */
  chatTime?: number;
}

/**
 * 近期随访数据
 */
export interface IApiFollowUpNotUrgedPatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 关注状态 */
  isMarkPatient: boolean;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 患者随访信息 */
  patientFollowUp: IApiFollowUpNotUrgedPatientDataPatientFollowUp;
  /** 患者标签 */
  tagList: IApiFollowUpNotUrgedPatientDataTagList[];
  /** 最新一次聊天信息 */
  patientChat?: IApiFollowUpNotUrgedPatientDataPatientChat;
  /** 剩余有效天数 */
  remainValidDays?: number;
}

/**
 * 条件分页查询健管师工作台近期随访未催办列表
 */
export interface IApiFollowUpNotUrgedPatient {
  /** 数据总条数 */
  totals: number;
  /** 近期随访数据 */
  data: IApiFollowUpNotUrgedPatientData[];
}

/**
 * 条件分页查询医生工作台近期复查已催办列表 - body 请求参数
 */
export interface IApiReviewUrgedPatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要未上传 */
  isNeedNotUploaded?: boolean;
  /** 是否需要已上传 */
  isNeedUploaded?: boolean;
  /** 是否需要将失效 */
  isNeedExpire?: boolean;
  /** 是否需要科研对照 */
  isNeedScientificCompare?: boolean;
  /** 是否需要到期续管 */
  isNeedExpireRenew?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型(5：医生，3：健管师，9：康复师) */
  userType?: number;
}

/**
 * 患者复查信息
 */
export interface IApiReviewUrgedPatientDataPatientReview {
  /** 复查id */
  reviewId?: number;
  /** 复查日期 */
  reviewDate?: number;
}

/**
 * 复查列表信息
 */
export interface IApiReviewUrgedPatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 关注状态 */
  isMarkPatient: boolean;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 患者复查信息 */
  patientReview: IApiReviewUrgedPatientDataPatientReview;
  /** 剩余有效天数 */
  remainValidDays?: number;
  /** 上传状态  1:已上传  0:未上传 */
  isUpload: boolean;
}

/**
 * 条件分页查询医生工作台近期复查已催办列表
 */
export interface IApiReviewUrgedPatient {
  /** 数据总条数 */
  totals: number;
  /** 复查列表信息 */
  data: IApiReviewUrgedPatientData[];
}

/**
 * 条件分页查询医生工作台近期复查未催办列表 - body 请求参数
 */
export interface IApiReviewNotUrgedPatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 是否需要未上传 */
  isNeedNotUploaded?: boolean;
  /** 是否需要已上传 */
  isNeedUploaded?: boolean;
  /** 是否需要将失效 */
  isNeedExpire?: boolean;
  /** 是否需要科研对照 */
  isNeedScientificCompare?: boolean;
  /** 是否需要到期续管 */
  isNeedExpireRenew?: boolean;
  /** 是否需要地区、医院、工作室 */
  isNeedRegHosGroup?: boolean;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 患者类型 */
  patientTypes?: number[];
  /** 病种 */
  diseaseTypes?: string[];
  /** 患者首次入组--开始入组时间 */
  orderEnrolStartTime?: number;
  /** 患者首次入组--结束入组时间 */
  orderEnrolEndTime?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型(5：医生，3：健管师，9：康复师) */
  userType?: number;
}

/**
 * 患者复查信息
 */
export interface IApiReviewNotUrgedPatientDataPatientReview {
  /** 复查id */
  reviewId?: number;
  /** 复查日期 */
  reviewDate?: number;
}

/**
 * 复查列表信息
 */
export interface IApiReviewNotUrgedPatientData {
  /** 患者id */
  patientId?: number;
  /** 患者id */
  patientType?: number;
  /** 数据类型 */
  searchType?: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者性别  1男  2女 */
  patientGender?: number;
  /** 患者年龄 */
  patientAge?: number;
  /** 群聊id */
  teamId?: string;
  /** 群聊类型
1为医助-医生-患者群聊
2为医助-患者群聊
3为患者-医生群聊
4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 关注状态 */
  isMarkPatient: boolean;
  /** 今日待办数 */
  currentTodoNum: number;
  /** 总待办数 */
  totalTodoNum: number;
  /** 患者复查信息 */
  patientReview: IApiReviewNotUrgedPatientDataPatientReview;
  /** 剩余有效天数 */
  remainValidDays?: number;
  /** 上传状态  1:已上传  0:未上传 */
  isUpload: boolean;
}

/**
 * 条件分页查询医生工作台近期复查未催办列表
 */
export interface IApiReviewNotUrgedPatient {
  /** 数据总条数 */
  totals: number;
  /** 复查列表信息 */
  data: IApiReviewNotUrgedPatientData[];
}

/**
 * 临床事件保存、修改 - body 请求参数
 */
export interface IApiPatientInfoPreserveEventParams {
  /** 事件id */
  clinicalId?: number;
  /** 患者id */
  patientId: number;
  /** 事件类型 */
  clinicalType: number;
  /** 时间 */
  clinicalTime: number;
  /** 原因 */
  clinicalCause: string;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
  /** 附件 */
  accessory?: string[];
}

/**
 * 临床事件保存、修改
 */
export interface IApiPatientInfoPreserveEvent {}

/**
 * 临床事件列表 - body 请求参数
 */
export interface IApiPatientInfoListEventParams {
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiPatientInfoListEventItem {
  /** 事件id */
  clinicalId?: number;
  /** 患者id */
  patientId?: number;
  /** 事件类型 */
  clinicalType?: number;
  /** 时间 */
  clinicalTime?: number;
  /** 原因 */
  clinicalCause?: string;
  /** 操作时间 */
  modifyTime?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 附件 */
  accessory?: string[];
}

/**
 * 临床事件列表
 */
export type IApiPatientInfoListEvent = IApiPatientInfoListEventItem[];

/**
 * 删除临床事件 - body 请求参数
 */
export interface IApiPatientInfoDeleteEventParams {
  /** 临床事件id */
  clinicalId: number;
}

/**
 * 删除临床事件
 */
export interface IApiPatientInfoDeleteEvent {}

/**
 * 查询患者时间节点 - body 请求参数
 */
export interface IApiPatientInfoQueryPatientTimesNodeParams {
  /** 患者id */
  patientId: number;
}

/**
 * 查询患者时间节点
 */
export interface IApiPatientInfoQueryPatientTimesNode {
  /** 付费时间 */
  payTime?: number;
  /** 出院时间 */
  outHospitalTime?: number;
  /** 入院时间 */
  inHospitalTime?: number;
}

/**
 * 查询用户实名认证 - body 请求参数
 */
export interface IApiPatientInfoQueryPatientStatusParams {
  /** 患者id */
  patientId: number;
}

/**
 * 查询用户实名认证
 */
export type IApiPatientInfoQueryPatientStatus = number;

/**
 * 获取患者基本信息 - body 请求参数
 */
export interface IApiPatientInfoBaseParams {
  /** 患者id */
  patientId: number;
}

/**
 * 亲情账号
 */
export interface IApiPatientInfoBaseFamilyList {
  /** 姓名 */
  name?: string;
  /** 关系 */
  relation?: string;
  /** 手机号 */
  phone?: string;
}

/**
 * 获取患者基本信息
 */
export interface IApiPatientInfoBase {
  /** 患者id */
  patientId?: number;
  /** 患者名称 */
  patientName?: string;
  /** 性别 */
  gender?: number;
  /** 年龄 */
  age?: number;
  /** 工作室名称 */
  hospitalName?: string;
  /** 工作室名称 */
  groupName?: string;
  /** 加入日期 */
  joinDate?: number;
  /** 身份证号码 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 家庭地址 */
  detailAddress?: string;
  /** 亲情账号 */
  familyList?: IApiPatientInfoBaseFamilyList[];
  /** 已管理天数 */
  manageTime?: number;
  /** 剩余管理天数 */
  excessTime?: number;
  /** 患者备注 */
  remarks?: string;
  /** 患者实名认证（0未认证，1已认证） */
  realNameAuth?: number;
  /** 是否确认入组 0 未确认 1 已确认 */
  inGroup?: number;
  /** 患者状态（全） */
  currentState?: number;
  /** 科研患者关联科研项目id */
  scientificId?: number;
  /** 科研患者关联科研项目名称 */
  scientificName?: string;
}

/**
 * 获取患者的关注状态 - body 请求参数
 */
export interface IApiPatientInfoAttentionListParams {
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userType?: number;
  /** 患者id列表 */
  patientIds: number[];
}

/**
 * 获取患者的关注状态
 */
export interface IApiPatientInfoAttentionList {}

/**
 * 获取患者风险等级 - body 请求参数
 */
export interface IApiPatientInfoRiskGradeParams {
  /** 患者id */
  patientId: number;
}

/**
 * 预后风险
 */
export interface IApiPatientInfoRiskGradePrognosticRisk {
  /** 类型 1:急性ACS风险 2:复杂ACS风险 3:非瓣膜性房颤脑卒中风险 */
  type?: number;
  /** 风险等级 */
  riskLevel?: string;
  /** 分数 */
  score?: number;
}

/**
 * 出血风险
 */
export interface IApiPatientInfoRiskGradeBleedingRisk {
  /** 类型 1:ACS出血风险 2:房颤抗凝血风险 */
  type?: number;
  /** 风险等级 */
  riskLevel?: string;
  /** 分数 */
  score?: number;
}

/**
 * ascvd风险
 */
export interface IApiPatientInfoRiskGradeAscvdRisk {
  /** 风险等级 */
  riskLevel?: string;
  /** 风险数量 */
  riskNumber?: number;
}

/**
 * 死亡风险
 */
export interface IApiPatientInfoRiskGradeDeathRisk {
  /** 风险等级 */
  riskLevel?: string;
  /** 临床诊断 ["非ST段抬高型心梗","急性心肌梗死"] */
  clinicalDiagnosis?: string;
  /** 心电图 ["LVEF≤35%","室性心动过速≥1次","RonT≥1次"] */
  ecg?: string;
}

/**
 * 获取患者风险等级
 */
export interface IApiPatientInfoRiskGrade {
  /** 预后风险 */
  prognosticRisk?: IApiPatientInfoRiskGradePrognosticRisk[];
  /** 出血风险 */
  bleedingRisk?: IApiPatientInfoRiskGradeBleedingRisk[];
  /** ascvd风险 */
  ascvdRisk?: IApiPatientInfoRiskGradeAscvdRisk;
  /** 死亡风险 */
  deathRisk?: IApiPatientInfoRiskGradeDeathRisk;
  /** 心衰等级  1：低危 2：中危 3高危 */
  chfLevel?: number;
  /** NYHA分级 1：1级 2：2级 3：3级 4: 4级 */
  nyhaLevel?: number;
  /** lvef分级 */
  lvefLevel?: number;
  /** 是否易损期 true 是 false 否 */
  vvp?: boolean;
}

/**
 * 获取患者危险因素、依从性 - body 请求参数
 */
export interface IApiPatientInfoRiskFactorParams {
  /** 患者id */
  patientId: number;
}

/**
 * 依从性
 */
export interface IApiPatientInfoRiskFactorCompliance {
  /** 复查 */
  review?: number;
  /** 随访 */
  followUp?: number;
  /** 血压 */
  blood?: number;
}

/**
 * 获取患者危险因素、依从性
 */
export interface IApiPatientInfoRiskFactor {
  /** 预后风险 */
  highRisk?: string;
  /** 依从性 */
  compliance?: IApiPatientInfoRiskFactorCompliance;
}

/**
 * 获取患者临床诊断、既往史 - body 请求参数
 */
export interface IApiPatientInfoClinicalParams {
  /** 患者id */
  patientId: number;
}

/**
 * 获取患者临床诊断、既往史
 */
export interface IApiPatientInfoClinical {
  /** 临床诊断 ["a","b"] */
  clinicalDiagnosis?: string[];
  /** 既往史 ["a","b"] */
  previousHistory?: string[];
}

/**
 * 获取患者手术信息 - body 请求参数
 */
export interface IApiPatientInfoSurgeryParams {
  /** 患者id */
  patientId: number;
}

/**
 * 手术列表
 */
export interface IApiPatientInfoSurgerySurveyList {
  /** 手术结论 */
  conclusion?: string;
  /** 手术时间 */
  surgeryTime?: string;
}

/**
 * 获取患者手术信息
 */
export interface IApiPatientInfoSurgery {
  /** 手术列表 */
  surveyList?: IApiPatientInfoSurgerySurveyList[];
}

/**
 * 修改患者备注信息 - body 请求参数
 */
export interface IApiPatientInfoRemarksParams {
  /** 患者id */
  patientId: number;
  /** 备注信息 */
  remarks: string;
}

/**
 * 修改患者备注信息
 */
export interface IApiPatientInfoRemarks {}

/**
 * 关注或取消关注患者 - body 请求参数
 */
export interface IApiPatientInfoAttentionParams {
  /** 患者id */
  patientId: number;
  /** 状态 true 关注  false取消关注 */
  status: boolean;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: string;
}

/**
 * 关注或取消关注患者
 */
export interface IApiPatientInfoAttention {}

/**
 * 修改患者地址信息 - body 请求参数
 */
export interface IApiPatientInfoUpdateAddressParams {
  /** 患者id */
  patientId: number;
  /** 省份 */
  province: string;
  /** 城市 */
  city: string;
  /** 区县 */
  county: string;
  /** 详细地址 */
  address: string;
}

/**
 * 修改患者地址信息
 */
export interface IApiPatientInfoUpdateAddress {}

/**
 * 获取用户历史标签 - body 请求参数
 */
export interface IApiPatientInfoTagHistoryParams {}

/**
 * 获取用户历史标签
 */
export interface IApiPatientInfoTagHistory {
  /** 标签列表 ["a","b"] */
  tagList?: string[];
}

/**
 * 新增患者标签 - body 请求参数
 */
export interface IApiPatientInfoTagAddParams {
  /** 患者id */
  patientId: number;
  /** 标签名称 */
  tagName: string;
}

/**
 * 新增患者标签
 */
export interface IApiPatientInfoTagAdd {}

/**
 * 删除患者标签 - body 请求参数
 */
export interface IApiPatientInfoTagDeleteParams {
  /** 患者标签id */
  patientTagId: number;
}

/**
 * 删除患者标签
 */
export interface IApiPatientInfoTagDelete {}

/**
 * 获取患者标签列表 - body 请求参数
 */
export interface IApiPatientInfoTagListParams {
  /** 患者id */
  patientId: number;
}

/**
 * 标签列表
 */
export interface IApiPatientInfoTagListTagList {
  /** 患者标签id */
  patientTagId?: number;
  /** 标签名称 */
  tagName?: string;
  /** 添加人 */
  userName?: string;
  /** 创建时间 */
  createTime?: string;
}

/**
 * 获取患者标签列表
 */
export interface IApiPatientInfoTagList {
  /** 标签列表 */
  tagList?: IApiPatientInfoTagListTagList[];
}

/**
 * 获取系统标签列表 - body 请求参数
 */
export interface IApiPatientInfoTagSystemParams {
  /** 当前页码 */
  pageNumber?: number;
  /** 页面大小 */
  pageSize?: number;
  /** 关键词 */
  keyword?: string;
}

/**
 * 获取系统标签列表
 */
export interface IApiPatientInfoTagSystem {
  /** 数据总条数 */
  totals?: number;
  /** 标签名称 ["a","b"] */
  data?: string[];
}

/**
 * 12、动态心电图结论查询 - body 请求参数
 */
export interface IApiCaseHistoryEcgConclusionParams {
  data: string;
}

/**
 *
 */
export interface IApiCaseHistoryEcgConclusionItem {
  /** 主键 */
  id?: number;
  /** 类型 */
  type?: number;
  /** 名称 */
  name?: string;
  /** 选项类型 0多选，1单选 */
  chooseType?: number;
}

/**
 * 12、动态心电图结论查询
 */
export type IApiCaseHistoryEcgConclusion = IApiCaseHistoryEcgConclusionItem[];

/**
 * 12导联心电图、动态心电图、心脏彩超页面构建数据 - body 请求参数
 */
export interface IApiCaseHistoryFieldParams {
  /** 获取心电图类型 1:12导联；2：动态心电图；3：心脏彩超 */
  type: number;
  /** 患者id */
  userId: number;
  page?: number;
  pageSize?: number;
}

/**
 * 下级字段
 */
export interface IApiCaseHistoryFieldItemChildren {
  /** 主键 */
  id?: number;
  /** 心电图类型 1:12导联；2：动态心电图；3：心脏彩超 */
  ecgType?: number;
  /** 模块类型 1:心率;2:室性节律;3:室上性节律;4:房颤/房扑分析;5:起搏分析;6:心率变异;7:二维及M型测值;8:组织多普勒测值;9:收缩功能; */
  modelType?: number;
  /** 字段类型 0为数字，1为字符，2为其他 */
  fieldType?: number;
  /** 数字类型 0为整数;1为其他 */
  numType?: number;
  /** 选择类型 1:单选;2:input */
  chooseType?: number;
  /** 字段名称 */
  name?: string;
  /** 单位 */
  unit?: string;
  /** jsonKey */
  jsonKey?: string;
  /** 上级id */
  pid?: number;
  /** 子选项 [{"key":1,"value":"有"},{"key":0,"value":"无"}] */
  options?: string;
  /** 下级字段 */
  children?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryFieldItem {
  /** 主键 */
  id?: number;
  /** 心电图类型 1:12导联；2：动态心电图；3：心脏彩超 */
  ecgType?: number;
  /** 模块类型 1:心率;2:室性节律;3:室上性节律;4:房颤/房扑分析;5:起搏分析;6:心率变异;7:二维及M型测值;8:组织多普勒测值;9:收缩功能; */
  modelType?: number;
  /** 字段类型 0为数字，1为字符，2为其他 */
  fieldType?: number;
  /** 数字类型 0为整数;1为其他 */
  numType?: number;
  /** 选择类型 1:单选;2:input */
  chooseType?: number;
  /** 字段名称 */
  name?: string;
  /** 单位 */
  unit?: string;
  /** jsonKey */
  jsonKey?: string;
  /** 上级id */
  pid?: number;
  /** 子选项 [{"key":1,"value":"有"},{"key":0,"value":"无"}] */
  options?: string;
  /** 下级字段 */
  children?: IApiCaseHistoryFieldItemChildren[];
}

/**
 * 12导联心电图、动态心电图、心脏彩超页面构建数据
 */
export type IApiCaseHistoryField = IApiCaseHistoryFieldItem[];

/**
 * 12导联心电图详情查询 - body 请求参数
 */
export interface IApiCaseHistorySearchEcgLeadParams {
  /** 报告id */
  reportId?: number;
}

/**
 * 指标信息
 */
export interface IApiCaseHistorySearchEcgLeadLeadInfo {
  /** 心率 */
  heartRate?: string;
  /** PR间期 */
  pr?: string;
  /** QRS间期 */
  qrs?: string;
  /** RR间期 */
  rr?: string;
  /** QT */
  qt?: string;
  /** QTC */
  qtc?: string;
  /** RV5 */
  rv?: string;
  /** SV1 */
  sv?: string;
  /** Paxis */
  paxis?: string;
  /** P */
  p?: string;
  /** QRSaxis */
  qrSaxis?: string;
  /** RV5+SV1 */
  rvSv?: string;
  /** RV5/SV1 */
  svRv?: string;
}

/**
 * 结论集合
 */
export interface IApiCaseHistorySearchEcgLeadConclusions {
  /** 结论id */
  conclusionId?: number;
  /** 结论类型 */
  type?: number;
  /** 选择类型 */
  chooseType?: number;
  /** 上级id */
  pid?: number;
  /** 备注 */
  remark?: string;
  /** 结论名称 */
  name?: string;
}

/**
 * 12导联心电图详情查询
 */
export interface IApiCaseHistorySearchEcgLead {
  /** 检查时间 */
  checkTime?: string;
  /** 指标信息 */
  leadInfo?: IApiCaseHistorySearchEcgLeadLeadInfo;
  /** 附件 */
  accessory?: string[];
  /** 结论集合 */
  conclusions?: IApiCaseHistorySearchEcgLeadConclusions[];
}

/**
 * 时间
 */
export interface IApiCaseHistoryPreserveRecoveryParamsTime {
  /** 测试前 */
  ago?: string;
  /** 测试后 */
  after?: string;
}

/**
 * 心率
 */
export interface IApiCaseHistoryPreserveRecoveryParamsHeartRate {
  /** 测试前 */
  ago?: number;
  /** 测试后 */
  after?: number;
}

/**
 * 测试前
 */
export interface IApiCaseHistoryPreserveRecoveryParamsBloodPressureAgo {
  /** 收缩压 */
  systolic?: number;
  /** 舒张压 */
  diastolic?: number;
}

/**
 * 测试后
 */
export interface IApiCaseHistoryPreserveRecoveryParamsBloodPressureAfter {
  /** 收缩压 */
  systolic?: number;
  /** 舒张压 */
  diastolic?: number;
}

/**
 * 血压
 */
export interface IApiCaseHistoryPreserveRecoveryParamsBloodPressure {
  /** 测试前 */
  ago?: IApiCaseHistoryPreserveRecoveryParamsBloodPressureAgo;
  /** 测试后 */
  after?: IApiCaseHistoryPreserveRecoveryParamsBloodPressureAfter;
}

/**
 * 氧饱和度
 */
export interface IApiCaseHistoryPreserveRecoveryParamsSaturation {
  /** 测试前 */
  ago?: number;
  /** 测试后 */
  after?: number;
}

/**
 * 呼吸困难评分
 */
export interface IApiCaseHistoryPreserveRecoveryParamsScore {
  /** 测试前 */
  ago?: number;
  /** 测试后 */
  after?: number;
}

/**
 * 是否暂停
 */
export interface IApiCaseHistoryPreserveRecoveryParamsSuspend {
  /** 备注 */
  remark?: string;
  /** 是否暂停 */
  isSuspend?: number;
}

/**
 * 是否提前终止
 */
export interface IApiCaseHistoryPreserveRecoveryParamsTermination {
  /** 备注 */
  remark?: string;
  /** 是否提前终止 */
  isTermination?: number;
}

/**
 * 6MWT保存、修改 - body 请求参数
 */
export interface IApiCaseHistoryPreserveRecoveryParams {
  /** 报告id */
  reportId?: number;
  /** 来源类型 0住院；1门诊；2复查；3手动添加 */
  sourceType?: number;
  /** 来源id */
  sourceId?: number;
  /** 患者id */
  patientId?: number;
  /** 报告项id */
  indexTermId?: number;
  /** 报告名称 */
  reportName?: string;
  /** 用户id --前端不用传 */
  userId?: number;
  /** 用户类型 --前端不用传 */
  userType?: string;
  /** 试验id */
  testId?: number;
  /** 试验时间 */
  testTime: string;
  /** 步行距离 */
  distance?: number;
  /** 时间 */
  time?: IApiCaseHistoryPreserveRecoveryParamsTime;
  /** 心率 */
  heartRate?: IApiCaseHistoryPreserveRecoveryParamsHeartRate;
  /** 血压 */
  bloodPressure?: IApiCaseHistoryPreserveRecoveryParamsBloodPressure;
  /** 氧饱和度 */
  saturation?: IApiCaseHistoryPreserveRecoveryParamsSaturation;
  /** 呼吸困难评分 */
  score?: IApiCaseHistoryPreserveRecoveryParamsScore;
  /** 是否暂停 */
  suspend: IApiCaseHistoryPreserveRecoveryParamsSuspend;
  /** 是否提前终止 */
  termination: IApiCaseHistoryPreserveRecoveryParamsTermination;
  /** 试验中症状 */
  inSymptom?: string;
  /** 结束症状 */
  endSymptom?: string;
}

/**
 * 6MWT保存、修改
 */
export interface IApiCaseHistoryPreserveRecovery {
  /** 病历id */
  patientHistoryId?: number;
}

/**
 * 6MWT症状查询 - body 请求参数
 */
export interface IApiCaseHistoryTestSymptomParams {
  data: string;
}

/**
 *
 */
export interface IApiCaseHistoryTestSymptomItem {
  /** 症状id */
  id?: number;
  /** 症状名称 */
  name?: string;
}

/**
 * 6MWT症状查询
 */
export type IApiCaseHistoryTestSymptom = IApiCaseHistoryTestSymptomItem[];

/**
 * 6MWT详情查询 - body 请求参数
 */
export interface IApiCaseHistoryRecoveryDetailParams {
  /** 报告id */
  reportId: number;
}

/**
 * 时间
 */
export interface IApiCaseHistoryRecoveryDetailTime {
  /** 测试前 */
  ago?: string;
  /** 测试后 */
  after?: string;
}

/**
 * 心率
 */
export interface IApiCaseHistoryRecoveryDetailHeartRate {
  /** 测试前 */
  ago?: number;
  /** 测试后 */
  after?: number;
}

/**
 * 测试前
 */
export interface IApiCaseHistoryRecoveryDetailBloodPressureAgo {
  /** 收缩压 */
  systolic?: number;
  /** 舒张压 */
  diastolic?: number;
}

/**
 * 测试后
 */
export interface IApiCaseHistoryRecoveryDetailBloodPressureAfter {
  /** 收缩压 */
  systolic?: number;
  /** 舒张压 */
  diastolic?: number;
}

/**
 * 血压
 */
export interface IApiCaseHistoryRecoveryDetailBloodPressure {
  /** 测试前 */
  ago?: IApiCaseHistoryRecoveryDetailBloodPressureAgo;
  /** 测试后 */
  after?: IApiCaseHistoryRecoveryDetailBloodPressureAfter;
}

/**
 * 氧饱和度
 */
export interface IApiCaseHistoryRecoveryDetailSaturation {
  /** 测试前 */
  ago?: number;
  /** 测试后 */
  after?: number;
}

/**
 * 呼吸困难评分
 */
export interface IApiCaseHistoryRecoveryDetailScore {
  /** 测试前 */
  ago?: number;
  /** 测试后 */
  after?: number;
}

/**
 * 是否暂停
 */
export interface IApiCaseHistoryRecoveryDetailSuspend {
  /** 备注 */
  remark?: string;
  /** 是否暂停 */
  isSuspend?: number;
}

/**
 * 是否提前终止
 */
export interface IApiCaseHistoryRecoveryDetailTermination {
  /** 备注 */
  remark?: string;
  /** 是否提前终止 */
  isTermination?: number;
}

/**
 * 6MWT详情查询
 */
export interface IApiCaseHistoryRecoveryDetail {
  /** 试验id */
  testId?: number;
  /** 试验时间 */
  testTime?: string;
  /** 步行距离 */
  distance?: number;
  /** 时间 */
  time?: IApiCaseHistoryRecoveryDetailTime;
  /** 心率 */
  heartRate?: IApiCaseHistoryRecoveryDetailHeartRate;
  /** 血压 */
  bloodPressure?: IApiCaseHistoryRecoveryDetailBloodPressure;
  /** 氧饱和度 */
  saturation?: IApiCaseHistoryRecoveryDetailSaturation;
  /** 呼吸困难评分 */
  score?: IApiCaseHistoryRecoveryDetailScore;
  /** 是否暂停 */
  suspend?: IApiCaseHistoryRecoveryDetailSuspend;
  /** 是否提前终止 */
  termination?: IApiCaseHistoryRecoveryDetailTermination;
  /** 试验中症状 */
  inSymptom?: string;
  /** 结束症状 */
  endSymptom?: string;
}

/**
 * cta详情 - body 请求参数
 */
export interface IApiCaseHistoryCtaDetailParams {
  /** 报告id */
  reportId: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
}

/**
 * cta参数集合
 */
export interface IApiCaseHistoryCtaDetailAllCta {
  /** LM_左主干狭窄% */
  lm?: number;
  /** LAD_前降支狭窄% */
  lad?: number;
  /** RCA_右冠脉狭窄% */
  rca?: number;
  /** 置入大动脉支架 */
  stent?: number;
  /** 大动脉支架置入数量 */
  stents?: number;
  /** Debakey分型 */
  debakey?: number;
  /** IMH_壁间血肿 */
  imh?: number;
  /** TAA_胸主动脉瘤 */
  taa?: number;
  /** AAA_腹主动脉瘤 */
  aaa?: number;
  /** PAU_穿透性溃疡 */
  pau?: number;
  /** ULP_溃疡样凸起 */
  ulp?: number;
  /** 主动脉假性动脉瘤 */
  aortic?: number;
  /** 其他 */
  other?: string;
  /** 受累主动脉最宽处直径（mm） */
  aorticDirect?: number;
  /** 假腔血栓化情况 */
  thrombosis?: number;
  /** 椎动脉优势情况 */
  dominance?: number;
  /** 头臂动脉 */
  brachiocephalic?: number;
  /** 左颈总动脉 */
  leftCarotid?: number;
  /** 左锁骨下动脉 */
  leftSubclavianArtery?: number;
  /** 腹腔干动脉 */
  celiacTrunk?: number;
  /** 肠系膜上动脉 */
  coronaryArtery?: number;
  /** 左肾动脉 */
  leftRenalArtery?: number;
  /** 右肾动脉 */
  rightRenalArtery?: number;
  /** 肠系膜下动脉 */
  inferiorMesentericArtery?: number;
  /** 左髂总动脉 */
  leftCommon?: number;
  /** 左髂外动脉 */
  leftExternal?: number;
  /** 左髂内动脉 */
  leftInternal?: number;
  /** 右髂总动脉 */
  rightCommon?: number;
  /** 右髂外动脉 */
  rightExternal?: number;
  /** 右髂内动脉 */
  rightInternal?: number;
  /** 肝囊肿 */
  cystOfLiver?: number;
  /** 肾囊肿 */
  renalCyst?: number;
  /** 胸腔积液 */
  pleuralEffusion?: number;
  /** 心包积液 */
  hydropericardium?: number;
  /** 纵膈积液 */
  mediastinum?: number;
  /** 主动脉血管变异 */
  aorticVariation?: number;
  /** 主动脉CTA备注 */
  aorticRemark?: string;
}

/**
 * cta详情
 */
export interface IApiCaseHistoryCtaDetail {
  /** 检查时间 */
  checkTime?: number;
  /** cta类型 */
  ctaType?: number;
  /** cta参数集合 */
  allCta?: IApiCaseHistoryCtaDetailAllCta;
  /** 附件 */
  accessory?: string[];
}

/**
 * 主诉查询 - body 请求参数
 */
export interface IApiCaseHistorySuitParams {
  data: string;
}

/**
 *
 */
export interface IApiCaseHistorySuitItem {
  /** 症状id */
  symptomId?: number;
  /** 症状名称 */
  symptomName?: string;
}

/**
 * 主诉查询
 */
export type IApiCaseHistorySuit = IApiCaseHistorySuitItem[];

/**
 * 住院检查详情 - body 请求参数
 */
export interface IApiCaseHistorySearchDiagnosisReportParams {
  /** 病历id/复查id */
  sourceId?: number;
  /** 来源类型 0住院；1门诊；2复查；3手动添加 */
  sourceType?: number;
}

/**
 * 普通报告
 */
export interface IApiCaseHistorySearchDiagnosisReportItemCheckIndex {
  /** 指标名称 */
  name?: string;
  /** 指标小类 */
  type?: number;
  /** 指标大类 */
  pid?: number;
  /** 单位 */
  unit?: string;
  /** 指标值 */
  content?: number;
}

/**
 * 12导联心电图报告
 */
export interface IApiCaseHistorySearchDiagnosisReportItemEcgList {
  /** 心率 */
  heartRate?: string;
  /** PR间期 */
  pr?: string;
  /** QRS间期 */
  qrs?: string;
  /** RR间期 */
  rr?: string;
  /** QT */
  qt?: string;
  /** QTC */
  qtc?: string;
  /** RV5 */
  rv?: string;
  /** SV1 */
  sv?: string;
  /** Paxis */
  paxis?: string;
  /** P */
  p?: string;
  /** QRSaxis */
  qrSaxis?: string;
  /** RV5+SV1 */
  rvSv?: string;
  /** RV5/SV1 */
  svRv?: string;
}

/**
 * 分析总时长
 */
export interface IApiCaseHistorySearchDiagnosisReportItemDynamicListTotalTime {
  /** 时 */
  hour?: number;
  /** 分 */
  minute?: number;
  /** 秒 */
  second?: number;
}

/**
 * 心率
 */
export interface IApiCaseHistorySearchDiagnosisReportItemDynamicListHeartRate {
  /** 总心搏数 */
  cardiomegalyNum?: string;
  /** 最慢心率 */
  slowestHeartRate?: string;
  /** 最快心率 */
  fastestHeartRate?: string;
  /** 平均心率 */
  averageHeartRate?: string;
  /** RR间期>2s */
  rr?: string;
  /** 最长一次 */
  longest?: string;
}

/**
 * 室上性节律
 */
export interface IApiCaseHistorySearchDiagnosisReportItemDynamicListSupraventricularRhythm {
  /** 房早总数 */
  prematureEjaculationNum?: string;
  /** 房早占比 */
  proportion?: string;
  /** 单发 */
  singleShot?: string;
  /** 成对 */
  paired?: string;
  /** 二联律 */
  bipartiteRhythm?: string;
  /** 三联律 */
  trigeminalRhythm?: string;
  /** 房速总数 */
  atrialTachycardiaNum?: string;
  /** 房速逸搏 */
  atrialTachycardias?: string;
  /** 最快房速 */
  fastestAtrialVelocity?: string;
  /** 最长房速 */
  longestAtrialVelocity?: string;
  /** 交界性早搏 */
  junctional?: string;
  /** 成对交界性早搏 */
  pairedJunctional?: string;
  /** 交界性逸搏 */
  boundaryFree?: string;
  /** 交界性心动过速 */
  junctionalTachycardia?: string;
  /** 房早未下传总数 */
  untransmittedAtriaNum?: string;
  /** 最长R-R */
  longestR?: string;
}

/**
 * 室性节律
 */
export interface IApiCaseHistorySearchDiagnosisReportItemDynamicListVentricularRhythm {
  /** 室早总数 */
  totalNum?: string;
  /** 室早占比 */
  proportion?: string;
  /** 单发 */
  singleShot?: string;
  /** 成对 */
  paired?: string;
  /** 二联律 */
  bipartiteRhythm?: string;
  /** 三联律 */
  trigeminalRhythm?: string;
  /** 室速总数 */
  vtNum?: string;
  /** 最快室速 */
  fastest?: string;
  /** 最长室速 */
  longest?: string;
  /** 室性逸搏 */
  ventricularEscape?: string;
  /** R on T */
  ronT?: string;
}

/**
 * 房颤/房扑分析
 */
export interface IApiCaseHistorySearchDiagnosisReportItemDynamicListAtrialFlutter {
  /** 房颤总数 */
  totalOfAtrialFibrillation?: string;
  /** 房颤占比 */
  atrialFibrillationProportion?: string;
  /** 房扑总数 */
  atrialFlutterTotalNum?: string;
  /** 房扑占比 */
  atrialFlutterProportion?: string;
  /** 房颤阵数 */
  numberOfAtrialFibrillation?: string;
  /** 房扑阵数 */
  atrialFlutterNum?: string;
  /** 房颤最长R-R间期 */
  longestAtrialFibrillation?: string;
  /** 房扑最长R-R间期 */
  longestAtrialFlutter?: string;
  /** 最长房颤持续时间 */
  longestTimeAtrialFibrillation?: string;
  /** 最长房扑持续时间 */
  longestTimeAtrialFlutter?: string;
}

/**
 * 起搏分析
 */
export interface IApiCaseHistorySearchDiagnosisReportItemDynamicListPacingAnalysis {
  /** 起搏心搏总数 */
  pacingBeatsNum?: string;
  /** 占总心搏 */
  pacingBeatsProportion?: string;
  /** 心房起搏 */
  atrialPacing?: string;
  /** 占总心搏 */
  atrialPacingProportion?: string;
  /** 心室起搏 */
  ventricularPacing?: string;
  /** 占总心搏 */
  ventricularPacingProportion?: string;
  /** 双腔起搏 */
  dualChamberPacing?: string;
  /** 占总心搏 */
  dualChamberPacingProportion?: string;
  /** 融合心搏 */
  fusionHeart?: string;
  /** 占总心搏 */
  fusionHeartProportion?: string;
  /** 窦性搏动计数 */
  sinusBeat?: string;
  /** 房室顺序起搏 */
  atrioventricularPacing?: string;
  /** 心室起搏伪融合 */
  ventricularFused?: string;
}

/**
 * 心率变异
 */
export interface IApiCaseHistorySearchDiagnosisReportItemDynamicListHrv {
  /** SDNN */
  sdnn?: string;
  /** SDANN */
  sdann?: string;
  /** SDNN Index */
  sdnnIndex?: string;
  /** RMSSD */
  rmssd?: string;
  /** pNN50 */
  pnn?: string;
  /** HF */
  hf?: string;
  /** LF */
  lf?: string;
  /** VLF */
  vlf?: string;
  /** 三角指数 */
  triangleIndex?: string;
  /** QT */
  qt?: string;
  /** QTC */
  qtc?: string;
}

/**
 * 动态心电图报告
 */
export interface IApiCaseHistorySearchDiagnosisReportItemDynamicList {
  /** 分析开始时间 */
  startTime?: string;
  /** 分析总时长 */
  totalTime?: IApiCaseHistorySearchDiagnosisReportItemDynamicListTotalTime;
  /** 心率 */
  heartRate?: IApiCaseHistorySearchDiagnosisReportItemDynamicListHeartRate;
  /** 室上性节律 */
  supraventricularRhythm?: IApiCaseHistorySearchDiagnosisReportItemDynamicListSupraventricularRhythm;
  /** 室性节律 */
  ventricularRhythm?: IApiCaseHistorySearchDiagnosisReportItemDynamicListVentricularRhythm;
  /** 房颤/房扑分析 */
  atrialFlutter?: IApiCaseHistorySearchDiagnosisReportItemDynamicListAtrialFlutter;
  /** 起搏分析 */
  pacingAnalysis?: IApiCaseHistorySearchDiagnosisReportItemDynamicListPacingAnalysis;
  /** 心率变异 */
  hrv?: IApiCaseHistorySearchDiagnosisReportItemDynamicListHrv;
}

/**
 * 心脏彩超报告
 */
export interface IApiCaseHistorySearchDiagnosisReportItemCardiacList {
  /** M型测值 */
  mType?: string;
  /** 收缩功能 */
  systolicFunction?: string;
  /** 组织多普勒测值 */
  tissueDoppler?: string;
}

/**
 * 起搏器程控报告
 */
export interface IApiCaseHistorySearchDiagnosisReportItemPacemakerList {
  /** 起搏器类型 */
  type?: string;
  /** 厂商 */
  manufacturer?: string;
  /** 置入时间 */
  insertTime?: string;
  /** 电量 */
  electricQuantity?: number;
  /** 心房高频事件 */
  atrialEvent?: number;
  /** 持续时间 */
  duration?: number;
  /** 植入节点 */
  implantationPoint?: string;
  /** 附件 */
  accessory?: string;
  /** 房速 */
  at?: number;
  /** 房颤 */
  af?: number;
  /** 房速时间 */
  timeInAt?: number;
  /** 房颤时间 */
  timeInAf?: number;
  /** 起搏器编码 */
  code?: number;
  /** 心室起搏比例 */
  vp?: number;
  /** 心房起搏比例 */
  ap?: number;
}

/**
 * 时间
 */
export interface IApiCaseHistorySearchDiagnosisReportItemSixMwtListTime {
  /** 测试前 */
  ago?: string;
  /** 测试后 */
  after?: string;
}

/**
 * 心率
 */
export interface IApiCaseHistorySearchDiagnosisReportItemSixMwtListHeartRate {
  /** 测试前 */
  ago?: number;
  /** 测试后 */
  after?: number;
}

/**
 * 测试前
 */
export interface IApiCaseHistorySearchDiagnosisReportItemSixMwtListBloodPressureAgo {
  /** 收缩压 */
  systolic?: number;
  /** 舒张压 */
  diastolic?: number;
}

/**
 * 测试后
 */
export interface IApiCaseHistorySearchDiagnosisReportItemSixMwtListBloodPressureAfter {
  /** 收缩压 */
  systolic?: number;
  /** 舒张压 */
  diastolic?: number;
}

/**
 * 血压
 */
export interface IApiCaseHistorySearchDiagnosisReportItemSixMwtListBloodPressure {
  /** 测试前 */
  ago?: IApiCaseHistorySearchDiagnosisReportItemSixMwtListBloodPressureAgo;
  /** 测试后 */
  after?: IApiCaseHistorySearchDiagnosisReportItemSixMwtListBloodPressureAfter;
}

/**
 * 氧饱和度
 */
export interface IApiCaseHistorySearchDiagnosisReportItemSixMwtListSaturation {
  /** 测试前 */
  ago?: number;
  /** 测试后 */
  after?: number;
}

/**
 * 呼吸困难评分
 */
export interface IApiCaseHistorySearchDiagnosisReportItemSixMwtListScore {
  /** 测试前 */
  ago?: number;
  /** 测试后 */
  after?: number;
}

/**
 * 是否暂停
 */
export interface IApiCaseHistorySearchDiagnosisReportItemSixMwtListSuspend {
  /** 备注 */
  remark?: string;
  /** 是否暂停 */
  isSuspend?: number;
}

/**
 * 是否提前终止
 */
export interface IApiCaseHistorySearchDiagnosisReportItemSixMwtListTermination {
  /** 备注 */
  remark?: string;
  /** 是否提前终止 */
  isTermination?: number;
}

/**
 * 6MWT报告集合
 */
export interface IApiCaseHistorySearchDiagnosisReportItemSixMwtList {
  /** 试验id */
  testId?: number;
  /** 试验时间 */
  testTime?: string;
  /** 步行距离 */
  distance?: number;
  /** 时间 */
  time?: IApiCaseHistorySearchDiagnosisReportItemSixMwtListTime;
  /** 心率 */
  heartRate?: IApiCaseHistorySearchDiagnosisReportItemSixMwtListHeartRate;
  /** 血压 */
  bloodPressure?: IApiCaseHistorySearchDiagnosisReportItemSixMwtListBloodPressure;
  /** 氧饱和度 */
  saturation?: IApiCaseHistorySearchDiagnosisReportItemSixMwtListSaturation;
  /** 呼吸困难评分 */
  score?: IApiCaseHistorySearchDiagnosisReportItemSixMwtListScore;
  /** 是否暂停 */
  suspend?: IApiCaseHistorySearchDiagnosisReportItemSixMwtListSuspend;
  /** 是否提前终止 */
  termination?: IApiCaseHistorySearchDiagnosisReportItemSixMwtListTermination;
  /** 试验中症状 */
  inSymptom?: string;
  /** 结束症状 */
  endSymptom?: string;
}

/**
 * 结论集合
 */
export interface IApiCaseHistorySearchDiagnosisReportItemConclusions {
  /** 结论id */
  conclusionId?: number;
  /** 结论类型 */
  type?: number;
  /** 选择类型 */
  chooseType?: number;
  /** 上级id */
  pid?: number;
  /** 备注 */
  remark?: string;
  /** 结论名称 */
  name?: string;
}

/**
 *
 */
export interface IApiCaseHistorySearchDiagnosisReportItem {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  /** 报告id */
  reportId?: number;
  /** 报告名称 */
  reportName?: string;
  /** 检查时间 */
  checkTime?: string;
  /** 指标项id */
  indexTermId?: number;
  /** 普通报告 */
  checkIndex?: IApiCaseHistorySearchDiagnosisReportItemCheckIndex[];
  /** 12导联心电图报告 */
  ecgList?: IApiCaseHistorySearchDiagnosisReportItemEcgList;
  /** 动态心电图报告 */
  dynamicList?: IApiCaseHistorySearchDiagnosisReportItemDynamicList;
  /** 心脏彩超报告 */
  cardiacList?: IApiCaseHistorySearchDiagnosisReportItemCardiacList;
  /** 起搏器程控报告 */
  pacemakerList?: IApiCaseHistorySearchDiagnosisReportItemPacemakerList;
  /** 6MWT报告集合 */
  sixMwtList?: IApiCaseHistorySearchDiagnosisReportItemSixMwtList;
  /** 附件 */
  accessory?: string[];
  /** 结论集合 */
  conclusions?: IApiCaseHistorySearchDiagnosisReportItemConclusions[];
  /** 更新时间 */
  modifyTime?: string;
}

/**
 * 住院检查详情
 */
export type IApiCaseHistorySearchDiagnosisReport =
  IApiCaseHistorySearchDiagnosisReportItem[];

/**
 * 指标信息
 */
export interface IApiCaseHistoryPreserveEcgParamsLeadInfo {
  /** 心率 */
  heartRate?: string;
  /** PR间期 */
  pr?: string;
  /** QRS间期 */
  qrs?: string;
  /** RR间期 */
  rr?: string;
  /** QT */
  qt?: string;
  /** QTC */
  qtc?: string;
  /** RV5 */
  rv?: string;
  /** SV1 */
  sv?: string;
  /** Paxis */
  paxis?: string;
  /** P */
  p?: string;
  /** QRSaxis */
  qrSaxis?: string;
  /** RV5+SV1 */
  rvSv?: string;
  /** RV5/SV1 */
  svRv?: string;
}

/**
 * 结论集合
 */
export interface IApiCaseHistoryPreserveEcgParamsConclusions {
  /** 结论id */
  conclusionId?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 保存12导联心电图、修改 - body 请求参数
 */
export interface IApiCaseHistoryPreserveEcgParams {
  /** 报告id */
  reportId?: number;
  /** 来源类型 0住院；1门诊；2复查；3手动添加 */
  sourceType?: number;
  /** 来源id */
  sourceId?: number;
  /** 患者id */
  patientId?: number;
  /** 报告项id */
  indexTermId?: number;
  /** 报告排序 */
  reportSort?: number;
  /** 报告名称 */
  reportName?: string;
  /** 是否入组病历 入组病历传 1 就欧克 */
  group?: number;
  /** 用户id --前端不用传 */
  userId?: number;
  /** 用户类型 --前端不用传 */
  userType?: string;
  /** ocr */
  ocr?: boolean;
  /** 12导联心电图id */
  leadId?: number;
  /** 检查时间 */
  checkTime?: string;
  /** 指标信息 */
  leadInfo?: IApiCaseHistoryPreserveEcgParamsLeadInfo;
  /** 附件 */
  accessory?: string[];
  /** 结论集合 */
  conclusions?: IApiCaseHistoryPreserveEcgParamsConclusions[];
}

/**
 * 保存12导联心电图、修改
 */
export interface IApiCaseHistoryPreserveEcg {
  /** 病历id */
  patientHistoryId?: number;
}

/**
 * cta参数集合
 */
export interface IApiCaseHistoryCtaPreserveParamsAllCta {
  /** LM_左主干狭窄% */
  lm?: number;
  /** LAD_前降支狭窄% */
  lad?: number;
  /** RCA_右冠脉狭窄% */
  rca?: number;
  /** 置入大动脉支架 */
  stent?: number;
  /** 大动脉支架置入数量 */
  stents?: number;
  /** Debakey分型 */
  debakey?: number;
  /** IMH_壁间血肿 */
  imh?: number;
  /** TAA_胸主动脉瘤 */
  taa?: number;
  /** AAA_腹主动脉瘤 */
  aaa?: number;
  /** PAU_穿透性溃疡 */
  pau?: number;
  /** ULP_溃疡样凸起 */
  ulp?: number;
  /** 主动脉假性动脉瘤 */
  aortic?: number;
  /** 其他 */
  other?: string;
  /** 受累主动脉最宽处直径（mm） */
  aorticDirect?: number;
  /** 假腔血栓化情况 */
  thrombosis?: number;
  /** 椎动脉优势情况 */
  dominance?: number;
  /** 头臂动脉 */
  brachiocephalic?: number;
  /** 左颈总动脉 */
  leftCarotid?: number;
  /** 左锁骨下动脉 */
  leftSubclavianArtery?: number;
  /** 腹腔干动脉 */
  celiacTrunk?: number;
  /** 肠系膜上动脉 */
  coronaryArtery?: number;
  /** 左肾动脉 */
  leftRenalArtery?: number;
  /** 右肾动脉 */
  rightRenalArtery?: number;
  /** 肠系膜下动脉 */
  inferiorMesentericArtery?: number;
  /** 左髂总动脉 */
  leftCommon?: number;
  /** 左髂外动脉 */
  leftExternal?: number;
  /** 左髂内动脉 */
  leftInternal?: number;
  /** 右髂总动脉 */
  rightCommon?: number;
  /** 右髂外动脉 */
  rightExternal?: number;
  /** 右髂内动脉 */
  rightInternal?: number;
  /** 肝囊肿 */
  cystOfLiver?: number;
  /** 肾囊肿 */
  renalCyst?: number;
  /** 胸腔积液 */
  pleuralEffusion?: number;
  /** 心包积液 */
  hydropericardium?: number;
  /** 纵膈积液 */
  mediastinum?: number;
  /** 主动脉血管变异 */
  aorticVariation?: number;
  /** 主动脉CTA备注 */
  aorticRemark?: string;
}

/**
 * 保存cta - body 请求参数
 */
export interface IApiCaseHistoryCtaPreserveParams {
  /** 报告id */
  reportId?: number;
  /** 来源类型 0住院；1门诊；2复查；3手动添加 */
  sourceType?: number;
  /** 来源id */
  sourceId?: number;
  /** 患者id */
  patientId?: number;
  /** 报告项id */
  indexTermId?: number;
  /** 报告排序 */
  reportSort?: number;
  /** 报告名称 */
  reportName?: string;
  /** 是否入组病历 入组病历传 1 就欧克 */
  group?: number;
  /** 用户id --前端不用传 */
  userId?: number;
  /** 用户类型 --前端不用传 */
  userType?: string;
  /** 检查时间 */
  checkTime: number;
  /** cta类型 1 冠脉cta 2 主动脉cta */
  ctaType: number;
  /** cta参数集合 */
  allCta?: IApiCaseHistoryCtaPreserveParamsAllCta;
  /** 附件 */
  accessory?: string[];
}

/**
 * 保存cta
 */
export type IApiCaseHistoryCtaPreserve = boolean;

/**
 *
 */
export interface IApiCaseHistoryInHospitalCheckParamsItem {
  /** 患者id */
  patientId: number;
  /** 报告id */
  indexTermId: number;
  /** 其他报告名称 */
  remark?: string;
  /** 报告名称 */
  name?: string;
  /** 报告排序 */
  reportSort?: number;
  /** 来源id */
  sourceId?: number;
  /** 来源类型 */
  sourceType: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
  /** 是否入组 */
  group?: number;
}

/**
 * 保存住院检查 - body 请求参数
 */
export type IApiCaseHistoryInHospitalCheckParams =
  IApiCaseHistoryInHospitalCheckParamsItem[];

/**
 * 保存住院检查
 */
export interface IApiCaseHistoryInHospitalCheck {
  /** 病历id */
  patientHistoryId?: number;
}

/**
 * cta参数集合
 */
export interface IApiCaseHistorySaveIndexDataParamsValueList {
  /** 字段id */
  fieldId?: number;
  /** 实测值 */
  measuredValue?: number;
  /** 占预测值比 */
  predictiveValue?: number;
  /** 静息 */
  pred?: number;
  /** 峰值 */
  peek?: number;
}

/**
 * 保存心肺运动试验指标数据 - body 请求参数
 */
export interface IApiCaseHistorySaveIndexDataParams {
  /** 报告id */
  reportId?: number;
  /** 来源类型 0住院；1门诊；2复查；3手动添加 */
  sourceType?: number;
  /** 来源id */
  sourceId?: number;
  /** 患者id */
  patientId?: number;
  /** 报告项id */
  indexTermId?: number;
  /** 报告排序 */
  reportSort?: number;
  /** 报告名称 */
  reportName?: string;
  /** 是否入组病历 入组病历传 1 就欧克 */
  group?: number;
  /** 用户id --前端不用传 */
  userId?: number;
  /** 用户类型 --前端不用传 */
  userType?: string;
  /** 检查时间 */
  checkTime: number;
  /** cta参数集合 */
  valueList?: IApiCaseHistorySaveIndexDataParamsValueList[];
  /** 附件 */
  accessory?: string[];
}

/**
 * 保存心肺运动试验指标数据
 */
export type IApiCaseHistorySaveIndexData = boolean;

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsPersonal_historyLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsPersonal_historyLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryAdmission_reportParamsPersonal_historyLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsPersonal_history {
  org_text?: string;
  label?: IApiCaseHistoryAdmission_reportParamsPersonal_historyLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsFamily_disease_historyLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsFamily_disease_historyLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryAdmission_reportParamsFamily_disease_historyLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsFamily_disease_history {
  org_text?: string;
  label?: IApiCaseHistoryAdmission_reportParamsFamily_disease_historyLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsPhysique_data {
  key?: string;
  name?: string;
  value?: string;
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsPlan_phase2_admission {
  value?: string;
  plan_phase2_admission_date?: string;
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsChief_complaintLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsChief_complaintLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryAdmission_reportParamsChief_complaintLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsChief_complaint {
  org_text?: string;
  label?: IApiCaseHistoryAdmission_reportParamsChief_complaintLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsPast_disease_historyLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsPast_disease_historyLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryAdmission_reportParamsPast_disease_historyLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsPast_disease_history {
  org_text?: string;
  label?: IApiCaseHistoryAdmission_reportParamsPast_disease_historyLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryAdmission_reportParamsClinical_events {
  clinical_id?: number;
  patient_id?: number;
  clinical_type?: number;
  clinical_time?: number;
  clinical_cause?: string;
  modify_time?: number;
  accessory?: string[];
}

/**
 * 入院信息保存 - body 请求参数
 */
export interface IApiCaseHistoryAdmission_reportParams {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  admission_date?: number;
  personal_history?: IApiCaseHistoryAdmission_reportParamsPersonal_history;
  family_disease_history?: IApiCaseHistoryAdmission_reportParamsFamily_disease_history;
  physique_data?: IApiCaseHistoryAdmission_reportParamsPhysique_data[];
  plan_phase2_admission?: IApiCaseHistoryAdmission_reportParamsPlan_phase2_admission;
  chief_complaint?: IApiCaseHistoryAdmission_reportParamsChief_complaint;
  admission_accessory?: string[];
  past_disease_history?: IApiCaseHistoryAdmission_reportParamsPast_disease_history;
  admission_reason?: string;
  present_medical_history?: string;
  clinical_events?: IApiCaseHistoryAdmission_reportParamsClinical_events;
}

/**
 * 入院信息保存
 */
export type IApiCaseHistoryAdmission_report = number;

/**
 * 入院详情 - body 请求参数
 */
export interface IApiCaseHistorySearchInHospitalParams {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历类型 */
  caseType?: number;
}

/**
 * 饮酒dto
 */
export interface IApiCaseHistorySearchInHospitalDrinkHistoryInfo {
  /** 1目前 2既往 */
  type?: number;
  /** 年 */
  year?: string;
  /** 啤酒量 */
  beer?: string;
  /** 白酒量 */
  liquor?: string;
}

/**
 * 饮酒史
 */
export interface IApiCaseHistorySearchInHospitalDrinkHistory {
  /** 是否饮酒 */
  isDrink?: number;
  /** 饮酒dto */
  info?: IApiCaseHistorySearchInHospitalDrinkHistoryInfo;
}

/**
 * 吸烟史详情信息
 */
export interface IApiCaseHistorySearchInHospitalSmokeHistoryInfo {
  /** type:1目前 2既往 */
  type?: number;
  /** 年 */
  year?: string;
  /** 吸烟多少支 */
  branch?: string;
}

/**
 * 吸烟史
 */
export interface IApiCaseHistorySearchInHospitalSmokeHistory {
  /** 是否吸烟: 0不吸烟，1吸烟 */
  isSmoke?: number;
  /** 吸烟史详情信息 */
  info?: IApiCaseHistorySearchInHospitalSmokeHistoryInfo;
}

/**
 * 家族史
 */
export interface IApiCaseHistorySearchInHospitalFamilyHistory {
  /** 疾病类型 1:心血管；2：肿瘤；3：遗传 */
  diseaseHistory?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 基本信息
 */
export interface IApiCaseHistorySearchInHospitalBasicInfo {
  /** 身高 */
  height?: string;
  /** 体重 */
  weight?: string;
  /** 舒张压 */
  highPressure?: string;
  /** 收缩压 */
  lowPressure?: string;
  /** 心率 */
  heartRate?: string;
}

/**
 * 二次入院时间
 */
export interface IApiCaseHistorySearchInHospitalSecondTime {
  /** 是否二期入院 */
  isSecondTime?: number;
  /** 二期入院时间 */
  time?: string;
}

/**
 * 主诉
 */
export interface IApiCaseHistorySearchInHospitalSuit {
  /** 数字 */
  time?: string;
  /** 单位 */
  unit?: string;
  /** 症状名称 */
  name?: string;
  /** 症状id */
  id?: number;
  /** 其他备注 */
  remark?: string;
}

/**
 * 临床事件
 */
export interface IApiCaseHistorySearchInHospitalClinicalEvents {
  /** 事件id */
  clinicalId?: number;
  /** 患者id */
  patientId?: number;
  /** 事件类型 */
  clinicalType?: number;
  /** 时间 */
  clinicalTime?: number;
  /** 原因 */
  clinicalCause?: string;
  /** 操作时间 */
  modifyTime?: number;
  /** 附件 */
  accessory?: string[];
}

/**
 * ai - 家族史
 */
export interface IApiCaseHistorySearchInHospitalAiFamilyHistory {
  key?: string;
  value?: string;
  id?: number;
}

/**
 * ai - 主诉
 */
export interface IApiCaseHistorySearchInHospitalAiSuit {
  key?: string;
  value?: string;
  id?: number;
}

/**
 * ai - 基本信息
 */
export interface IApiCaseHistorySearchInHospitalAiBasicInfo {
  key?: string;
  value?: string;
  id?: number;
}

/**
 * ai - 既往史
 */
export interface IApiCaseHistorySearchInHospitalAiClinicalHistory {
  key?: string;
  value?: string;
  id?: number;
}

/**
 * ai - 个人史
 */
export interface IApiCaseHistorySearchInHospitalAiPersonalHistory {
  key?: string;
  value?: string;
  id?: number;
}

/**
 * 入院详情
 */
export interface IApiCaseHistorySearchInHospital {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  /** 病历id */
  patientHistoryId?: number;
  /** 病历类型 */
  caseType?: number;
  /** 入院id */
  beId?: number;
  /** 入院时间 */
  inTime?: number;
  /** 饮酒史 */
  drinkHistory?: IApiCaseHistorySearchInHospitalDrinkHistory;
  /** 吸烟史 */
  smokeHistory?: IApiCaseHistorySearchInHospitalSmokeHistory;
  /** 家族史 */
  familyHistory?: IApiCaseHistorySearchInHospitalFamilyHistory[];
  /** 基本信息 */
  basicInfo?: IApiCaseHistorySearchInHospitalBasicInfo;
  /** 二次入院时间 */
  secondTime?: IApiCaseHistorySearchInHospitalSecondTime;
  /** 主诉 */
  suit?: IApiCaseHistorySearchInHospitalSuit[];
  /** 附件 */
  accessory?: string[];
  /** 更新时间 */
  modifyTime?: number;
  /** 拼好后病种 */
  nestedDiseaseNameList?: string[];
  /** 事件原因 */
  eventReason?: number;
  /** 主诉原文 */
  suitText?: string;
  /** 现病史 */
  presentIllness?: string;
  /** 家族史原文 */
  familyHistoryText?: string;
  /** 既往史原文 */
  previousHistory?: string;
  /** 个人史原文 */
  personalHistoryText?: string;
  /** 临床事件 */
  clinicalEvents?: IApiCaseHistorySearchInHospitalClinicalEvents;
  /** 版本号 */
  version?: number;
  /** ai - 家族史 */
  aiFamilyHistory?: IApiCaseHistorySearchInHospitalAiFamilyHistory[];
  /** ai - 主诉 */
  aiSuit?: IApiCaseHistorySearchInHospitalAiSuit[];
  /** ai - 基本信息 */
  aiBasicInfo?: IApiCaseHistorySearchInHospitalAiBasicInfo[];
  /** ai - 既往史 */
  aiClinicalHistory?: IApiCaseHistorySearchInHospitalAiClinicalHistory[];
  /** 既往史-结构化 */
  diseaseStructured?: string[];
  /** ai - 个人史 */
  aiPersonalHistory?: IApiCaseHistorySearchInHospitalAiPersonalHistory[];
}

/**
 * 出院详情 - query 请求参数
 */
export interface IApiCaseHistoryDischarge_reportQuery {
  /** 病历id */
  patient_history_id?: string;
}

/**
 * 出院详情 - body 请求参数
 */
export interface IApiCaseHistoryDischarge_reportParams {
  data: string;
}

/**
 *
 */
export interface IApiCaseHistoryDischarge_reportDischarge_diagnosisLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryDischarge_reportDischarge_diagnosisLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryDischarge_reportDischarge_diagnosisLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryDischarge_reportDischarge_diagnosis {
  org_text?: string;
  label?: IApiCaseHistoryDischarge_reportDischarge_diagnosisLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryDischarge_reportMedicationDrug_spec {
  ingredients?: string;
  content_unit?: string;
  unit?: string;
  package_num?: string;
  package_unit?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDischarge_reportMedicationDrug_amount {
  ingredients?: string;
  content_unit?: string;
}

/**
 *
 */
export interface IApiCaseHistoryDischarge_reportMedication {
  drug_info_id?: number;
  drug_name?: string;
  drug_usage?: string;
  drug_spec?: IApiCaseHistoryDischarge_reportMedicationDrug_spec;
  drug_mode?: string;
  drug_amount?: IApiCaseHistoryDischarge_reportMedicationDrug_amount;
  medicine_time?: number;
  common_name?: string;
}

/**
 * 出院详情
 */
export interface IApiCaseHistoryDischarge_report {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  accessory?: string[];
  modify_time?: number;
  id?: number;
  discharge_date?: number;
  discharge_diagnosis?: IApiCaseHistoryDischarge_reportDischarge_diagnosis;
  medication_start_time?: number;
  medication?: IApiCaseHistoryDischarge_reportMedication[];
  discharge_accessory?: string[];
}

/**
 * 删除手术 - body 请求参数
 */
export interface IApiCaseHistoryDeleteSurgeryParams {
  /** 手术id */
  surgeryId: number;
}

/**
 * 删除手术
 */
export interface IApiCaseHistoryDeleteSurgery {}

/**
 * 删除病历 - body 请求参数
 */
export interface IApiCaseHistoryPatientHistoryDeleteParams {
  /** 病历id */
  patientHistoryId: number;
  /** 病历类型 0住院 1门诊 */
  caseType: number;
}

/**
 * 删除病历
 */
export type IApiCaseHistoryPatientHistoryDelete = undefined;

/**
 * 分析总时长
 */
export interface IApiCaseHistoryPreserveEcgDynamicParamsTotalTime {
  /** 时 */
  hour?: number;
  /** 分 */
  minute?: number;
  /** 秒 */
  second?: number;
}

/**
 * 心率
 */
export interface IApiCaseHistoryPreserveEcgDynamicParamsHeartRate {
  /** 总心搏数 */
  cardiomegalyNum?: string;
  /** 最慢心率 */
  slowestHeartRate?: string;
  /** 最快心率 */
  fastestHeartRate?: string;
  /** 平均心率 */
  averageHeartRate?: string;
  /** RR间期>2s */
  rr?: string;
  /** 最长一次 */
  longest?: string;
}

/**
 * 室上性节律
 */
export interface IApiCaseHistoryPreserveEcgDynamicParamsSupraventricularRhythm {
  /** 房早总数 */
  prematureEjaculationNum?: string;
  /** 房早占比 */
  proportion?: string;
  /** 单发 */
  singleShot?: string;
  /** 成对 */
  paired?: string;
  /** 二联律 */
  bipartiteRhythm?: string;
  /** 三联律 */
  trigeminalRhythm?: string;
  /** 房速总数 */
  atrialTachycardiaNum?: string;
  /** 房速逸搏 */
  atrialTachycardias?: string;
  /** 最快房速 */
  fastestAtrialVelocity?: string;
  /** 最长房速 */
  longestAtrialVelocity?: string;
  /** 交界性早搏 */
  junctional?: string;
  /** 成对交界性早搏 */
  pairedJunctional?: string;
  /** 交界性逸搏 */
  boundaryFree?: string;
  /** 交界性心动过速 */
  junctionalTachycardia?: string;
  /** 房早未下传总数 */
  untransmittedAtriaNum?: string;
  /** 最长R-R */
  longestR?: string;
}

/**
 * 室性节律
 */
export interface IApiCaseHistoryPreserveEcgDynamicParamsVentricularRhythm {
  /** 室早总数 */
  totalNum?: string;
  /** 室早占比 */
  proportion?: string;
  /** 单发 */
  singleShot?: string;
  /** 成对 */
  paired?: string;
  /** 二联律 */
  bipartiteRhythm?: string;
  /** 三联律 */
  trigeminalRhythm?: string;
  /** 室速总数 */
  vtNum?: string;
  /** 最快室速 */
  fastest?: string;
  /** 最长室速 */
  longest?: string;
  /** 室性逸搏 */
  ventricularEscape?: string;
  /** R on T */
  ronT?: number;
}

/**
 * 房颤/房扑分析
 */
export interface IApiCaseHistoryPreserveEcgDynamicParamsAtrialFlutter {
  /** 房颤总数 */
  totalOfAtrialFibrillation?: string;
  /** 房颤占比 */
  atrialFibrillationProportion?: string;
  /** 房扑总数 */
  atrialFlutterTotalNum?: string;
  /** 房扑占比 */
  atrialFlutterProportion?: string;
  /** 房颤阵数 */
  numberOfAtrialFibrillation?: string;
  /** 房扑阵数 */
  atrialFlutterNum?: string;
  /** 房颤最长R-R间期 */
  longestAtrialFibrillation?: string;
  /** 房扑最长R-R间期 */
  longestAtrialFlutter?: string;
  /** 最长房颤持续时间 */
  longestTimeAtrialFibrillation?: string;
  /** 最长房扑持续时间 */
  longestTimeAtrialFlutter?: string;
}

/**
 * 起搏分析
 */
export interface IApiCaseHistoryPreserveEcgDynamicParamsPacingAnalysis {
  /** 起搏心搏总数 */
  pacingBeatsNum?: string;
  /** 占总心搏 */
  pacingBeatsProportion?: string;
  /** 心房起搏 */
  atrialPacing?: string;
  /** 占总心搏 */
  atrialPacingProportion?: string;
  /** 心室起搏 */
  ventricularPacing?: string;
  /** 占总心搏 */
  ventricularPacingProportion?: string;
  /** 双腔起搏 */
  dualChamberPacing?: string;
  /** 占总心搏 */
  dualChamberPacingProportion?: string;
  /** 融合心搏 */
  fusionHeart?: string;
  /** 占总心搏 */
  fusionHeartProportion?: string;
  /** 窦性搏动计数 */
  sinusBeat?: string;
  /** 房室顺序起搏 */
  atrioventricularPacing?: string;
  /** 心室起搏伪融合 */
  ventricularFused?: string;
}

/**
 * 心率变异
 */
export interface IApiCaseHistoryPreserveEcgDynamicParamsHrv {
  /** SDNN */
  sdnn?: string;
  /** SDANN */
  sdann?: string;
  /** SDNN Index */
  sdnnIndex?: string;
  /** RMSSD */
  rmssd?: string;
  /** pNN50 */
  pnn?: string;
  /** HF */
  hf?: string;
  /** LF */
  lf?: string;
  /** VLF */
  vlf?: string;
  /** 三角指数 */
  triangleIndex?: string;
  /** QT */
  qt?: string;
  /** QTC */
  qtc?: string;
}

/**
 * 结论集合
 */
export interface IApiCaseHistoryPreserveEcgDynamicParamsConclusions {
  /** 结论id */
  conclusionId?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 动态心电图保存、修改 - body 请求参数
 */
export interface IApiCaseHistoryPreserveEcgDynamicParams {
  /** 报告id */
  reportId?: number;
  /** 来源类型 0住院；1门诊；2复查；3手动添加 */
  sourceType?: number;
  /** 来源id */
  sourceId?: number;
  /** 患者id */
  patientId?: number;
  /** 报告项id */
  indexTermId?: number;
  /** 报告排序 */
  reportSort?: number;
  /** 报告名称 */
  reportName?: string;
  /** 是否入组病历 入组病历传 1 就欧克 */
  group?: number;
  /** 用户id --前端不用传 */
  userId?: number;
  /** 用户类型 --前端不用传 */
  userType?: string;
  /** ocr */
  ocr?: boolean;
  /** 检查时间 */
  checkTime?: string;
  /** 分析开始时间 */
  startTime?: string;
  /** 分析总时长 */
  totalTime?: IApiCaseHistoryPreserveEcgDynamicParamsTotalTime;
  /** 心率 */
  heartRate?: IApiCaseHistoryPreserveEcgDynamicParamsHeartRate;
  /** 室上性节律 */
  supraventricularRhythm?: IApiCaseHistoryPreserveEcgDynamicParamsSupraventricularRhythm;
  /** 室性节律 */
  ventricularRhythm?: IApiCaseHistoryPreserveEcgDynamicParamsVentricularRhythm;
  /** 房颤/房扑分析 */
  atrialFlutter?: IApiCaseHistoryPreserveEcgDynamicParamsAtrialFlutter;
  /** 起搏分析 */
  pacingAnalysis?: IApiCaseHistoryPreserveEcgDynamicParamsPacingAnalysis;
  /** 心率变异 */
  hrv?: IApiCaseHistoryPreserveEcgDynamicParamsHrv;
  /** 附件 */
  accessory?: string[];
  /** 结论集合 */
  conclusions?: IApiCaseHistoryPreserveEcgDynamicParamsConclusions[];
}

/**
 * 动态心电图保存、修改
 */
export interface IApiCaseHistoryPreserveEcgDynamic {
  /** 病历id */
  patientHistoryId?: number;
}

/**
 * 动态心电图详情查询 - body 请求参数
 */
export interface IApiCaseHistorySearchEcgDynamicParams {
  /** 报告id */
  reportId?: number;
}

/**
 * 分析总时长
 */
export interface IApiCaseHistorySearchEcgDynamicTotalTime {
  /** 时 */
  hour?: number;
  /** 分 */
  minute?: number;
  /** 秒 */
  second?: number;
}

/**
 * 心率
 */
export interface IApiCaseHistorySearchEcgDynamicHeartRate {
  /** 总心搏数 */
  cardiomegalyNum?: string;
  /** 最慢心率 */
  slowestHeartRate?: string;
  /** 最快心率 */
  fastestHeartRate?: string;
  /** 平均心率 */
  averageHeartRate?: string;
  /** RR间期>2s */
  rr?: string;
  /** 最长一次 */
  longest?: string;
}

/**
 * 室上性节律
 */
export interface IApiCaseHistorySearchEcgDynamicSupraventricularRhythm {
  /** 房早总数 */
  prematureEjaculationNum?: string;
  /** 房早占比 */
  proportion?: string;
  /** 单发 */
  singleShot?: string;
  /** 成对 */
  paired?: string;
  /** 二联律 */
  bipartiteRhythm?: string;
  /** 三联律 */
  trigeminalRhythm?: string;
  /** 房速总数 */
  atrialTachycardiaNum?: string;
  /** 房速逸搏 */
  atrialTachycardias?: string;
  /** 最快房速 */
  fastestAtrialVelocity?: string;
  /** 最长房速 */
  longestAtrialVelocity?: string;
  /** 交界性早搏 */
  junctional?: string;
  /** 成对交界性早搏 */
  pairedJunctional?: string;
  /** 交界性逸搏 */
  boundaryFree?: string;
  /** 交界性心动过速 */
  junctionalTachycardia?: string;
  /** 房早未下传总数 */
  untransmittedAtriaNum?: string;
  /** 最长R-R */
  longestR?: string;
}

/**
 * 室性节律
 */
export interface IApiCaseHistorySearchEcgDynamicVentricularRhythm {
  /** 室早总数 */
  totalNum?: string;
  /** 室早占比 */
  proportion?: string;
  /** 单发 */
  singleShot?: string;
  /** 成对 */
  paired?: string;
  /** 二联律 */
  bipartiteRhythm?: string;
  /** 三联律 */
  trigeminalRhythm?: string;
  /** 室速总数 */
  vtNum?: string;
  /** 最快室速 */
  fastest?: string;
  /** 最长室速 */
  longest?: string;
  /** 室性逸搏 */
  ventricularEscape?: string;
  /** R on T */
  ronT?: string;
}

/**
 * 房颤/房扑分析
 */
export interface IApiCaseHistorySearchEcgDynamicAtrialFlutter {
  /** 房颤总数 */
  totalOfAtrialFibrillation?: string;
  /** 房颤占比 */
  atrialFibrillationProportion?: string;
  /** 房扑总数 */
  atrialFlutterTotalNum?: string;
  /** 房扑占比 */
  atrialFlutterProportion?: string;
  /** 房颤阵数 */
  numberOfAtrialFibrillation?: string;
  /** 房扑阵数 */
  atrialFlutterNum?: string;
  /** 房颤最长R-R间期 */
  longestAtrialFibrillation?: string;
  /** 房扑最长R-R间期 */
  longestAtrialFlutter?: string;
  /** 最长房颤持续时间 */
  longestTimeAtrialFibrillation?: string;
  /** 最长房扑持续时间 */
  longestTimeAtrialFlutter?: string;
}

/**
 * 起搏分析
 */
export interface IApiCaseHistorySearchEcgDynamicPacingAnalysis {
  /** 起搏心搏总数 */
  pacingBeatsNum?: string;
  /** 占总心搏 */
  pacingBeatsProportion?: string;
  /** 心房起搏 */
  atrialPacing?: string;
  /** 占总心搏 */
  atrialPacingProportion?: string;
  /** 心室起搏 */
  ventricularPacing?: string;
  /** 占总心搏 */
  ventricularPacingProportion?: string;
  /** 双腔起搏 */
  dualChamberPacing?: string;
  /** 占总心搏 */
  dualChamberPacingProportion?: string;
  /** 融合心搏 */
  fusionHeart?: string;
  /** 占总心搏 */
  fusionHeartProportion?: string;
  /** 窦性搏动计数 */
  sinusBeat?: string;
  /** 房室顺序起搏 */
  atrioventricularPacing?: string;
  /** 心室起搏伪融合 */
  ventricularFused?: string;
}

/**
 * 心率变异
 */
export interface IApiCaseHistorySearchEcgDynamicHrv {
  /** SDNN */
  sdnn?: string;
  /** SDANN */
  sdann?: string;
  /** SDNN Index */
  sdnnIndex?: string;
  /** RMSSD */
  rmssd?: string;
  /** pNN50 */
  pnn?: string;
  /** HF */
  hf?: string;
  /** LF */
  lf?: string;
  /** VLF */
  vlf?: string;
  /** 三角指数 */
  triangleIndex?: string;
  /** QT */
  qt?: string;
  /** QTC */
  qtc?: string;
}

/**
 * 结论集合
 */
export interface IApiCaseHistorySearchEcgDynamicConclusions {
  /** 结论id */
  conclusionId?: number;
  /** 结论类型 */
  type?: number;
  /** 选择类型 */
  chooseType?: number;
  /** 上级id */
  pid?: number;
  /** 备注 */
  remark?: string;
  /** 结论名称 */
  name?: string;
}

/**
 * 动态心电图详情查询
 */
export interface IApiCaseHistorySearchEcgDynamic {
  /** 检查时间 */
  checkTime?: string;
  /** 分析开始时间 */
  startTime?: string;
  /** 分析总时长 */
  totalTime?: IApiCaseHistorySearchEcgDynamicTotalTime;
  /** 心率 */
  heartRate?: IApiCaseHistorySearchEcgDynamicHeartRate;
  /** 室上性节律 */
  supraventricularRhythm?: IApiCaseHistorySearchEcgDynamicSupraventricularRhythm;
  /** 室性节律 */
  ventricularRhythm?: IApiCaseHistorySearchEcgDynamicVentricularRhythm;
  /** 房颤/房扑分析 */
  atrialFlutter?: IApiCaseHistorySearchEcgDynamicAtrialFlutter;
  /** 起搏分析 */
  pacingAnalysis?: IApiCaseHistorySearchEcgDynamicPacingAnalysis;
  /** 心率变异 */
  hrv?: IApiCaseHistorySearchEcgDynamicHrv;
  /** 附件 */
  accessory?: string[];
  /** 结论集合 */
  conclusions?: IApiCaseHistorySearchEcgDynamicConclusions[];
}

/**
 * 心肺运动试验详情 - body 请求参数
 */
export interface IApiCaseHistoryIndexValueSearchParams {
  /** 报告id */
  reportId: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
}

/**
 * 指标详情
 */
export interface IApiCaseHistoryIndexValueSearchCpetValue {
  /** 字段id */
  fieldId?: number;
  /** 名字 */
  name?: string;
  /** 实测值 */
  measuredValue?: number;
  /** 占预测值比 */
  predictiveValue?: number;
  /** 静息 */
  pred?: number;
  /** 峰值 */
  peek?: number;
}

/**
 * 心肺运动试验详情
 */
export interface IApiCaseHistoryIndexValueSearch {
  /** 检查时间 */
  checkTime?: number;
  /** 指标详情 */
  cpetValue?: IApiCaseHistoryIndexValueSearchCpetValue[];
  /** 附件 */
  accessory?: string[];
}

/**
 * M型测值
 */
export interface IApiCaseHistoryPreserveCardiacParamsMType {
  /** 升主动脉内径（AO） */
  ao?: string;
  /** 主动脉窦部内径 */
  aorticSinus?: string;
  /** 主肺动脉内径（PA） */
  pa?: string;
  /** 室间隔厚度（IVS) */
  ivs?: string;
  /** 室间隔运动幅度 */
  amplitude?: string;
  /** 左室后壁厚度（LVPW） */
  lvpw?: string;
  /** 左室内径（LV） */
  lv?: string;
  /** 左房内径（LA） */
  la?: string;
  /** 右室内径（RV） */
  rv?: string;
  /** 右房内径（RA） */
  ra?: string;
  /** 右室流出道（RVOT） */
  rvot?: string;
  /** 左室流出道（LVOT） */
  lvot?: string;
  /** 舒张末期内径（LVDd） */
  lvdd?: string;
  /** 左心室收缩末期内径（LVDs） */
  lvds?: string;
  /** 主肺动脉（MPA */
  mpa?: string;
  /** 左室舒张末径 */
  endDiastolic?: string;
}

/**
 * 收缩功能
 */
export interface IApiCaseHistoryPreserveCardiacParamsSystolicFunction {
  /** 舒张末期容量（EDV） */
  edv?: string;
  /** 收缩末期容量（ESV） */
  esv?: string;
  /** 舒张末期内径（LVD） */
  lvd?: string;
  /** 收缩末期内径（LVS） */
  lvs?: string;
  /** 每搏输出量(SV) */
  sv?: string;
  /** 左室射血分数(EF) */
  ef?: string;
  /** 左室短轴缩短率（FS） */
  fs?: string;
  /** 左室缩短率（LVFS） */
  lvfs?: string;
  /** 室壁瘤 */
  lva?: string[];
  /** 主动脉最大流速 */
  maximumAortic?: string;
  /** 肺动脉最大流速 */
  pulmonaryArtery?: string;
}

/**
 * 组织多普勒测值
 */
export interface IApiCaseHistoryPreserveCardiacParamsTissueDoppler {
  /** 二尖瓣s峰 */
  mitralValvePeakS?: string;
  /** 二尖瓣E峰 */
  mitralValvePeakE?: string;
  /** 二尖瓣a峰 */
  mitralValvePeakA?: string;
  /** 三尖瓣s峰 */
  tricuspidValvePeakS?: string;
  /** 三尖瓣e峰 */
  tricuspidValvePeakE?: string;
  /** 三尖瓣a峰 */
  tricuspidValvePeakA?: string;
  /** 二尖瓣瓣口面积（MVA） */
  mva?: string;
  /** 主动脉瓣口面积（AVA） */
  ava?: string;
  /** 二尖瓣口血流速度（MV） */
  mv?: string;
  /** 主动脉瓣口流速（AV） */
  av?: string;
  /** 三尖瓣口血流速度TV */
  tv?: string;
  /** 肺动脉瓣口流速PV */
  pv?: string;
  /** 跨瓣压差PG */
  pg?: string;
  /** Mve */
  mve?: string;
  /** MVa */
  ma?: string;
  /** TRVmax */
  trvMax?: string;
  /** TAPSE */
  tapse?: string;
  /** E/e */
  ee?: string;
  /** 房缺大小、流速 */
  atrialDefect?: string;
  /** 室缺大小、流速 */
  ventricularDefect?: string;
  /** 肺动脉压力SPAP */
  spap?: string;
  /** Nakata指数(PAI指数) */
  pai?: string;
  /** Mcgoon指数 */
  mcgoon?: string;
}

/**
 * 结论集合
 */
export interface IApiCaseHistoryPreserveCardiacParamsConclusions {
  /** 结论id */
  conclusionId?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 心脏彩超保存、修改 - body 请求参数
 */
export interface IApiCaseHistoryPreserveCardiacParams {
  /** 报告id */
  reportId?: number;
  /** 来源类型 0住院；1门诊；2复查；3手动添加 */
  sourceType?: number;
  /** 来源id */
  sourceId?: number;
  /** 患者id */
  patientId?: number;
  /** 报告项id */
  indexTermId?: number;
  /** 报告排序 */
  reportSort?: number;
  /** 报告名称 */
  reportName?: string;
  /** 是否入组病历 入组病历传 1 就欧克 */
  group?: number;
  /** 用户id --前端不用传 */
  userId?: number;
  /** 用户类型 --前端不用传 */
  userType?: string;
  /** ocr */
  ocr?: boolean;
  /** 检查时间 */
  checkTime?: string;
  /** M型测值 */
  mType?: IApiCaseHistoryPreserveCardiacParamsMType;
  /** 收缩功能 */
  systolicFunction?: IApiCaseHistoryPreserveCardiacParamsSystolicFunction;
  /** 组织多普勒测值 */
  tissueDoppler?: IApiCaseHistoryPreserveCardiacParamsTissueDoppler;
  /** 附件 */
  accessory?: string[];
  /** 结论集合 */
  conclusions?: IApiCaseHistoryPreserveCardiacParamsConclusions[];
}

/**
 * 心脏彩超保存、修改
 */
export interface IApiCaseHistoryPreserveCardiac {
  /** 病历id */
  patientHistoryId?: number;
}

/**
 * 心脏彩超结论查询 - body 请求参数
 */
export interface IApiCaseHistoryCardiacConclusionParams {
  data: string;
}

/**
 * 下级结论
 */
export interface IApiCaseHistoryCardiacConclusionItemChildren {
  /** 主键 */
  id?: number;
  /** 上级id */
  pid?: number;
  /** 名称 */
  name?: string;
  /** 选择类型 */
  chooseType?: number;
  /** 下级结论 */
  children?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryCardiacConclusionItem {
  /** 主键 */
  id?: number;
  /** 上级id */
  pid?: number;
  /** 名称 */
  name?: string;
  /** 选择类型 */
  chooseType?: number;
  /** 下级结论 */
  children?: IApiCaseHistoryCardiacConclusionItemChildren[];
}

/**
 * 心脏彩超结论查询
 */
export type IApiCaseHistoryCardiacConclusion =
  IApiCaseHistoryCardiacConclusionItem[];

/**
 * 心脏彩超详情查询 - body 请求参数
 */
export interface IApiCaseHistorySearchEcgCardiacParams {
  /** 报告id */
  reportId?: number;
}

/**
 * M型测值
 */
export interface IApiCaseHistorySearchEcgCardiacMType {
  /** 升主动脉内径（AO） */
  ao?: string;
  /** 主动脉窦部内径 */
  aorticSinus?: string;
  /** 主肺动脉内径（PA） */
  pa?: string;
  /** 室间隔厚度（IVS) */
  ivs?: string;
  /** 室间隔运动幅度 */
  amplitude?: string;
  /** 左室后壁厚度（LVPW） */
  lvpw?: string;
  /** 左室内径（LV） */
  lv?: string;
  /** 左房内径（LA） */
  la?: string;
  /** 右室内径（RV） */
  rv?: string;
  /** 右房内径（RA） */
  ra?: string;
  /** 右室流出道（RVOT） */
  rvot?: string;
  /** 左室流出道（LVOT） */
  lvot?: string;
  /** 舒张末期内径（LVDd） */
  lvdd?: string;
  /** 左心室收缩末期内径（LVDs） */
  lvds?: string;
  /** 主肺动脉（MPA */
  mpa?: string;
  /** 左室舒张末径 */
  endDiastolic?: string;
}

/**
 * 收缩功能
 */
export interface IApiCaseHistorySearchEcgCardiacSystolicFunction {
  /** 舒张末期容量（EDV） */
  edv?: string;
  /** 收缩末期容量（ESV） */
  esv?: string;
  /** 舒张末期内径（LVD） */
  lvd?: string;
  /** 收缩末期内径（LVS） */
  lvs?: string;
  /** 每搏输出量(SV) */
  sv?: string;
  /** 左室射血分数(EF) */
  ef?: string;
  /** 左室短轴缩短率（FS） */
  fs?: string;
  /** 左室缩短率（LVFS） */
  lvfs?: string;
  /** 室壁瘤 */
  lva?: string[];
  /** 主动脉最大流速 */
  maximumAortic?: string;
  /** 肺动脉最大流速 */
  pulmonaryArtery?: string;
}

/**
 * 组织多普勒测值
 */
export interface IApiCaseHistorySearchEcgCardiacTissueDoppler {
  /** 二尖瓣s峰 */
  mitralValvePeakS?: string;
  /** 二尖瓣E峰 */
  mitralValvePeakE?: string;
  /** 二尖瓣a峰 */
  mitralValvePeakA?: string;
  /** 三尖瓣s峰 */
  tricuspidValvePeakS?: string;
  /** 三尖瓣e峰 */
  tricuspidValvePeakE?: string;
  /** 三尖瓣a峰 */
  tricuspidValvePeakA?: string;
  /** 二尖瓣瓣口面积（MVA） */
  mva?: string;
  /** 主动脉瓣口面积（AVA） */
  ava?: string;
  /** 二尖瓣口血流速度（MV） */
  mv?: string;
  /** 主动脉瓣口流速（AV） */
  av?: string;
  /** 三尖瓣口血流速度TV */
  tv?: string;
  /** 肺动脉瓣口流速PV */
  pv?: string;
  /** 跨瓣压差PG */
  pg?: string;
  /** Mve */
  mve?: string;
  /** MVa */
  mVa?: string;
  /** TRVmax */
  trvMax?: string;
  /** TAPSE */
  tapse?: string;
  /** E/e */
  ee?: string;
  /** 房缺大小、流速 */
  atrialDefect?: string;
  /** 室缺大小、流速 */
  ventricularDefect?: string;
  /** 肺动脉压力SPAP */
  spap?: string;
  /** Nakata指数(PAI指数) */
  pai?: string;
  /** Mcgoon指数 */
  mcgoon?: string;
}

/**
 * 结论集合
 */
export interface IApiCaseHistorySearchEcgCardiacConclusions {
  /** 结论id */
  conclusionId?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 心脏彩超详情查询
 */
export interface IApiCaseHistorySearchEcgCardiac {
  /** 报告id */
  reportId?: number;
  /** 来源类型 0住院；1门诊；2复查；3手动添加 */
  sourceType?: number;
  /** 来源id */
  sourceId?: number;
  /** 患者id */
  patientId?: number;
  /** 报告项id */
  indexTermId?: number;
  /** 报告名称 */
  reportName?: string;
  /** 用户id --前端不用传 */
  userId?: number;
  /** 用户类型 --前端不用传 */
  userType?: string;
  /** 检查时间 */
  checkTime?: string;
  /** M型测值 */
  mType?: IApiCaseHistorySearchEcgCardiacMType;
  /** 收缩功能 */
  systolicFunction?: IApiCaseHistorySearchEcgCardiacSystolicFunction;
  /** 组织多普勒测值 */
  tissueDoppler?: IApiCaseHistorySearchEcgCardiacTissueDoppler;
  /** 附件 */
  accessory?: string[];
  /** 结论集合 */
  conclusions?: IApiCaseHistorySearchEcgCardiacConclusions[];
}

/**
 * 心血管画像 - body 请求参数
 */
export interface IApiCaseHistoryHeartPictureParams {
  /** 患者id */
  patientId?: number;
}

/**
 * 节段数据
 */
export interface IApiCaseHistoryHeartPictureItemSegmentData {
  /** 程度 */
  degree?: string;
  /** 别名 */
  alias?: string;
  /** 位置 */
  location?: string;
  /** 支架 */
  support?: string;
  /** 节段名称 */
  segmentName?: string;
}

/**
 *
 */
export interface IApiCaseHistoryHeartPictureItem {
  /** 手术类型 */
  surgeryType?: number;
  /** 节段数据 */
  segmentData?: IApiCaseHistoryHeartPictureItemSegmentData[];
  /** 手术名称 */
  surgeryName?: string;
  /** 手术时间 */
  surgeryTime?: number;
}

/**
 * 心血管画像
 */
export type IApiCaseHistoryHeartPicture = IApiCaseHistoryHeartPictureItem[];

/**
 * 手术详情 - query 请求参数
 */
export interface IApiCaseHistoryOperation_recordQuery {
  source_id?: string;
  entry_task?: string;
  sub_task_id?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportPersonalHistoryLabelItems {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportPersonalHistoryLabel {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: IApiCaseHistoryOperation_recordAdmission_reportPersonalHistoryLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportPersonalHistory {
  orgText?: string;
  label?: IApiCaseHistoryOperation_recordAdmission_reportPersonalHistoryLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportFamilyDiseaseHistoryLabelItems {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportFamilyDiseaseHistoryLabel {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: IApiCaseHistoryOperation_recordAdmission_reportFamilyDiseaseHistoryLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportFamilyDiseaseHistory {
  orgText?: string;
  label?: IApiCaseHistoryOperation_recordAdmission_reportFamilyDiseaseHistoryLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportPhysiqueData {
  key?: string;
  name?: string;
  value?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportPlan_phase_2_admission {
  value?: string;
  plan_phase_2_admission_date?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportChiefComplaintLabelItems {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportChiefComplaintLabel {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: IApiCaseHistoryOperation_recordAdmission_reportChiefComplaintLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportChiefComplaint {
  orgText?: string;
  label?: IApiCaseHistoryOperation_recordAdmission_reportChiefComplaintLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportPastDiseaseHistoryLabelItems {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportPastDiseaseHistoryLabel {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: IApiCaseHistoryOperation_recordAdmission_reportPastDiseaseHistoryLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportPastDiseaseHistory {
  orgText?: string;
  label?: IApiCaseHistoryOperation_recordAdmission_reportPastDiseaseHistoryLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_reportClinicalEvents {
  clinicalId?: number;
  patientId?: number;
  patientHistoryId?: number;
  clinicalType?: number;
  clinicalTime?: number;
  clinicalCause?: string;
  modifyTime?: number;
  accessory?: string[];
  userId?: number;
  userType?: string;
  userName?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordAdmission_report {
  userId?: number;
  userType?: string;
  userName?: string;
  sourceType?: number;
  sourceId?: number;
  patientId?: number;
  group?: number;
  ocr?: boolean;
  entryTask?: boolean;
  subTaskId?: number;
  date?: number;
  otherDate?: number;
  accessory?: string[];
  otherAccessory?: string[];
  modifyTime?: number;
  id?: number;
  admissionDate?: number;
  personalHistory?: IApiCaseHistoryOperation_recordAdmission_reportPersonalHistory;
  familyDiseaseHistory?: IApiCaseHistoryOperation_recordAdmission_reportFamilyDiseaseHistory;
  physiqueData?: IApiCaseHistoryOperation_recordAdmission_reportPhysiqueData[];
  plan_phase_2_admission?: IApiCaseHistoryOperation_recordAdmission_reportPlan_phase_2_admission;
  chiefComplaint?: IApiCaseHistoryOperation_recordAdmission_reportChiefComplaint;
  admissionAccessory?: string[];
  pastDiseaseHistory?: IApiCaseHistoryOperation_recordAdmission_reportPastDiseaseHistory;
  admissionReason?: string;
  presentMedicalHistory?: string;
  clinicalEvents?: IApiCaseHistoryOperation_recordAdmission_reportClinicalEvents;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOperation_recordOperation_diagnosisOperation_conclusionLabelItems {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOperation_recordOperation_diagnosisOperation_conclusionLabel {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: IApiCaseHistoryOperation_recordOperation_recordOperation_diagnosisOperation_conclusionLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOperation_recordOperation_diagnosisOperation_conclusion {
  en_session?: string;
  label?: IApiCaseHistoryOperation_recordOperation_recordOperation_diagnosisOperation_conclusionLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOperation_recordOperation_diagnosis {
  label?: string;
  operation_conclusion?: IApiCaseHistoryOperation_recordOperation_recordOperation_diagnosisOperation_conclusion[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOperation_record {
  operation_id?: number;
  operation_date?: number;
  operation_diagnosis?: IApiCaseHistoryOperation_recordOperation_recordOperation_diagnosis;
  conclusion_text?: string;
  operation_accessory?: string[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordDischarge_reportDischargeDiagnosisLabelItems {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordDischarge_reportDischargeDiagnosisLabel {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: IApiCaseHistoryOperation_recordDischarge_reportDischargeDiagnosisLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordDischarge_reportDischargeDiagnosis {
  orgText?: string;
  label?: IApiCaseHistoryOperation_recordDischarge_reportDischargeDiagnosisLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordDischarge_reportMedicationDrugSpec {
  ingredients?: string;
  contentUnit?: string;
  unit?: string;
  packageNum?: string;
  packageUnit?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordDischarge_reportMedicationDrugAmount {
  ingredients?: string;
  contentUnit?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordDischarge_reportMedication {
  drugInfoId?: number;
  drugName?: string;
  drugUsage?: string;
  drugSpec?: IApiCaseHistoryOperation_recordDischarge_reportMedicationDrugSpec;
  drugMode?: string;
  drugAmount?: IApiCaseHistoryOperation_recordDischarge_reportMedicationDrugAmount;
  medicineTime?: number;
  commonName?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordDischarge_report {
  userId?: number;
  userType?: string;
  userName?: string;
  sourceType?: number;
  sourceId?: number;
  patientId?: number;
  group?: number;
  ocr?: boolean;
  entryTask?: boolean;
  subTaskId?: number;
  date?: number;
  otherDate?: number;
  accessory?: string[];
  otherAccessory?: string[];
  modifyTime?: number;
  id?: number;
  dischargeDate?: number;
  dischargeDiagnosis?: IApiCaseHistoryOperation_recordDischarge_reportDischargeDiagnosis;
  medicationStartTime?: number;
  medication?: IApiCaseHistoryOperation_recordDischarge_reportMedication[];
  dischargeAccessory?: string[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_recordChiefComplaintLabelItems {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_recordChiefComplaintLabel {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: IApiCaseHistoryOperation_recordOutpatient_recordChiefComplaintLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_recordChiefComplaint {
  orgText?: string;
  label?: IApiCaseHistoryOperation_recordOutpatient_recordChiefComplaintLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_recordDiagnosisLabelItems {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_recordDiagnosisLabel {
  key?: string;
  value?: string;
  id?: number;
  old_id?: string;
  items?: IApiCaseHistoryOperation_recordOutpatient_recordDiagnosisLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_recordDiagnosis {
  orgText?: string;
  label?: IApiCaseHistoryOperation_recordOutpatient_recordDiagnosisLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_record {
  userId?: number;
  userType?: string;
  userName?: string;
  sourceType?: number;
  sourceId?: number;
  patientId?: number;
  group?: number;
  ocr?: boolean;
  entryTask?: boolean;
  subTaskId?: number;
  date?: number;
  otherDate?: number;
  accessory?: string[];
  otherAccessory?: string[];
  modifyTime?: number;
  chiefComplaint?: IApiCaseHistoryOperation_recordOutpatient_recordChiefComplaint;
  diagnosis?: IApiCaseHistoryOperation_recordOutpatient_recordDiagnosis;
  outpatientAccessory?: string[];
  outpatientDate?: number;
  presentMedicalHistory?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_examMedicationDrugSpec {
  ingredients?: string;
  contentUnit?: string;
  unit?: string;
  packageNum?: string;
  packageUnit?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_examMedicationDrugAmount {
  ingredients?: string;
  contentUnit?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_examMedication {
  drugInfoId?: number;
  drugName?: string;
  drugUsage?: string;
  drugSpec?: IApiCaseHistoryOperation_recordOutpatient_examMedicationDrugSpec;
  drugMode?: string;
  drugAmount?: IApiCaseHistoryOperation_recordOutpatient_examMedicationDrugAmount;
  medicineTime?: number;
  commonName?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordOutpatient_exam {
  userId?: number;
  userType?: string;
  userName?: string;
  sourceType?: number;
  sourceId?: number;
  patientId?: number;
  group?: number;
  ocr?: boolean;
  entryTask?: boolean;
  subTaskId?: number;
  date?: number;
  otherDate?: number;
  accessory?: string[];
  otherAccessory?: string[];
  modifyTime?: number;
  medicationStartTime?: number;
  medication?: IApiCaseHistoryOperation_recordOutpatient_examMedication[];
  medicationAccessory?: string[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordSymptomFollowUpListQuestionAnswerLinkAnswerInfoChooseContent {
  answerContent?: string;
  answerId?: string;
  answerRemark?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordSymptomFollowUpListQuestionAnswerLinkAnswerInfo {
  linkContent?: string;
  type?: string;
  answerId?: string;
  chooseContent?: IApiCaseHistoryOperation_recordSymptomFollowUpListQuestionAnswerLinkAnswerInfoChooseContent[];
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordSymptomFollowUpListQuestionAnswerAnswerInfo {
  answerId?: string;
  reason?: string;
  hospitalTime?: string;
  answerContent?: string;
  answerPictureList?: string[];
  specificSituation?: string;
  inpatientDepartment?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordSymptomFollowUpListQuestionAnswerChooseContent {
  answerContent?: string;
  answerId?: string;
  answerRemark?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordSymptomFollowUpListQuestionAnswer {
  questionContent?: string;
  linkAnswerInfo?: IApiCaseHistoryOperation_recordSymptomFollowUpListQuestionAnswerLinkAnswerInfo[];
  trueAnswer?: string;
  questionId?: string;
  pQuestionId?: number;
  answerInfo?: IApiCaseHistoryOperation_recordSymptomFollowUpListQuestionAnswerAnswerInfo[];
  type?: number;
  questionType?: number;
  followUpQuestionId?: number;
  required?: number;
  chooseContent?: IApiCaseHistoryOperation_recordSymptomFollowUpListQuestionAnswerChooseContent[];
  sid?: number;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordSymptomFollowUpList {
  followUpId?: number;
  drugInfoId?: number;
  userId?: number;
  date?: number;
  status?: number;
  times?: number;
  conclusion?: string;
  doctorOpinion?: string;
  overview?: string;
  wakeNum?: number;
  editor?: number;
  editorType?: number;
  editTime?: number;
  editName?: string;
  symptomStatus?: number;
  questionAnswer?: IApiCaseHistoryOperation_recordSymptomFollowUpListQuestionAnswer[];
  serviceType?: string;
  distributeYear?: number;
  sourceId?: number;
  sourceType?: number;
}

/**
 *
 */
export interface IApiCaseHistoryOperation_recordLifestyleFollowUpList {
  userQuestionnaireId?: number;
  userId?: number;
  status?: number;
  startTime?: number;
  completeTime?: number;
  score?: string;
  conclusion?: string;
  editor?: number;
  editorType?: number;
  editTime?: number;
  editorName?: string;
  questionnaireId?: number;
  questionnaireName?: string;
  questionInfo?: string;
  answerInfo?: string;
  questionnaireVersion?: number;
  serviceType?: string;
  distributeYear?: number;
  productId?: number;
  isHidden?: number;
  createTime?: number;
  questionnaireRule?: string;
  sourceId?: number;
  sourceType?: number;
}

/**
 * 手术详情
 */
export interface IApiCaseHistoryOperation_record {
  user_id?: number;
  user_type?: string;
  user_name?: string;
  source_type?: number;
  source_id?: number;
  patient_id?: number;
  group?: number;
  ocr?: boolean;
  entry_task?: boolean;
  sub_task_id?: number;
  date?: number;
  other_date?: number;
  accessory?: string[];
  other_accessory?: string[];
  modify_time?: number;
  admission_report?: IApiCaseHistoryOperation_recordAdmission_report;
  operation_record?: IApiCaseHistoryOperation_recordOperation_record[];
  discharge_report?: IApiCaseHistoryOperation_recordDischarge_report;
  outpatient_record?: IApiCaseHistoryOperation_recordOutpatient_record;
  outpatient_exam?: IApiCaseHistoryOperation_recordOutpatient_exam;
  symptomFollowUpList?: IApiCaseHistoryOperation_recordSymptomFollowUpList[];
  lifestyleFollowUpList?: IApiCaseHistoryOperation_recordLifestyleFollowUpList[];
}

/**
 * 手术详情 - query 请求参数
 */
export interface IApiCaseHistoryOperationQuery {
  source_id: string;
  entry_task?: string;
  sub_task_id?: string;
}

/**
 * 手术详情 - body 请求参数
 */
export interface IApiCaseHistoryOperationParams {
  data: string;
}

/**
 *
 */
export interface IApiCaseHistoryOperationItem {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  /** 手术id */
  surgeryId?: number;
  /** 手术时间 */
  surgeryTime?: number;
  /** 手术结论 */
  conclusion?: string;
  /** 手术信息 */
  surgeryInfo?: string;
  /** 手术原文 */
  surgeryText?: string;
  /** 更新时间 */
  modifyTime?: number;
  /** 附件 */
  accessory?: string[];
}

/**
 * 手术详情
 */
export type IApiCaseHistoryOperation = IApiCaseHistoryOperationItem[];

/**
 * 报告原文跳转复查、病例，title时间查询 - body 请求参数
 */
export interface IApiCaseHistoryTitleTimeParams {
  /** 来源id */
  sourceId: number;
  /** 来源类型 */
  sourceType: number;
}

/**
 * 报告原文跳转复查、病例，title时间查询
 */
export interface IApiCaseHistoryTitleTime {
  /** 复查、病历时间 */
  titleTime?: number;
}

/**
 * 规格
 */
export interface IApiCaseHistoryDiagnosisLeaveHospital_1742795995687ParamsDrugInfoDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 单次用量
 */
export interface IApiCaseHistoryDiagnosisLeaveHospital_1742795995687ParamsDrugInfoDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
}

/**
 * 出院用药
 */
export interface IApiCaseHistoryDiagnosisLeaveHospital_1742795995687ParamsDrugInfo {
  /** 药品id */
  drugInfoId?: number;
  /** 药品名称 */
  drugName?: string;
  /** 用法(频率) */
  drugUsage?: string;
  /** 规格 */
  drugSpec?: IApiCaseHistoryDiagnosisLeaveHospital_1742795995687ParamsDrugInfoDrugSpec;
  /** 用药方式 */
  drugMode?: string;
  /** 单次用量 */
  drugAmount?: IApiCaseHistoryDiagnosisLeaveHospital_1742795995687ParamsDrugInfoDrugAmount;
  /** 服药时间1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
  /** 通用名称 */
  commonName?: string;
}

/**
 * 临床诊断
 */
export interface IApiCaseHistoryDiagnosisLeaveHospital_1742795995687ParamsDiagnosisClinical {
  key?: string;
  value?: string;
  id?: number;
}

/**
 * 新增或修改出院信息_copy - body 请求参数
 */
export interface IApiCaseHistoryDiagnosisLeaveHospital_1742795995687Params {
  /** 入院时间 */
  inTime?: number;
  /** 是否入组病历 */
  group?: number;
  /** 病历类型 */
  caseType: number;
  /** 患者id */
  patientId?: number;
  /** 是否为ocr传参 */
  ocr?: boolean;
  /** 病历id */
  patientHistoryId?: number;
  /** 出院id */
  outId?: number;
  /** 出院时间 */
  outTime?: number;
  /** 附件 */
  accessory?: string[];
  /** 开始用药时间 */
  drugStartTime?: number;
  /** 出院用药 */
  drugInfo?: IApiCaseHistoryDiagnosisLeaveHospital_1742795995687ParamsDrugInfo[];
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
  /** 诊断原文 */
  diagnosis?: string;
  /** 临床诊断结构化 */
  diagnosisStructured?: string[];
  /** 临床诊断 */
  diagnosisClinical?: IApiCaseHistoryDiagnosisLeaveHospital_1742795995687ParamsDiagnosisClinical[];
}

/**
 * 新增或修改出院信息_copy
 */
export interface IApiCaseHistoryDiagnosisLeaveHospital_1742795995687 {
  /** 病历id */
  patientHistoryId?: number;
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_examParamsDischargeDiagnosisLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_examParamsDischargeDiagnosisLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryOutpatient_examParamsDischargeDiagnosisLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_examParamsDischargeDiagnosis {
  orgText?: string;
  label?: IApiCaseHistoryOutpatient_examParamsDischargeDiagnosisLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_examParamsMedicationDrugSpec {
  ingredients?: string;
  contentUnit?: string;
  unit?: string;
  packageNum?: string;
  packageUnit?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_examParamsMedicationDrugAmount {
  ingredients?: string;
  contentUnit?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_examParamsMedication {
  drugInfoId?: number;
  drugName?: string;
  drugUsage?: string;
  drugSpec?: IApiCaseHistoryOutpatient_examParamsMedicationDrugSpec;
  drugMode?: string;
  drugAmount?: IApiCaseHistoryOutpatient_examParamsMedicationDrugAmount;
  medicineTime?: number;
  commonName?: string;
}

/**
 * 新增或修改门诊处方信息 - body 请求参数
 */
export interface IApiCaseHistoryOutpatient_examParams {
  userId?: number;
  userType?: string;
  userName?: string;
  sourceType?: number;
  sourceId?: number;
  patientId?: number;
  group?: number;
  ocr?: boolean;
  entryTask?: boolean;
  subTaskId?: number;
  date?: number;
  accessory?: string[];
  modifyTime?: number;
  id?: number;
  dischargeDate?: number;
  dischargeDiagnosis?: IApiCaseHistoryOutpatient_examParamsDischargeDiagnosis;
  medicationStartTime?: number;
  medication?: IApiCaseHistoryOutpatient_examParamsMedication[];
  dischargeAccessory?: string[];
}

/**
 * 新增或修改门诊处方信息
 */
export type IApiCaseHistoryOutpatient_exam = number;

/**
 * 查询病种列表 - body 请求参数
 */
export interface IApiCaseHistoryDiseaseParams {
  /** 病种模糊查询名称 */
  diseaseName?: string;
}

/**
 * 子级
 */
export interface IApiCaseHistoryDiseaseItemChildren {
  /** 病种名称 */
  diseaseName?: string;
  /** 病种id */
  diseaseId?: number;
  /** 上级id */
  pId?: number;
  /** 子级 */
  children?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryDiseaseItem {
  /** 病种名称 */
  diseaseName?: string;
  /** 病种id */
  diseaseId?: number;
  /** 上级id */
  pId?: number;
  /** 子级 */
  children?: IApiCaseHistoryDiseaseItemChildren[];
}

/**
 * 查询病种列表
 */
export type IApiCaseHistoryDisease = IApiCaseHistoryDiseaseItem[];

/**
 * 查询节段列表 - body 请求参数
 */
export interface IApiCaseHistorySegmentParams {
  data: string;
}

/**
 *
 */
export interface IApiCaseHistorySegmentItem {
  /** 节段id */
  segmentId?: number;
  /** 节段名称 */
  segmentName?: string;
  /** 位置 */
  location?: string;
  /** 别名 */
  alias?: string;
}

/**
 * 查询节段列表
 */
export type IApiCaseHistorySegment = IApiCaseHistorySegmentItem[];

/**
 * 确认入组 - body 请求参数
 */
export interface IApiCaseHistoryInGroupParams {
  /** 患者id */
  patientId?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
}

/**
 * 确认入组
 */
export type IApiCaseHistoryInGroup = undefined;

/**
 * 获取心肺运动试验报告字段 - body 请求参数
 */
export interface IApiCaseHistoryIndexFieldParams {
  data: string;
}

/**
 *
 */
export interface IApiCaseHistoryIndexFieldItem {
  /** 字段id */
  fieldId?: number;
  /** 字段名称 */
  name?: string;
  /** 单位 */
  unit?: string;
  /** 正常值 */
  normalValue?: string;
  /** 实测值 */
  enableMeasuredValue?: boolean;
  /** 占预测值比 */
  enablePredictiveValue?: boolean;
  /** 静息 */
  enablePred?: boolean;
  /** 峰值 */
  enablePeek?: boolean;
}

/**
 * 获取心肺运动试验报告字段
 */
export type IApiCaseHistoryIndexField = IApiCaseHistoryIndexFieldItem[];

/**
 * 获取患者第一次一般情况调查问卷吸烟饮酒 - body 请求参数
 */
export interface IApiCaseHistoryPatientQuestionnaireParams {
  /** 患者id */
  patientId?: string;
}

/**
 * 吸烟史详情信息
 */
export interface IApiCaseHistoryPatientQuestionnaireSmokeHistoryInfo {
  /** type:1目前 2既往 */
  type?: number;
  /** 年 */
  year?: number;
  /** 吸烟多少支 */
  branch?: number;
}

/**
 * 吸烟史
 */
export interface IApiCaseHistoryPatientQuestionnaireSmokeHistory {
  /** 是否吸烟: 0不吸烟，1吸烟 */
  isSmoke?: number;
  /** 吸烟史详情信息 */
  info?: IApiCaseHistoryPatientQuestionnaireSmokeHistoryInfo;
}

/**
 * 饮酒dto
 */
export interface IApiCaseHistoryPatientQuestionnaireDrinkHistoryInfo {
  /** type: 0既往 1目前 */
  type?: number;
  /** 年 */
  year?: string;
  /** 啤酒量 */
  beer?: string;
  /** 白酒量 */
  liquor?: string;
}

/**
 * 饮酒史
 */
export interface IApiCaseHistoryPatientQuestionnaireDrinkHistory {
  /** 是否饮酒 */
  isDrink?: number;
  /** 饮酒dto */
  info?: IApiCaseHistoryPatientQuestionnaireDrinkHistoryInfo;
}

/**
 * 获取患者第一次一般情况调查问卷吸烟饮酒
 */
export interface IApiCaseHistoryPatientQuestionnaire {
  /** 吸烟史 */
  smokeHistory?: IApiCaseHistoryPatientQuestionnaireSmokeHistory;
  /** 饮酒史 */
  drinkHistory?: IApiCaseHistoryPatientQuestionnaireDrinkHistory;
}

/**
 * 起搏器程控保存、修改 - body 请求参数
 */
export interface IApiCaseHistoryPreservePacemakerParams {
  /** 报告id */
  reportId?: number;
  /** 来源类型 0住院；1门诊；2复查；3手动添加 */
  sourceType?: number;
  /** 来源id */
  sourceId?: number;
  /** 患者id */
  patientId?: number;
  /** 报告项id */
  indexTermId?: number;
  /** 报告排序 */
  reportSort?: number;
  /** 报告名称 */
  reportName?: string;
  /** 是否入组病历 入组病历传 1 就欧克 */
  group?: number;
  /** 用户id --前端不用传 */
  userId?: number;
  /** 用户类型 --前端不用传 */
  userType?: string;
  /** ocr */
  ocr?: boolean;
  /** 起搏器id */
  pacemakerId?: number;
  /** 检查时间 */
  checkTime?: number;
  /** 起搏器类型 */
  type?: string;
  /** 起搏器编码 */
  code?: number;
  /** 厂商 */
  manufacturer?: string;
  /** 置入时间 */
  insertTime?: number;
  /** 房速 */
  at?: number;
  /** 房颤 */
  af?: number;
  /** 房速时间 */
  timeInAt?: number;
  /** 房颤时间 */
  timeInAf?: number;
  /** 电量 */
  electricQuantity?: number;
  /** 心房高频事件 */
  atrialEvent?: number;
  /** 持续时间 */
  duration?: number;
  /** 植入节点 */
  implantationPoint?: string;
  /** 附件 */
  accessory?: string[];
  /** 心室起搏比例 */
  vp?: number;
  /** 心房起搏比例 */
  ap?: number;
  /** AV间期 */
  avInterval?: number;
  /** 心室敏感程度（VS） */
  heartSensitivity?: number;
  /** 感知房室间期 */
  perceptionOfRoomInterval?: number;
}

/**
 * 起搏器程控保存、修改
 */
export interface IApiCaseHistoryPreservePacemaker {
  /** 病历id */
  patientHistoryId?: number;
}

/**
 * 起搏器程控详情 - body 请求参数
 */
export interface IApiCaseHistorySearchEcgPacemakerParams {
  /** 报告id */
  reportId?: number;
  /** 来源类型 0住院；1门诊；2复查；3手动添加 */
  sourceType?: number;
  /** 来源id */
  sourceId?: number;
  /** 患者id */
  patientId?: number;
  /** 报告项id */
  indexTermId?: number;
  /** 报告排序 */
  reportSort?: number;
  /** 报告名称 */
  reportName?: string;
  /** 是否入组病历 入组病历传 1 就欧克 */
  group?: number;
  /** 用户id --前端不用传 */
  userId?: number;
  /** 用户类型 --前端不用传 */
  userType?: string;
}

/**
 * 起搏器程控详情
 */
export interface IApiCaseHistorySearchEcgPacemaker {
  /** 病历类型 */
  caseType?: number;
  /** 起搏器id */
  pacemakerId?: number;
  /** 病历id */
  patientHistoryId?: number;
  /** 患者id */
  userId?: number;
  /** 检查时间 */
  checkTime?: number;
  /** 起搏器类型 */
  type?: string;
  /** 厂商 */
  manufacturer?: string;
  /** 置入时间 */
  insertTime?: number;
  /** 电量 */
  electricQuantity?: number;
  /** 心房高频事件 */
  atrialEvent?: number;
  /** 持续时间 */
  duration?: number;
  /** 植入节点 */
  implantationPoint?: string;
  /** 附件 */
  accessory?: string[];
  /** 房速 */
  at?: number;
  /** 房颤 */
  af?: number;
  /** 房速时间 */
  timeInAt?: number;
  /** 房颤时间 */
  timeInAf?: number;
  /** 起搏器编码 */
  code?: number;
  /** 心室起搏比例 */
  vp?: number;
  /** 心房起搏比例 */
  ap?: number;
  /** AV间期 */
  avInterval?: number;
  /** 心室敏感程度（VS） */
  heartSensitivity?: number;
  /** 感知房室间期 */
  perceptionOfRoomInterval?: number;
}

/**
 * 通过类型获取图片选择记录 - body 请求参数
 */
export interface IApiCaseHistoryDiagnosisAccessoryParams {
  /** 2 复查 3 入组 0 住院 1 门诊 */
  type: number;
  /** 患者id */
  patientId: number;
}

/**
 *
 */
export interface IApiCaseHistoryDiagnosisAccessoryItem {
  /** 业务id */
  businessId?: number;
  /** 内容 */
  name?: string;
  /** 状态 */
  status?: string;
  /** 时间 */
  time?: number;
}

/**
 * 通过类型获取图片选择记录
 */
export type IApiCaseHistoryDiagnosisAccessory =
  IApiCaseHistoryDiagnosisAccessoryItem[];

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsPersonalHistoryLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsPersonalHistoryLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryOutpatientParamsPersonalHistoryLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsPersonalHistory {
  orgText?: string;
  label?: IApiCaseHistoryOutpatientParamsPersonalHistoryLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsFamilyDiseaseHistoryLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsFamilyDiseaseHistoryLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryOutpatientParamsFamilyDiseaseHistoryLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsFamilyDiseaseHistory {
  orgText?: string;
  label?: IApiCaseHistoryOutpatientParamsFamilyDiseaseHistoryLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsPhysiqueData {
  key?: string;
  name?: string;
  value?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsPlan_phase_2_admission {
  value?: string;
  plan_phase_2_admission_date?: string;
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsChiefComplaintLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsChiefComplaintLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryOutpatientParamsChiefComplaintLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsChiefComplaint {
  orgText?: string;
  label?: IApiCaseHistoryOutpatientParamsChiefComplaintLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsPastDiseaseHistoryLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsPastDiseaseHistoryLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryOutpatientParamsPastDiseaseHistoryLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsPastDiseaseHistory {
  orgText?: string;
  label?: IApiCaseHistoryOutpatientParamsPastDiseaseHistoryLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatientParamsClinicalEvents {
  clinicalId?: number;
  patientId?: number;
  clinicalType?: number;
  clinicalTime?: number;
  clinicalCause?: string;
  modifyTime?: number;
  accessory?: string[];
}

/**
 * 门诊信息保存 - body 请求参数
 */
export interface IApiCaseHistoryOutpatientParams {
  userId?: number;
  userType?: string;
  userName?: string;
  sourceType?: number;
  sourceId?: number;
  patientId?: number;
  group?: number;
  ocr?: boolean;
  entryTask?: boolean;
  subTaskId?: number;
  date?: number;
  accessory?: string[];
  modifyTime?: number;
  id?: number;
  admissionDate?: number;
  personalHistory?: IApiCaseHistoryOutpatientParamsPersonalHistory;
  familyDiseaseHistory?: IApiCaseHistoryOutpatientParamsFamilyDiseaseHistory;
  physiqueData?: IApiCaseHistoryOutpatientParamsPhysiqueData[];
  plan_phase_2_admission?: IApiCaseHistoryOutpatientParamsPlan_phase_2_admission;
  chiefComplaint?: IApiCaseHistoryOutpatientParamsChiefComplaint;
  admissionAccessory?: string[];
  pastDiseaseHistory?: IApiCaseHistoryOutpatientParamsPastDiseaseHistory;
  admissionReason?: string;
  presentMedicalHistory?: string;
  clinicalEvents?: IApiCaseHistoryOutpatientParamsClinicalEvents;
}

/**
 * 门诊信息保存
 */
export type IApiCaseHistoryOutpatient = number;

/**
 *
 */
export interface IApiCaseHistoryOutpatient_recordParamsChiefComplaintLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_recordParamsChiefComplaintLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryOutpatient_recordParamsChiefComplaintLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_recordParamsChiefComplaint {
  orgText?: string;
  label?: IApiCaseHistoryOutpatient_recordParamsChiefComplaintLabel[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_recordParamsDiagnosisLabelItems {
  key?: string;
  value?: string;
  id?: number;
  items?: any[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_recordParamsDiagnosisLabel {
  key?: string;
  value?: string;
  id?: number;
  items?: IApiCaseHistoryOutpatient_recordParamsDiagnosisLabelItems[];
}

/**
 *
 */
export interface IApiCaseHistoryOutpatient_recordParamsDiagnosis {
  orgText?: string;
  label?: IApiCaseHistoryOutpatient_recordParamsDiagnosisLabel[];
}

/**
 * 门诊信息保存 - body 请求参数
 */
export interface IApiCaseHistoryOutpatient_recordParams {
  userId?: number;
  userType?: string;
  userName?: string;
  sourceType?: number;
  sourceId?: number;
  patientId?: number;
  group?: number;
  ocr?: boolean;
  entryTask?: boolean;
  subTaskId?: number;
  date?: number;
  accessory?: string[];
  modifyTime?: number;
  chiefComplaint?: IApiCaseHistoryOutpatient_recordParamsChiefComplaint;
  diagnosis?: IApiCaseHistoryOutpatient_recordParamsDiagnosis;
  outpatientAccessory?: string[];
  outpatientDate?: number;
  presentMedicalHistory?: string;
}

/**
 * 门诊信息保存
 */
export type IApiCaseHistoryOutpatient_record = number;

/**
 * 附件池删除 - body 请求参数
 */
export interface IApiCaseHistoryAccessoryDeleteParams {
  /** 来源id */
  sourceId: number;
  /** 来源类型 */
  sourceType: number;
  /** 图片地址 */
  url: string;
}

/**
 * 附件池删除
 */
export type IApiCaseHistoryAccessoryDelete = boolean;

/**
 * 附件池新增附件 - body 请求参数
 */
export interface IApiCaseHistoryUploadAccessoryParams {
  /** 来源id */
  sourceId?: number;
  /** 来源类型 */
  sourceType: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 患者id */
  patientId: number;
  /** 入院时间 */
  inTime?: number;
  /** 附件 */
  url?: string[];
}

/**
 * 附件池新增附件
 */
export interface IApiCaseHistoryUploadAccessory {
  /** 病历id */
  patientHistoryId?: number;
}

/**
 * 附件池查询 - body 请求参数
 */
export interface IApiCaseHistoryAccessoriesParams {
  /** 当前页 */
  page: number;
  /** 每页条数 */
  pageSize: number;
  /** 来源id */
  sourceId: number;
  /** 来源类型 0：住院 1：门诊 2：复查 3：入组 */
  sourceType: number;
}

/**
 * 附件池查询
 */
export interface IApiCaseHistoryAccessories {
  total?: number;
  contents?: string[];
}

/**
 * 患者硬件设备绑定、换绑 - body 请求参数
 */
export interface IApiPatientDeviceChangeParams {
  /** 患者id */
  patientId: number;
  /** 血压计编号 */
  soNo: string;
  /** sm卡号 */
  simNo?: string;
  /** 类型 1:爱奥乐  2:脉搏波 3:智能手表 */
  type: number;
}

/**
 * 患者硬件设备绑定、换绑
 */
export interface IApiPatientDeviceChange {}

/**
 * 患者硬件设备解绑 - body 请求参数
 */
export interface IApiPatientDeviceDeleteParams {
  /** 患者id */
  patientId: number;
  /** 血压计编号 */
  soNo: string;
  /** sm卡号 */
  simNo?: string;
  /** 类型 1:爱奥乐  2:脉搏波 3:智能手表 */
  type: number;
}

/**
 * 患者硬件设备解绑
 */
export interface IApiPatientDeviceDelete {}

/**
 * 查询患者设备绑定记录 - body 请求参数
 */
export interface IApiPatientDeviceQueryBindRecordParams {
  /** 患者id */
  patientId: number;
  /** 设备类型 */
  deviceType?: string;
}

/**
 *
 */
export interface IApiPatientDeviceQueryBindRecordItem {
  /** 硬件编号 */
  deviceNo?: string;
  /** 患者id */
  patientId?: number;
  /** 操作人id */
  operatorId?: number;
  /** 操作人姓名 */
  operatorName?: string;
  /** 操作时间 */
  operationTime?: number;
  /** 操作类型 BIND绑定、UNBIND解绑 */
  operationType?: string;
  /** 设备类型 */
  deviceType?: string;
}

/**
 * 查询患者设备绑定记录
 */
export type IApiPatientDeviceQueryBindRecord =
  IApiPatientDeviceQueryBindRecordItem[];

/**
 * 硬件设备列表 - body 请求参数
 */
export interface IApiPatientDeviceTypeListParams {
  data: string;
}

/**
 * 设备列表
 */
export interface IApiPatientDeviceTypeListTypeList {
  /** 设备类型id */
  deviceTypeId?: number;
  /** 设备名称 */
  deviceName?: string;
}

/**
 * 硬件设备列表
 */
export interface IApiPatientDeviceTypeList {
  /** 设备列表 */
  typeList?: IApiPatientDeviceTypeListTypeList[];
}

/**
 * 获取患者硬件设备列表 - body 请求参数
 */
export interface IApiPatientDeviceListParams {
  /** 患者id */
  patientId: number;
}

/**
 * 设备列表
 */
export interface IApiPatientDeviceListDeviceList {
  /** 血压计编号 */
  soNo?: string;
  /** 类型 1:爱奥乐  2:脉搏波 3:智能手表 */
  type?: number;
  /** sim编号 */
  simNo?: string;
  /** 绑定时间 */
  bindTime?: string;
}

/**
 * 获取患者硬件设备列表
 */
export interface IApiPatientDeviceList {
  /** 设备列表 */
  deviceList?: IApiPatientDeviceListDeviceList[];
}

/**
 * 停止患者用药 - body 请求参数
 */
export interface IApiDrugPatientStopParams {
  /** 患者用药id */
  drugInfoId: string;
}

/**
 * 停止患者用药
 */
export interface IApiDrugPatientStop {}

/**
 * 根据id获取患者用药详情 - body 请求参数
 */
export interface IApiDrugPatientDetailsParams {
  /** 患者用药id */
  drugInfoId: number;
}

/**
 * 单次用量
 */
export interface IApiDrugPatientDetailsDrugListDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 其他 不能格式化的老数据 */
  custom?: string;
}

/**
 * 规格
 */
export interface IApiDrugPatientDetailsDrugListDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 药品列表
 */
export interface IApiDrugPatientDetailsDrugList {
  /** 药品名称 */
  drugName?: string;
  /** 通用名称 */
  commonName?: string;
  /** 单次用量 */
  drugAmount?: IApiDrugPatientDetailsDrugListDrugAmount;
  /** 频率 */
  drugUsage?: string;
  /** 服药时间 1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
  /** 规格 */
  drugSpec?: IApiDrugPatientDetailsDrugListDrugSpec;
  /** 用药方式（用法) */
  drugMode?: string;
}

/**
 * 根据id获取患者用药详情
 */
export interface IApiDrugPatientDetails {
  /** 患者用药id */
  drugInfoId?: number;
  /** 药品列表 */
  drugList?: IApiDrugPatientDetailsDrugList[];
  /** 开始用药时间 */
  medicationTime?: string;
  /** 是否停止用药 true停止用药，null/false未停止用药 */
  isStop?: boolean;
  /** 停止用药时间 */
  stopTime?: string;
  /** 创建时间 */
  createTime?: string;
  /** 调药原因 */
  adjustReason?: string[];
  /** 其他调整原因 */
  adjustReasonOther?: string;
  /** 操作日志 */
  drugOperation?: string;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
}

/**
 * 获取患者历史用药列表 - body 请求参数
 */
export interface IApiDrugPatientHistoryParams {
  /** 患者id */
  patientId?: number;
  /** 当前页码 */
  pageNumber?: number;
  /** 页面大小 */
  pageSize?: number;
}

/**
 * 单次用量
 */
export interface IApiDrugPatientHistoryDataDrugListDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 其他 不能格式化的老数据 */
  custom?: string;
}

/**
 * 规格
 */
export interface IApiDrugPatientHistoryDataDrugListDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 药品列表
 */
export interface IApiDrugPatientHistoryDataDrugList {
  /** 药品名称 */
  drugName?: string;
  /** 通用名称 */
  commonName?: string;
  /** 单次用量 */
  drugAmount?: IApiDrugPatientHistoryDataDrugListDrugAmount;
  /** 频率 */
  drugUsage?: string;
  /** 服药时间 1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
  /** 规格 */
  drugSpec?: IApiDrugPatientHistoryDataDrugListDrugSpec;
  /** 用药方式（用法) */
  drugMode?: string;
}

/**
 * 患者用药列表
 */
export interface IApiDrugPatientHistoryData {
  /** 患者用药id */
  drugInfoId?: number;
  /** 药品列表 */
  drugList?: IApiDrugPatientHistoryDataDrugList[];
  /** 开始用药时间 */
  medicationTime?: string;
  /** 是否停止用药 true停止用药，null/false未停止用药 */
  isStop?: boolean;
  /** 停止用药时间 */
  stopTime?: string;
  /** 创建时间 */
  createTime?: string;
  /** 调药原因 */
  adjustReason?: string[];
  /** 其他调整原因 */
  adjustReasonOther?: string;
  /** 操作日志 */
  drugOperation?: string;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
}

/**
 * 获取患者历史用药列表
 */
export interface IApiDrugPatientHistory {
  /** 数据总条数 */
  totals?: number;
  /** 患者用药列表 */
  data?: IApiDrugPatientHistoryData[];
}

/**
 * 获取患者当前用药 - body 请求参数
 */
export interface IApiDrugPatientCurrentParams {
  /** 患者id */
  patientId: number;
}

/**
 * 单次用量
 */
export interface IApiDrugPatientCurrentDrugListDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 其他 不能格式化的老数据 */
  custom?: string;
}

/**
 * 规格
 */
export interface IApiDrugPatientCurrentDrugListDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 药品列表
 */
export interface IApiDrugPatientCurrentDrugList {
  /** 药品名称 */
  drugName?: string;
  /** 通用名称 */
  commonName?: string;
  /** 单次用量 */
  drugAmount?: IApiDrugPatientCurrentDrugListDrugAmount;
  /** 频率 */
  drugUsage?: string;
  /** 服药时间 1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
  /** 规格 */
  drugSpec?: IApiDrugPatientCurrentDrugListDrugSpec;
  /** 用药方式（用法) */
  drugMode?: string;
}

/**
 * 获取患者当前用药
 */
export interface IApiDrugPatientCurrent {
  /** 患者用药id */
  drugInfoId?: number;
  /** 药品列表 */
  drugList?: IApiDrugPatientCurrentDrugList[];
  /** 开始用药时间 */
  medicationTime?: string;
  /** 是否停止用药 true停止用药，null/false未停止用药 */
  isStop?: boolean;
  /** 停止用药时间 */
  stopTime?: string;
  /** 创建时间 */
  createTime?: string;
  /** 调药原因 */
  adjustReason?: string[];
  /** 其他调整原因 */
  adjustReasonOther?: string;
  /** 操作日志 */
  drugOperation?: string;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
}

/**
 * 规格
 */
export interface IApiDrugPatientAdjustParamsDrugDetailListDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 单次用量
 */
export interface IApiDrugPatientAdjustParamsDrugDetailListDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
}

/**
 * 药品列表
 */
export interface IApiDrugPatientAdjustParamsDrugDetailList {
  /** 药品名称 */
  drugName: string;
  /** 通用名称 */
  commonName?: string;
  /** 频率 */
  drugUsage: string;
  /** 规格 */
  drugSpec: IApiDrugPatientAdjustParamsDrugDetailListDrugSpec;
  /** 用法 */
  drugMode: string;
  /** 单次用量 */
  drugAmount: IApiDrugPatientAdjustParamsDrugDetailListDrugAmount;
  /** 服药时间 1：早上，2：中午，3：晚上，4：早中晚，5：早晚，6：午晚，7：早午 */
  medicineTime: number;
}

/**
 * 调整患者用药 - body 请求参数
 */
export interface IApiDrugPatientAdjustParams {
  /** 患者id */
  patientId: number;
  conditionId?: number;
  /** 出院id */
  outId?: number;
  /** 类型 0:用户手动添加用药; 2:医助添加用药; 3:医助调整用药 */
  type?: number;
  /** 调药意见 */
  advice?: string;
  /** 调药原因 */
  adjustReason?: string[];
  /** 其他调整原因 */
  adjustReasonOther?: string;
  /** 操作日志 */
  drugOperation?: string;
  /** 药品列表 */
  drugDetailList: IApiDrugPatientAdjustParamsDrugDetailList[];
  /** 来源id 来源id（随访id、病历id） */
  sourceId?: number;
  /** 来源类型 数据来源类型（1随访事件、2风险管理事件、4二期入院） */
  sourceType?: number;
  /** 是否生成调药跟踪 true 跟踪 */
  adjustDrugTrack?: boolean;
  /** 用户id */
  roleId?: number;
  /** 用户角色 */
  roleType?: string;
  /** 开始用药时间 */
  medicationTime?: string;
  /** 是否继续用药 */
  continueDrug?: boolean;
}

/**
 * 调整患者用药
 */
export interface IApiDrugPatientAdjust {
  /** 患者用药id */
  drugInfoId?: string;
}

/**
 * 使用老密码修改密码 - body 请求参数
 */
export interface IApiHealthUpdatePwdParams {
  /** 旧密码 */
  oldPassword: string;
  /** 新密码 */
  password: string;
  userId?: number;
  /** 用户角色 */
  userType?: number;
}

/**
 * 使用老密码修改密码
 */
export interface IApiHealthUpdatePwd {}

/**
 * 修改用户密码 - body 请求参数
 */
export interface IApiHealthUpdatePasswordParams {
  /** 手机号 */
  loginAccount: string;
  password: string;
}

/**
 * 修改用户密码
 */
export interface IApiHealthUpdatePassword {}

/**
 * 发送短信验证码 - body 请求参数
 */
export interface IApiHealthUserSendParams {
  /** 手机号 */
  loginAccount: string;
  /** 短信类型 1登录  2修改密码 */
  type?: number;
}

/**
 * 发送短信验证码
 */
export interface IApiHealthUserSend {}

/**
 * 查询是否绑定公众号 - body 请求参数
 */
export interface IApiHealthCheckBindParams {
  data: string;
}

/**
 * 查询是否绑定公众号
 */
export interface IApiHealthCheckBind {
  /** 是否绑定微信 true 是 false 否 */
  bind?: boolean;
}

/**
 * 用户登录 - body 请求参数
 */
export interface IApiHealthUserLoginParams {
  /** 用户账号 */
  loginAccount: string;
  /** 密码 */
  password?: string;
  /** 验证码 */
  verifyCode?: string;
  /** 登录类型 1：用户名、密码 2：手机号验证码 */
  type: number;
}

/**
 * 用户登录
 */
export interface IApiHealthUserLogin {
  /** 用户ID */
  userId?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户角色  医生：ASSISTANT, 健康管理师：CUSTOMER_SERVER, 运动康复师：REHAB, 实习生：INTERN, */
  userRoles?: string[];
  /** token */
  token?: string;
  /** 网易id */
  imAccid?: string;
  /** 网易token */
  imToken?: string;
  /** 医生角色类型 1医生助理、2医学部经理 */
  doctorRole?: number;
  /** 是否登录呼叫中心  1 登录呼叫中心  0 否 */
  isLogin?: number;
  /** 坐席工号 */
  cno?: string;
  /** 企业编码 */
  enterpriseCode?: string;
  /** 坐席密码 */
  seatsPassword?: string;
}

/**
 * 短信验证码校验 - body 请求参数
 */
export interface IApiHealthVerifyCodeParams {
  /** 用户名 */
  loginAccount: string;
  /** 验证码 */
  verifyCode: string;
}

/**
 * 短信验证码校验
 */
export interface IApiHealthVerifyCode {}

/**
 * 获取医生注册二维码 - body 请求参数
 */
export interface IApiHealthQrCodeRegisterParams {
  data: string;
}

/**
 * 获取医生注册二维码
 */
export interface IApiHealthQrCodeRegister {
  /** 二维码地址 */
  qrCode?: string;
}

/**
 * 获取系统登录类型 - body 请求参数
 */
export interface IApiHealthUserLoginTypeParams {
  /** 字典code */
  dictCode: string;
}

/**
 * 获取系统登录类型
 */
export interface IApiHealthUserLoginType {
  /** 字典值 */
  dictValueId?: string;
}

/**
 * 解绑微信 - body 请求参数
 */
export interface IApiHealthUnbindWechatParams {
  data: string;
}

/**
 * 解绑微信
 */
export type IApiHealthUnbindWechat = undefined;

/**
 * 退出登录 - body 请求参数
 */
export interface IApiHealthUserLoginOutParams {
  data: string;
}

/**
 * 退出登录
 */
export interface IApiHealthUserLoginOut {}

/**
 * 全部报告项查询 - body 请求参数
 */
export interface IApiPatientReportListAllParams {
  data: string;
}

/**
 *
 */
export interface IApiPatientReportListAllItem {
  /** id */
  indexTermId?: number;
  /** 指标名称 */
  name?: string;
  /** 指标小类 */
  type?: number;
  /** 报告类型(1生化;2临检;3免疫;4心电;5超声;6造影;7放射;8MRI;9内镜;10其他;11自定义检查) */
  reportType?: number;
  /** 报告排序 */
  reportSort?: number;
}

/**
 * 全部报告项查询
 */
export type IApiPatientReportListAll = IApiPatientReportListAllItem[];

/**
 * 删除报告 - body 请求参数
 */
export interface IApiPatientReportRemoveParams {
  /** 报告id */
  reportId: number;
}

/**
 * 删除报告
 */
export interface IApiPatientReportRemove {}

/**
 * 动态血压详情查询 - body 请求参数
 */
export interface IApiPatientReportBloodDetailParams {
  /** 患者id */
  patientId: number;
  /** 提醒时间 */
  checkTime: string;
  /** 动态血压结束时间 */
  bloodPressureEndTime: string;
}

/**
 *
 */
export interface IApiPatientReportBloodDetailItem {
  /** 时段 */
  timeFrame?: string;
  /** 收缩压 */
  systolic?: string;
  /** 舒张压 */
  diastolic?: string;
  /** 测量时间 */
  measureTime?: string;
}

/**
 * 动态血压详情查询
 */
export type IApiPatientReportBloodDetail = IApiPatientReportBloodDetailItem[];

/**
 * 图片档案查询 - body 请求参数
 */
export interface IApiPatientReportPicturesParams {
  /** 患者id */
  patientId?: number;
  /** 当前页 */
  page?: number;
  /** 当前页条数 */
  pageSize: number;
}

/**
 *
 */
export interface IApiPatientReportPicturesContents {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  /** 创建时间 */
  createTime?: number;
  /** 图片地址 */
  url?: string[];
}

/**
 * 图片档案查询
 */
export interface IApiPatientReportPictures {
  total?: number;
  contents?: IApiPatientReportPicturesContents[];
}

/**
 * 报告下指标查询 - body 请求参数
 */
export interface IApiPatientReportIndexParams {
  /** 报告指标id */
  indexTermId: number;
}

/**
 *
 */
export interface IApiPatientReportIndexItem {
  /** 指标名称 */
  name?: string;
  /** 指标小类 */
  type?: number;
  /** 指标大类 */
  pid?: number;
  /** 单位 */
  unit?: string;
  /** 指标值 */
  content?: number;
}

/**
 * 报告下指标查询
 */
export type IApiPatientReportIndex = IApiPatientReportIndexItem[];

/**
 * 报告列表查询 - body 请求参数
 */
export interface IApiPatientReportListParams {
  /** 报告id */
  indexTermId: number;
  /** 患者id */
  patientId: number;
  /** 结论类型 */
  conType?: number;
  /** 是否报告 */
  report?: number;
  /** 当前页 */
  page?: number;
  /** 每页显示数量 */
  pageSize?: number;
}

/**
 * 结论
 */
export interface IApiPatientReportListDataConclusions {
  /** 结论id */
  conclusionId?: number;
  /** 结论类型 */
  type?: number;
  /** 选择类型 */
  chooseType?: number;
  /** 上级id */
  pid?: number;
  /** 备注 */
  remark?: string;
  /** 结论名称 */
  name?: string;
}

/**
 * 数据
 */
export interface IApiPatientReportListData {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  /** 报告id */
  reportId?: number;
  /** 报告指标id */
  indexTermId?: number;
  /** 来源 */
  sourceId?: number;
  /** 来源类型 */
  sourceType?: number;
  /** 报告名称 */
  reportName?: string;
  /** 检查时间 */
  checkTime?: string;
  /** 结论 */
  conclusions?: IApiPatientReportListDataConclusions[];
  /** 附件 */
  accessory?: string[];
  /** 添加时间 */
  modifyTime?: string;
  /** 动态血压状态 0 未开始 1 进行中 2 已结束 */
  bloodPressureStatus?: number;
  /** 动态血压结束时间 */
  bloodPressureEndTime?: string;
}

/**
 * 报告列表查询
 */
export interface IApiPatientReportList {
  /** 总条数 */
  records?: number;
  /** 总页数 */
  total?: number;
  /** 当前页 */
  page?: number;
  /** 条数 */
  rows?: number;
  /** 数据 */
  data?: IApiPatientReportListData[];
}

/**
 * 报告原文项列表查询 - body 请求参数
 */
export interface IApiPatientReportUpParams {
  data: string;
}

/**
 *
 */
export interface IApiPatientReportUpItem {
  /** 报告指标id */
  indexTermId?: number;
  /** 报告名称 */
  reportName?: string;
  /** 报告排序 */
  reportSort?: number;
  /** 结论类型 */
  conType?: number;
  /** 是否报告 */
  report?: number;
}

/**
 * 报告原文项列表查询
 */
export type IApiPatientReportUp = IApiPatientReportUpItem[];

/**
 * 报告详情查询 - body 请求参数
 */
export interface IApiPatientReportDetailParams {
  /** 报告id */
  reportId: number;
}

/**
 * 指标数据
 */
export interface IApiPatientReportDetailCheckIndex {
  /** 指标名称 */
  name?: string;
  /** 指标小类 */
  type?: number;
  /** 指标大类 */
  pid?: number;
  /** 单位 */
  unit?: string;
  /** 指标值 */
  content?: number;
  /** 正常值 */
  normalValue?: string;
}

/**
 * 报告详情查询
 */
export interface IApiPatientReportDetail {
  /** 检查时间 */
  checkTime?: number;
  /** 指标数据 */
  checkIndex?: IApiPatientReportDetailCheckIndex[];
  /** 附件 */
  accessory?: string[];
  /** 报告名称 */
  reportName?: string;
  /** 添加时间 */
  modifyTime?: number;
  /** 用户名称 */
  userName?: string;
}

/**
 * 指标数据
 */
export interface IApiPatientReportOperateParamsCheckIndex {
  /** 指标小类 */
  type?: number;
  /** 指标大类 */
  pid?: number;
  /** 指标数据 */
  content?: number;
}

/**
 * 指标保存 - body 请求参数
 */
export interface IApiPatientReportOperateParams {
  /** 患者id */
  patientId: number;
  /** 添加类型 0住院；1门诊；2复查；3手动添加 */
  sourceType: number;
  /** 复查、病历id */
  sourceId?: number;
  /** 报告id */
  reportId?: number;
  /** 报告指标id */
  indexTermId: number;
  /** 备注 */
  remark?: string;
  /** 报告名称 */
  reportName?: string;
  /** 报告排序 */
  reportSort?: number;
  /** 检查时间 */
  checkTime: string;
  /** 报告内容 */
  accessory?: string[];
  /** 指标数据 */
  checkIndex?: IApiPatientReportOperateParamsCheckIndex[];
  /** 用户iD */
  userId?: number;
  /** 用户类型 */
  userType?: string;
}

/**
 * 指标保存
 */
export interface IApiPatientReportOperate {}

/**
 *
 */
export interface IApiPatientReportOperateListParamsItem {
  /** 患者id */
  patientId: number;
  /** 报告id */
  indexTermId: number;
  /** 其他报告名称 */
  remark?: string;
  /** 报告名称 */
  name?: string;
  /** 报告排序 */
  reportSort?: number;
  /** 来源id */
  sourceId?: number;
  /** 来源类型 */
  sourceType: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
}

/**
 * 指标集合保存 - body 请求参数
 */
export type IApiPatientReportOperateListParams =
  IApiPatientReportOperateListParamsItem[];

/**
 * 指标集合保存
 */
export interface IApiPatientReportOperateList {}

/**
 * OCR关键字查询 - body 请求参数
 */
export interface IApiOcrQueryKeywordsParams {
  /** 根ID */
  id?: number;
  /** 父ID */
  pid?: number;
  /** 关键词 */
  keyword?: string;
  /** 关键词分类 */
  category?: string;
  /** 关键词类型 */
  cls?: string;
}

/**
 *
 */
export interface IApiOcrQueryKeywordsItem {
  /** 主键ID */
  id?: number;
  /** 上一节点ID */
  pid?: number;
  /** 关键词 */
  keyword?: string;
  /** 分类 */
  category?: string;
  /** 类型 */
  cls?: string;
  /** 版本 */
  version?: number;
  /** 正序正则 */
  rePos?: string;
  /** 正序反向 */
  reNeg?: string;
  /** 是否模型字段 */
  forModel?: boolean;
  /** 是否必要字段 */
  necessity?: boolean;
  /** 前端类型 */
  uiMethod?: string;
}

/**
 * OCR关键字查询
 */
export type IApiOcrQueryKeywords = IApiOcrQueryKeywordsItem[];

/**
 * ai结构化识别 - body 请求参数
 */
export interface IApiOcrAiIdentifyParams {
  sentence: string;
  sessionName?: string;
  category?: string;
}

/**
 * ai结构化识别
 */
export interface IApiOcrAiIdentify {
  resultStatus?: number;
  aiParseData?: string;
}

/**
 * 入院记录列表 - body 请求参数
 */
export interface IApiOcrInHistoryParams {
  /** 患者id */
  userId: number;
}

/**
 * 病历信息
 */
export interface IApiOcrInHistoryHistoryInfo {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历名称 */
  name?: string;
  /** 病历完善度 */
  dataPerfect?: number;
  /** 入院时间/门诊时间 */
  inTime?: number;
  /** 出院时间 */
  leaveTime?: number;
  /** 是否入组病例 */
  isGroup?: number;
}

/**
 * 入院记录列表
 */
export interface IApiOcrInHistory {
  /** 病历数 */
  historyNum?: number;
  /** 病历信息 */
  historyInfo?: IApiOcrInHistoryHistoryInfo[];
}

/**
 * 入院记录列表 - body 请求参数
 */
export interface IInHistoryParams {
  /** 患者id */
  userId: number;
}

/**
 * 病历信息
 */
export interface IInHistoryHistoryInfo {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历名称 */
  name?: string;
  /** 病历完善度 */
  dataPerfect?: number;
  /** 入院时间/门诊时间 */
  inTime?: number;
  /** 出院时间 */
  leaveTime?: number;
  /** 是否入组病例 */
  isGroup?: number;
}

/**
 * 入院记录列表
 */
export interface IInHistory {
  /** 病历数 */
  historyNum?: number;
  /** 病历信息 */
  historyInfo?: IInHistoryHistoryInfo[];
}

/**
 * 出院记录列表 - body 请求参数
 */
export interface IApiOcrOutHistoryParams {
  /** 患者id */
  userId: number;
}

/**
 * 病历信息
 */
export interface IApiOcrOutHistoryHistoryInfo {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历名称 */
  name?: string;
  /** 病历完善度 */
  dataPerfect?: number;
  /** 入院时间/门诊时间 */
  inTime?: number;
  /** 出院时间 */
  leaveTime?: number;
  /** 是否入组病例 */
  isGroup?: number;
}

/**
 * 出院记录列表
 */
export interface IApiOcrOutHistory {
  /** 病历数 */
  historyNum?: number;
  /** 病历信息 */
  historyInfo?: IApiOcrOutHistoryHistoryInfo[];
}

/**
 * 出院记录列表 - body 请求参数
 */
export interface IOutHistoryParams {
  /** 患者id */
  userId: number;
}

/**
 * 病历信息
 */
export interface IOutHistoryHistoryInfo {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历名称 */
  name?: string;
  /** 病历完善度 */
  dataPerfect?: number;
  /** 入院时间/门诊时间 */
  inTime?: number;
  /** 出院时间 */
  leaveTime?: number;
  /** 是否入组病例 */
  isGroup?: number;
}

/**
 * 出院记录列表
 */
export interface IOutHistory {
  /** 病历数 */
  historyNum?: number;
  /** 病历信息 */
  historyInfo?: IOutHistoryHistoryInfo[];
}

/**
 * 发起ocr识别 - body 请求参数
 */
export interface IApiOcrStartIdentifyParams {
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告） */
  annexType: number;
  /** 是否重新扫描（false：否，true：是） */
  isReIdentify?: boolean;
  /** 图片url */
  url: string;
}

/**
 * 基本信息
 */
export interface IApiOcrStartIdentifyLdParseDataInHospitalRecordBasicInfo {
  /** 身高 */
  height?: string;
  /** 体重 */
  weight?: string;
  /** 收缩压 */
  highPressure?: string;
  /** 舒张压 */
  lowPressure?: string;
  /** 心率 */
  heartRate?: string;
}

/**
 * 入院记录
 */
export interface IApiOcrStartIdentifyLdParseDataInHospitalRecord {
  /** 原文 */
  originalText?: string;
  /** 主诉、现病史 */
  mainSuit?: string;
  /** 个人史 */
  personalHistory?: string;
  /** 家族史 */
  familyHistory?: string;
  /** 既往史 */
  pastHistory?: string;
  /** 入院时间 */
  inHospitalTime?: string;
  /** 基本信息 */
  basicInfo?: IApiOcrStartIdentifyLdParseDataInHospitalRecordBasicInfo;
}

/**
 * 出院记录
 */
export interface IApiOcrStartIdentifyLdParseDataOutHospitalRecord {
  /** 原文 */
  originalText?: string;
  /** 临床诊断 */
  clinicalDiagnosis?: string;
}

/**
 * 检验报告指标信息
 */
export interface IApiOcrStartIdentifyLdParseDataInspectionReportIndexInfo {
  /** 指标大类 */
  checkType?: number;
  /** 指标小类 */
  indexType?: number;
  /** 指标信息 */
  content?: number;
  /** 指标名称 */
  name?: string;
}

/**
 * 检验报告
 */
export interface IApiOcrStartIdentifyLdParseDataInspectionReport {
  /** 检验报告时间 */
  reportTime?: string;
  /** 检验报告指标信息 */
  indexInfo?: IApiOcrStartIdentifyLdParseDataInspectionReportIndexInfo[];
}

/**
 * 门诊报告
 */
export interface IApiOcrStartIdentifyLdParseDataOutpatientReport {
  /** 原文 */
  originalText?: string;
  /** 主诉 */
  mainSuit?: string;
  /** 诊断 */
  diagnosis?: string;
}

/**
 * 左医识别解析数据
 */
export interface IApiOcrStartIdentifyLdParseData {
  /** 入院记录 */
  inHospitalRecord?: IApiOcrStartIdentifyLdParseDataInHospitalRecord;
  /** 出院记录 */
  outHospitalRecord?: IApiOcrStartIdentifyLdParseDataOutHospitalRecord;
  /** 检验报告 */
  inspectionReport?: IApiOcrStartIdentifyLdParseDataInspectionReport;
  /** 门诊报告 */
  outpatientReport?: IApiOcrStartIdentifyLdParseDataOutpatientReport;
}

/**
 *
 */
export interface IApiOcrStartIdentifyAiParseDataDrinkHistoryInfo {
  /** type: 0既往 1目前 */
  type?: string;
  /** 年 */
  year?: string;
  /** 啤酒量 */
  beer?: string;
  /** 白酒量 */
  liquor?: string;
}

/**
 * 饮酒史
 */
export interface IApiOcrStartIdentifyAiParseDataDrinkHistory {
  /** isDrink是否喝酒  0:不喝酒，1:喝酒 */
  isDrink?: string;
  info?: IApiOcrStartIdentifyAiParseDataDrinkHistoryInfo;
}

/**
 * 吸烟史详情信息
 */
export interface IApiOcrStartIdentifyAiParseDataSmokeHistoryInfo {
  /** type:1目前 2既往 */
  type?: string;
  /** 年 */
  year?: string;
  /** 吸烟多少支 */
  branch?: string;
}

/**
 * 吸烟史
 */
export interface IApiOcrStartIdentifyAiParseDataSmokeHistory {
  /** isSmoke是否吸烟: 0不吸烟，1吸烟 */
  isSmoke?: string;
  /** 吸烟史详情信息 */
  info?: IApiOcrStartIdentifyAiParseDataSmokeHistoryInfo;
}

/**
 * 家族史
 */
export interface IApiOcrStartIdentifyAiParseDataFamilyHistoryList {
  /** 疾病类型 1:心血管；2：肿瘤；3：遗传 */
  diseaseHistory?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 主诉
 */
export interface IApiOcrStartIdentifyAiParseDataSuitList {
  /** 数字 */
  time?: string;
  /** 单位 */
  unit?: string;
  /** 症状名称 */
  name?: string;
  /** 症状id */
  id?: number;
}

/**
 * 病种列表
 */
export interface IApiOcrStartIdentifyAiParseDataDiseaseList {
  /** 病种id */
  diseaseId?: number;
  /** 上级病种id */
  pid?: number;
  /** 病种名称 */
  diseaseName?: string;
  /** 备注 */
  remark?: string;
}

/**
 * ai结构化识别解析数据
 */
export interface IApiOcrStartIdentifyAiParseData {
  /** 饮酒史 */
  drinkHistory?: IApiOcrStartIdentifyAiParseDataDrinkHistory;
  /** 吸烟史 */
  smokeHistory?: IApiOcrStartIdentifyAiParseDataSmokeHistory;
  /** 家族史 */
  familyHistoryList?: IApiOcrStartIdentifyAiParseDataFamilyHistoryList[];
  /** 主诉 */
  suitList?: IApiOcrStartIdentifyAiParseDataSuitList[];
  /** 病种列表 */
  diseaseList?: IApiOcrStartIdentifyAiParseDataDiseaseList[];
  /** 病种名称列表 */
  diseaseNameList?: string[];
}

/**
 * 发起ocr识别
 */
export interface IApiOcrStartIdentify {
  /** 图片识别状态 */
  ocrTaskStatus?: number;
  /** 识别结果状态（0识别失败  1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告） */
  photoType?: number;
  /** 左医识别解析数据 */
  ldParseData?: IApiOcrStartIdentifyLdParseData;
  /** ai结构化识别解析数据 */
  aiParseData?: IApiOcrStartIdentifyAiParseData;
}

/**
 * 发起ocr识别 (第二阶段) - body 请求参数
 */
export interface IApiOcrStartScanParams {
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告、5手术记录、6 12导联心电图、7动态心电图、8心脏彩超） */
  annexType: number;
  /** 是否重新扫描（false：否，true：是） */
  isReIdentify?: boolean;
  /** 图片url */
  url: string;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
}

/**
 * 基本信息
 */
export interface IApiOcrStartScanLdParseDataInHospitalRecordBasicInfo {
  /** 身高 */
  height?: string;
  /** 体重 */
  weight?: string;
  /** 收缩压 */
  highPressure?: string;
  /** 舒张压 */
  lowPressure?: string;
  /** 心率 */
  heartRate?: string;
}

/**
 * 入院记录
 */
export interface IApiOcrStartScanLdParseDataInHospitalRecord {
  /** 原文 */
  originalText?: string;
  /** 主诉 */
  mainSuit?: string;
  /** 现病史 */
  medicalHistory?: string;
  /** 个人史 */
  personalHistory?: string;
  /** 家族史 */
  familyHistory?: string;
  /** 既往史 */
  pastHistory?: string;
  /** 入院时间 */
  inHospitalTime?: string;
  /** 基本信息 */
  basicInfo?: IApiOcrStartScanLdParseDataInHospitalRecordBasicInfo;
}

/**
 * 出院记录
 */
export interface IApiOcrStartScanLdParseDataOutHospitalRecord {
  /** 原文 */
  originalText?: string;
  /** 临床诊断 */
  clinicalDiagnosis?: string;
}

/**
 * 检验报告指标信息
 */
export interface IApiOcrStartScanLdParseDataInspectionReportIndexInfo {
  /** 指标大类 */
  checkType?: number;
  /** 指标小类 */
  indexType?: number;
  /** 指标信息 */
  content?: number;
  /** 指标名称 */
  name?: string;
}

/**
 * 检验报告
 */
export interface IApiOcrStartScanLdParseDataInspectionReport {
  /** 检验报告时间 */
  reportTime?: string;
  /** 检验报告指标信息 */
  indexInfo?: IApiOcrStartScanLdParseDataInspectionReportIndexInfo[];
  /** 原文 */
  originalText?: string;
}

/**
 * 门诊报告
 */
export interface IApiOcrStartScanLdParseDataOutpatientReport {
  /** 原文 */
  originalText?: string;
  /** 主诉 */
  mainSuit?: string;
  /** 诊断 */
  diagnosis?: string;
}

/**
 * 手术记录
 */
export interface IApiOcrStartScanLdParseDataSurgicalRecord {
  /** 原文 */
  originalText?: string;
}

/**
 * 心脏彩超
 */
export interface IApiOcrStartScanLdParseDataColorUltrasoundDTO {
  /** 原文 */
  originalText?: string;
}

/**
 * 十二导联心电图
 */
export interface IApiOcrStartScanLdParseDataTwelveLeadEleDTO {
  /** 原文 */
  originalText?: string;
}

/**
 * 门诊处方
 */
export interface IApiOcrStartScanLdParseDataOutPatientPrescriptionDTO {
  /** 原文 */
  originalText?: string;
}

/**
 * 动态心电图
 */
export interface IApiOcrStartScanLdParseDataDynamicDTO {
  /** 原文 */
  originalText?: string;
}

/**
 * 左医识别解析数据
 */
export interface IApiOcrStartScanLdParseData {
  /** 入院记录 */
  inHospitalRecord?: IApiOcrStartScanLdParseDataInHospitalRecord;
  /** 出院记录 */
  outHospitalRecord?: IApiOcrStartScanLdParseDataOutHospitalRecord;
  /** 检验报告 */
  inspectionReport?: IApiOcrStartScanLdParseDataInspectionReport;
  /** 门诊报告 */
  outpatientReport?: IApiOcrStartScanLdParseDataOutpatientReport;
  /** 手术记录 */
  surgicalRecord?: IApiOcrStartScanLdParseDataSurgicalRecord;
  /** 心脏彩超 */
  colorUltrasoundDTO?: IApiOcrStartScanLdParseDataColorUltrasoundDTO;
  /** 十二导联心电图 */
  twelveLeadEleDTO?: IApiOcrStartScanLdParseDataTwelveLeadEleDTO;
  /** 门诊处方 */
  outPatientPrescriptionDTO?: IApiOcrStartScanLdParseDataOutPatientPrescriptionDTO;
  /** 动态心电图 */
  dynamicDTO?: IApiOcrStartScanLdParseDataDynamicDTO;
}

/**
 *
 */
export interface IApiOcrStartScanAiParseDataDrinkHistoryInfo {
  /** type: 0既往 1目前 */
  type?: number;
  /** 年 */
  year?: string;
  /** 啤酒量 */
  beer?: string;
  /** 白酒量 */
  liquor?: string;
}

/**
 * 饮酒史
 */
export interface IApiOcrStartScanAiParseDataDrinkHistory {
  /** isDrink是否喝酒  0:不喝酒，1:喝酒 */
  isDrink?: number;
  info?: IApiOcrStartScanAiParseDataDrinkHistoryInfo;
}

/**
 * 吸烟史详情信息
 */
export interface IApiOcrStartScanAiParseDataSmokeHistoryInfo {
  /** type:1目前 2既往 */
  type?: number;
  /** 年 */
  year?: string;
  /** 吸烟多少支 */
  branch?: string;
}

/**
 * 吸烟史
 */
export interface IApiOcrStartScanAiParseDataSmokeHistory {
  /** isSmoke是否吸烟: 0不吸烟，1吸烟 */
  isSmoke?: number;
  /** 吸烟史详情信息 */
  info?: IApiOcrStartScanAiParseDataSmokeHistoryInfo;
}

/**
 * 家族史
 */
export interface IApiOcrStartScanAiParseDataFamilyHistoryList {
  /** 疾病类型 1:心血管；2：肿瘤；3：遗传 */
  diseaseHistory?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 主诉
 */
export interface IApiOcrStartScanAiParseDataSuitList {
  /** 数字 */
  time?: string;
  /** 单位 */
  unit?: string;
  /** 症状名称 */
  name?: string;
  /** 症状id */
  id?: number;
}

/**
 * 病种列表
 */
export interface IApiOcrStartScanAiParseDataDiseaseList {
  /** 病种id */
  diseaseId?: number;
  /** 上级病种id */
  pid?: number;
  /** 病种名称 */
  diseaseName?: string;
  /** 备注 */
  remark?: string;
}

/**
 * ai结构化识别解析数据
 */
export interface IApiOcrStartScanAiParseData {
  /** 饮酒史 */
  drinkHistory?: IApiOcrStartScanAiParseDataDrinkHistory;
  /** 吸烟史 */
  smokeHistory?: IApiOcrStartScanAiParseDataSmokeHistory;
  /** 家族史 */
  familyHistoryList?: IApiOcrStartScanAiParseDataFamilyHistoryList[];
  /** 主诉 */
  suitList?: IApiOcrStartScanAiParseDataSuitList[];
  /** 病种列表 */
  diseaseList?: IApiOcrStartScanAiParseDataDiseaseList[];
  /** 病种名称列表 */
  diseaseNameList?: string[];
}

/**
 * 发起ocr识别 (第二阶段)
 */
export interface IApiOcrStartScan {
  /** 图片识别状态 */
  ocrTaskStatus?: number;
  /** 识别结果状态（0识别失败  1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告） */
  photoType?: number;
  /** 左医识别解析数据 */
  ldParseData?: IApiOcrStartScanLdParseData;
  /** ai结构化识别解析数据 */
  aiParseData?: IApiOcrStartScanAiParseData;
}

/**
 * 患者复查列表 - body 请求参数
 */
export interface IApiOcrReviewListParams {
  /** 患者id */
  userId: number;
}

/**
 * 复查信息
 */
export interface IApiOcrReviewListReviewInfo {
  /** 复查id */
  reviewId?: number;
  /** 复查次数--0代表个性化复查 */
  times?: number;
  /** 复查状态--1未开始 16检查待确认 32检查待上传 64 待确认结论 128 复查完成 */
  status?: number;
  /** 预计复查开始时间 */
  date?: string;
  /** 医助确认时间 */
  processTime?: string;
}

/**
 * 患者复查列表
 */
export interface IApiOcrReviewList {
  /** 复查数 */
  reviewNum?: number;
  /** 复查信息 */
  reviewInfo?: IApiOcrReviewListReviewInfo[];
}

/**
 * 批量OCR识别提交 - body 请求参数
 */
export interface IApiOcrBatchScanImageParams {
  urlList: string[];
  userId: number;
  userType: number;
  subTaskId: number;
}

/**
 * 批量OCR识别提交
 */
export interface IApiOcrBatchScanImage {
  batchTaskId?: number;
}

/**
 * 批量获取图片状态 - body 请求参数
 */
export interface IApiOcrPhotoStatusParams {
  /** 图片url集合 */
  urls: string[];
}

/**
 *
 */
export interface IApiOcrPhotoStatusItem {
  /** 图片地址 */
  url?: string;
  /** 识别状态（0识别中  1已识别  2未识别） */
  ocrStatus?: number;
  /** 识别结果状态（0识别失败  1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告） */
  photoType?: number;
}

/**
 * 批量获取图片状态
 */
export type IApiOcrPhotoStatus = IApiOcrPhotoStatusItem[];

/**
 * 指定版本字典 - body 请求参数
 */
export interface IApiOcrAppointKeywordParams {
  /** 字典版本 */
  version: number;
}

/**
 * 字典数据
 */
export interface IApiOcrAppointKeywordKeywordList {
  /** 字典id */
  id?: number;
  /** 父id */
  pid?: number;
  /** 字典key */
  keyword?: string;
  /** 字典类别 */
  category?: string;
  /** 类名 */
  cls?: string;
  /** 字典版本 */
  version?: number;
}

/**
 * 指定版本字典
 */
export interface IApiOcrAppointKeyword {
  /** 字典版本 */
  version?: number;
  /** 字典数据 */
  keywordList?: IApiOcrAppointKeywordKeywordList[];
}

/**
 * 新版OCR识别提交 - body 请求参数
 */
export interface IApiOcrNewScanImageParams {
  url: string;
  rotation?: number;
  userId?: number;
  userType?: number;
}

/**
 * 新版OCR识别提交
 */
export interface IApiOcrNewScanImage {
  ocrTaskStatus?: number;
  resultStatus?: number;
  photoType?: number;
  ldRawData?: string;
  ldParseData?: string;
  aiParseData?: string;
  enhancedUrl?: string;
  taskId?: number;
}

/**
 * 最新版本字典 - body 请求参数
 */
export interface IApiOcrLatestKeywordParams {
  data: string;
}

/**
 * 字典数据
 */
export interface IApiOcrLatestKeywordKeywordList {
  /** 字典id */
  id?: number;
  /** 父id */
  pid?: number;
  /** 字典key */
  keyword?: string;
  /** 字典类别 */
  category?: string;
  /** 类名 */
  cls?: string;
  /** 字典版本 */
  version?: number;
}

/**
 * 最新版本字典
 */
export interface IApiOcrLatestKeyword {
  /** 字典版本 */
  version?: number;
  /** 字典数据 */
  keywordList?: IApiOcrLatestKeywordKeywordList[];
}

/**
 * 查询最新版本项目分类 - body 请求参数
 */
export interface IApiOcrCategoryLatestQueryParams {
  enCategory?: string;
  enCategoryList?: string[];
}

/**
 * undefined
 */
export interface IApiOcrCategoryLatestQueryItemSessions {
  enSession?: string;
  sessionName?: string;
}

/**
 *
 */
export interface IApiOcrCategoryLatestQueryItem {
  enCategory?: string;
  category?: string;
  sessions?: IApiOcrCategoryLatestQueryItemSessions[];
}

/**
 * 查询最新版本项目分类
 */
export type IApiOcrCategoryLatestQuery = IApiOcrCategoryLatestQueryItem[];

/**
 * 直接扫描图片---3.4.4 - body 请求参数
 */
export interface IApiOcrScanImageParams {
  /** 图片url */
  url: string;
  /** 0为不旋转， 1为顺时针90度， 2为顺时针180度， 3为顺时针270度。注：如果不传rotation 则默认为0 */
  rotation?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
}

/**
 * 直接扫描图片---3.4.4
 */
export interface IApiOcrScanImage {
  /** 图片识别状态 */
  ocrTaskStatus?: number;
  /** 识别结果状态（0识别失败  1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告） */
  photoType?: number;
  /** 图片数据结构话 */
  ldParseData?: string;
  /** ai结构化识别解析数据 */
  aiParseData?: string;
  /** ai解析图片数据 */
  ldRawData?: string;
  /** 增强图 */
  enhancedUrl?: string;
  /** 任务id */
  taskId?: number;
}

/**
 * 矫正图片 - body 请求参数
 */
export interface IApiOcrImgRectifiedParams {
  /** 图片地址 */
  url?: string;
  /** 坐标 */
  corners?: number[][];
}

/**
 * 矫正图片
 */
export interface IApiOcrImgRectified {
  /** 任务id */
  taskId?: number;
  /** 原始图片地址 */
  rogUrl?: string;
  /** 矫正后的图片 */
  url?: string;
  /** 图片旋转角度,分为 0,1,2,3 ， 0为不旋转， 1为顺时针90度， 2为顺时针180度， 3为顺时针270度。 */
  rotation?: number;
  /** 坐标 */
  corners?: number[][];
}

/**
 * 获取图片定位中内容 - body 请求参数
 */
export interface IApiOcrBoundingboxParams {
  taskId: number;
  url: string;
}

/**
 * undefined
 */
export interface IApiOcrBoundingboxItemsTokens {
  tokenBoxes?: number[][];
  tokenText?: string;
}

/**
 * undefined
 */
export interface IApiOcrBoundingboxItems {
  text?: string;
  boundingBoxes?: number[][];
  tokens?: IApiOcrBoundingboxItemsTokens[];
}

/**
 * 获取图片定位中内容
 */
export interface IApiOcrBoundingbox {
  taskId?: number;
  taskType?: string;
  version?: string;
  items?: IApiOcrBoundingboxItems[];
}

/**
 * 获取指定版本词典数据 - body 请求参数
 */
export interface IApiOcrCertainVersionKeywordParams {
  enCategory?: string;
  enCategoryList?: string[];
  version: number;
}

/**
 * undefined
 */
export interface IApiOcrCertainVersionKeywordItemItemsOptions {
  id?: number;
  pid?: number;
  key?: string;
  value?: string;
  enSession?: string;
  sessionName?: string;
  rowId?: number;
  rowOrder?: number;
  necessity?: boolean;
  uiDisable?: boolean;
  uiMethod?: string;
  uiOptions?: string;
  uiRules?: string;
  version?: number;
  options?: any[];
  items?: any[];
  columns?: any[];
}

/**
 * undefined
 */
export interface IApiOcrCertainVersionKeywordItemItemsItems {
  id?: number;
  pid?: number;
  key?: string;
  value?: string;
  enSession?: string;
  sessionName?: string;
  rowId?: number;
  rowOrder?: number;
  necessity?: boolean;
  uiDisable?: boolean;
  uiMethod?: string;
  uiOptions?: string;
  uiRules?: string;
  version?: number;
  options?: any[];
  items?: any[];
  columns?: any[];
}

/**
 * undefined
 */
export interface IApiOcrCertainVersionKeywordItemItemsColumns {
  id?: number;
  pid?: number;
  key?: string;
  value?: string;
  enSession?: string;
  sessionName?: string;
  rowId?: number;
  rowOrder?: number;
  necessity?: boolean;
  uiDisable?: boolean;
  uiMethod?: string;
  uiOptions?: string;
  uiRules?: string;
  version?: number;
  options?: any[];
  items?: any[];
  columns?: any[];
}

/**
 * undefined
 */
export interface IApiOcrCertainVersionKeywordItemItems {
  id?: number;
  pid?: number;
  key?: string;
  value?: string;
  enSession?: string;
  sessionName?: string;
  rowId?: number;
  rowOrder?: number;
  necessity?: boolean;
  uiDisable?: boolean;
  uiMethod?: string;
  uiOptions?: string;
  uiRules?: string;
  version?: number;
  options?: IApiOcrCertainVersionKeywordItemItemsOptions[];
  items?: IApiOcrCertainVersionKeywordItemItemsItems[];
  columns?: IApiOcrCertainVersionKeywordItemItemsColumns[];
}

/**
 *
 */
export interface IApiOcrCertainVersionKeywordItem {
  key?: string;
  value?: string;
  fromCls?: string;
  fromTable?: string;
  necessity?: boolean;
  uiDisable?: boolean;
  uiMethod?: string;
  uiOptions?: string;
  uiRules?: string;
  version?: number;
  items?: IApiOcrCertainVersionKeywordItemItems[];
}

/**
 * 获取指定版本词典数据
 */
export type IApiOcrCertainVersionKeyword = IApiOcrCertainVersionKeywordItem[];

/**
 * 获取最新版本所有词典数据 - body 请求参数
 */
export interface IApiOcrAllLatestKeywordParams {
  enCategory?: string;
  enCategoryList?: string[];
}

/**
 * undefined
 */
export interface IApiOcrAllLatestKeywordItemItemsOptions {
  id?: number;
  pid?: number;
  key?: string;
  value?: string;
  enSession?: string;
  sessionName?: string;
  rowId?: number;
  rowOrder?: number;
  necessity?: boolean;
  uiDisable?: boolean;
  uiMethod?: string;
  uiOptions?: string;
  uiRules?: string;
  version?: number;
  options?: any[];
  items?: any[];
  columns?: any[];
}

/**
 * undefined
 */
export interface IApiOcrAllLatestKeywordItemItemsItems {
  id?: number;
  pid?: number;
  key?: string;
  value?: string;
  enSession?: string;
  sessionName?: string;
  rowId?: number;
  rowOrder?: number;
  necessity?: boolean;
  uiDisable?: boolean;
  uiMethod?: string;
  uiOptions?: string;
  uiRules?: string;
  version?: number;
  options?: any[];
  items?: any[];
  columns?: any[];
}

/**
 * undefined
 */
export interface IApiOcrAllLatestKeywordItemItemsColumns {
  id?: number;
  pid?: number;
  key?: string;
  value?: string;
  enSession?: string;
  sessionName?: string;
  rowId?: number;
  rowOrder?: number;
  necessity?: boolean;
  uiDisable?: boolean;
  uiMethod?: string;
  uiOptions?: string;
  uiRules?: string;
  version?: number;
  options?: any[];
  items?: any[];
  columns?: any[];
}

/**
 * undefined
 */
export interface IApiOcrAllLatestKeywordItemItems {
  id?: number;
  pid?: number;
  key?: string;
  value?: string;
  enSession?: string;
  sessionName?: string;
  rowId?: number;
  rowOrder?: number;
  necessity?: boolean;
  uiDisable?: boolean;
  uiMethod?: string;
  uiOptions?: string;
  uiRules?: string;
  version?: number;
  options?: IApiOcrAllLatestKeywordItemItemsOptions[];
  items?: IApiOcrAllLatestKeywordItemItemsItems[];
  columns?: IApiOcrAllLatestKeywordItemItemsColumns[];
}

/**
 *
 */
export interface IApiOcrAllLatestKeywordItem {
  key?: string;
  value?: string;
  fromCls?: string;
  fromTable?: string;
  necessity?: boolean;
  uiDisable?: boolean;
  uiMethod?: string;
  uiOptions?: string;
  uiRules?: string;
  version?: number;
  items?: IApiOcrAllLatestKeywordItemItems[];
}

/**
 * 获取最新版本所有词典数据
 */
export type IApiOcrAllLatestKeyword = IApiOcrAllLatestKeywordItem[];

/**
 * 计算纸张角度 - body 请求参数
 */
export interface IApiOcrImgCornerParams {
  /** 图片所在的oss 地址 */
  url: string;
  /** 0为不旋转， 1为逆时针90度， 2为逆时针180度， 3为逆时针270度。注：如果不传rotation 则默认为0 */
  rotation?: number;
}

/**
 * 计算纸张角度
 */
export interface IApiOcrImgCorner {
  /** 任务id */
  taskId?: number;
  /** 坐标 */
  corners?: number[][];
  /** 原始图片地址 */
  orgUrl?: string;
  /** 图片旋转角度,分为 0,1,2,3 ， 0为不旋转， 1为顺时针90度， 2为顺时针180度， 3为顺时针270度。 */
  rotation?: number;
}

/**
 * 通过类型重新扫描图片 - body 请求参数
 */
export interface IApiOcrReformatImageParams {
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告、5手术记录、6 12导联心电图、7动态心电图、8心脏彩超，9门诊处方） */
  annexType: number;
  /** plaintext */
  content: string;
  /** taskId */
  taskId: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
}

/**
 * 通过类型重新扫描图片
 */
export interface IApiOcrReformatImage {
  /** 图片识别状态 */
  ocrTaskStatus?: number;
  /** 识别结果状态（0识别失败  1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告） */
  photoType?: number;
  /** 图片数据结构话 */
  ldParseData?: string;
  /** ai结构化识别解析数据 */
  aiParseData?: string;
  /** ai解析图片数据 */
  ldRawData?: string;
  /** 增强图 */
  enhancedUrl?: string;
  /** 任务id */
  taskId?: number;
}

/**
 * 重新识别---提交识别 - body 请求参数
 */
export interface IApiOcrReScanImageParams {
  /** 图片地址 */
  url: string;
  /** 坐标 */
  corners?: number[][];
  /** 0为不旋转， 1为顺时针90度， 2为顺时针180度， 3为顺时针270度。注：如果不传rotation 则默认为0 */
  rotation: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
}

/**
 * 重新识别---提交识别
 */
export interface IApiOcrReScanImage {
  /** 图片识别状态 */
  ocrTaskStatus?: number;
  /** 识别结果状态（0识别失败  1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告） */
  photoType?: number;
  /** 图片数据结构话 */
  ldParseData?: string;
  /** ai结构化识别解析数据 */
  aiParseData?: string;
  /** ai解析图片数据 */
  ldRawData?: string;
  /** 增强图 */
  enhancedUrl?: string;
  /** 任务id */
  taskId?: number;
}

/**
 * 门诊报告列表 - body 请求参数
 */
export interface IApiOcrOutpatientHistoryParams {
  /** 患者id */
  userId: number;
}

/**
 * 病历信息
 */
export interface IApiOcrOutpatientHistoryHistoryInfo {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历名称 */
  name?: string;
  /** 病历完善度 */
  dataPerfect?: number;
  /** 入院时间/门诊时间 */
  inTime?: number;
  /** 出院时间 */
  leaveTime?: number;
  /** 是否入组病例 */
  isGroup?: number;
}

/**
 * 门诊报告列表
 */
export interface IApiOcrOutpatientHistory {
  /** 病历数 */
  historyNum?: number;
  /** 病历信息 */
  historyInfo?: IApiOcrOutpatientHistoryHistoryInfo[];
}

/**
 * 门诊报告列表 - body 请求参数
 */
export interface IOutpatientHistoryParams {
  /** 患者id */
  userId: number;
}

/**
 * 病历信息
 */
export interface IOutpatientHistoryHistoryInfo {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历名称 */
  name?: string;
  /** 病历完善度 */
  dataPerfect?: number;
  /** 入院时间/门诊时间 */
  inTime?: number;
  /** 出院时间 */
  leaveTime?: number;
  /** 是否入组病例 */
  isGroup?: number;
}

/**
 * 门诊报告列表
 */
export interface IOutpatientHistory {
  /** 病历数 */
  historyNum?: number;
  /** 病历信息 */
  historyInfo?: IOutpatientHistoryHistoryInfo[];
}

/**
 * 手术表
 */
export interface IApiAddOrUpdateBeHospitalizedParamsSurgeryDTOS {
  /** 手术id */
  surgeryId?: number;
  /** 手术时间 */
  surgeryTime?: string;
  /** 结论 */
  conclusion?: string;
  /** 手术信息 */
  surgeryInfo?: string;
  /** 附件 */
  accessory?: string;
}

/**
 * 药品类
 */
export interface IApiAddOrUpdateBeHospitalizedParamsDrugDetailDTO {
  /** 药品名称 */
  drugName?: string;
  /** 用法(频率) */
  drugUsage?: string;
  /** 规格 */
  drugSpec?: string;
  /** 用药方式（用法) */
  drugMode?: string;
  /** 单次用量 */
  drugAmount?: string;
  /** 服药时间 1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
}

/**
 * 指标类
 */
export interface IApiAddOrUpdateBeHospitalizedParamsIndexDTO {
  /** 指标大类 */
  checkType?: number;
  /** 指标小类 */
  indexType?: number;
  /** 指标数据 */
  content?: string;
  /** 检查时间 */
  checkTime?: string;
  /** 异常等级 */
  errorLevel?: number;
}

/**
 * 新增或修改入院信息 - body 请求参数
 */
export interface IApiAddOrUpdateBeHospitalizedParams {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历名称 */
  caseName?: string;
  /** 病历类型 */
  caseType?: number;
  /** 患者id */
  userId?: number;
  /** 资料完善度 */
  dataPerfect?: number;
  /** 住院检查附件 */
  checkAccessory?: string;
  /** 创建时间 */
  createTime?: string;
  /** 二期住院时间 */
  secondTime?: string;
  /** 住院id */
  beId?: number;
  /** 住院时间 */
  inTime?: string;
  /** 饮酒史 */
  drinkHistory?: string;
  /** 吸烟史 */
  smokeHistory?: string;
  /** 家族史 */
  familyHistory?: string;
  /** 基本信息 */
  basicInfo?: string;
  /** 主诉 */
  suit?: string;
  /** 附件 */
  accessory?: string;
  /** 手术表 */
  surgeryDTOS?: IApiAddOrUpdateBeHospitalizedParamsSurgeryDTOS[];
  /** 病种id */
  diseaseIds?: number[];
  /** 其他病种id */
  otherDiseaseId?: number;
  /** 病种备注 */
  remark?: string;
  /** 出院时间 */
  leaveTime?: string;
  /** 出院id */
  outId?: number;
  /** 药品信息id */
  drugInfoId?: number;
  /** 药品类 */
  drugDetailDTO?: IApiAddOrUpdateBeHospitalizedParamsDrugDetailDTO[];
  /** 指标类 */
  indexDTO?: IApiAddOrUpdateBeHospitalizedParamsIndexDTO[];
  /** 病种名称 */
  diseaseName?: string;
  /** 心电图id */
  ecgId?: number;
}

/**
 * 新增或修改入院信息
 */
export interface IApiAddOrUpdateBeHospitalized {
  /** 病历id */
  patientHistoryId?: number;
  /** 住院id */
  beId?: number;
}

/**
 * 手术表
 */
export interface IApiAddOrUpdateLeaveHospitalParamsSurgeryDTOS {
  /** 手术id */
  surgeryId?: number;
  /** 手术时间 */
  surgeryTime?: string;
  /** 结论 */
  conclusion?: string;
  /** 手术信息 */
  surgeryInfo?: string;
  /** 附件 */
  accessory?: string;
}

/**
 * 药品类
 */
export interface IApiAddOrUpdateLeaveHospitalParamsDrugDetailDTO {
  /** 药品名称 */
  drugName?: string;
  /** 用法(频率) */
  drugUsage?: string;
  /** 规格 */
  drugSpec?: string;
  /** 用药方式（用法) */
  drugMode?: string;
  /** 单次用量 */
  drugAmount?: string;
  /** 服药时间 1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
}

/**
 * 指标类
 */
export interface IApiAddOrUpdateLeaveHospitalParamsIndexDTO {
  /** 指标大类 */
  checkType?: number;
  /** 指标小类 */
  indexType?: number;
  /** 指标数据 */
  content?: string;
  /** 检查时间 */
  checkTime?: string;
  /** 异常等级 */
  errorLevel?: number;
}

/**
 * 新增或修改出院信息 - body 请求参数
 */
export interface IApiAddOrUpdateLeaveHospitalParams {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历名称 */
  caseName?: string;
  /** 病历类型 */
  caseType?: number;
  /** 患者id */
  userId?: number;
  /** 资料完善度 */
  dataPerfect?: number;
  /** 住院检查附件 */
  checkAccessory?: string;
  /** 创建时间 */
  createTime?: string;
  /** 二期住院时间 */
  secondTime?: string;
  /** 住院id */
  beId?: number;
  /** 住院时间 */
  inTime?: string;
  /** 饮酒史 */
  drinkHistory?: string;
  /** 吸烟史 */
  smokeHistory?: string;
  /** 家族史 */
  familyHistory?: string;
  /** 基本信息 */
  basicInfo?: string;
  /** 主诉 */
  suit?: string;
  /** 附件 */
  accessory?: string;
  /** 手术表 */
  surgeryDTOS?: IApiAddOrUpdateLeaveHospitalParamsSurgeryDTOS[];
  /** 病种id */
  diseaseIds?: number[];
  /** 其他病种id */
  otherDiseaseId?: number;
  /** 病种备注 */
  remark?: string;
  /** 出院时间 */
  leaveTime?: string;
  /** 出院id */
  outId?: number;
  /** 药品信息id */
  drugInfoId?: number;
  /** 药品类 */
  drugDetailDTO?: IApiAddOrUpdateLeaveHospitalParamsDrugDetailDTO[];
  /** 指标类 */
  indexDTO?: IApiAddOrUpdateLeaveHospitalParamsIndexDTO[];
  /** 病种名称 */
  diseaseName?: string;
  /** 心电图id */
  ecgId?: number;
}

/**
 * 新增或修改出院信息
 */
export type IApiAddOrUpdateLeaveHospital = number;

/**
 * 手术表
 */
export interface IApiQueryDiseaseListParamsSurgeryDTOS {
  /** 手术id */
  surgeryId?: number;
  /** 手术时间 */
  surgeryTime?: string;
  /** 结论 */
  conclusion?: string;
  /** 手术信息 */
  surgeryInfo?: string;
  /** 附件 */
  accessory?: string;
}

/**
 * 药品类
 */
export interface IApiQueryDiseaseListParamsDrugDetailDTO {
  /** 药品名称 */
  drugName?: string;
  /** 用法(频率) */
  drugUsage?: string;
  /** 规格 */
  drugSpec?: string;
  /** 用药方式（用法) */
  drugMode?: string;
  /** 单次用量 */
  drugAmount?: string;
  /** 服药时间 1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
}

/**
 * 指标类
 */
export interface IApiQueryDiseaseListParamsIndexDTO {
  /** 指标大类 */
  checkType?: number;
  /** 指标小类 */
  indexType?: number;
  /** 指标数据 */
  content?: string;
  /** 检查时间 */
  checkTime?: string;
  /** 异常等级 */
  errorLevel?: number;
}

/**
 * 查询病种列表或者模糊匹配 - body 请求参数
 */
export interface IApiQueryDiseaseListParams {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历名称 */
  caseName?: string;
  /** 病历类型 */
  caseType?: number;
  /** 患者id */
  userId?: number;
  /** 资料完善度 */
  dataPerfect?: number;
  /** 住院检查附件 */
  checkAccessory?: string;
  /** 创建时间 */
  createTime?: string;
  /** 二期住院时间 */
  secondTime?: string;
  /** 住院id */
  beId?: number;
  /** 住院时间 */
  inTime?: string;
  /** 饮酒史 */
  drinkHistory?: string;
  /** 吸烟史 */
  smokeHistory?: string;
  /** 家族史 */
  familyHistory?: string;
  /** 基本信息 */
  basicInfo?: string;
  /** 主诉 */
  suit?: string;
  /** 附件 */
  accessory?: string;
  /** 手术表 */
  surgeryDTOS?: IApiQueryDiseaseListParamsSurgeryDTOS[];
  /** 病种id */
  diseaseIds?: number[];
  /** 其他病种id */
  otherDiseaseId?: number;
  /** 病种备注 */
  remark?: string;
  /** 出院时间 */
  leaveTime?: string;
  /** 出院id */
  outId?: number;
  /** 药品信息id */
  drugInfoId?: number;
  /** 药品类 */
  drugDetailDTO?: IApiQueryDiseaseListParamsDrugDetailDTO[];
  /** 指标类 */
  indexDTO?: IApiQueryDiseaseListParamsIndexDTO[];
  /** 病种名称 */
  diseaseName?: string;
  /** 心电图id */
  ecgId?: number;
}

/**
 * undefined
 */
export interface IApiQueryDiseaseListItemKey {}

/**
 *
 */
export interface IApiQueryDiseaseListItem {
  key?: IApiQueryDiseaseListItemKey;
}

/**
 * 查询病种列表或者模糊匹配
 */
export type IApiQueryDiseaseList = IApiQueryDiseaseListItem[];

/**
 * 查询症状列表 - body 请求参数
 */
export interface IApiQuerySymptomListParams {
  data: string;
}

/**
 *
 */
export interface IApiQuerySymptomListItem {
  /** 症状id */
  symptomId?: number;
  /** 症状名称 */
  symptomName?: string;
}

/**
 * 查询症状列表
 */
export type IApiQuerySymptomList = IApiQuerySymptomListItem[];

/**
 * 手术表
 */
export interface IApiGetOnlyBeHospitalizedParamsSurgeryDTOS {
  /** 手术id */
  surgeryId?: number;
  /** 手术时间 */
  surgeryTime?: string;
  /** 结论 */
  conclusion?: string;
  /** 手术信息 */
  surgeryInfo?: string;
  /** 附件 */
  accessory?: string;
}

/**
 * 药品类
 */
export interface IApiGetOnlyBeHospitalizedParamsDrugDetailDTO {
  /** 药品名称 */
  drugName?: string;
  /** 用法(频率) */
  drugUsage?: string;
  /** 规格 */
  drugSpec?: string;
  /** 用药方式（用法) */
  drugMode?: string;
  /** 单次用量 */
  drugAmount?: string;
  /** 服药时间 1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: number;
}

/**
 * 指标类
 */
export interface IApiGetOnlyBeHospitalizedParamsIndexDTO {
  /** 指标大类 */
  checkType?: number;
  /** 指标小类 */
  indexType?: number;
  /** 指标数据 */
  content?: string;
  /** 检查时间 */
  checkTime?: string;
  /** 异常等级 */
  errorLevel?: number;
}

/**
 * 获取住院信息 - body 请求参数
 */
export interface IApiGetOnlyBeHospitalizedParams {
  /** 病历id */
  patientHistoryId?: number;
  /** 病历名称 */
  caseName?: string;
  /** 病历类型 */
  caseType?: number;
  /** 患者id */
  userId?: number;
  /** 资料完善度 */
  dataPerfect?: number;
  /** 住院检查附件 */
  checkAccessory?: string;
  /** 创建时间 */
  createTime?: string;
  /** 二期住院时间 */
  secondTime?: string;
  /** 住院id */
  beId?: number;
  /** 住院时间 */
  inTime?: string;
  /** 饮酒史 */
  drinkHistory?: string;
  /** 吸烟史 */
  smokeHistory?: string;
  /** 家族史 */
  familyHistory?: string;
  /** 基本信息 */
  basicInfo?: string;
  /** 主诉 */
  suit?: string;
  /** 附件 */
  accessory?: string;
  /** 手术表 */
  surgeryDTOS?: IApiGetOnlyBeHospitalizedParamsSurgeryDTOS[];
  /** 病种id */
  diseaseIds?: number[];
  /** 其他病种id */
  otherDiseaseId?: number;
  /** 病种备注 */
  remark?: string;
  /** 出院时间 */
  leaveTime?: string;
  /** 出院id */
  outId?: number;
  /** 药品信息id */
  drugInfoId?: number;
  /** 药品类 */
  drugDetailDTO?: IApiGetOnlyBeHospitalizedParamsDrugDetailDTO[];
  /** 指标类 */
  indexDTO?: IApiGetOnlyBeHospitalizedParamsIndexDTO[];
  /** 病种名称 */
  diseaseName?: string;
  /** 心电图id */
  ecgId?: number;
}

/**
 * 下级病种（结构一致）
 */
export interface IApiGetOnlyBeHospitalizedKeyChildren {}

/**
 * undefined
 */
export interface IApiGetOnlyBeHospitalizedKey {
  /** 病种名字 */
  diseaseName: string;
  /** 下级病种（结构一致） */
  children?: IApiGetOnlyBeHospitalizedKeyChildren;
  /** 上级id */
  pId?: string;
  /** 病种id */
  diseaseId: string;
}

/**
 * 获取住院信息
 */
export interface IApiGetOnlyBeHospitalized {
  key?: IApiGetOnlyBeHospitalizedKey;
}

/**
 * 获取所有指标项 - body 请求参数
 */
export interface IApiRiskMaCheckIndexListParams {
  data: string;
}

/**
 *
 */
export interface IApiRiskMaCheckIndexListItem {
  /** 指标名称 */
  name?: string;
  /** 指标类型 */
  indexType?: number;
  /** 上级指标id */
  checkType?: number;
}

/**
 * 获取所有指标项
 */
export type IApiRiskMaCheckIndexList = IApiRiskMaCheckIndexListItem[];

/**
 * 获取病历出院信息 - query 请求参数
 */
export interface IApiGetOnlyLeaveHospitalQuery {
  /** 请求参数 */
  request?: string;
}

/**
 * 获取病历出院信息 - body 请求参数
 */
export interface IApiGetOnlyLeaveHospitalParams {
  /** 请求参数 */
  data: string;
}

/**
 * undefined
 */
export interface IApiGetOnlyLeaveHospitalKeyDrugList {
  /** 药品名称 */
  drugName?: string;
  /** 用法(频率) */
  drugUsage?: string;
  /** 规格 */
  drugSpec?: string;
  /** 用药方式（用法) */
  drugMode?: string;
  /** 单次用量 */
  drugAmount?: string;
  /** 服药时间 1：早上， 2：中午， 3：晚上， 4：早中晚， 5：早晚, 6：午晚, 7：早午 */
  medicineTime?: string;
}

/**
 * undefined
 */
export interface IApiGetOnlyLeaveHospitalKeyLeaveHospitalInfo {
  /** 出院id */
  outId?: number;
  /** 出院时间 */
  leaveTime?: string;
  /** 附件 */
  accessory?: string;
}

/**
 * undefined
 */
export interface IApiGetOnlyLeaveHospitalKeyClinicalDiagnosis {
  /** 病种名 */
  diseaseName?: string;
  /** 上级id */
  pid?: string;
  /** 0既往史 1 临床诊断 */
  type?: string;
  /** 病种id */
  diseaseId?: string;
}

/**
 * undefined
 */
export interface IApiGetOnlyLeaveHospitalKey {
  drugList?: IApiGetOnlyLeaveHospitalKeyDrugList;
  leaveHospitalInfo?: IApiGetOnlyLeaveHospitalKeyLeaveHospitalInfo;
  clinicalDiagnosis?: IApiGetOnlyLeaveHospitalKeyClinicalDiagnosis;
  stringClinicalList?: string[];
}

/**
 * 获取病历出院信息
 */
export interface IApiGetOnlyLeaveHospital {
  key?: IApiGetOnlyLeaveHospitalKey;
}

/**
 * 删除数据 - body 请求参数
 */
export interface IApiIndexDataDeleteParams {
  /** id */
  id: number;
  /** 指标模板名称 */
  templateName: string;
}

/**
 * 删除数据
 */
export type IApiIndexDataDelete = boolean;

/**
 * 动态监测血压 - body 请求参数
 */
export interface IApiIndexWatchMonitorParams {
  /** 患者id */
  patientId: number;
  /** 操作人id */
  uid: number;
  /** 角色类型 */
  roleType: number;
  /** 提醒时间 */
  remindTime: string;
  /** 监测周期 1 24h /2 48h /3 72h */
  monitorType: number;
  /** 白昼测量监测 */
  earlyFrequency: number;
  /** 夜晚测量监测 */
  lateFrequency: number;
}

/**
 * 动态监测血压
 */
export interface IApiIndexWatchMonitor {}

/**
 * 平均分布查询 - body 请求参数
 */
export interface IApiIndexEvenDistributionQueryParams {
  /** 患者id */
  patientId: number;
  /** 指标模板名称 */
  templateName: string;
  /** 平均分布时间类型  1 日平均/2周平均 /3月平均 */
  timeType: number;
}

/**
 * 内容数据
 */
export interface IApiIndexEvenDistributionQueryContentResponseDTO {
  /** 总数 */
  totalNum?: number;
  /** 异常数 */
  errorNum?: number;
  /** 最高心率 */
  highHeart?: number;
  /** 最低心率 */
  lowHeart?: number;
  /** 平均心率 */
  avgHeart?: number;
  /** ttr */
  ttR?: string;
  /** nttR */
  nttR?: string;
  /** 最大收缩压、空腹最高血糖 */
  maxHigh?: number;
  /** 最小收缩压、空腹最低血糖 */
  minHigh?: number;
  /** 最大收缩压、空腹最高血糖 平均 */
  avgHigh?: number;
  /** 最大舒张压、非空腹最高血糖 */
  maxLow?: number;
  /** 最小舒张压、非空腹最低血糖 */
  minLow?: number;
  /** 最小舒张压、非空腹最低血糖 平均 */
  avgLow?: number;
  /** 血压、心率 天数 */
  day?: number;
  /** 最大血脂 */
  maxBloodFat?: number;
  /** 最小血脂 */
  minBloodFat?: number;
  /** 最大空腹血糖异常等级 */
  maxFastingGiuErrorLevel?: number;
  /** 最大非空腹血糖异常等级 */
  maxNonFastingGiuErrorLevel?: number;
  /** 最小空腹血糖异常等级 */
  minFastingGiuErrorLevel?: number;
  /** 最小非空腹血糖异常等级 */
  minNonFastingGiuErrorLevel?: number;
  /** 最大血脂等级 */
  maxContentErrorLevel?: number;
  /** 最小血脂等级 */
  minContentErrorLevel?: number;
  /** 最大心率异常等级 */
  maxHeartRateErrorLevel?: number;
  /** 最小心率异常等级 */
  minHeartRateErrorLevel?: number;
  /** 最大收缩压异常等级 */
  maxHighPressureErrorLevel?: number;
  /** 最大舒张压异常等级 */
  maxLowPressureErrorLevel?: number;
  /** 最小收缩压异常等级 */
  minHighPressureErrorLevel?: number;
  /** 最小舒张压异常等级 */
  minLowPressureErrorLevel?: number;
}

/**
 * 指标数据
 */
export interface IApiIndexEvenDistributionQueryIndexDataDTOList {
  /** 横坐标 */
  key?: string;
  /** 纵坐标 */
  highValue?: number;
  /** 纵坐标 */
  lowValue?: number;
  /** 纵坐标 */
  heartRate?: number;
}

/**
 * 基线数据
 */
export interface IApiIndexEvenDistributionQueryBaseLineResponseDTO {
  /** 最高舒张压 */
  maxLowPressureLine?: number;
  /** 最低舒张压 */
  minLowPressureLine?: number;
  /** 最高收缩压 */
  maxHighPressureLine?: number;
  /** 最低收缩压 */
  minHighPressureLine?: number;
  /** 最低心率 */
  minHeartLine?: number;
  /** 最高心率 */
  maxHeartLine?: number;
  /** 最高血脂 */
  maxFatBloodLine?: number;
  /** 最低血脂 */
  minFatBloodLine?: number;
}

/**
 * 平均分布查询
 */
export interface IApiIndexEvenDistributionQuery {
  /** 内容数据 */
  contentResponseDTO?: IApiIndexEvenDistributionQueryContentResponseDTO;
  /** 指标数据 */
  indexDataDTOList?: IApiIndexEvenDistributionQueryIndexDataDTOList[];
  /** 基线数据 */
  baseLineResponseDTO?: IApiIndexEvenDistributionQueryBaseLineResponseDTO;
}

/**
 * 异常分布查询 - body 请求参数
 */
export interface IApiIndexAbnormalDistributionQueryParams {
  /** 患者id */
  patientId: number;
  /** 指标模板名称 */
  templateName: string;
  /** 平均分布时间类型  1 周平均/2月平均 */
  timeType: number;
}

/**
 * 内容数据
 */
export interface IApiIndexAbnormalDistributionQueryContentResponseDTO {
  /** 总数 */
  totalNum?: number;
  /** 异常数 */
  errorNum?: number;
  /** 最高心率 */
  highHeart?: number;
  /** 最低心率 */
  lowHeart?: number;
  /** 平均心率 */
  avgHeart?: number;
  /** ttr */
  ttR?: string;
  /** 最大收缩压、空腹最高血糖 */
  maxHigh?: number;
  /** 最小收缩压、空腹最低血糖 */
  minHigh?: number;
  /** 最大收缩压、空腹最高血糖 平均 */
  avgHigh?: number;
  /** 最大舒张压、非空腹最高血糖 */
  maxLow?: number;
  /** 最小舒张压、非空腹最低血糖 */
  minLow?: number;
  /** 最小舒张压、非空腹最低血糖 平均 */
  avgLow?: number;
  /** 血压、心率 天数 */
  day?: number;
  /** 最大血脂 */
  maxBloodFat?: number;
  /** 最小血脂 */
  minBloodFat?: number;
}

/**
 * 指标数据
 */
export interface IApiIndexAbnormalDistributionQueryIndexDataDTOList {
  /** 横坐标 */
  date?: string;
  /** 纵坐标 */
  errorNum?: number;
}

/**
 * 异常分布查询
 */
export interface IApiIndexAbnormalDistributionQuery {
  /** 内容数据 */
  contentResponseDTO?: IApiIndexAbnormalDistributionQueryContentResponseDTO;
  /** 指标数据 */
  indexDataDTOList?: IApiIndexAbnormalDistributionQueryIndexDataDTOList[];
}

/**
 * 折线图查询 - body 请求参数
 */
export interface IApiIndexLineChartQueryParams {
  /** 患者id */
  patientId: number;
  /** 大类id */
  checkType: number;
  /** 小类id */
  indexType?: number;
  /** 指标模板名称 */
  templateName: string;
  /** 折线图时间类型  1 周 /2 15天/ 3 30天/ 4 60天/ 0 全部 */
  timeType?: number;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
}

/**
 * 基线
 */
export interface IApiIndexLineChartQueryBaseLineDto {
  /** 最高舒张压 */
  maxLowPressureLine?: number;
  /** 最低舒张压 */
  minLowPressureLine?: number;
  /** 最高收缩压 */
  maxHighPressureLine?: number;
  /** 最低收缩压 */
  minHighPressureLine?: number;
  /** 最低心率 */
  minHeartLine?: number;
  /** 最高心率 */
  maxHeartLine?: number;
  /** 最高血脂 */
  maxFatBloodLine?: number;
  /** 最低血脂 */
  minFatBloodLine?: number;
}

/**
 * 数据
 */
export interface IApiIndexLineChartQueryDataList {
  /** id */
  id?: number;
  /** 患者id */
  patientId?: number;
  /** 来源 */
  dataSource?: string;
  /** 记录时间 */
  recordingTime?: number;
  /** 收缩压 */
  highPressure?: string;
  /** 舒张压 */
  lowPressure?: string;
  /** 处理结果 1 电话沟通 2 在线沟通 3待观察 4观察、5调药、6就诊、7住院 */
  handlerResult?: number;
  /** 处理人 */
  handler?: string;
  /** 处理时间 */
  handleTime?: number;
  /** 心率 */
  heartRate?: string;
  /** 血糖类型 1:空腹，2:餐后2小时血糖，3:随机血糖 */
  bloodSugarType?: number;
  /** 空腹 */
  fastingGiu?: number;
  /** 餐后2小时血糖 */
  nonFastingGiu?: number;
  /** 血糖 随机 */
  giu?: number;
  /** 尿量 */
  urineOutput?: number;
  /** 饮水量 */
  waterVolume?: number;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** 指标值 */
  indexValue?: number;
  /** 报告日期 */
  reportTime?: number;
  /** 异常等级 */
  errorLevel?: number;
  /** 是否删除 */
  isDelete?: boolean;
  /** 上传人 （1患者上传、2医生上传、3健康管理师上传、4运动康复师上传） */
  uploaderType?: number;
  /** 心脏彩超数据 */
  content?: number;
  /** 差值 */
  diff?: number;
}

/**
 * 折线图查询
 */
export interface IApiIndexLineChartQuery {
  /** 单位 */
  unit?: string;
  /** 基线 */
  baseLineDto?: IApiIndexLineChartQueryBaseLineDto;
  /** 数据 */
  dataList?: IApiIndexLineChartQueryDataList[];
}

/**
 * 指标分析表格查询 - body 请求参数
 */
export interface IApiIndexFromQueryParams {
  /** 患者id */
  patientId: number;
  /** 大类id */
  checkType: number;
  /** 小类id */
  indexType?: number;
  /** 指标模板名称 */
  templateName: string;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 页码 */
  page?: number;
  /** 页面大小 */
  pageSize?: number;
}

/**
 * 数据
 */
export interface IApiIndexFromQueryDataList {
  /** id */
  id?: number;
  /** 患者id */
  patientId?: number;
  /** 来源 */
  dataSource?: string;
  /** 记录时间 */
  recordingTime?: number;
  /** 收缩压 */
  highPressure?: string;
  /** 舒张压 */
  lowPressure?: string;
  /** 处理结果 1 电话沟通 2 在线沟通 3待观察 4观察、5调药、6就诊、7住院 */
  handlerResult?: number;
  /** 处理人 */
  handler?: string;
  /** 处理时间 */
  handleTime?: number;
  /** 心率 */
  heartRate?: string;
  /** 血糖类型 1:空腹，2:餐后2小时血糖，3:随机血糖 */
  bloodSugarType?: number;
  /** 空腹 */
  fastingGiu?: number;
  /** 餐后2小时血糖 */
  nonFastingGiu?: number;
  /** 血糖 随机 */
  giu?: number;
  /** 尿量 */
  urineOutput?: number;
  /** 饮水量 */
  waterVolume?: number;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** 指标值 */
  indexValue?: number;
  /** 报告日期 */
  reportTime?: number;
  /** 异常等级 */
  errorLevel?: number;
  /** 是否删除 */
  isDelete?: boolean;
  /** 上传人 （1患者上传、2医生上传、3健康管理师上传、4运动康复师上传） */
  uploaderType?: number;
  /** 心脏彩超数据 */
  content?: number;
  /** 差值 */
  diff?: number;
}

/**
 * 文案数据
 */
export interface IApiIndexFromQueryContentResponseDTO {
  /** 总数 */
  totalNum?: number;
  /** 异常数 */
  errorNum?: number;
  /** 最高心率 */
  highHeart?: number;
  /** 最低心率 */
  lowHeart?: number;
  /** 平均心率 */
  avgHeart?: number;
  /** ttr */
  ttR?: string;
  /** nttR */
  nttR?: string;
  /** 最大收缩压、空腹最高血糖 */
  maxHigh?: number;
  /** 最小收缩压、空腹最低血糖 */
  minHigh?: number;
  /** 最大收缩压、空腹最高血糖 平均 */
  avgHigh?: number;
  /** 最大舒张压、非空腹最高血糖 */
  maxLow?: number;
  /** 最小舒张压、非空腹最低血糖 */
  minLow?: number;
  /** 最小舒张压、非空腹最低血糖 平均 */
  avgLow?: number;
  /** 血压、心率 天数 */
  day?: number;
  /** 最大血脂 */
  maxBloodFat?: number;
  /** 最小血脂 */
  minBloodFat?: number;
  /** 最大空腹血糖异常等级 */
  maxFastingGiuErrorLevel?: number;
  /** 最大非空腹血糖异常等级 */
  maxNonFastingGiuErrorLevel?: number;
  /** 最小空腹血糖异常等级 */
  minFastingGiuErrorLevel?: number;
  /** 最小非空腹血糖异常等级 */
  minNonFastingGiuErrorLevel?: number;
  /** 最大血脂等级 */
  maxContentErrorLevel?: number;
  /** 最小血脂等级 */
  minContentErrorLevel?: number;
  /** 最大心率异常等级 */
  maxHeartRateErrorLevel?: number;
  /** 最小心率异常等级 */
  minHeartRateErrorLevel?: number;
  /** 最大收缩压异常等级 */
  maxHighPressureErrorLevel?: number;
  /** 最大舒张压异常等级 */
  maxLowPressureErrorLevel?: number;
  /** 最小收缩压异常等级 */
  minHighPressureErrorLevel?: number;
  /** 最小舒张压异常等级 */
  minLowPressureErrorLevel?: number;
}

/**
 * 指标列表
 */
export interface IApiIndexFromQueryIndexTypeResponseDTOList {
  name?: string;
  indexType?: number;
}

/**
 * 指标分析表格查询
 */
export interface IApiIndexFromQuery {
  /** 数据 */
  dataList?: IApiIndexFromQueryDataList[];
  /** 总条数 */
  totals?: number;
  /** 文案数据 */
  contentResponseDTO?: IApiIndexFromQueryContentResponseDTO;
  /** 指标列表 */
  indexTypeResponseDTOList?: IApiIndexFromQueryIndexTypeResponseDTOList[];
  /** 单位 */
  unit?: string;
}

/**
 * 数据新增或修改 - body 请求参数
 */
export interface IApiIndexDataModifyParams {
  id?: number;
  /** 指标模板名称 */
  templateName: string;
  /** 患者id */
  patientId: number;
  /** 操作人id */
  uid: number;
  /** 角色类型 */
  roleType: number;
  /** 收缩压 */
  highPressure?: string;
  /** 舒张压 */
  lowPressure?: string;
  /** 记录时间 */
  recordingTime?: number;
  /** 指标值 */
  indexValue?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** bmi */
  bmi?: number;
  /** 尿量 */
  urineOutput?: number;
  /** 饮水量 */
  waterVolume?: number;
  /** 心脏彩超指标值 */
  content?: number;
  /** 血糖类型 1:空腹，2:餐后2小时血糖，3:随机血糖 */
  sugarType?: number;
}

/**
 * 数据新增或修改
 */
export type IApiIndexDataModify = number;

/**
 * 查询患者存在的指标项 - body 请求参数
 */
export interface IApiIndexQueryParams {
  /** 患者id */
  patientId: number;
}

/**
 * 指标项
 */
export interface IApiIndexQueryResponseDTOList {
  /** 指标名称 */
  name?: string;
  /** 指标类型名称 */
  templateName?: string;
  /** 指标id */
  checkType?: number;
  /** 是否异常 */
  isError?: boolean;
  /** 是否置灰 */
  isGray?: boolean;
}

/**
 * 查询患者存在的指标项
 */
export interface IApiIndexQuery {
  /** 指标项 */
  responseDTOList?: IApiIndexQueryResponseDTOList[];
}

/**
 * 查询指标下级 - body 请求参数
 */
export interface IApiIndexSubordinateQueryParams {
  /** 患者id */
  patientId: number;
  /** 大类id */
  checkType: number;
}

/**
 *
 */
export interface IApiIndexSubordinateQueryItem {
  name?: string;
  indexType?: number;
}

/**
 * 查询指标下级
 */
export type IApiIndexSubordinateQuery = IApiIndexSubordinateQueryItem[];

/**
 * 查询数据详情 - body 请求参数
 */
export interface IApiIndexDataInfoQueryParams {
  /** id */
  id: number;
  /** 指标模板名称 */
  templateName: string;
}

/**
 * 查询数据详情
 */
export interface IApiIndexDataInfoQuery {
  /** id */
  id?: number;
  /** 来源 */
  dataSource?: string;
  /** 记录时间 */
  recordingTime?: string;
  /** 收缩压 */
  highPressure?: string;
  /** 舒张压 */
  lowPressure?: string;
  /** 处理结果 */
  handlerResult?: number;
  /** 处理人 */
  handler?: string;
  /** 处理时间 */
  handleTime?: string;
  /** 心率 */
  heartRate?: string;
  /** 血糖类型 */
  bloodSugarType?: number;
  /** 血糖 */
  giu?: number;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** 指标值 */
  indexValue?: number;
  /** 报告日期 */
  reportTime?: string;
  /** 异常等级 */
  errorLevel?: number;
  /** 是否删除 */
  isDelete?: boolean;
  /** 上传人 （1患者上传、2医生上传、3健康管理师上传、4运动康复师上传） */
  uploaderType?: number;
}

/**
 * 查询文案数据 - body 请求参数
 */
export interface IApiIndexContentQueryParams {
  /** 患者id */
  patientId: number;
  /** 大类id */
  checkType: number;
  /** 小类id */
  indexType?: number;
  /** 指标模板名称 */
  templateName: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 页码 */
  pageNumber?: number;
  /** 页面大小 */
  pageSize?: number;
}

/**
 * 查询文案数据
 */
export interface IApiIndexContentQuery {
  /** 总数 */
  totalNum?: number;
  /** 异常数 */
  errorNum?: number;
  /** 最高心率 */
  highHeart?: number;
  /** 最低心率 */
  lowHeart?: number;
  /** 平均心率 */
  avgHeart?: number;
  /** ttr */
  ttR?: string;
  /** 非空腹ttr */
  nttR?: string;
  /** 最大收缩压、空腹最高血糖 */
  maxHigh?: number;
  /** 最小收缩压、空腹最低血糖 */
  minHigh?: number;
  /** 最大收缩压、空腹最高血糖 平均 */
  avgHigh?: number;
  /** 最大舒张压、非空腹最高血糖 */
  maxLow?: number;
  /** 最小舒张压、非空腹最低血糖 */
  minLow?: number;
  /** 最小舒张压、非空腹最低血糖 平均 */
  avgLow?: number;
  /** 血压、心率 天数 */
  day?: number;
  /** 最大血脂 */
  maxBloodFat?: number;
  /** 最小血脂 */
  minBloodFat?: number;
  /** 最大空腹异常等级 */
  maxFastingGiuErrorLevel?: number;
  /** 最大非空腹异常等级 */
  maxNonFastingGiuErrorLevel?: number;
  /** 最小空腹异常等级 */
  minFastingGiuErrorLevel?: number;
  /** 最小非空腹异常等级 */
  minNonFastingGiuErrorLevel?: number;
  /** 最大血脂等级 */
  maxContentErrorLevel?: number;
  /** 最小血脂等级 */
  minContentErrorLevel?: number;
  /** 最大心率异常等级 */
  maxHeartRateErrorLevel?: number;
  /** 最小心率异常等级 */
  minHeartRateErrorLevel?: number;
  maxHighPressureErrorLevel?: number;
  maxLowPressureErrorLevel?: number;
  minHighPressureErrorLevel?: number;
  minLowPressureErrorLevel?: number;
}

/**
 * 调整阈值 - body 请求参数
 */
export interface IApiIndexUpdateThresholdParams {
  /** userId */
  userId?: number;
  /** roleType */
  roleType?: number;
  /** id */
  id?: number;
  /** 患者id */
  patientId: number;
  /** 指标模板名称 */
  templateName: string;
  /** 收缩压正常值 */
  systolicNormalValue?: number;
  /** 舒张压正常值 */
  diastolicNormalValue?: number;
  /** 一级收缩压 */
  systolicLevel1Value?: number;
  /** 一级舒张压 */
  diastolicLevel1Value?: number;
  /** 二级舒张压 */
  diastolicLevel2Value?: number;
  /** 三级舒张压 */
  diastolicLevel3Value?: number;
  /** 二级收缩压 */
  systolicLevel2Value?: number;
  /** 三级收缩压 */
  systolicLevel3Value?: number;
  /** 最低心率值 */
  minHeartRateValue?: number;
  /** 偏高心率 */
  tallHeartRateValue?: number;
  /** 最高心率值 */
  maxHeartRateValue?: number;
  /** 是否开启提醒监测血糖 0关闭 1开启 */
  status?: number;
  /** 1级预警  最大值(空腹血糖/随机血糖/餐后2小时血糖) */
  lev1Max?: number;
  /** 1级预警  最小值(空腹血糖/随机血糖/餐后2小时血糖) */
  lev1Min?: number;
  /** 2级预警  餐后2小时血糖最小值 */
  lev2DinnerMin?: number;
  /** 2级预警  餐后2小时血糖最大值 */
  lev2DinnerMax?: number;
  /** 2级预警  空腹血糖最小值 */
  lev2StomachMin?: number;
  /** 2级预警  空腹血糖最大值 */
  lev2StomachMax?: number;
  /** 2级预警  随机血糖最小值 */
  lev2RandomMin?: number;
  /** 2级预警  随机血糖最大值 */
  lev2RandomMax?: number;
  /** 最高血脂 */
  ldlcMax?: number;
  /** 最低血脂 */
  ldlcMin?: number;
}

/**
 * 调整阈值
 */
export type IApiIndexUpdateThreshold = number;

/**
 * 阈值详情 - body 请求参数
 */
export interface IApiIndexThresholdInfoQueryParams {
  /** 患者id */
  patientId: number;
  /** 指标模板名称 */
  templateName: string;
}

/**
 * 阈值详情
 */
export interface IApiIndexThresholdInfoQuery {
  /** id */
  id?: number;
  /** 收缩压正常值 */
  systolicNormalValue?: number;
  /** 舒张压正常值 */
  diastolicNormalValue?: number;
  /** 一级收缩压 */
  systolicLevel1Value?: number;
  /** 一级舒张压 */
  diastolicLevel1Value?: number;
  /** 二级舒张压 */
  diastolicLevel2Value?: number;
  /** 三级舒张压 */
  diastolicLevel3Value?: number;
  /** 二级收缩压 */
  systolicLevel2Value?: number;
  /** 三级收缩压 */
  systolicLevel3Value?: number;
  /** 最低心率值 */
  minHeartRateValue?: number;
  /** 偏高心率 */
  tallHeartRateValue?: number;
  /** 最高心率值 */
  maxHeartRateValue?: number;
  /** 是否开启提醒监测血糖 0关闭 1开启 */
  status?: number;
  /** 1级预警  最大值(空腹血糖/随机血糖/餐后2小时血糖) */
  lev1Max?: number;
  /** 1级预警  最小值(空腹血糖/随机血糖/餐后2小时血糖) */
  lev1Min?: number;
  /** 2级预警  餐后2小时血糖最小值 */
  lev2DinnerMin?: number;
  /** 2级预警  餐后2小时血糖最大值 */
  lev2DinnerMax?: number;
  /** 2级预警  空腹血糖最小值 */
  lev2StomachMin?: number;
  /** 2级预警  空腹血糖最大值 */
  lev2StomachMax?: number;
  /** 2级预警  随机血糖最小值 */
  lev2RandomMin?: number;
  /** 2级预警  随机血糖最大值 */
  lev2RandomMax?: number;
  /** 最高血脂 */
  ldlcMax?: number;
  /** 最低血脂 */
  ldlcMin?: number;
}

/**
 * 仅看我的--修改 - body 请求参数
 */
export interface IApiBacklogLeaveMarkParams {
  /** 账号 */
  account: string;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
  /** 类型 1：待办列表 2：提醒事项列表 */
  type: number;
  /** 是否勾选   否0 1是    --查询接口  非必传  --修改接口  必传 */
  onlyMine?: number;
}

/**
 * 仅看我的--修改
 */
export type IApiBacklogLeaveMark = boolean;

/**
 * 仅看我的--查看 - body 请求参数
 */
export interface IApiBacklogQueryMarkParams {
  /** 账号 */
  account: string;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
  /** 类型 1：待办列表 2：提醒事项列表 */
  type: number;
  /** 是否勾选   否0 1是    --查询接口  非必传  --修改接口  必传 */
  onlyMine?: number;
}

/**
 * 仅看我的--查看
 */
export type IApiBacklogQueryMark = number;

/**
 * 分页获取待办/整改/质疑列表 - body 请求参数
 */
export interface IApiBacklogPageQueryParams {
  pageNumber?: number;
  pageSize?: number;
  /** 负责人id */
  headId?: number;
  /** 负责人角色（1医生、2健康管理师、3运动康复师） */
  headRole?: number;
  /** 大类：1整改、2待办、3质疑 */
  category?: number[];
  /** 仅看我的 0否 1是 */
  onlyMine: number;
  /** 处理记录 0否 1是 */
  processed: number;
  /** 患者id */
  patientId: number;
}

/**
 * 处理结果
 */
export interface IApiBacklogPageQueryBacklogResponseListBacklogResult {
  /** 待办id */
  backlogId?: number;
  /** 负责人id */
  headId?: number;
  /** 负责人名字 */
  headName?: string;
  /** 负责人角色（1医生、2健康管理师、3运动康复师） */
  headRole?: number;
  /** 处理时间 */
  time?: number;
  /** 处理结果内容 */
  content?: string;
}

/**
 * 待办列表
 */
export interface IApiBacklogPageQueryBacklogResponseList {
  /** 待办id */
  backlogId?: number;
  /** 患者id */
  patientId?: number;
  /** 负责人id */
  headId?: number;
  /** 负责人名字 */
  headName?: string;
  /** 负责人角色（1医生、2健康管理师、3运动康复师） */
  headRole?: number;
  /** 待办期限 */
  term?: number;
  /** 超期时间 */
  overdueTime?: number;
  /** 大类：1整改、2待办、3质疑 */
  category?: number;
  /** 待办类型 */
  type?: number;
  /** 待办内容 */
  content?: string;
  /** 数据来源id */
  sourceId?: number;
  /** 待办图标 */
  imageUrl?: string;
  /** 是否推送患者 */
  mqPatient?: number;
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 提醒时间 */
  remindTime?: number;
  /** 患者姓名 */
  patientName?: string;
  /** 处理类型 */
  status?: number;
  /** 处理结果 */
  backlogResult?: IApiBacklogPageQueryBacklogResponseListBacklogResult;
}

/**
 * 分页获取待办/整改/质疑列表
 */
export interface IApiBacklogPageQuery {
  /** 当前页码 */
  pageNumber?: number;
  /** 页面大小 */
  pageSize?: number;
  /** 总页码 */
  totalNumber?: number;
  /** 数据总条数 */
  totals?: number;
  /** 待办列表 */
  backlogResponseList?: IApiBacklogPageQueryBacklogResponseList[];
}

/**
 * 初始化延迟待办 - body 请求参数
 */
export interface IApiBacklogInitParams {
  /** 患者id */
  patientId?: number;
  /** 负责人id */
  headId?: number;
  /** 负责人角色（1医生、2健康管理师、3运动康复师） */
  headRole: number;
  /** 初始化时间 */
  initTime: number;
  /** 待办内容 */
  content: string;
  /** 待办来源id */
  sourceId: number;
  /** 待办类型 */
  type: string;
}

/**
 * 初始化延迟待办
 */
export type IApiBacklogInit = boolean;

/**
 * 删除待办 - body 请求参数
 */
export interface IApiBacklogRemoveParams {
  /** 待办id--前端 */
  backlogId?: number;
  /** 患者id */
  patientId?: number;
  /** 待办类型 */
  type: string;
}

/**
 * 删除待办
 */
export type IApiBacklogRemove = boolean;

/**
 * 待办处理 - body 请求参数
 */
export interface IApiBacklogDealParams {
  /** 待办id--前端 */
  backlogId?: number;
  /** 患者id */
  patientId?: number;
  /** 待办来源id */
  sourceId?: number;
  /** 待办类型 */
  type: string;
  /** 处理结果内容 */
  content: string;
  /** 操作人id */
  headId?: number;
  /** 操作人角色（1医生、2健康管理师、3运动康复师） */
  headRole?: number;
}

/**
 * 待办处理
 */
export type IApiBacklogDeal = boolean;

/**
 * 待办延期 - body 请求参数
 */
export interface IApiBacklogDelayParams {
  /** 待办id */
  backlogId: number;
  /** 提醒时间 */
  remindTime: number;
  /** 待办类型 */
  type: string;
}

/**
 * 待办延期
 */
export type IApiBacklogDelay = boolean;

/**
 * 条件
 */
export interface IApiBacklogRemindListParamsOptions {
  /** 上级id */
  pid?: number;
  /** 选项名称 */
  name?: string;
  /** 选项类型 */
  type?: number;
  /** 状态 */
  status?: number;
}

/**
 * 提醒事项列表 - body 请求参数
 */
export interface IApiBacklogRemindListParams {
  /** 患者id */
  patientId?: number;
  /** 仅看我的 0否 1是 */
  onlyMine?: number;
  /** 未处理的 0否 1是 */
  untreated?: number;
  /** 条件 */
  options?: IApiBacklogRemindListParamsOptions[];
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 超期时间 */
  overdueTime?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 当前页 */
  pageNum?: number;
  /** 每页条数 */
  pageSize?: number;
}

/**
 * 处理结果
 */
export interface IApiBacklogRemindListBacklogResponseListBacklogResult {
  /** 待办id */
  backlogId?: number;
  /** 负责人id */
  headId?: number;
  /** 负责人名字 */
  headName?: string;
  /** 负责人角色（1医生、2健康管理师、3运动康复师） */
  headRole?: number;
  /** 处理时间 */
  time?: number;
  /** 处理结果内容 */
  content?: string;
}

/**
 * 待办列表
 */
export interface IApiBacklogRemindListBacklogResponseList {
  paginationInfo: { pageNum: any };
  /** 待办id */
  backlogId?: number;
  /** 患者id */
  patientId?: number;
  /** 负责人id */
  headId?: number;
  /** 负责人名字 */
  headName?: string;
  /** 负责人角色（1医生、2健康管理师、3运动康复师） */
  headRole?: number;
  /** 待办期限 */
  term?: number;
  /** 超期时间 */
  overdueTime?: number;
  /** 大类：1整改、2待办、3质疑 */
  category?: number;
  /** 待办类型 */
  type?: number;
  /** 待办内容 */
  content?: string;
  /** 数据来源id */
  sourceId?: number;
  /** 待办图标 */
  imageUrl?: string;
  /** 是否推送患者 */
  mqPatient?: number;
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 提醒时间 */
  remindTime?: number;
  /** 患者姓名 */
  patientName?: string;
  /** 处理类型 */
  status?: number;
  /** 处理结果 */
  backlogResult?: IApiBacklogRemindListBacklogResponseListBacklogResult;
}

/**
 * 提醒事项列表
 */
export interface IApiBacklogRemindList {
  /** 当前页码 */
  pageNumber?: number;
  /** 页面大小 */
  pageSize?: number;
  /** 总页码 */
  totalNumber?: number;
  /** 数据总条数 */
  totals?: number;
  /** 待办列表 */
  backlogResponseList?: IApiBacklogRemindListBacklogResponseList[];
}

/**
 * 条件
 */
export interface IApiBacklogRemindNumParamsOptions {
  /** 上级id */
  pid?: number;
  /** 选项名称 */
  name?: string;
  /** 选项类型 */
  type?: number;
  /** 状态 */
  status?: number;
}

/**
 * 提醒事项数量统计 - body 请求参数
 */
export interface IApiBacklogRemindNumParams {
  /** 患者id */
  patientId?: number;
  /** 仅看我的 0否 1是 */
  onlyMine?: number;
  /** 未处理的 0否 1是 */
  untreated?: number;
  /** 条件 */
  options?: IApiBacklogRemindNumParamsOptions[];
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 超期时间 */
  overdueTime?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 当前页 */
  pageNum?: number;
  /** 每页条数 */
  pageSize?: number;
}

/**
 *
 */
export interface IApiBacklogRemindNumItem {
  /** 日期 */
  date?: number;
  /** 整改数量 */
  revampCount?: number;
  /** 待办数量 */
  backlogCount?: number;
  /** 质疑数量 */
  doubtCount?: number;
}

/**
 * 提醒事项数量统计
 */
export type IApiBacklogRemindNum = IApiBacklogRemindNumItem[];

/**
 * 新增待办 - body 请求参数
 */
export interface IApiBacklogCreateParams {
  /** 患者id */
  patientId?: number;
  /** 负责人id */
  headId?: number;
  /** 负责人角色（1医生、2健康管理师、3运动康复师） */
  headRole: number;
  /** 初始化时间 */
  initTime?: number;
  /** 提醒时间--前端 */
  remindTime?: number;
  /** 待办期限--前端 */
  term?: number;
  /** 待办内容 */
  content: string;
  /** 待办来源id */
  sourceId?: number;
  /** 待办类型 */
  type: string;
  /** 是否推送患者 0否 1是--前端 */
  mqPatient?: number;
  /** 自定义提醒时间 */
  remindTimes?: number[];
}

/**
 * 新增待办
 */
export type IApiBacklogCreate = boolean;

/**
 * 查询提醒列表筛选项 - body 请求参数
 */
export interface IApiBacklogQueryOptionsParams {
  /** 选项类型 2 待办类型 3 待办处理类型 */
  code: number;
  /** 用户类型 */
  userType?: string;
}

/**
 *
 */
export interface IApiBacklogQueryOptionsItem {
  /** id */
  id?: number;
  /** 选项类型 */
  code?: number;
  /** 上级id */
  pid?: number;
  /** 选项名称 */
  name?: string;
  /** 大类：1整改、2待办、3质疑 */
  category?: number;
  /** 选项类型 */
  type?: number;
}

/**
 * 查询提醒列表筛选项
 */
export type IApiBacklogQueryOptions = IApiBacklogQueryOptionsItem[];

/**
 * 获取七牛云上传凭证 - body 请求参数
 */
export interface IApiCommonOssVoucherParams {
  data: string;
}

/**
 * 获取七牛云上传凭证
 */
export type IApiCommonOssVoucher = string;

/**
 * 处理事件 - body 请求参数
 */
export interface IApiRiskHandleEventParams {
  patientId?: number;
  treatmentMethod?: number;
  processingTime?: string;
  uid?: number;
  roleType?: number;
}

/**
 * 处理事件
 */
export interface IApiRiskHandleEvent {}

/**
 * 查询异常图表数据 - body 请求参数
 */
export interface IApiRiskDeviceViewParams {
  /** 患者id */
  patientId: number;
  /** 1 周平均  2月平均  3全部 */
  type?: number;
}

/**
 *
 */
export interface IApiRiskDeviceViewItem {
  /** 时间 */
  date?: string;
  /** 心率 */
  heartRate?: number;
  /** 收缩压 */
  highPressure?: number;
  /** 舒张压 */
  lowPressure?: number;
}

/**
 * 查询异常图表数据
 */
export type IApiRiskDeviceView = IApiRiskDeviceViewItem[];

/**
 * 查询血压异常栏数据 - body 请求参数
 */
export interface IApiRiskDeviceExDataParams {
  /** 患者id */
  patientId: number;
  /** 1 周平均  2月平均  3全部 */
  type?: number;
}

/**
 * 查询血压异常栏数据
 */
export interface IApiRiskDeviceExData {
  avgHeartRate?: number;
  avgHighPressure?: number;
  avgLowPressure?: number;
  deviceNum?: number;
  deviceExceptionNum?: number;
  maxExceptionLevel?: number;
  maxHeartRate?: number;
  maxHighPressure?: number;
  maxLowPressure?: number;
  minHeartRate?: number;
  minHighPressure?: number;
  minLowPressure?: number;
}

/**
 * 查询血压数据 - body 请求参数
 */
export interface IApiRiskDeviceDataParams {
  patientId: number;
}

/**
 * 血压数据
 */
export interface IApiRiskDeviceDataDeviceDataList {
  deviceRecordId?: number;
  patientId?: number;
  highPressure?: string;
  lowPressure?: string;
  heartRate?: string;
  errorLevel?: number;
  testTime?: string;
  heartLevel?: number;
  processing?: number;
  treatmentMethod?: number;
  processingResult?: string;
  processingTime?: string;
  recordType?: number;
  deviceType?: number;
  deviceNo?: number;
  uploaderType?: number;
  uploaderId?: number;
  /** 数据来源 */
  dataSource?: string;
  /** 处理人 */
  handler?: string;
  /** 处理人类型 1医生 2健康管理师 3运动康复师 */
  handlerType?: number;
  /** 处理人id */
  handlerId?: number;
  /** 是否能删除 */
  isDelete?: boolean;
}

/**
 * 查询血压数据
 */
export interface IApiRiskDeviceData {
  /** 血压数据 */
  deviceDataList?: IApiRiskDeviceDataDeviceDataList[];
  /** 总条数 */
  totals?: number;
}

/**
 * 查询血糖风险数据图 - body 请求参数
 */
export interface IApiRiskBloodViewParams {
  patientId: number;
}

/**
 *
 */
export interface IApiRiskBloodViewItem {
  blood?: string;
  type?: number;
}

/**
 * 查询血糖风险数据图
 */
export type IApiRiskBloodView = IApiRiskBloodViewItem[];

/**
 * 获取患者目标值 - body 请求参数
 */
export interface IApiRiskTargetValueParams {
  patientId: number;
}

/**
 * 获取患者目标值
 */
export interface IApiRiskTargetValue {
  bloodFat?: string;
  bloodPressure?: string;
  bloodSugarEmpty?: string;
  bloodSugarNotEmpty?: string;
  hearRate?: string;
}

/**
 * 通过类型处理异常 - body 请求参数
 */
export interface IApiRiskHandleErrorParams {
  patientId: number;
  /** 1 电话沟通 2 在线沟通 3待观察
4观察、5调药、6就诊、7住院 */
  treatmentMethod: number;
  processingTime?: number;
  /** 1血压 2心率 3血糖 4 体重 */
  type: number;
}

/**
 * 通过类型处理异常
 */
export interface IApiRiskHandleError {}

/**
 * 风险管理体重风险数据 - body 请求参数
 */
export interface IApiRiskWeightDataParams {
  /** 患者id */
  patientId: number;
}

/**
 * 血压数据
 */
export interface IApiRiskWeightDataWeightResponse {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  /** id */
  id?: number;
  /** 患者id */
  patientId?: number;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** 创建时间 */
  generateTime?: number;
  /** 更新时间 */
  updateTime?: number;
  /** 数据来源 */
  dataSource?: number;
  /** 是否删除 */
  deleted?: boolean;
  /** 数据上传人类型（1患者上传、2医生上传、3健康管理师上传、4运动康复师上传） */
  uploaderType?: number;
  /** 上传人id */
  uploaderId?: number;
  /** 数据来源 */
  source?: string;
  /** 1 异常 0正常 */
  errorLevel?: number;
  /** 处理人类型 1/医生 2/健康管理师 3/运动康复师 */
  handlerType?: number;
  /** 处理人id */
  handlerId?: string;
  /** 处理结果 1 电话沟通 2 在线沟通 3待观察
4观察、5调药、6就诊、7住院 */
  treatmentMethod?: number;
  processingResult?: string;
  /** 处理时间 */
  processingTime?: number;
}

/**
 * 风险管理体重风险数据
 */
export interface IApiRiskWeightData {
  /** 血压数据 */
  weightResponse?: IApiRiskWeightDataWeightResponse[];
  /** 总条数 */
  totals?: number;
}

/**
 * 风险管理血糖风险数据 - body 请求参数
 */
export interface IApiRiskBloodDataParams {
  /** 患者id */
  patientId: number;
}

/**
 * 血压数据
 */
export interface IApiRiskBloodDataBloodDataList {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  bloodSugarId?: number;
  patientId?: number;
  giu?: number;
  errorLevel?: number;
  type?: number;
  uploadTime?: number;
  processing?: number;
  treatmentMethod?: number;
  processingResult?: string;
  processingTime?: number;
  handlerId?: number;
  handler?: string;
  handlerType?: number;
  uploaderType?: number;
  uploaderId?: number;
  /** 是否能删除 */
  deleted?: boolean;
  /** 数据来源 */
  dataSource?: string;
  /** 异常等级 */
  abnormalLevel?: number;
}

/**
 * 风险管理血糖风险数据
 */
export interface IApiRiskBloodData {
  /** 血压数据 */
  bloodDataList?: IApiRiskBloodDataBloodDataList[];
  /** 总条数 */
  totals?: number;
}

/**
 * 当前角色未接来电数 - body 请求参数
 */
export interface IApiAddressMissCallsParams {
  data: string;
}

/**
 * 当前角色未接来电数
 */
export type IApiAddressMissCalls = number;

/**
 * 通讯录详情
 */
export interface IApiAddressUpdateParamsAddressBookList {
  /** 手机号 */
  phone: string;
  /** 姓名 */
  name: string;
  /** 关系 */
  relation: string;
}

/**
 * 新增或编辑患者关联电话簿 - body 请求参数
 */
export interface IApiAddressUpdateParams {
  /** 患者id */
  userId: number;
  /** 通讯录详情 */
  addressBookList: IApiAddressUpdateParamsAddressBookList[];
}

/**
 * 新增或编辑患者关联电话簿
 */
export interface IApiAddressUpdate {}

/**
 * 查询坐席电话列表 - body 请求参数
 */
export interface IApiAddressPhoneListParams {
  /** cno */
  cno: string;
}

/**
 * undefined
 */
export interface IApiAddressPhoneListClientTelModelList {
  /** 电话 */
  tel?: string;
  /** 类型 */
  telType?: number;
  /** 是否绑定 */
  isBind?: number;
}

/**
 * 查询坐席电话列表
 */
export interface IApiAddressPhoneList {
  clientTelModelList?: IApiAddressPhoneListClientTelModelList[];
}

/**
 * 查询患者医生团队 - body 请求参数
 */
export interface IApiAddressDoctorListParams {
  /** 患者id */
  patientId: number;
}

/**
 * 医生列表
 */
export interface IApiAddressDoctorListDoctorList {
  /** 姓名 */
  name?: string;
  /** 手机号 */
  phone?: string;
}

/**
 * 查询患者医生团队
 */
export interface IApiAddressDoctorList {
  /** 医生列表 */
  doctorList?: IApiAddressDoctorListDoctorList[];
}

/**
 * 查询患者及患者通讯录信息 - body 请求参数
 */
export interface IApiAddressPatientListParams {
  /** 患者id */
  userId: number;
}

/**
 * 通讯录详情
 */
export interface IApiAddressPatientListAddressBookList {
  /** 手机号 */
  phone?: string;
  /** 姓名 */
  name?: string;
  /** 关系 */
  relation?: string;
}

/**
 * 查询患者及患者通讯录信息
 */
export interface IApiAddressPatientList {
  /** 通讯录详情 */
  addressBookList?: IApiAddressPatientListAddressBookList[];
}

/**
 * 通话记录状态
 */
export interface IApiAddressCallListParamsCallStatus {
  /** 呼叫类型 1 呼入 2 呼出 */
  type?: number;
  /** 接听状态  呼入时（1座席接听、2已呼叫座席，座席未接听 3系统接听）
                 呼出时（1客户未接听 2 座席未接听 3 双方接听） */
  status?: number;
  /** 是否已读 */
  isRead?: number;
}

/**
 * 查询通话记录列表 - body 请求参数
 */
export interface IApiAddressCallListParams {
  /** 患者id */
  patientId?: number;
  /** 患者姓名或手机号 */
  keyword?: string;
  /** 通话记录状态 */
  callStatus?: IApiAddressCallListParamsCallStatus[];
  /** 接听时间 开始 */
  startTime?: number;
  /** 接听时间 结束 */
  endTime?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 当前页 */
  pageNumber?: number;
  /** 页面大小 */
  pageSize?: number;
}

/**
 *
 */
export interface IApiAddressCallListContents {
  id?: number;
  /** 患者id */
  patientId?: number;
  /** 患者姓名 */
  patientName?: string;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 手机号 */
  customerNumber?: string;
  /** 通话类型 1 呼入 2 呼出 */
  type?: number;
  /** 接听状态 呼入时（1座席接听、2已呼叫座席，座席未接听 3系统接听）
        呼出时（1客户未接听 2 座席未接听 3 双方接听） */
  callStatus?: number;
  /** 响铃时间 */
  callTime?: number;
  /** 坐席号 */
  cno?: string;
  /** 客户省份 */
  customerProvince?: string;
  /** 客户城市 */
  customerCity?: string;
  /** 接通时长 */
  bridgeDuration?: number;
  /** 通话记录唯一标识 */
  uniqueId?: string;
  /** 挂机方 */
  endReason?: string;
  /** 接通时间 */
  bridgeTime?: number;
  /** 呼叫中心返回 call_status 中文 */
  status?: string;
  /** 呼叫中心返回 type 中文 */
  callType?: string;
  /** 机主名称 */
  name?: string;
  /** 关系 */
  relation?: string;
  /** 热线号码 */
  hotline?: string;
  /** 等待时长 */
  waitDuration?: number;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 坐席电话 */
  agentPhone?: string;
  /** 坐席名称 */
  agentName?: string;
  /** 通话来源 */
  source?: string;
  /** 录音文件url */
  fileUrl?: string;
  /** 是否已读(0-未读/1-已读) */
  isRead?: number;
}

/**
 * 查询通话记录列表
 */
export interface IApiAddressCallList {
  total?: number;
  contents?: IApiAddressCallListContents[];
}

/**
 * 查询通话记录详情 - body 请求参数
 */
export interface IApiAddressCallDetailsParams {
  /** 呼叫状态 :  1 呼入 2 呼出 */
  type: number;
  /** 通话记录唯一标识 */
  mainUniqueId: string;
}

/**
 *
 */
export interface IApiAddressCallDetailsCallBackInfo {
  /** 客户来电号码，带区号 */
  customerNumber?: string;
  /** 来电热线号码 */
  hotline?: string;
  /** 呼入类型 */
  callType?: string;
  /** 接听状态 */
  status?: string;
  /** 总时长 */
  totalDuration?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 接通时间 */
  bridgeTime?: string;
  /** 队列号 */
  qno?: string;
  /** 队列名称 */
  qname?: string;
  /** 坐席号 */
  cno?: string;
  /** 座席电话 */
  clientNumber?: string;
}

/**
 *
 */
export interface IApiAddressCallDetailsCallAgentList {
  /** 座席电话 */
  clientName?: string;
  /** 队列号 */
  qno?: string;
  /** 座席号 */
  cno?: string;
  /** 呼叫类型 */
  callType?: string;
  /** 呼叫结果 */
  status?: string;
  /** 呼叫情况 */
  sipCause?: string;
  /** 是否开启主叫记忆 */
  remember?: string;
  /** 接起时间 */
  startTime?: string;
  /** 接听时间 */
  answerTime?: string;
  /** 通话时长 */
  totalDuration?: string;
  /** 通话记录主通道唯一标识 */
  mainUniqueId?: string;
  /** 通话记录详情唯一标识 */
  uniqueId?: string;
}

/**
 * 查询通话记录详情
 */
export interface IApiAddressCallDetails {
  callBackInfo?: IApiAddressCallDetailsCallBackInfo;
  callAgentList?: IApiAddressCallDetailsCallAgentList[];
}

/**
 * 查询队列列表以及队列对应坐席列表 - body 请求参数
 */
export interface IApiAddressQueueListParams {
  data: string;
}

/**
 * 队列号，队列名映射
 */
export interface IApiAddressQueueListMembersModelListQueuesMap {
  key?: string;
}

/**
 * 坐席列表
 */
export interface IApiAddressQueueListMembersModelList {
  /** 座席号 */
  cno?: string;
  /** 座席名称 */
  clientName?: string;
  /** 电话类型，1:电话；2:IP话机 */
  bindType?: number;
  /** 绑定电话 */
  bindTel?: string;
  /** 客户电话，固话类型需要添加区号，手机类型不加 0，固话带IP话机以 “-” 分隔。 如果企业开启号码隐藏功能，可从弹屏事件中获取customerNumberKey的值，进行外呼 */
  customerNumber?: string;
  /** 客户来电号码加密串 */
  customerNumberEncrypt?: string;
  /** 置忙类型 */
  pauseType?: number;
  /** 置忙状态描述 */
  pauseDescription?: string;
  /** 座席状态， IDLE：空闲； PAUSE：置忙； WRAPUP：整理； CALLING： 呼叫中； RINGING：响铃； BUSY：通话 */
  agentStatus?: string;
  /** 座席状态详情 */
  agentStatusDetail?: string;
  /** 座席来电次数 */
  incomingCallCount?: number;
  /** 座席来电接听次数 */
  bridgeCallCount?: number;
  /** 队列号，队列名映射 */
  queuesMap?: IApiAddressQueueListMembersModelListQueuesMap;
  /** 队列来电接听数 */
  queueIncomingCallCount?: number;
  /** 状态时长 */
  stateDuration?: number;
  /** 登录时长 */
  loginDuration?: number;
}

/**
 * 队列列表
 */
export interface IApiAddressQueueListMembers {
  /** 坐席列表 */
  modelList?: IApiAddressQueueListMembersModelList[];
}

/**
 * 查询队列列表以及队列对应坐席列表
 */
export interface IApiAddressQueueList {
  /** 队列列表 */
  members?: IApiAddressQueueListMembers[];
}

/**
 * 清除未接来电红点标记 - body 请求参数
 */
export interface IApiAddressSignCleanParams {
  /** 唯一标识 */
  uniqueId?: string;
  /** 手机号 */
  phone?: string;
}

/**
 * 清除未接来电红点标记
 */
export type IApiAddressSignClean = boolean;

/**
 * 记录呼叫事件 - body 请求参数
 */
export interface IApiAddressCallItemParams {
  /** 电话 */
  callPhone: string;
  /** 呼叫类型 1呼出 2呼入 */
  callType: number;
}

/**
 * undefined
 */
export interface IApiAddressCallItemClientTelModelList {
  /** 电话 */
  tel?: string;
  /** 类型 */
  telType?: number;
  /** 是否绑定 */
  isBind?: number;
}

/**
 * 记录呼叫事件
 */
export interface IApiAddressCallItem {
  clientTelModelList?: IApiAddressCallItemClientTelModelList[];
}

/**
 * 通过姓名和手机号搜索符合要求的患者及患者家属 - body 请求参数
 */
export interface IApiAddressPatientKeywordParams {
  /** 关键词 */
  keyword: string;
}

/**
 *
 */
export interface IApiAddressPatientKeywordItem {
  /** 手机号 */
  phone?: string;
  /** 姓名 */
  name?: string;
}

/**
 * 通过姓名和手机号搜索符合要求的患者及患者家属
 */
export type IApiAddressPatientKeyword = IApiAddressPatientKeywordItem[];

/**
 * 通过手机号返回来电姓名 - body 请求参数
 */
export interface IApiAddressSeatsPhoneParams {
  /** 手机号 */
  phone: string;
}

/**
 * 通过手机号返回来电姓名
 */
export interface IApiAddressSeatsPhone {
  /** 姓名 */
  name?: string;
}

/**
 * 查询角色下的地区-医院-工作室 - body 请求参数
 */
export interface IApiSearchRegionHospitalGroupParams {
  /** 角色id */
  roleId?: number;
  /** 角色类型  医生-5 健管师-3 运动康复师-7 */
  roleType?: number;
}

/**
 * 地区信息
 */
export interface IApiSearchRegionHospitalGroupRegionList {
  /** 地区id */
  id?: number;
  /** 地区名称 */
  regionName?: string;
}

/**
 * 医院信息
 */
export interface IApiSearchRegionHospitalGroupHospitalList {
  /** 父id--地区id */
  pid?: number;
  /** 医院id */
  id?: number;
  /** 医院名称 */
  hospitalName?: string;
}

/**
 * 工作室信息
 */
export interface IApiSearchRegionHospitalGroupGroupList {
  /** 父id--医院id */
  pid?: number;
  /** 工作室id */
  id?: number;
  /** 工作室名称 */
  groupName?: string;
}

/**
 * 查询角色下的地区-医院-工作室
 */
export interface IApiSearchRegionHospitalGroup {
  /** 地区信息 */
  regionList?: IApiSearchRegionHospitalGroupRegionList[];
  /** 医院信息 */
  hospitalList?: IApiSearchRegionHospitalGroupHospitalList[];
  /** 工作室信息 */
  groupList?: IApiSearchRegionHospitalGroupGroupList[];
}

/**
 * 指标数据
 */
export interface IApiPatientReviewUploadParamsCheckIndex {
  /** 指标小类 */
  type?: number;
  /** 指标大类 */
  pid?: number;
  /** 指标数据 */
  content?: number;
}

/**
 * 上传复查报告、指标 - body 请求参数
 */
export interface IApiPatientReviewUploadParams {
  /** 报告id */
  reportId: number;
  /** 检查时间 */
  checkTime: number;
  /** 报告内容 */
  accessory: string[];
  /** 指标数据 */
  checkIndex: IApiPatientReviewUploadParamsCheckIndex[];
  /** 用户iD */
  userId?: number;
  /** 用户类型 */
  userType?: string;
}

/**
 * 上传复查报告、指标
 */
export interface IApiPatientReviewUpload {}

/**
 * 复查项目
 */
export interface IApiPatientReviewUpdateInfoParamsReportList {
  /** 报告id */
  indexTermId: number;
  /** 其他报告名称 */
  remark?: string;
  /** 报告名称 */
  name?: string;
  /** 报告排序 */
  reportSort?: number;
}

/**
 * 修改复查详情 - body 请求参数
 */
export interface IApiPatientReviewUpdateInfoParams {
  /** 患者id */
  patientId: number;
  /** 复查日期 */
  date: number;
  /** 复查项目 */
  reportList: IApiPatientReviewUpdateInfoParamsReportList[];
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
  /** 复查id */
  reviewId?: number;
}

/**
 * 修改复查详情
 */
export interface IApiPatientReviewUpdateInfo {}

/**
 * 分页获取复查/诊疗全部数据 - body 请求参数
 */
export interface IApiPatientReviewTreatPageParams {
  /** 复查/诊疗类型 0：住院，1：门诊，2：入组，3：复查 */
  type?: number[];
  /** 患者id */
  patientId: number;
  /** 每页大小 */
  pageSize?: number;
  /** 页码 */
  page?: number;
}

/**
 * 复查/诊疗数据
 */
export interface IApiPatientReviewTreatPageTreatList {
  /** 复查/诊疗id */
  treatId?: number;
  /** 复查/诊疗日期 */
  date?: string;
  /** 复查/诊疗类型 0：住院，1：门诊，2：入组，3：复查 */
  type?: number;
  /** 复查/诊疗名称 */
  name?: string;
  /** 复查状态 1未开始，32：未上传，64：已上传，128：已完成，-1：已失效 */
  status?: number;
  /** 诊断信息 */
  treatInfo?: string;
}

/**
 * 分页获取复查/诊疗全部数据
 */
export interface IApiPatientReviewTreatPage {
  /** 复查/诊疗数据 */
  treatList?: IApiPatientReviewTreatPageTreatList[];
  /** 总条数 */
  totals?: number;
}

/**
 * 删除复查 - body 请求参数
 */
export interface IApiPatientReviewDeleteParams {
  /** 复查id */
  reviewId: number;
}

/**
 * 删除复查
 */
export interface IApiPatientReviewDelete {}

/**
 * 填写复查结论、建议 - body 请求参数
 */
export interface IApiPatientReviewConclusionParams {
  /** 复查id */
  reviewId: number;
  /** 复查结论 */
  conclusion: string;
  /** 医生建议 */
  doctorOpinion: string;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
}

/**
 * 填写复查结论、建议
 */
export interface IApiPatientReviewConclusion {}

/**
 * 普通报告
 */
export interface IApiPatientReviewParamsIndexListCheckIndex {
  /** 名字 */
  name?: string;
  /** 指标小类 */
  type?: number;
  /** 指标大类 */
  pid?: number;
  /** 指标数据 */
  content?: number;
  /** 单位 */
  unit?: string;
}

/**
 * 指标项
 */
export interface IApiPatientReviewParamsIndexList {
  /** 报告名称 */
  reportName?: string;
  /** 指标项id */
  indexTermId?: number;
  /** 检查时间 */
  checkTime?: number;
  /** 普通报告 */
  checkIndex?: IApiPatientReviewParamsIndexListCheckIndex[];
}

/**
 * 复查结论拼接 - body 请求参数
 */
export interface IApiPatientReviewParams {
  /** 患者id */
  patientId?: number;
  /** 复查时间 */
  reviewTime?: number;
  /** 指标项 */
  indexList?: IApiPatientReviewParamsIndexList[];
  /** 心电图结论 */
  ecgConclusion?: string[];
}

/**
 * 复查结论拼接
 */
export type IApiPatientReview = string;

/**
 * 普通报告
 */
export interface IApiPatientReviewReviewConclusionParamsIndexListCheckIndex {
  /** 名字 */
  name?: string;
  /** 指标小类 */
  type?: number;
  /** 指标大类 */
  pid?: number;
  /** 指标数据 */
  content?: number;
  /** 单位 */
  unit?: string;
}

/**
 * 指标项
 */
export interface IApiPatientReviewReviewConclusionParamsIndexList {
  /** 报告名称 */
  reportName?: string;
  /** 指标项id */
  indexTermId?: number;
  /** 检查时间 */
  checkTime?: number;
  /** 普通报告 */
  checkIndex?: IApiPatientReviewReviewConclusionParamsIndexListCheckIndex[];
}

/**
 * 心电图结论
 */
export interface IApiPatientReviewReviewConclusionParamsEcgConclusion {
  /** 心电图名称 */
  ecgName?: string;
  /** 结论 */
  conclusion?: string[];
}

/**
 * 复查结论拼接 - body 请求参数
 */
export interface IApiPatientReviewReviewConclusionParams {
  /** 患者id */
  patientId?: number;
  /** 复查时间 */
  reviewTime?: number;
  /** 指标项 */
  indexList?: IApiPatientReviewReviewConclusionParamsIndexList[];
  /** 心电图结论 */
  ecgConclusion?: IApiPatientReviewReviewConclusionParamsEcgConclusion[];
}

/**
 * 复查结论拼接
 */
export type IApiPatientReviewReviewConclusion = string;

/**
 * 批量移动天数 - body 请求参数
 */
export interface IApiPatientReviewBatchModifyParams {
  /** 批量移动天数 */
  day: number;
  /** 1往前移动 2往后移动 */
  type: number;
  /** 患者id */
  patientId: number;
  /** 随访类型 1症状随访、生活方式随访 */
  followType?: number;
}

/**
 * 批量移动天数
 */
export interface IApiPatientReviewBatchModify {}

/**
 * 查询复查减免文案 - body 请求参数
 */
export interface IApiPatientReviewQueryReductionMsgParams {
  /** 患者id */
  patientId: number;
  /** 复查id */
  reviewId: number;
}

/**
 * 查询复查减免文案
 */
export interface IApiPatientReviewQueryReductionMsg {
  /** 是否显示复查文案 */
  reviewMsg?: boolean;
  /** 是否显示续费文案 */
  renewMsg?: boolean;
  /** 是否复查减免被使用（true：已使用，false：未使用） */
  userStatus?: boolean;
}

/**
 * 复查项目
 */
export interface IApiPatientReviewAddParamsReportList {
  /** 报告id */
  indexTermId: number;
  /** 其他报告名称 */
  remark?: string;
  /** 报告名称 */
  name?: string;
  /** 报告排序 */
  reportSort?: number;
}

/**
 * 添加自定义复查 - body 请求参数
 */
export interface IApiPatientReviewAddParams {
  /** 患者id */
  patientId: number;
  /** 复查日期 */
  date: number;
  /** 复查项目 */
  reportList: IApiPatientReviewAddParamsReportList[];
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: string;
  /** 复查id */
  reviewId?: number;
}

/**
 * 添加自定义复查
 */
export interface IApiPatientReviewAdd {}

/**
 * 获取复查/诊疗时间轴数据 - body 请求参数
 */
export interface IApiPatientReviewTreatListParams {
  /** 复查/诊疗类型 0：住院，1：门诊，2：入组，3：复查 */
  type?: number[];
  /** 患者id */
  patientId: number;
}

/**
 * 复查/诊疗时间轴列表
 */
export interface IApiPatientReviewTreatListTreatList {
  /** 复查/诊疗id */
  treatId?: number;
  /** 复查/诊疗日期 */
  date?: string;
  /** 复查/诊疗类型 0：住院，1：门诊，2：入组，3：复查 */
  type?: number;
  /** 复查/诊疗名称 */
  name?: string;
  /** 复查状态 1未开始，32：未上传，64：已上传，128：已完成，-1：已失效 */
  status?: number;
  /** 诊断信息 */
  treatInfo?: string;
}

/**
 * 获取复查/诊疗时间轴数据
 */
export interface IApiPatientReviewTreatList {
  /** 开始时间 */
  startDate?: string;
  /** 结束时间 */
  endDate?: string;
  /** 复查/诊疗时间轴列表 */
  treatList?: IApiPatientReviewTreatListTreatList[];
}

/**
 * 获取复查详情 - body 请求参数
 */
export interface IApiPatientReviewDetailParams {
  /** 复查id */
  reviewId: number;
}

/**
 * 获取复查详情
 */
export interface IApiPatientReviewDetail {
  /** 复查日期 */
  date?: string;
  /** 复查状态 1未开始，32：未上传，64：已上传，128：已完成，-1：已失效 */
  status?: number;
  /** 复查结论 */
  conclusion?: string;
  /** 医生建议 */
  doctorOpinion?: string;
  /** 用户名称 */
  userName?: string;
  /** 角色id */
  roleId?: number;
  /** 角色类型 */
  roleType?: number;
  /** 更新时间 */
  modifyTime?: string;
  /** 复查次数 0自定义复查 */
  times?: number;
}

/**
 * 获取调整复查计划列表 - body 请求参数
 */
export interface IApiPatientReviewAdjustListParams {
  /** 患者id */
  patientId: number;
}

/**
 * 检查项列表
 */
export interface IApiPatientReviewAdjustListItemReviewCheckList {
  /** 主键id */
  reviewCheckId?: number;
  /** 检查项id */
  checkId?: number;
  /** 项目名称 */
  checkName?: string;
}

/**
 *
 */
export interface IApiPatientReviewAdjustListItem {
  /** 复查id */
  reviewId?: number;
  /** date */
  date?: number;
  /** 检查项列表 */
  reviewCheckList?: IApiPatientReviewAdjustListItemReviewCheckList[];
  /** 第几次复查  （0为自定义复查） */
  times?: number;
  /** 状态 */
  status?: number;
}

/**
 * 获取调整复查计划列表
 */
export type IApiPatientReviewAdjustList = IApiPatientReviewAdjustListItem[];

/**
 * 分页获取常用语列表 - body 请求参数
 */
export interface IApiPatientConversationSpeechcraftListParams {
  /** 角色id */
  roleId?: number;
  /** 角色类型  医生-5 健管师-3 运动康复师-7 */
  roleType?: number;
  /** 模糊查询常用语 */
  keyword?: string;
  /** 仅看个人（1 通用、2 个人） */
  onlyMine: number;
  /** 当前页 */
  page?: number;
  /** 分页大小 */
  pageSize?: number;
}

/**
 * 常用语列表
 */
export interface IApiPatientConversationSpeechcraftListSpeechcraftList {
  /** 常用语id */
  speechcraftId?: number;
  /** 常用语内容 */
  speechcraftContent?: string;
  /** 常用语类型 1 通用、2 个人 */
  type?: number;
}

/**
 * 分页获取常用语列表
 */
export interface IApiPatientConversationSpeechcraftList {
  /** 当前页码 */
  pageNumber?: number;
  /** 页面大小 */
  pageSize?: number;
  /** 总页码 */
  totalNumber?: number;
  /** 数据总条数 */
  totals?: number;
  /** 常用语列表 */
  speechcraftList?: IApiPatientConversationSpeechcraftListSpeechcraftList[];
}

/**
 * 删除个人常用语 - body 请求参数
 */
export interface IApiPatientConversationSpeechcraftDeleteParams {
  /** 语料id */
  speechcraftId: number;
  /** 角色id */
  roleId?: number;
  /** 角色类型  医生-5 健管师-3 运动康复师-7 */
  roleType?: number;
}

/**
 * 删除个人常用语
 */
export type IApiPatientConversationSpeechcraftDelete = boolean;

/**
 * 查询问题分类 - body 请求参数
 */
export interface IApiPatientConversationQuestionTypeParams {
  data: string;
}

/**
 * 问题类型列表
 */
export interface IApiPatientConversationQuestionTypeQuestionTypeList {
  /** 问题类型 */
  advisoryTypeId?: number;
  /** 名称 */
  name?: string;
}

/**
 * 查询问题分类
 */
export interface IApiPatientConversationQuestionType {
  /** 问题类型列表 */
  questionTypeList?: IApiPatientConversationQuestionTypeQuestionTypeList[];
}

/**
 * 查询问题答复列表 - body 请求参数
 */
export interface IApiPatientConversationQuestionAnswerParams {
  /** 问题内容 */
  unknownQuestion?: string;
  /** 关键词 */
  keyword?: string;
  /** 问题类型id */
  advisoryTypeId?: number;
  /** 页码 */
  page?: number;
  /** 页大小 */
  pageSize?: number;
}

/**
 * 答案列表
 */
export interface IApiPatientConversationQuestionAnswerQuestionAnswerListAnswerList {
  /** 答案id */
  advisroyAnswerId?: number;
  /** 答案内容 */
  answerContent?: string;
}

/**
 * 问题答案列表
 */
export interface IApiPatientConversationQuestionAnswerQuestionAnswerList {
  /** 问题类型 */
  questionType?: string;
  /** 答案列表 */
  answerList?: IApiPatientConversationQuestionAnswerQuestionAnswerListAnswerList[];
}

/**
 * 查询问题答复列表
 */
export interface IApiPatientConversationQuestionAnswer {
  /** 问题答案列表 */
  questionAnswerList?: IApiPatientConversationQuestionAnswerQuestionAnswerList[];
  /** 总条数 */
  total?: number;
}

/**
 * 添加个人常用语 - body 请求参数
 */
export interface IApiPatientConversationSpeechcraftAddParams {
  /** 角色id */
  roleId?: number;
  /** 角色类型  医生-5 健管师-3 运动康复师-7 */
  roleType?: number;
  /** 常用语内容 */
  speechcraftContent: string;
}

/**
 * 添加个人常用语
 */
export interface IApiPatientConversationSpeechcraftAdd {}

/**
 * 编辑个人常用语 - body 请求参数
 */
export interface IApiPatientConversationSpeechcraftEditParams {
  /** 语料id */
  speechcraftId: number;
  /** 语料内容 */
  speechcraftContent?: string;
  /** 角色id */
  roleId?: number;
  /** 角色类型  医生-5 健管师-3 运动康复师-7 */
  roleType?: number;
}

/**
 * 编辑个人常用语
 */
export type IApiPatientConversationSpeechcraftEdit = boolean;

/**
 * 获取当前群聊的成员列表 - body 请求参数
 */
export interface IApiPatientConversationTeamMemberParams {
  /** 群聊编号 */
  teamNumber: string;
}

/**
 * 群聊成员列表
 */
export interface IApiPatientConversationTeamMemberMemberList {
  /** 网易账号id */
  imAccid?: number;
  /** 角色名称 */
  name?: string;
  /** 角色类型 -1：患者，5：医生，3：健康管理师，9：运动康复师 */
  type?: number;
}

/**
 * 获取当前群聊的成员列表
 */
export interface IApiPatientConversationTeamMember {
  /** 群聊成员列表 */
  memberList?: IApiPatientConversationTeamMemberMemberList[];
}

/**
 * 获取当前角色的患者群聊列表 - body 请求参数
 */
export interface IApiPatientConversationTeamListParams {
  /** 患者id */
  patientId: number;
}

/**
 * 群聊用户列表
 */
export interface IApiPatientConversationTeamListTeamListUserList {
  /** 用户id */
  userId?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户手机号 */
  userPhone?: string;
  /** 角色名称 */
  roleName?: string;
  /** 用户角色 枚举值 */
  userType?: number;
}

/**
 * 群聊列表
 */
export interface IApiPatientConversationTeamListTeamList {
  /** 群聊号 */
  teamNumber?: string;
  /** 群聊类型 1为医生-专家群聊；2为医生-患者-健康管理师-运动康复师群聊；3为患者-专家群聊；4为医生-健康管理师-运动康复师； */
  teamType?: number;
  /** 群聊名称 */
  teamName?: string;
  /** 群聊用户列表 */
  userList?: IApiPatientConversationTeamListTeamListUserList[];
}

/**
 * 获取当前角色的患者群聊列表
 */
export interface IApiPatientConversationTeamList {
  /** 群聊列表 */
  teamList?: IApiPatientConversationTeamListTeamList[];
}

/**
 * 锚点 - body 请求参数
 */
export interface IApiPatientConversationAnchorParams {
  /** 患者id */
  patientId: number;
}

/**
 * 锚点
 */
export interface IApiPatientConversationAnchor {
  /** 是否有调药跟踪待办 */
  isDrug?: boolean;
  /** 是否随访周期内 */
  isFollow?: boolean;
  /** 是否复查周期内 */
  isReview?: boolean;
  /** 是否异常 */
  isError?: boolean;
}

/**
 * 查询互联网医院 - body 请求参数
 */
export interface IApiPhysicianQueryInternetHospitalParams {
  data: string;
}

/**
 *
 */
export interface IApiPhysicianQueryInternetHospitalItem {
  /** 互联网医院id */
  internetDepartmentId?: number;
  /** 名称 */
  name?: string;
}

/**
 * 查询互联网医院
 */
export type IApiPhysicianQueryInternetHospital =
  IApiPhysicianQueryInternetHospitalItem[];

/**
 * 查询医生备案详情 - body 请求参数
 */
export interface IApiPhysicianQueryInfoParams {
  data: string;
}

/**
 * 查询医生备案详情
 */
export interface IApiPhysicianQueryInfo {
  /** 职称 */
  title?: number;
  /** 互联网科室 */
  internetDepartmentId?: number;
  /** 医疗机构 */
  medicalInstitution?: string;
  /** 身份证号 */
  idCard?: string;
  /** 临床工作年限 */
  clinicalYears?: number;
  /** 医疗机构编码 */
  medicalInstitutionCode?: string;
  /** 医师资格证号 */
  qualificationCertificateNo?: string;
  /** 证号编号 */
  certificateNo?: string;
  /** 专业 */
  major?: string;
  /** 发证机关 */
  issuingAuthority?: string;
  /** 学历 */
  education?: number;
  /** 发证日期 */
  issuingTime?: string;
  /** 签发人 */
  issuer?: string;
  /** 毕业学校 */
  graduationSchool?: string;
  /** 执业证医生执业证号 */
  doctorLicenseNo?: string;
  /** 执业证发证日期 */
  licenseNoTime?: string;
  /** 执业证发证机关 */
  licenseAuthority?: string;
  /** 执业证证书编号 */
  practicingCertificateNo?: string;
  /** 执业证签发人 */
  certificateIssuer?: string;
  /** 是否有三医账号 */
  medicalAccount?: number;
  /** 密保问题 */
  securityQuestion?: number;
  /** 密保答案 */
  securityAnswer?: string;
  /** 邮箱 */
  mailbox?: string;
  /** 密码 */
  password?: string;
  /** 0未备案 1待处理 2备案中 3已备案 */
  status?: number;
  /** 申请时间 */
  applicationTime?: string;
  /** 姓名 */
  name?: string;
  /** 手机号 */
  phone?: string;
  /** 性别 1男 2女 */
  gender?: number;
}

/**
 * 查询医生是否备案
 */
export type IApiPhysicianWhetherRegister = boolean;

/**
 * 添加或修改医师备案信息 - body 请求参数
 */
export interface IApiPhysicianModifyParams {
  assistantId?: number;
  /** 职称 */
  title: number;
  /** 互联网科室 */
  internetDepartmentId: number;
  /** 医疗机构 */
  medicalInstitution: string;
  /** 身份证号 */
  idCard: string;
  /** 临床工作年限 */
  clinicalYears: number;
  /** 医疗机构编码 */
  medicalInstitutionCode: string;
  /** 医师资格证号 */
  qualificationCertificateNo: string;
  /** 证号编号 */
  certificateNo: string;
  /** 专业 */
  major: string;
  /** 发证机关 */
  issuingAuthority: string;
  /** 学历 */
  education: number;
  /** 发证日期 */
  issuingTime: string;
  /** 签发人 */
  issuer: string;
  /** 毕业学校 */
  graduationSchool: string;
  /** 执业证医生执业证号 */
  doctorLicenseNo: string;
  /** 执业证发证日期 */
  licenseNoTime: string;
  /** 执业证发证机关 */
  licenseAuthority: string;
  /** 执业证证书编号 */
  practicingCertificateNo: string;
  /** 执业证签发人 */
  certificateIssuer: string;
  /** 是否有三医账号 */
  medicalAccount: number;
  /** 密保问题 */
  securityQuestion: number;
  /** 密保答案 */
  securityAnswer: string;
  /** 邮箱 */
  mailbox: string;
  /** 密码 */
  password: string;
  /** 0未备案 1待处理 2备案中 3已备案 */
  status?: number;
  /** 申请时间 */
  applicationTime?: string;
  /** 姓名 */
  name: string;
  /** 手机号 */
  phone: string;
  /** 性别 1男 2女 */
  gender: number;
}

/**
 * 添加或修改医师备案信息
 */
export interface IApiPhysicianModify {}

/**
 * 条件分页查询群聊编号 - body 请求参数
 */
export interface IApiTeamConversationInfoParams {
  /** 页码 */
  page: number;
  /** 分页大小 */
  pageSize: number;
}

/**
 * 条件分页查询群聊编号
 */
export interface IApiTeamConversationInfo {
  /** 群聊主键id */
  teamNumber?: string[];
  /** 总条数 */
  totals?: number;
}

/**
 * 根据群聊编号查询群聊成员信息 - body 请求参数
 */
export interface IApiTeamConversationMemberInfoParams {
  /** 群聊编号 */
  teamConvId: string;
}

/**
 * 群聊成员信息
 */
export interface IApiTeamConversationMemberInfoTeamConvUsers {
  /** 用户id（患者、医生、专家） */
  userId?: number;
  /** 用户类型（user、assistant、doctor） */
  userRole?: string;
  /** 用户accId */
  accId?: string;
}

/**
 * 群聊成员信息
 */
export interface IApiTeamConversationMemberInfoTeamConv {
  /** 群聊编号 */
  teamConvId?: string;
  /** 群聊成员信息 */
  users?: IApiTeamConversationMemberInfoTeamConvUsers[];
}

/**
 * 根据群聊编号查询群聊成员信息
 */
export interface IApiTeamConversationMemberInfo {
  /** 群聊成员信息 */
  teamConv?: IApiTeamConversationMemberInfoTeamConv;
}

/**
 * 获取系统药品列表 - body 请求参数
 */
export interface IApiDrugSystemListParams {
  /** 关键字 */
  keyword?: string;
  /** 药品类型 */
  typeId?: number;
  /** 药品类型列表 */
  typeIdList?: number[];
  /** 是否只获取前十条 true 是 false否 默认false */
  limit?: boolean;
  /** 药品id */
  drugId?: number;
}

/**
 * 规格
 */
export interface IApiDrugSystemListDrugListDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 单次用量
 */
export interface IApiDrugSystemListDrugListDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 其他 不能格式化的老数据 */
  custom?: string;
}

/**
 * 药品列表
 */
export interface IApiDrugSystemListDrugList {
  /** 药品名称 */
  drugName?: string;
  /** 通用名称 */
  commonName?: string;
  /** 药品id */
  drugId?: number;
  /** 规格 */
  drugSpec?: IApiDrugSystemListDrugListDrugSpec;
  /** 单次用量 */
  drugAmount?: IApiDrugSystemListDrugListDrugAmount;
  /** 用药方式 */
  drugMode?: string;
  /** 频率 */
  drugUsage?: string;
  /** 用药时间 */
  takingTime?: string;
}

/**
 * 获取系统药品列表
 */
export interface IApiDrugSystemList {
  /** 药品列表 */
  drugList?: IApiDrugSystemListDrugList[];
}

/**
 * 获取系统药品详情 - body 请求参数
 */
export interface IApiDrugSystemDetailParams {
  /** 药品名称 */
  name?: string;
  /** 药品id */
  drugId?: number;
}

/**
 * 规格
 */
export interface IApiDrugSystemDetailDrugSpec {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
  /** 单位 */
  unit?: string;
  /** 制剂 */
  packageNum?: string;
  /** 包装单位 */
  packageUnit?: string;
}

/**
 * 单次用量
 */
export interface IApiDrugSystemDetailDrugAmount {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
}

/**
 * 单次最大用量
 */
export interface IApiDrugSystemDetailMaxUsage {
  /** 成分含量 */
  ingredients?: string;
  /** 含量单位 */
  contentUnit?: string;
}

/**
 * 获取系统药品详情
 */
export interface IApiDrugSystemDetail {
  /** 药品名称 */
  drugName?: string;
  /** 通用名称 */
  commonName?: string;
  /** 药品id */
  drugId?: number;
  /** 规格 */
  drugSpec?: IApiDrugSystemDetailDrugSpec;
  /** 单次用量 */
  drugAmount?: IApiDrugSystemDetailDrugAmount;
  /** 用药方式 */
  drugMode?: string;
  /** 频率 */
  drugUsage?: string;
  /** 类型名称 */
  className?: string;
  /** 适用症 */
  drugApplicability?: string;
  /** 禁忌 */
  drugTaboo?: string;
  /** 注意事项 */
  drugAttention?: string;
  /** 用药贴士 */
  medicationTips?: string;
  /** 单次最大用量 */
  maxUsage?: IApiDrugSystemDetailMaxUsage;
  /** 过量情况 */
  excessiveSituation?: string;
  /** 不良反应 */
  adverseReactions?: string;
  /** 漏服情况 */
  leakSituation?: string;
}

/**
 * 获取药品类型列表 - body 请求参数
 */
export interface IApiDrugSystemTypeParams {
  data: string;
}

/**
 * 药品类型列表
 */
export interface IApiDrugSystemTypeTypeList {
  /** 类型id */
  typeId?: number;
  /** 名称 */
  name?: string;
}

/**
 * 获取药品类型列表
 */
export interface IApiDrugSystemType {
  /** 药品类型列表 */
  typeList?: IApiDrugSystemTypeTypeList[];
}

/**
 * 问卷
 */
export interface IApiFollowUpdateInfoParamsQuestionnaireList {
  /** 问卷id */
  questionnaireId: number;
  /** 问卷名称 */
  questionnaireName: string;
}

/**
 * 修改随访 - body 请求参数
 */
export interface IApiFollowUpdateInfoParams {
  /** 患者id */
  patientId?: number;
  /** 类型 (1症状随访、2生活方式随访） */
  followType?: number;
  /** 问卷 */
  questionnaireList?: IApiFollowUpdateInfoParamsQuestionnaireList[];
  /** 修改生活方式随访之前的时间 */
  beforeTime?: number;
  /** 修改之后的时间 */
  afterTime?: number;
  /** 症状随访id */
  followId?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
}

/**
 * 修改随访
 */
export interface IApiFollowUpdateInfo {}

/**
 * 删除随访 - body 请求参数
 */
export interface IApiFollowAdjustDeleteParams {
  /** 患者id */
  patientId?: number;
  /** 类型 (1症状随访、2生活方式随访) */
  type?: number;
  /** id */
  followId?: number;
  /** date */
  date?: number;
}

/**
 * 删除随访
 */
export interface IApiFollowAdjustDelete {}

/**
 * 处理症状随访事件 - body 请求参数
 */
export interface IApiFollowDealSymptomParams {
  /** 症状随访id */
  followUpId: number;
  /** 处理方式 1、继续观察 2、药物调整 3、门诊 4、住院 */
  dealType: number;
}

/**
 * 处理症状随访事件
 */
export interface IApiFollowDealSymptom {}

/**
 * 批量移动天数 - body 请求参数
 */
export interface IApiFollowBatchModifyParams {
  /** 批量移动天数 */
  day: number;
  /** 1往前移动 2往后移动 */
  type: number;
  /** 患者id */
  patientId: number;
  /** 随访类型 1症状随访、生活方式随访 */
  followType?: number;
}

/**
 * 批量移动天数
 */
export interface IApiFollowBatchModify {}

/**
 * 问卷列表
 */
export interface IApiFollowAdjustInsertFollowParamsFollowQuestionnaireList {
  /** 问卷id */
  questionnaireId: number;
  /** 问卷名称 */
  questionnaireName: string;
}

/**
 * 新增随访 - body 请求参数
 */
export interface IApiFollowAdjustInsertFollowParams {
  /** 随访日期 */
  date: number;
  /** 新增类型(1症状随访、2生活方式随访) */
  type: number;
  /** 问卷列表 */
  followQuestionnaireList: IApiFollowAdjustInsertFollowParamsFollowQuestionnaireList[];
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 患者id */
  patientId?: number;
}

/**
 * 新增随访
 */
export interface IApiFollowAdjustInsertFollow {}

/**
 * 查询当前患者可供选择的问卷 - body 请求参数
 */
export interface IApiFollowAdjustQueryQuestionnaireParams {
  /** 患id */
  patientId?: number;
  /** 问卷类型 （3症状随访、1生活方式随访） */
  type?: number;
}

/**
 *
 */
export interface IApiFollowAdjustQueryQuestionnaireItem {
  /** 问卷id */
  questionnaireId?: number;
  /** 问卷名称 */
  questionnaireName?: string;
}

/**
 * 查询当前患者可供选择的问卷
 */
export type IApiFollowAdjustQueryQuestionnaire =
  IApiFollowAdjustQueryQuestionnaireItem[];

/**
 * 查询患者只填写一次的相关问卷问题数据 - body 请求参数
 */
export interface IApiFollowLifestyleRuleInfoParams {
  /** 患者id */
  patientId: number;
}

/**
 * 查询患者只填写一次的相关问卷问题数据
 */
export interface IApiFollowLifestyleRuleInfo {
  /** 主键id */
  id?: number;
  /** 患者id */
  patientId?: number;
  /** 问卷id */
  questionnaireId?: number;
  /** 规则属性 */
  desc?: string;
}

/**
 * 查询症状随访、生活方式随访列表 - body 请求参数
 */
export interface IApiFollowQueryListParams {
  /** 患者id */
  patientId: number;
  /** 随访类型(1症状随访、2生活方式随访) */
  followTypeList?: number[];
}

/**
 * 问卷列表
 */
export interface IApiFollowQueryListItemFollowQuestionnaireList {
  /** 问卷id */
  questionnaireId?: number;
  /** 问卷名称 */
  questionnaireName?: string;
}

/**
 *
 */
export interface IApiFollowQueryListItem {
  /** 随访id */
  followId?: number;
  /** date */
  date?: number;
  /** 问卷列表 */
  followQuestionnaireList?: IApiFollowQueryListItemFollowQuestionnaireList[];
  /** 第几次随访  （0为自定义随访） */
  times?: number;
  /** 状态 */
  status?: number;
}

/**
 * 查询症状随访、生活方式随访列表
 */
export type IApiFollowQueryList = IApiFollowQueryListItem[];

/**
 * 编辑生活方式随访 - body 请求参数
 */
export interface IApiFollowLifestyleEditParams {
  /** 生活方式随访id */
  userQuestionnaireId: number;
  /** 分数 */
  score?: string;
  /** 结论 */
  conclusion?: string;
  /** 问题信息 */
  questionInfo?: string;
  /** 答案信息 */
  answerInfo?: string;
  /** 描述 */
  desc?: string;
  /** 编辑人 */
  editor?: number;
  /** 编辑人类型 1医生；2健康管理师；3运动康复师；4患者 */
  editorType?: number;
}

/**
 * 编辑生活方式随访
 */
export type IApiFollowLifestyleEdit = undefined;

/**
 * 编辑症状随访 - body 请求参数
 */
export interface IApiFollowSymptomEditParams {
  /** 症状随访id */
  followUpId: number;
  /** 患者id */
  patientId: number;
  /** 填写的问题的答案 */
  questions: string;
  /** 编辑人 */
  editor?: number;
  /** 编辑人类型 1医生；2健康管理师；3运动康复师；4患者 */
  editorType?: number;
}

/**
 * 编辑症状随访
 */
export type IApiFollowSymptomEdit = undefined;

/**
 * 获取患者随访记录 - body 请求参数
 */
export interface IApiFollowRecordsParams {
  /** 患者id */
  patientId: number;
}

/**
 * 症状随访信息
 */
export interface IApiFollowRecordsFollowUpInfo {
  /** 症状随访id */
  followUpId?: number;
  /** 症状随访日期 */
  followUpDate?: string;
  /** 症状随访状态 */
  status?: number;
  /** HEART_FAILURE:心衰；CORONARY_HEART_DISEASE:冠心病 */
  serviceType?: string;
}

/**
 * 生活方式随访信息
 */
export interface IApiFollowRecordsQuestionnaireInfo {
  /** 生活方式随访日期 */
  userQuestionnaireDate?: string;
  /** 生活方式随访状态 */
  status?: number;
  /** HEART_FAILURE:心衰；CORONARY_HEART_DISEASE:冠心病 */
  serviceType?: string;
  /** 问卷版本号 */
  questionnaireVersion?: string;
}

/**
 * 获取患者随访记录
 */
export interface IApiFollowRecords {
  /** 症状随访信息 */
  followUpInfo?: IApiFollowRecordsFollowUpInfo[];
  /** 生活方式随访信息 */
  questionnaireInfo?: IApiFollowRecordsQuestionnaireInfo[];
}

/**
 * 获取生活方式随访详情 - body 请求参数
 */
export interface IApiFollowLifestyleDetailParams {
  /** 患者id */
  patientId: number;
  /** 生活方式随访日期 */
  userQuestionnaireDate: string;
}

/**
 *
 */
export interface IApiFollowLifestyleDetailItem {
  /** 主键 */
  userQuestionnaireId?: number;
  /** 患者id */
  userId?: number;
  /** 状态 0待填写 1已完成 */
  status?: number;
  /** 问卷开始时间 */
  startTime?: string;
  /** 分数 */
  score?: string;
  /** 结论 */
  conclusion?: string;
  /** 编辑人 */
  editor?: number;
  /** 编辑人类型 1医生；2健康管理师；3运动康复师；4患者 */
  editorType?: number;
  /** 编辑时间 */
  editTime?: string;
  /** 编辑人名称 */
  editorName?: string;
  /** 问卷id */
  questionnaireId?: number;
  /** 问卷名称 */
  questionnaireName?: string;
  /** 问卷问题内容 */
  questionInfo?: string;
  /** 问卷答案内容 */
  answerInfo?: string;
  /** 问卷版本 1老问卷 2新问卷 */
  questionnaireVersion?: number;
  /** 服务类型  HEART_FAILURE:心衰；CORONARY_HEART_DISEASE:冠心病 */
  serviceType?: string;
}

/**
 * 获取生活方式随访详情
 */
export type IApiFollowLifestyleDetail = IApiFollowLifestyleDetailItem[];

/**
 * 获取症状随访详情 - body 请求参数
 */
export interface IApiFollowSymptomDetailParams {
  /** 症状随访id */
  followUpId: number;
}

/**
 * 选项内容
 */
export interface IApiFollowSymptomDetailQuestionAnswerLinkAnswerInfoChooseContent {
  /** 选项名称 */
  answerContent?: string;
  /** 选项id */
  answerId?: string;
}

/**
 * 选项关联内容
 */
export interface IApiFollowSymptomDetailQuestionAnswerLinkAnswerInfo {
  /** 关联内容描述 */
  linkContent?: string;
  /** 关联内容类型（1：文本输入，2：日期，3：图片，4：多选题） */
  type?: string;
  /** 选项id */
  answerId?: string;
  /** 选项内容 */
  chooseContent?: IApiFollowSymptomDetailQuestionAnswerLinkAnswerInfoChooseContent[];
}

/**
 * 回答信息
 */
export interface IApiFollowSymptomDetailQuestionAnswerAnswerInfo {
  /** 选项id */
  answerId?: string;
  /** 原因 */
  reason?: string;
  /** 住院时间 */
  hospitalTime?: string;
  /** 其他（填空回答） */
  answerContent?: string;
  /** 上传图片 */
  answerPictureList?: string[];
  /** 集体情况 */
  specificSituation?: string;
  /** 住院科室 */
  inpatientDepartment?: string;
}

/**
 * 选项信息
 */
export interface IApiFollowSymptomDetailQuestionAnswerChooseContent {
  /** 选项名称 */
  answerContent?: string;
  /** 选项id */
  answerId?: string;
}

/**
 * 症状随访问题答案
 */
export interface IApiFollowSymptomDetailQuestionAnswer {
  /** 问题内容 */
  questionContent?: string;
  /** 选项关联内容 */
  linkAnswerInfo?: IApiFollowSymptomDetailQuestionAnswerLinkAnswerInfo[];
  /** 标识哪一个选项有子问题 */
  trueAnswer?: string;
  /** 问题id */
  questionId?: string;
  /** 父id */
  pQuestionId?: number;
  /** 回答信息 */
  answerInfo?: IApiFollowSymptomDetailQuestionAnswerAnswerInfo[];
  /** 4是基础问题 2是医助满意度问题  1是随访添加的问题 5是糖网患者问题 6是房颤患者问题 7房颤健康总结 8是科研问题 */
  type?: number;
  /** 题型 1,单选 2,多选 3,填空题 4,数字填空 5,含小数填空 6,范围题 7,计算题8.多项填空 */
  questionType?: number;
  /** 随访问卷id */
  followUpQuestionId?: number;
  /** 是否必填 0非必填 1必填 */
  required?: number;
  /** 选项信息 */
  chooseContent?: IApiFollowSymptomDetailQuestionAnswerChooseContent[];
  /** 是否有子问题(有子问题：1,无子问题：0) */
  sid?: number;
}

/**
 * 获取症状随访详情
 */
export interface IApiFollowSymptomDetail {
  /** 主键 */
  followUpId?: number;
  /** 用药id */
  drugInfoId?: number;
  /** 患者id */
  userId?: number;
  /** 预计随访开始时间 */
  date?: string;
  /** 随访状态（1 未开始 4 问卷待填写    64 结论待填写 128 随访完成） */
  status?: number;
  /** 随访次数 */
  times?: number;
  /** 结论 */
  conclusion?: string;
  /** 医生意见 */
  doctorOpinion?: string;
  /** 随访概述 */
  overview?: string;
  /** 随访提醒次数，大于6次不再提醒 */
  wakeNum?: string;
  /** 编辑人id */
  editor?: number;
  /** 编辑人类型 1医生；2健康管理师；3运动康复师；4患者 */
  editorType?: number;
  /** 编辑时间 */
  editTime?: string;
  /** 编辑人名称 */
  editName?: string;
  /** 是否存在症状 0不存在 1存在-未处理 2存在-已处理 */
  symptomStatus?: number;
  /** 症状随访问题答案 */
  questionAnswer?: IApiFollowSymptomDetailQuestionAnswer[];
  /** 服务类型  HEART_FAILURE:心衰；CORONARY_HEART_DISEASE:冠心病 */
  serviceType?: string;
}

/**
 * 随访调整计划列表--生活方式随访 - body 请求参数
 */
export interface IApiFollowAdjustLifeListParams {
  /** 患者id */
  patientId: number;
}

/**
 * 问卷列表
 */
export interface IApiFollowAdjustLifeListItemFollowQuestionnaireList {
  /** 问卷id */
  questionnaireId?: number;
  /** 问卷名称 */
  questionnaireName?: string;
}

/**
 *
 */
export interface IApiFollowAdjustLifeListItem {
  /** 随访id */
  followId?: number;
  /** date */
  date?: number;
  /** 问卷列表 */
  followQuestionnaireList?: IApiFollowAdjustLifeListItemFollowQuestionnaireList[];
  /** 第几次随访  （0为自定义随访） */
  times?: number;
  /** 状态 */
  status?: number;
}

/**
 * 随访调整计划列表--生活方式随访
 */
export type IApiFollowAdjustLifeList = IApiFollowAdjustLifeListItem[];

/**
 * 随访调整计划列表--症状随访查询 - body 请求参数
 */
export interface IApiFollowAdjustSymptomListParams {
  /** 患者id */
  patientId: number;
}

/**
 * 问卷列表
 */
export interface IApiFollowAdjustSymptomListItemFollowQuestionnaireList {
  /** 问卷id */
  questionnaireId?: number;
  /** 问卷名称 */
  questionnaireName?: string;
}

/**
 *
 */
export interface IApiFollowAdjustSymptomListItem {
  /** 随访id */
  followId?: number;
  /** date */
  date?: number;
  /** 问卷列表 */
  followQuestionnaireList?: IApiFollowAdjustSymptomListItemFollowQuestionnaireList[];
  /** 第几次随访  （0为自定义随访） */
  times?: number;
  /** 状态 */
  status?: number;
}

/**
 * 随访调整计划列表--症状随访查询
 */
export type IApiFollowAdjustSymptomList = IApiFollowAdjustSymptomListItem[];

/**
 * 指标类
 */
export interface IApiScanModifyCheckIndexParamsIndexVo {
  /** 指标大类 */
  checkType?: number;
  /** 指标小类 */
  indexType?: number;
  /** 指标数据 */
  content?: string;
  /** 检查时间 */
  checkTime?: string;
  /** 异常等级 */
  errorLevel?: number;
}

/**
 * ocr-检查报告-保存住院、门诊、复查 - body 请求参数
 */
export interface IApiScanModifyCheckIndexParams {
  /** 用户id */
  userId?: number;
  /** 患者id */
  patientId?: number;
  /** 用户id */
  userType?: number;
  /** 病历id */
  patientHistoryId?: number;
  /** 资料完善度 */
  dataPerfect?: number;
  /** 附件地址 */
  url?: string;
  /** 报告类型 0 住院 1 门诊 2 复查 */
  caseType?: number;
  /** 指标类 */
  indexVo?: IApiScanModifyCheckIndexParamsIndexVo[];
}

/**
 * ocr-检查报告-保存住院、门诊、复查
 */
export interface IApiScanModifyCheckIndex {}

/**
 * 查询第一页未读消息数（用于前端展示消息红点） - body 请求参数
 */
export interface IApiMessageCenterNumParams {
  data: string;
}

/**
 * 查询第一页未读消息数（用于前端展示消息红点）
 */
export type IApiMessageCenterNum = number;

/**
 * 消息中心列表查询 - body 请求参数
 */
export interface IApiMessageCenterListParams {
  pageNumber?: number;
  pageSize?: number;
  /** 用戶id */
  userId?: number;
  /** 用戶类型 */
  userType?: number;
  /** 消息类型 */
  messageTypes?: string[];
}

/**
 *
 */
export interface IApiMessageCenterListResponseMsgContentChatRecordDTO {
  patientId?: number;
  patientName?: string;
  sourceId?: string;
  conversationId?: string;
  chatId?: string;
  senderName?: string;
  content?: string;
}

/**
 *
 */
export interface IApiMessageCenterListResponseMsgContentDataCompletedDTO {
  patientId?: number;
  patientName?: string;
  sourceId?: string;
  occurredTime?: number;
  content?: string;
}

/**
 *
 */
export interface IApiMessageCenterListResponseMsgContentPatientEnrollmentDTO {
  patientId?: number;
  patientName?: string;
  sourceId?: string;
  gender?: number;
  age?: number;
  groupName?: string;
  groupId?: number;
}

/**
 *
 */
export interface IApiMessageCenterListResponseMsgContentPatientInfoExtendDTO {
  patientId?: number;
  patientName?: string;
  sourceId?: string;
  content?: string;
  chatId?: number;
}

/**
 * 消息体
 */
export interface IApiMessageCenterListResponseMsgContent {
  chatRecordDTO?: IApiMessageCenterListResponseMsgContentChatRecordDTO;
  dataCompletedDTO?: IApiMessageCenterListResponseMsgContentDataCompletedDTO;
  patientEnrollmentDTO?: IApiMessageCenterListResponseMsgContentPatientEnrollmentDTO;
  patientInfoExtendDTO?: IApiMessageCenterListResponseMsgContentPatientInfoExtendDTO;
}

/**
 * 消息
 */
export interface IApiMessageCenterListResponse {
  /** 消息id */
  msgId?: number;
  /** 用户id */
  userId?: number;
  /** 患者id */
  patientId?: number;
  /** 用户类型 */
  uerType?: string;
  /** 消息类型 */
  msgType?: string;
  /** 消息状态 */
  msgStatus?: string;
  /** 消息体 */
  msgContent?: IApiMessageCenterListResponseMsgContent;
  /** 是否是系统消息 */
  sysMsg?: boolean;
  /** 最新一次更新时间 */
  latestTime?: number;
  /** 消息头 */
  msgTitle?: string;
  /** 消息内容 */
  msgSimpleContent?: string;
  /** 来源id */
  sourceId?: string;
  /** 二级来源 */
  secondSourceId?: string;
  /** 时间 */
  sourceTime?: number;
}

/**
 * 消息中心列表查询
 */
export interface IApiMessageCenterList {
  /** 消息总数 */
  totals?: number;
  /** 消息 */
  response?: IApiMessageCenterListResponse[];
}

/**
 * 消息已读 - body 请求参数
 */
export interface IApiMessageCenterReadParams {
  /** 消息id */
  msgId?: number;
  /** 消息类型 */
  messageType?: string;
  /** 来源id */
  sourceId?: string;
  /** 消息类型 */
  messageTypes?: string[];
}

/**
 * 消息已读
 */
export type IApiMessageCenterRead = boolean;

/**
 * 清空消息 - body 请求参数
 */
export interface IApiMessageCenterClearParams {
  /** 消息id */
  msgId?: number;
  /** 消息类型 */
  messageType?: string;
  /** 来源id */
  sourceId?: string;
  /** 消息类型 */
  messageTypes?: string[];
}

/**
 * 清空消息
 */
export type IApiMessageCenterClear = boolean;
