/**
 * 通话相关接口类型定义
 */

/** 基础响应结构 */
export interface BaseResponse<T = any> {
  /** 响应码 */
  code: string;
  /** 响应消息 */
  message: string;
  /** 错误数据 */
  errorData?: Record<string, any>;
  /** 响应数据 */
  data: T;
}

/** 分页响应结构 */
export interface PaginatedResponse<T = any> {
  /** 总数 */
  total: number;
  /** 内容列表 */
  contents: T[];
}

/** 通讯录标签枚举 */
export enum AddressBookTag {
  /** 常用 */
  USUAL = 'USUAL',
  /** 最近 */
  LAST = 'LAST',
}

/** 线路 */
export enum CallSource {
  /** 天润联通 */
  AICC = 'AICC',
  /** 容联云 */
  MOOR = 'MOOR',
}

// ==================== 请求参数类型 ====================

/** 查询患者通讯录列表请求参数 */
export interface PatientAddressBookQueryParams {
  /** 患者id */
  patientId: number;
}

/** 查询患者亲情账号列表请求参数 */
export interface PatientUserOpenQueryParams {
  /** 患者id */
  patientId: number;
}

/** 查询外显号码请求参数 */
export interface CallShowListQueryParams {
  /** 电话号码 */
  phone: string;
  /** 来源 */
  source: CallSource;
  /** 患者id */
  patientId?: number;
}

/** 查询被叫号码归属地请求参数 */
export interface CallAreaQueryParams {
  /** 电话号码 */
  phone: string;
}

/** 未接来电统计请求参数 */
export interface MissedCallTotalQueryParams {
  /** 用户id */
  userId: number;
  /** 用户类型 */
  userType: number;
  /** 页面大小 */
  pageSize?: number;
  /** 页码 */
  pageNumber?: number;
}

/** 查询未接来电请求参数 */
export interface MissedCallPageQueryParams {
  /** 用户id */
  userId: number;
  /** 用户类型 */
  userType: number;
  /** 页面大小 */
  pageSize?: number;
  /** 页码 */
  pageNumber?: number;
}

/** 通话记录已读标记请求参数 */
export interface CallRecordReadParams {
  /** 通话记录唯一标识 */
  uniqueId: string;
  /** 操作员id */
  operatorId?: number;
}

// ==================== 响应数据类型 ====================

/** 患者通讯录项 */
export interface PatientAddressBookItem {
  /** 手机号 */
  phone: string;
  /** 姓名 */
  name: string;
  /** 关系 */
  relation: string;
  /** 通讯录ID */
  addressBookId: number;
  /** 标签 */
  tags: AddressBookTag[];
}

/** 患者亲情账号项 */
export interface PatientUserOpenItem {
  /** 手机号 */
  phone: string;
  /** 姓名 */
  name: string;
  /** 关系 */
  relation: string;
  /** 标签 */
  tags: AddressBookTag[];
}

/** 患者通讯录列表响应 */
export type PatientAddressBookListResponse = BaseResponse<
  PatientAddressBookItem[]
>;

/** 外显号码项 */
export interface CallShowItem {
  /** 电话号码 */
  phone: string;
  /** 省份 */
  province: string;
  /** 城市 */
  city: string;
  /** 是否选中 */
  selected: boolean;
  /** 标签 */
  tags: string[];
  /** 用户名 */
  userName: string;
  /** 通话时间 */
  callTime: number;
  /** 通话状态 */
  callStatus: string;
  /** 排序 */
  sort: number;
  /** 通话类型 1 呼入 2 呼出 */
  type?: 1 | 2;
}

/** 号码归属地信息 */
export interface CallAreaInfo {
  /** 省份 */
  province: string;
  /** 城市 */
  city: string;
  /** 上次拨打线路 */
  source: CallSource;
}

/** 未接来电记录项 */
export interface MissedCallItem {
  /** 记录ID */
  id: number;
  /** 患者id */
  patientId: number;
  /** 患者姓名 */
  patientName: string;
  /** 用户id */
  userId: number;
  /** 用户类型 */
  userType: number;
  /** 手机号 */
  customerNumber: string;
  /** 通话类型 1 呼入 2 呼出 */
  type: number;
  /** 接听状态 */
  callStatus: number;
  /** 响铃时间 */
  callTime: number;
  /** 坐席号 */
  cno: string;
  /** 客户省份 */
  customerProvince: string;
  /** 客户城市 */
  customerCity: string;
  /** 接通时长 */
  bridgeDuration: number;
  /** 通话记录唯一标识 */
  uniqueId: string;
  /** 挂机方 */
  endReason: string;
  /** 接通时间 */
  bridgeTime: number;
  /** 呼叫中心返回 call_status 中文 */
  status: string;
  /** 呼叫中心返回 type 中文 */
  callType: string;
  /** 机主名称 */
  name: string;
  /** 关系 */
  relation: string;
  /** 热线号码 */
  hotline: string;
  /** 等待时长 */
  waitDuration: number;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
  /** 坐席电话 */
  agentPhone: string;
  /** 通话来源 */
  sourceType: string;
  /** 录音文件url */
  fileUrl: string;
}

/** 患者亲情账号列表响应 */
export type PatientUserOpenListResponse = BaseResponse<PatientUserOpenItem[]>;

/** 外显号码列表响应 */
export type CallShowListResponse = CallShowItem[];

/** 号码归属地响应 */
export type CallAreaResponse = CallAreaInfo;

/** 未接来电数量响应 */
export type MissedCallTotalResponse = BaseResponse<number>;

/** 未接来电列表响应 */
export type MissedCallPageResponse = BaseResponse<
  PaginatedResponse<MissedCallItem>
>;

/** 通话记录已读标记响应 */
export type CallRecordReadResponse = boolean;
