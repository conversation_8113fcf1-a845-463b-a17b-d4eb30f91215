import { reactive, ref } from 'vue';

type CacheState = 'pending' | 'success' | 'error';

interface CacheItem<T> {
  data: T | null;
  status: CacheState;
  error: Error | null;
  promise: Promise<T> | null;
  timestamp: number;
}

/** 缓存存储对象 */
const cacheMap = reactive(new Map<string, CacheItem<any>>());

/** 默认缓存时间 24 小时 */
const DEFAULT_EXPIRE_TIME = 24 * 60 * 60 * 1000;

export function useCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: {
    expireTime?: number;
    forceRefresh?: boolean;
  } = {}
) {
  const { expireTime = DEFAULT_EXPIRE_TIME, forceRefresh = false } = options;

  // 初始化响应式数据
  const data = ref<T | null>(null);
  const status = ref<CacheState>('pending');
  const error = ref<Error | null>(null);

  // 检查缓存有效性
  const isCacheValid = (item: CacheItem<T>): boolean =>
    Date.now() - item.timestamp < expireTime;

  // 获取或初始化缓存项
  let cacheItem = cacheMap.get(key);
  if (!cacheItem || forceRefresh) {
    cacheItem = {
      data: null,
      status: 'pending',
      error: null,
      promise: null,
      timestamp: 0,
    };
    cacheMap.set(key, cacheItem);
  }

  // 如果缓存有效且存在数据
  if (!forceRefresh && cacheItem.data && isCacheValid(cacheItem)) {
    data.value = cacheItem.data;
    status.value = cacheItem.status;
    return { data, status, error };
  }

  // 如果已经有正在进行的请求
  if (cacheItem.promise && !forceRefresh) {
    cacheItem.promise
      .then(res => {
        data.value = res;
        status.value = 'success';
      })
      .catch(err => {
        error.value = err;
        status.value = 'error';
      });
    return { data, status, error };
  }

  // 发起新请求
  cacheItem!.promise = fetcher()
    .then(response => {
      cacheItem!.data = response;
      cacheItem!.status = 'success';
      cacheItem!.timestamp = Date.now();
      data.value = response;
      return response;
    })
    .catch(err => {
      cacheItem!.status = 'error';
      cacheItem!.error = err;
      error.value = err;
      throw err;
    })
    .finally(() => {
      cacheItem!.promise = null;
    });
  cacheItem!.status = 'pending';

  // 返回统一的数据接口
  return {
    data,
    status,
    error,
    // 提供手动刷新方法
    refresh: () => {
      return useCache(key, fetcher, { ...options, forceRefresh: true });
    },
  };
}
