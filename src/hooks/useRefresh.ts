import bus from '@/lib/bus';
import { useOcrScan } from '@/store/module/useOcrScan';
const useOcrScanInfo = useOcrScan();
export const useRenderRefresh = (data: any) => {
  const renderKey = ref(0);

  const update = () => {
    renderKey.value += 1;
  };
  const updateHandler = (id: number) => {
    if (data.value?.id === id && useOcrScanInfo.detaleImgUpdateMedicalRecord) {
      update();
    }
  };
  onMounted(() => {
    bus.on('refresh-record-data', updateHandler);
  });
  onUnmounted(() => {
    bus.off('refresh-record-data', updateHandler);
  });
  return { renderKey };
};
