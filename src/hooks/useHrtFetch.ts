import { TOKEN_KEY } from '@/constant/cache';
import { createFetch, useLocalStorage } from '@vueuse/core';

export const useHrtFetch = createFetch({
  baseUrl: import.meta.env.VITE_APP_baseUrl,
  options: {
    /** 请求拦截 */
    async beforeFetch({ options }) {
      /** 用户登录后的token */
      const token = useLocalStorage(TOKEN_KEY, '').value;
      /** 如果token存在，则每次请求带上token */
      if (token) {
        options.headers = {
          ...options.headers,
          Authorization: token,
        };
      }

      return { options };
    },
    afterFetch({ response, data }) {
      // response.json();
      console.log('Response:', response, data);
      return { response, data };
    },
  },
  fetchOptions: {
    mode: 'cors',
  },
});

export default useHrtFetch;
