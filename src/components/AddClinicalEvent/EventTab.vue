<template>
  <div class="row-item">
    <span class="label">是否发生临床事件:</span>
    <el-radio-group v-model="isClinicalEvent">
      <el-radio :value="0" size="large">有</el-radio>
      <el-radio :value="6" size="large">无</el-radio>
    </el-radio-group>
  </div>
  <div v-if="!isClinicalEvent" class="event-box">
    <div class="tab-event flex">
      <div
        v-for="item in eventList"
        :key="item.tabValue"
        class="tab-event-item"
        :class="{ active: inActiveTab === item.tabValue }"
        @click="inActiveTab = item.tabValue"
      >
        {{ item.label }}
      </div>
    </div>

    <div class="time">
      <span class="label">时间</span>
      <el-date-picker
        v-model="displayObj.time!"
        type="date"
        format="YYYY/MM/DD"
        value-format="x"
        placeholder="选择日期时间"
        :disabled-date="pickerOptions"
      />
    </div>
    <span class="reason">原因</span>
    <el-scrollbar height="300px">
      <div
        v-for="item in displayObj.reasons"
        :key="item.key"
        class="reason-item"
      >
        <template v-if="displayObj.multiple">
          <el-checkbox v-model="item.value">{{ item.label }}</el-checkbox>
          <template v-if="item.value">
            <div
              v-if="item.label === '其它'"
              class="concrete flex items-center"
            >
              <div class="mr-lg flex-shrink-0">具体原因</div>
              <el-input
                v-model="item.other"
                style="width: 100%"
                type="textarea"
                placeholder="请输入"
                class="other-input"
                maxlength="50"
                show-word-limit
                oninput="value = value.replace(/；|-/g, '')"
              />
            </div>
            <div v-show="item.concrete.list.length > 0" class="concrete">
              <span class="left">具体原因</span>
              <el-select
                v-model="item.concrete.value"
                placeholder="请选择"
                style="width: 240px"
              >
                <el-option
                  v-for="concreteItem in item.concrete.list"
                  :key="concreteItem.value"
                  :label="concreteItem.label"
                  :value="concreteItem.value"
                />
              </el-select>
              <el-input
                v-show="item.concrete.value === '其他'"
                v-model="item.concrete.other"
                placeholder="请输入"
                class="other-input"
                maxlength="50"
                oninput="value = value.replace(/；|-/g, '')"
              />
            </div>
          </template>
        </template>
        <template v-else>
          <el-radio v-model="displayObj.value" :label="item.value">{{
            item.label
          }}</el-radio>
        </template>
        <div class="tips">
          {{ item.tips }}
        </div>
      </div>
    </el-scrollbar>
  </div>
  <div class="upload-box">
    <span class="label">附件记录:</span>
    <div class="content">
      <UploadImages v-model:img-list="accessory" :is-view="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount } from 'vue';
import UploadImages from '@/components/UploadImages/index.vue';

interface Props {
  currChooseEvent: any;
  activeTab?: number;
  userId: number;
  list?: any;
}

const props = withDefaults(defineProps<Props>(), {
  activeTab: 0,
  list: [
    {
      multiple: true, // 原因是否可以多选，可以多选
      value: [],
      tabValue: 1,
      label: 'MACE事件',
      time: '',
      reasons: [
        {
          label: '再次心肌梗死',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '非计划再次血运重建手术（包括介入、冠脉搭桥）',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '再入院',
          value: '',
          concrete: [
            { label: '心力衰竭', value: '心力衰竭' },
            { label: '主诊断病情加重', value: '主诊断病情加重' },
            { label: '其他心脑血管事件', value: '其他心脑血管事件' },
          ],
          other: '',
        },
        {
          label: '脑卒中',
          value: '',
          concrete: [
            { label: '脑梗死', value: '脑梗死' },
            { label: '脑出血', value: '脑出血' },
            { label: '其他或未知原因', value: '其他或未知原因' },
          ],
          other: '',
        },
        {
          label: '新发心律失常',
          value: '',
          concrete: [
            { label: '室性早搏', value: '室性早搏' },
            { label: '室性心动过速', value: '室性心动过速' },
            { label: '室上性心动过速', value: '室上性心动过速' },
            { label: '心动过缓', value: '心动过缓' },
            { label: '传导阻滞', value: '传导阻滞' },
            { label: '房颤', value: '房颤' },
            { label: '其他', value: '其他' },
          ],
          other: '',
        },
        {
          label: '心源性休克',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '出血I型',
          value: '',
          concrete: [],
          other: '',
          tips:
            '无需立即干预的出血，患者无需因此就医或住院，包括出血后未经\n' +
            '咨询医生而自行停药等情况',
        },
        {
          label: '出血II型',
          value: '',
          concrete: [],
          other: '',
          tips: '任何明显的、需要立即干预的出血，包括:(1)需要内科、非手术干预;(2)需住院或提升治疗级别;(3)需要进行持续评估的出血',
        },
        {
          label: '出血III型',
          value: '',
          concrete: [],
          other: '',
          tips:
            '3a型明显出血且血红蛋白下降30~50g/L;需输血治疗\n' +
            '3b型明显出血且血红蛋白下降≥50g/L;心脏压塞;需外科手术干预或控\n' +
            '制的出血(除外牙齿、鼻部、皮肤及痔疮);需静脉应3b型用血管活\n' +
            '性药物的出血\n' +
            '3c型颅内出血(除外微景脑出血、脑梗死后出血转化、椎管内出血):经\n' +
            '影像学检查、腰椎穿刺证实的出血;损害视力的出血',
        },
        {
          label: '出血V型',
          value: '',
          concrete: [],
          other: '',
          tips:
            '5型致死性出血\n' +
            '5a型未经尸检或影像学检查证实的临床可疑的致死性出血\n' +
            '5b型经尸检或影像学检查证实的确切的致死性出血',
        },
        {
          label: '其它',
          value: '',
          other: '',
          concrete: [],
        },
      ],
    },
    {
      multiple: true, // 原因是否可以多选，可以多选
      value: [],
      tabValue: 2,
      label: '药物不良反应',
      time: '',
      reasons: [
        {
          label: '肝功能损害',
          value: '',
          concrete: [
            { label: '转氨酶＞3倍', value: '转氨酶＞3倍' },
            { label: '其他', value: '其他' },
          ],
          other: '',
        },
        {
          label: '肌肉损害',
          value: '',
          concrete: [
            {
              label: '肌酸激酶升高超过参考值5倍',
              value: '肌酸激酶升高超过参考值5倍',
            },
            { label: '其他', value: '其他' },
          ],
          other: '',
        },
        {
          label: '其它',
          value: '',
          other: '',
          concrete: [],
        },
      ],
    },
    {
      multiple: true, // 原因是否可以多选，可以多选
      value: '',
      tabValue: 4,
      label: '死亡事件',
      time: '',
      reasons: [
        {
          label: '死亡',
          value: 0,
          concrete: [
            { label: '心源性死亡', value: '心源性死亡' },
            { label: '其他或未知原因', value: '其他或未知原因' },
          ],
          other: '',
        },
      ],
    },
    {
      multiple: true, // 原因是否可以多选，可以多选
      value: '',
      tabValue: 5,
      label: '其他',
      time: '',
      reasons: [
        {
          label: '新发透析（主动脉术后、心衰患者）',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '支架感染',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '支架内血栓形成',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '支架内再狭窄',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '主动脉术后：主动脉破裂/濒临破裂',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '主动脉术后：残余夹层扩张≥5.5cm',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '主动脉术后：严重内漏',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '主动脉术后：瓣膜反流',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '主动脉术后：支架源性新发破口',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '主动脉术后：逆行性A型夹层',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '起搏器：起搏器电极脱落',
          value: '',
          concrete: [],
          other: '',
        },
        {
          label: '其它',
          value: '',
          other: '',
          concrete: [],
        },
      ],
    },
  ],
});

const inActiveTabIndexMap = {
  1: 0,
  2: 1,
  4: 2,
  5: 3,
  6: 0,
};

let eventList = ref([]);

let accessory = ref([]);

const inActiveTab = ref<number>(1);

let pickerOptions = (time: { getTime: () => number }) => {
  return time.getTime() > Date.now();
};

let displayObj = ref({});

const isClinicalEvent = ref<number>(0);

watch(
  () => inActiveTab.value,
  () => {
    displayObj.value = eventList.value[inActiveTabIndexMap[inActiveTab.value]];
  }
);

const currChooseEventObj = ref({});

const initData = () => {
  let info = {};
  if (props.currChooseEvent.clinicalCause) {
    splitText(props.currChooseEvent.clinicalCause);
    info = {
      tab: props.currChooseEvent.clinicalType,
      time: props.currChooseEvent.clinicalTime,
      arrValue: splitText(props.currChooseEvent.clinicalCause),
    };
  }
  currChooseEventObj.value = info;
  isClinicalEvent.value = props.currChooseEvent.clinicalType === 6 ? 6 : 0;
  accessory.value = props.currChooseEvent.accessory ?? [];
  inActiveTab.value = props.activeTab ?? 1;
  eventList.value = props.list.map((item, index) => {
    let isCurrEvent = currChooseEventObj.value.tab === item.tabValue;
    return {
      key: String(index),
      time: isCurrEvent ? currChooseEventObj.value.time : '',
      label: item.label,
      value: item.value,
      tabValue: item.tabValue,
      multiple: item.multiple,
      reasons: item.reasons.map((reasonItem, reasonIndex) => {
        let currReasonItem =
          currChooseEventObj.value.arrValue?.[reasonItem.label] || null;
        return {
          key: String(index) + reasonIndex,
          label: reasonItem.label,
          value: currReasonItem ? true : reasonItem.value,
          other: reasonItem.label === '其它' ? currReasonItem?.[0] : '',
          concrete: {
            value: currReasonItem ? currReasonItem[0] : [],
            other: currReasonItem?.length > 1 ? currReasonItem[1] : '',
            list: reasonItem.concrete.map((concreteItem, concreteIndex) => {
              return {
                key: String(String(index) + reasonIndex) + concreteIndex,
                label: concreteItem.label,
                value: concreteItem.value,
              };
            }),
          },
          tips: reasonItem.tips,
        };
      }),
    };
  });
  displayObj.value = eventList.value[inActiveTabIndexMap[inActiveTab.value]];
};

const splitText = text => {
  let arrValue = text.split('；');
  let info = {};
  for (let i = 0; i < arrValue.length; i++) {
    let el = arrValue[i];
    el = el.split('-');
    info[el[0]] = el.splice(1);
  }
  return info;
};

const dealSubmitData = () => {
  const data = JSON.parse(JSON.stringify(displayObj.value));
  let clinicalCause = '';
  // 多选提交数据处理
  // 过滤掉没有选中的数据，然后将选中的label标签和选中值组合起来
  clinicalCause = data.reasons
    .filter(item => item.value)
    .map(item => {
      const { other, label } = item;
      if (other && label === '其它') return `${label}-${other}`;

      const concrete = item.concrete.value;
      item.concrete.other = concrete === '其他' ? item.concrete.other : '';
      // 选择的具体原因是非其他，则正常组装，否则加上其他原因后面的输入文字
      const notOtherClinicalCause = [item.label, concrete]
        .filter(text => text)
        .join('-');
      return [notOtherClinicalCause, item.concrete.other]
        .filter(text => text)
        .join('-');
    })
    .join('；');

  const submitData = {
    patientId: props.userId,
    clinicalType:
      isClinicalEvent.value === 0 ? data.tabValue : isClinicalEvent.value,
    clinicalTime: isClinicalEvent.value === 0 ? data.time : new Date(),
    clinicalCause: isClinicalEvent.value === 0 ? clinicalCause : '无临床事件',
    accessory: accessory.value,
  };
  if (!submitData.clinicalTime) {
    ElMessage({
      type: 'warning',
      message: '请选择时间！',
    });
    return false;
  }
  if (!submitData.clinicalCause) {
    ElMessage({
      type: 'warning',
      message: '请选择原因！',
    });
    return false;
  }

  return submitData;
};

defineExpose({
  dealSubmitData,
});
onBeforeMount(() => {
  initData();
});
defineOptions({
  name: 'EventTab',
});
</script>

<style lang="less">
.event-box {
  text-align: left !important;
  .tab-event {
    border-left: 1px solid #dcdee0;
    border-radius: 4px 0 0 0;
    margin: 16px 0;
    position: relative;
    &::after {
      display: inline-block;
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      border-bottom: 1px solid #dcdee0;
    }
    .tab-event-item {
      min-width: 112px;
      height: 40px;
      line-height: 40px;
      background: #f7f8fa;
      border: 1px solid #dcdee0;
      text-align: center;
      border-left: none;
      z-index: 2;
      &:first-of-type {
        border-radius: 4px 0 0 0;
      }
      &:last-of-type {
        border-radius: 0 4px 0 0;
      }
    }
    .active {
      border-bottom: 1px solid #fff;
      background: #fff;
    }
  }
  .time {
    display: flex;
    align-items: center;
    margin: 9px 0 24px 0;

    .label {
      font-size: 14px;
      font-weight: normal;
      color: rgba(32, 53, 73, 1);
      margin-right: 16px;
    }
  }

  .reason {
    font-size: 14px;
    font-weight: bold;
    color: rgba(32, 53, 73, 1);
  }

  .reason-item {
    &:nth-child(n + 2) {
      margin-top: 16px;
    }
    .tips {
      font-size: 12px;
      color: rgb(153, 153, 153);
    }
  }

  .concrete {
    box-sizing: border-box;
    padding: 8px 12px 12px 12px;
    margin: 8px 0 0 24px;
    background-color: rgba(247, 248, 250, 1);

    .left {
      font-size: 14px;
      display: block;
      font-weight: normal;
      color: rgba(32, 53, 73, 1);
      margin: 0 0 8px 0;
    }

    .other-input {
      width: 230px;
    }
  }

  .el-tabs__item {
    font-size: 14px;
    font-weight: normal !important;
    color: rgba(50, 50, 51, 1);
    background-color: rgba(247, 248, 250, 1);
  }

  .is-active {
    color: rgba(50, 50, 51, 1) !important;
    background-color: rgba(255, 255, 255, 1);
  }
}
.row-item {
  display: flex;
  align-items: center;
  .label {
    margin-right: 12px;
    font-weight: bold;
    font-size: 14px;
    color: #3a4762;
  }
  .content {
    flex: 1;
  }
}
.upload-box {
  display: flex;
  margin-top: 16px;
  .label {
    margin-right: 12px;
    font-weight: bold;
    font-size: 14px;
    color: #3a4762;
  }
  .content {
    flex: 1;
  }
}
</style>
