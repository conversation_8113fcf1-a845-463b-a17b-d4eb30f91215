<template>
  <Dialog
    v-model:visible="clinicalEventVisible"
    :width="600"
    title="临床事件"
    @close="closeHandler"
  >
    <AddClinicalEvent
      ref="refAddClinicalEvent"
      v-model:visible="clinicalEventVisible"
      :user-id="globalStore.userId!"
      :curr-choose-event="currChooseEvent"
    />
    <template #footer>
      <div class="btn-box">
        <div class="cancel-btn" @click="cancel">取消</div>
        <div class="sure-btn" @click="confirm">确定</div>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import Dialog from '@/components/Dialog/index.vue';
import useGlobal from '@/store/module/useGlobal';
import AddClinicalEvent from './index.vue';
interface IProps {
  visible: boolean;
  data: any;
}

const props = defineProps<IProps>();
const emits = defineEmits(['update:visible', 'change']);
const globalStore = useGlobal();
const currChooseEvent = ref<any>({});
const clinicalEventVisible = ref(props.visible);
const refAddClinicalEvent = shallowRef();

const closeHandler = () => {
  emits('update:visible', false);
};
const cancel = () => {
  clinicalEventVisible.value = false;
  emits('update:visible', false);
};
const confirm = () => {
  const params = refAddClinicalEvent.value.onClose().params;
  if (params) {
    emits('change', params);
    emits('update:visible', false);
    // ElMessageBox.confirm('确定提交内容并保存?', '确定', {
    //   confirmButtonText: '确定',
    //   cancelButtonText: '取消',
    //   type: 'warning',
    //   draggable: true,
    // })
    //   .then(() => {
    //     if (currChooseEvent.value.clinicalId) {
    //       params.clinicalId = currChooseEvent.value.clinicalId;
    //     }
    //     params.clinicalTime = dayjs(params.clinicalTime).format('YYYY-MM-DD');
    //     // saveEvent(params).then(() => {
    //     //   clinicalEventVisible.value = false;
    //     //   //保存事件成功后， 刷新患者临床事件信息
    //     //   bus.emit('refresh-patient-Info-event');
    //     //   ElMessage.success('保存成功！');
    //     // });
    //   })
    //   .catch(() => {});
  }
};

watch(
  () => props.visible,
  val => {
    clinicalEventVisible.value = val;
  }
);
watch(
  () => props.data,
  val => {
    currChooseEvent.value = val ?? {};
  }
);
defineOptions({
  name: 'EventDialog',
});
</script>

<style scoped lang="less">
// todo
</style>
