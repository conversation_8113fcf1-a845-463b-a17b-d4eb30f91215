<template>
  <div class="card-wrapper w-full bg-white mb-2xs">
    <div v-if="headerShow" class="header flex-bc pt-sm pr-sm">
      <div class="header-title leading-xl flex relative pl-sm items-baseline">
        <div class="header-title-primary font-semibold text-base">
          {{ title }}
        </div>
        <div v-if="subTitle" class="tips text-sm ml-2xs">
          {{ subTitle }}
        </div>
      </div>
      <div class="flex items-center">
        <div v-if="toolsTips" class="tips text-sm mr-lg">
          {{ toolsTips }}
        </div>
        <slot name="tools"></slot>
      </div>
    </div>
    <div class="p-16" :style="{ paddingTop: headerShow ? '' : '1px' }">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface CardWrapperProps {
  title: string;
  subTitle?: string;
  toolsTips?: string;
  headerShow?: boolean;
}
withDefaults(defineProps<CardWrapperProps>(), {
  title: '',
  subTitle: undefined,
  toolsTips: undefined,
  headerShow: true,
});
</script>
<style scoped lang="less">
.card-wrapper {
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(186, 200, 212, 0.5);

  .header {
    &-title {
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 16px;
        background: var(--color-primary);
        border-radius: 2px;
      }

      &-primary {
        color: #101b25;
      }
    }
  }
  .tips {
    color: #708293;
  }
}
</style>
