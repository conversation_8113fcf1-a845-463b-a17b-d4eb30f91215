<template>
  <div>
    <div class="header">
      <el-icon :size="16" class="db-arrow" @click="prevYear">
        <i-ep-DArrowLeft />
      </el-icon>
      <el-icon :size="16" @click="prevMonth">
        <i-ep-ArrowLeft />
      </el-icon>
      <span class="date-title">{{ headLabelDataStr }}</span>
      <el-icon :size="16" @click="nextMonth">
        <i-ep-ArrowRight />
      </el-icon>
      <el-icon :size="16" class="db-arrow" @click="nextYear">
        <i-ep-DArrowRight />
      </el-icon>
    </div>
    <CalenderWeekSwiper
      ref="CalenderWeekSwiperRef"
      v-model:init-date="initDate"
      :count-date="calenderTagMap"
      @get-sunday="getCurrentWeekSunday"
      @change-current-week-data="changeWeekData"
      @choose-date="chooseDate"
    />
  </div>
</template>

<script setup lang="ts">
import CalenderWeekSwiper from '../CalenderWeekSwiper/index.vue';
import { useReminder } from '@/store/module/useReminder';

const reminderStore = useReminder();
interface Props {
  date: Date | null;
}
const props = defineProps<Props>();

const emit = defineEmits([
  'update:date',
  'getSunday',
  'changeCheckedDate',
  'getMonthDate',
]);

const calenderTagMap = computed(() => reminderStore.calenderTagMap);

const initDate = ref<Date | null>(null);

const CalenderWeekSwiperRef = ref(null);

const getCurrentWeekSunday = value => {
  emit('getSunday', value);
};

const changeWeekData = value => {
  emit('getMonthDate', new Date(value[0].sendDate));
};

const chooseDate = value => {
  emit('changeCheckedDate', value);
};

const headLabelDataStr = computed(
  () => CalenderWeekSwiperRef.value?.headLabelDataStr
);

const prevYear = () => {
  CalenderWeekSwiperRef.value?.prevYear();
};
const prevMonth = () => {
  CalenderWeekSwiperRef.value?.prevMonth();
};
const nextMonth = () => {
  CalenderWeekSwiperRef.value?.nextMonth();
};
const nextYear = () => {
  CalenderWeekSwiperRef.value?.nextYear();
};

onMounted(() => {
  initDate.value = props.date;
});
watch(
  () => initDate.value,
  () => {
    emit('update:date', initDate.value);
  }
);
watch(
  () => props.date,
  () => {
    initDate.value = props.date;
  }
);
</script>

<style scoped lang="less">
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: none;
  padding-top: 14px;
  padding-bottom: 0;
  .date-title {
    font-weight: bold;
    color: #3a4762;
    padding: 0 42px;
  }
  .el-icon {
    cursor: pointer;
    width: 2rem;
  }
}
.body {
  padding: 0 2px;
  .date-day {
    display: flex;
    justify-content: space-between;
  }
}
.swiper-slide {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  //box-sizing: border-box;
  .week-block {
    flex: 1;
    text-align: center;
    padding: 12px 0;
    color: #939cae;
    font-size: 14px;
    .week-title {
      padding-bottom: 16px;
    }
    .date-block {
      flex: 1;
      padding: 8px;
      background-color: #f7f8fa;
      box-sizing: border-box;
      .date-number {
        display: flex;
        justify-content: flex-end;
        .normal {
          width: 26px;
          height: 26px;
          border-radius: 50%;
          text-align: center;
          font-size: 16px;
          color: #3a4762;
        }
        .is-selected {
          background-color: #0a73e4;
          color: #fff;
        }
        .cla-box {
          min-width: 24px;
          min-height: 24px;
          background-color: #e6eeff;
          text-align: center;
          margin-top: 4px;
          color: #2e6be6;
          font-size: 16px;
        }
        .no-value {
          background-color: #f7f8fa;
        }
      }
    }
    .date-block:not(:first-child) {
      border-left: 2px solid #fff;
      border-right: 2px solid #fff;
    }
    .date-block:not(:last-child) {
      border-left: 2px solid #fff;
      border-right: 2px solid #fff;
    }
    .date-block:last-child {
      border-left: 2px solid #fff;
    }
    .date-block:first-child {
      border-right: 2px solid #fff;
    }
  }
}
</style>
