<template>
  <div ref="listRef" class="list-container">
    <div
      v-for="item in reminderList"
      :key="item"
      v-infinite-scroll="load"
      :data-pid="item.patientId"
      class="patient-card"
      @click="handleMemoryPatient(item)"
    >
      <div
        class="flex items-center justify-between text-base text-[#3A4762] font-bold"
      >
        <div class="flex items-center max-w-196">
          <Text>
            {{ item.patientName }}
          </Text>
          <span class="text-[#E1E5ED] text-xs px-2 text-center -mt-2">|</span>
          <Text>
            {{ item.groupName }}
          </Text>
        </div>
        <div
          :class="[
            'flex items-center font-base text-xs text-white h-17 leading-[17px] rounded-[4px] px-8 pl-6',
            item.currentState === 1
              ? 'bg-gradient-to-r  from-[#EDC986] to-[#EE9E29]'
              : 'bg-[#3BB4CC]',
          ]"
        >
          <div
            v-if="[1].includes(item.currentState) && item.excessTime"
            class="tag"
          >
            <img width="26" src="@/assets/imgs/overview/vip-icon.svg" alt="" />
          </div>
          <div v-if="item.currentState === 2" class="tag">科研干预</div>
          <div v-if="item.currentState === 3" class="tag">科研对照</div>
          <span
            v-if="
              (item.currentState === 1 && item.excessTime) ||
              [2, 3].includes(item.currentState)
            "
            class="mr-6 text-white text-xs scale-75 -mt-3"
          >
            |
          </span>
          <span>已服务：{{ item.manageTime || '--' }}天</span>
          ，
          <span>剩余时长：{{ item.excessTime || '--' }}天</span>
          <div class="max-w-58">
            <Text>
              {{ item.scientificName ? `(${item.scientificName})` : '' }}
            </Text>
          </div>
        </div>
      </div>
      <div class="content-box *:mb-8">
        <!-- <ReminderCard
          v-for="toDoItem in item.children"
          :key="toDoItem.backlogId"
          ref="reminderCardRef"
          :to-do-obj="toDoItem"
          @get-operation-reminder="handleReminderCard"
          @handle-todo="handleTodo"
          @delete-todo="deleteTodo"
        /> -->
        <HandleRecord
          v-for="(it, index) in item.children"
          :key="index"
          :item="it"
          :need-show-time="false"
          @click="handleClick(it)"
        >
          <template #operation>
            <template
              v-if="it.overdueTime >= new Date().getTime() && it.status !== 2"
            >
              <div
                v-if="it.type === 10"
                class="text-[#E63746] cursor-pointer mr-16"
                @click="deleteTodo(it)"
              >
                删除
              </div>
              <div
                v-if="it.status === 1 || it.status === 3"
                class="text-[#E37221] cursor-pointer"
                @click="delayHandle(it)"
              >
                稍后提醒
              </div>
              <div
                v-if="
                  it.type !== 12 &&
                  it.type !== 26 &&
                  (it.status === 1 || it.status === 3)
                "
                class="text-[#2e6be6] handle cursor-pointer ml-16"
                @click="handleTodo(it)"
              >
                <template v-if="it.category === 1">整改回复</template>
                <template v-else>处理</template>
              </div>
            </template>
            <template v-else>
              <div class="text-[#7a8599] cursor-pointer">
                {{ it.status === 2 ? '已处理' : '已超期' }}
              </div>
            </template>
          </template>
        </HandleRecord>
      </div>
    </div>
    <!-- 相关待办弹窗组件 -->
    <Dialog
      v-if="dialogVisible"
      :width="width"
      :dialog-visible="dialogVisible"
      @close-dialog="closeDialog"
    />
    <!-- 处理自定义待办 -->
    <SecondaryConfirmation
      :custom-type="customType"
      :custom-title="customTitle"
      @close="closeCustom"
    />
  </div>
</template>

<script lang="ts" setup>
import Dialog from '@/pages/Workbench/Right/components/PatientTodo/components/Dialog.vue';
import SecondaryConfirmation from '@/pages/Workbench/Right/components/PatientTodo/components/SecondaryConfirmation.vue';
import HandleRecord from '@/pages/Workbench/Right/components/PatientTodo/components/RecordCard.vue';
import Text from '@/components/Text/index.vue';
import bus from '@/lib/bus';
import useTodo from '@/store/module/useTodo';
import { useReminder } from '@/store/module/useReminder';
import useGlobal from '@/store/module/useGlobal';
import store from '@/store';
import dayjs from 'dayjs';
import { getFollowSymptomDetail } from '@/api/followup';
import useDoubt from '@/store/module/useDoubt';
import {
  getRiskType,
  riskTabTitleMap,
} from '@/pages/Workbench/Right/components/PatientTodo';
import {
  IComponentType,
  IRecordSourceType,
} from '@/store/module/useComponentsTabAction';
import useTabs from '@/store/module/useTabs';

let useTodoInfo = useTodo();
const globalStore = useGlobal();

const handleClick = item => {
  globalStore.setUserId(item.patientId);
};
const tabStore = useTabs();
const tabAction = store.useComponentsTabAction();
const emit = defineEmits(['delayReminder', 'loadMore', 'close']);

const reminderStore = useReminder();
const doubtStore = useDoubt();

const reminderList = computed(() =>
  reminderStore.showAllUndoRecord
    ? reminderStore.reminderTodoList
    : reminderStore.reminderList
);

const reminderCardRef = ref<any[]>([]);

const closeAllPopover = () => {
  if (reminderCardRef.value && Array.isArray(reminderCardRef.value)) {
    reminderCardRef.value.forEach(cardRef => {
      if (cardRef && typeof cardRef.closePopover === 'function') {
        cardRef.closePopover();
      }
    });
  }
};
const listRef = ref<HTMLElement | null>(null);

const handleMemoryPatient = item => {
  if (!listRef.value) return;
  console.log(listRef.value.parentNode);
  sessionStorage.setItem(
    'list-memory',
    JSON.stringify({
      showAllUndoRecord: reminderStore.showAllUndoRecord,
      pid: item.patientId,
      maxPage: item.paginationInfo.pageNum,
      backupPosition:
        (listRef.value.parentElement as HTMLElement).scrollTop ?? 0,
    })
  );
};

let currentToDoItem = ref({});

const delayHandle = (item: any) => {
  currentToDoItem.value = item;

  dialogVisible.value = true;
  useTodoInfo.setStatus(2);
  width.value = '21%';
  useTodoInfo.setTodoInfo(item);
};
const handleTodo = (item: any) => {
  setTimeout(() => {
    currentToDoItem.value = item;
    if (item.category === 1) {
      useTodoInfo.setStatus(233);
      init(item);
      return;
    }

    if ([1, 18].includes(item.type)) {
      let obj = {
        sourceId: item.sourceId,
        checkTime: item.content.slice(0, 10),
        actionType: !item.sourceId ? 'add' : 'view',
        sourceType: 3,
        name: '入组',
      };
      toTab(obj);
    } else if (item.type === 2) {
      useTodoInfo.setStatus(3);
      init(item);
    } else if (item.type === 3) {
      useTodoInfo.setStatus(4);
      init(item);
    } else if (item.type === 4) {
      const riskType = getRiskType(item.content);
      tabAction.setAction({
        componentType: 4,
        name: riskType ? riskTabTitleMap[riskType] : '风险评估',
        mode: 'new',
        mainTabCode: 2,
        data: {
          sourceId: item.sourceId,
          sourceType: 2,
          riskType: riskType,
        },
      });
      bus.emit('open-component-tab');
    } else if ([5, 15].includes(item.type)) {
      getFollowSymptomDetail({ followUpId: item.sourceId }).then(res => {
        let { date } = res;
        const followUpData = {
          followUpDate: date,
          followUpId: item.sourceId,
          sourceId: item.sourceId,
          sourceType: 1,
          key: item.sourceId,
        };
        tabAction.setAction({
          componentType: 6,
          name: dayjs(date).format('YYYY-MM-DD') + ' 症状随访',
          data: followUpData,
        });
        bus.emit('open-component-tab');
      });
    } else if (item.type === 9) {
      useTodoInfo.setStatus(9);
      init(item);
    } else if (item.type === 6) {
      useTodoInfo.setStatus(6);
      init(item);
    } else if (item.type === 7) {
      useTodoInfo.setStatus(7);
      init(item);
    } else if (item.type === 8) {
      useTodoInfo.setStatus(8);
      init(item);
    } else if (item.type === 10) {
      customType.value = true;
      customTitle.value = '完成';
      useTodoInfo.setStatus(10);
      init(item);
    } else if (item.type === 11) {
      useTodoInfo.setStatus(11);
      init(item);
    } else if (item.type === 12) {
      emit('delayReminder', { type: 12 });
    } else if (item.type === 13) {
      // 复查结论填写
      tabAction.setAction({
        componentType: 2,
        name: dayjs(item.time).format('YYYY-MM-DD') + ' 复查',
        mode: 'new',
        data: {
          id: item.sourceId,
          recordActionType: {
            actionType: 'view',
            sourceType: 2,
          },
        },
      });
      bus.emit('open-component-tab');
    } else if (item.type === 14) {
      // 临床事件跟踪 打开新建事件弹窗
      tabStore.clearCache();
      const group = 'patient_info';
      const curTab = tabStore.getCurrentPatientTabs(group);
      if (curTab) {
        const curId = curTab?.[1]?.id ?? '';
        if (curId) {
          tabStore.mainActiveTab = 2;
          tabStore.patientActiveTabMap[group] = curTab?.[1]?.id ?? '';
        }
      }
      setTimeout(() => {
        bus.emit('open-clinical-event');
      });
    } else if (item.type === 16) {
      // 量表随访提醒
      useTodoInfo.setStatus(999);
      init(item);
    } else if (item.type === 17) {
      doubtStore.backlogId = item.backlogId;
      doubtStore.doubtCheck(item.sourceId);
      // 访视质疑
      emit('close');
    } else if (item.type === 19) {
      // 科研入组宣教
      useTodoInfo.setStatus(999);
      init(item);
    } else {
      useTodoInfo.setStatus(item.type);
      init(item);
    }
  });
};

const deleteTodo = (item: any) => {
  currentToDoItem.value = item;
  useTodoInfo.setTodoInfo(item);
  customType.value = true;
  customTitle.value = '删除';
};
let init = (item: any) => {
  useTodoInfo.setTodoInfo(item);
  if (item.type !== 10) dialogVisible.value = true;
  if (item.type === 5) {
    width.value = '26%';
  } else {
    width.value = '21%';
    width.value = '600px';
  }
  if (item.catogry === 1) {
    width.value = '600px';
  }
};

const toTab = (item: any) => {
  const { sourceId, sourceType, checkTime, actionType, name } = item;
  const curSourceType = [0, 1, 2, 3].includes(sourceType!) ? sourceType! : -1;
  tabAction.setAction({
    componentType: curSourceType as IComponentType,
    name: `${checkTime || ''} ${name || ''}`,
    data: {
      id: sourceId,
      recordActionType: {
        actionType,
        sourceType: curSourceType as IRecordSourceType,
      },
    },
  });
  bus.emit('open-component-tab');
};

const dialogVisible = ref<boolean>(false);
// 弹窗的宽度
let width = ref<string>('32%');

const closeDialog = () => {
  dialogVisible.value = false;
  emit('delayReminder', currentToDoItem.value);
};

let customType = ref<boolean>(false);

let customTitle = ref<string>('');

let closeCustom = value => {
  customType.value = false;
  if (value === 1) {
    emit('delayReminder', currentToDoItem.value);
  }
};

const load = () => {
  emit('loadMore');
};

defineExpose({
  closeAllPopover,
});
</script>

<style scoped lang="less">
.list-container {
  box-sizing: border-box;
  background-color: white;
  padding: 16px;
  .content-box {
    padding: 12px 0;
  }
  .tag {
    min-width: 56px;
    height: 18px;
    font-style: italic;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-right: 6px;
  }
}
</style>
