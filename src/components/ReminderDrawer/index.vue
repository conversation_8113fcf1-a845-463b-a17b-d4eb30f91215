<template>
  <div>
    <el-drawer
      :model-value="reminderVisible"
      direction="rtl"
      style="min-width: 560px"
      @close="close"
    >
      <template #header>
        <div class="flex items-center">
          提醒事项
          <span class="count">
            ({{ showAllUndoRecord ? reminderNumber : totalsRemindersNum || 0 }})
          </span>
        </div>
      </template>
      <template #default>
        <div
          v-if="showAllUndoRecord"
          class="flex items-center justify-between p-16 text-[#3A4762]"
        >
          <div class="flex items-center">
            截止当前时间，尚有
            <span class="text-[#E63746]">
              {{ reminderNumber || 0 }}
            </span>
            个事务急需处理。
            <div
              class="flex items-center text-left text-[#2E6BE6] ml-12 cursor-pointer"
              @click="refreshTable"
            >
              <el-icon size="16">
                <i-ep-refresh />
              </el-icon>
              刷新
            </div>
          </div>
          <div
            class="flex items-center cursor-pointer text-[#2E6BE6]"
            @click="handleAll"
          >
            查看全部
            <el-icon size="12" class="mt-2"><i-ep-arrow-right /></el-icon>
          </div>
        </div>
        <div v-if="showAllUndoRecord" class="flex items-center px-16">
          <el-checkbox-group
            v-model="reminderStore.filterOptions"
            @change="refreshTable"
          >
            <el-checkbox
              :label="'质控整改(' + qualityRecordStore.typedTodoNum[0] + ')'"
              :value="1"
            />
            <el-checkbox
              :label="'待办事项(' + qualityRecordStore.typedTodoNum[1] + ')'"
              :value="2"
            />
            <el-checkbox
              :label="'科研质疑(' + qualityRecordStore.typedTodoNum[2] + ')'"
              :value="3"
            />
          </el-checkbox-group>
        </div>
        <SelectGroup
          v-show="!showAllUndoRecord"
          v-loading="isLoading"
          @params-change="paramsChange"
        />
        <div ref="scorllRef" class="scroll-box" @scroll="handleScroll">
          <div v-if="!showAllUndoRecord" ref="calenderBoxRef" class="px-16">
            <div v-loading="isLoading" class="calender-box">
              <Calendar
                v-if="expandCalender"
                v-model:date="checkedDate"
                v-model:init-date="initCalenderMonthDate"
                @change-checked-date="checkedDateChange"
                @get-month-date="getTargetMonthDate"
              />
              <CalenderWeek
                v-if="!expandCalender"
                v-model:date="checkedDate"
                @get-sunday="getWeekSunday"
                @change-checked-date="checkedDateChange"
                @get-month-date="getTargetMonthDate"
              />
              <div class="expand-btn" @click="expandCalender = !expandCalender">
                {{ !expandCalender ? '展开月历' : '收起月历' }}
                <el-icon
                  :size="16"
                  :class="[!expandCalender ? 'expand' : 'shrink']"
                >
                  <i-ep-DArrowLeft />
                </el-icon>
              </div>
            </div>
          </div>
          <div v-if="!showAllUndoRecord" class="calenderWeekSimple-wrapper">
            <CalenderWeekSimple
              v-if="showCalenderWeekSimple"
              v-model:date="checkedDate"
              @get-sunday="getWeekSunday"
              @change-checked-date="checkedDateChange"
              @get-month-date="getTargetMonthDate"
            />
          </div>
          <ReminderList
            ref="reminderListRef"
            @delay-reminder="delayReminder"
            @load-more="loadReminderList"
            @close="close"
          />
          <el-divider v-if="isLoading">
            <el-icon :size="18" class="loading-icon">
              <i-ep-loading />
            </el-icon>
          </el-divider>
          <el-divider v-if="pageNum === totalNumber && !isLoading">
            <span class="bottom-divider">没有更多了</span>
          </el-divider>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { getOnlyMineStatus } from '@/api/reminder';
import { getUserRoles } from '@/pages/Workbench/Right/components/PatientTodo';
import { useUserStore } from '@/store';
import useGlobal from '@/store/module/useGlobal';
import { useQualityRecord } from '@/store/module/useQualityRecord';
import { useReminder } from '@/store/module/useReminder';
import dayjs from 'dayjs';
import { debounce } from 'lodash-es';
import Calendar from './Calender/index.vue';
import CalenderWeek from './CalenderWeek/index.vue';
import CalenderWeekSimple from './CalenderWeekSimple/index.vue';
import ReminderList from './ReminderList/index.vue';
import SelectGroup from './SelectGroup/index.vue';

const reminderStore = useReminder();

const props = defineProps({
  reminderVisible: {
    default: false,
    type: Boolean,
  },
});

const totalsRemindersNum = computed(() => reminderStore.totals);

const emit = defineEmits(['update:reminderVisible']);

const reminderListRef = ref(null);

let useGlobalInfo = useGlobal();

const qualityRecordStore = useQualityRecord();
const userStore = useUserStore();

const reminderNumber = computed(() => qualityRecordStore.reminderNumber);

const showAllUndoRecord = computed(() => reminderStore.showAllUndoRecord);

// 为两种模式分别管理分页状态
const normalModePagination = ref({
  pageNum: 1,
  totalNumber: 1,
  isLoading: false,
});

const allUndoModePagination = ref({
  pageNum: 1,
  totalNumber: 1,
  isLoading: false,
});

// 当前模式的分页状态
const currentPagination = computed(() =>
  showAllUndoRecord.value
    ? allUndoModePagination.value
    : normalModePagination.value
);

// 当前模式的加载状态
const isLoading = computed(() => currentPagination.value.isLoading);

// 当前模式的页码
const pageNum = computed({
  get: () => currentPagination.value.pageNum,
  set: value => {
    currentPagination.value.pageNum = value;
  },
});

// 当前模式的总页数
const totalNumber = computed({
  get: () => currentPagination.value.totalNumber,
  set: value => {
    currentPagination.value.totalNumber = value;
  },
});

const handleAll = () => {
  reminderStore.showAllUndoRecord = false;
  if (!reminderStore.reminderList || reminderStore.reminderList.length === 0) {
    // 重置普通模式的分页状态
    normalModePagination.value.pageNum = 1;
    normalModePagination.value.totalNumber = 1;
    getReminderListData(1);
  } else {
    toMemoryPostion();
  }
};

//日历切换
const checkedDate = ref(new Date());

const calenderBoxRef = ref();

const initCalenderMonthDate = ref(new Date());

let expandCalender = ref(false);

const showCalenderWeekSimple = ref(false);

const handleScroll = (event: Event) => {
  if (!calenderBoxRef.value) return;
  const target = event.target as HTMLElement;
  reminderListRef.value?.closeAllPopover();
  const { scrollTop } = target;
  showCalenderWeekSimple.value =
    calenderBoxRef.value.clientHeight - scrollTop < 20;
  if (scrollTop === 0) {
    showCalenderWeekSimple.value = false;
  }
};

const getWeekSunday = value => {
  initCalenderMonthDate.value = value;
};

const loadReminderList = debounce(() => {
  console.log('loadMore', pageNum.value, totalNumber.value);
  if (pageNum.value < totalNumber.value) {
    pageNum.value++;
    getReminderListData(pageNum.value);
  }
}, 500);

const selectParams = ref({
  options: [
    { pid: null, name: '全部', type: null, category: 1, status: 1 },
    { pid: null, name: '全部', type: null, category: 2, status: 1 },
    { pid: null, name: '全部', type: null, category: 3, status: 1 },
  ],
  onlyMine: parseInt(localStorage.getItem('onlyMineStatus') || '0'),
  status: null,
});
const pageSize = 50;
const getReminderListData = (pageNum: number, param?: any) => {
  let params: any = {
    ...selectParams.value,
    startTime: dayjs(checkedDate.value ? checkedDate.value : new Date())
      .startOf('day')
      .valueOf(),
    endTime: dayjs(checkedDate.value ? checkedDate.value : new Date())
      .endOf('day')
      .valueOf(),
    patientId: useGlobalInfo.userId,
    userType: String(getUserRoles()),
    pageNum,
    pageSize,
  };
  params = { ...params, ...param };
  currentPagination.value.isLoading = true;

  // 3.5需求，对参数进行处理，筛选时间为截止今日的未处理待办
  if (showAllUndoRecord.value) {
    params = {
      ...params,
      options: [
        {
          pid: null,
          name: '全部',
          type: null,
          category: 1,
          status: null as any,
        },
        { pid: null, name: '全部', type: null, category: 2, status: null },
        { pid: null, name: '全部', type: null, category: 3, status: null },
      ],
      startTime: dayjs(new Date(qualityRecordStore.lastTodoTime))
        .startOf('day')
        .valueOf(),
      endTime: dayjs(new Date()).endOf('day').valueOf(),
      untreated: 1,
      all: false,
      onlyMine: 1,
    };
  }
  params.options = params.options.filter(it =>
    reminderStore.filterOptions.includes(it.category)
  );
  if (pageNum === 1) {
    const param = {
      userId: userStore.accountId,
      userType: getUserRoles(),
    };
    qualityRecordStore.getReminderNum(param);
  }
  return new Promise((resolve, reject) => {
    reminderStore
      .getReminderListFnc(params)
      .then((res: any) => {
        totalNumber.value = res.totalNumber;
        resolve(res);
      })
      .finally(() => {
        currentPagination.value.isLoading = false;
      })
      .catch(reject);
  });
};

// 列表刷新
const refreshTable = () => {
  // 重置当前模式的分页状态
  currentPagination.value.pageNum = 1;
  getReminderListData(1);
  const param = {
    userId: userStore.accountId,
    userType: getUserRoles(),
  };
  qualityRecordStore.getReminderNum(param);
  getMonthDate();
};

const checkedDateChange = (newDate?: Date) => {
  // 如果传入了新日期，更新 checkedDate
  if (newDate) {
    checkedDate.value = newDate;
  }
  // 只在普通模式下重置分页
  if (!showAllUndoRecord.value) {
    normalModePagination.value.pageNum = 1;
    getReminderListData(1);
  }
};

const targetMonthDate = ref('');

const getTargetMonthDate = value => {
  targetMonthDate.value = value;
  getMonthDate();
};

const getMonthDate = debounce(() => {
  const dateRange = getMonthDateRange(
    targetMonthDate.value ? targetMonthDate.value : checkedDate.value
  );
  let params = {
    ...selectParams.value,
    patientId: useGlobalInfo.userId,
    startTime: dateRange.firstDayOfPreviousMonth,
    endTime: dateRange.lastDayOfNextMonth,
  };
  params.options = params.options.filter(it =>
    reminderStore.filterOptions.includes(it.category)
  );
  reminderStore.getReminderCountFnc(params);
}, 200);

const getMonthDateRange = date => {
  // 获取前一个月的第一天
  const firstDayOfPreviousMonth = dayjs(date)
    .subtract(1, 'month')
    .startOf('month')
    .startOf('day')
    .valueOf();

  // 获取后一个月的最后一天
  const lastDayOfNextMonth = dayjs(date)
    .add(1, 'month')
    .endOf('month')
    .endOf('day')
    .valueOf();
  return { firstDayOfPreviousMonth, lastDayOfNextMonth };
};

const paramsChange = value => {
  const { options, onlyMine, status } = value;
  selectParams.value.options = options;
  selectParams.value.onlyMine = onlyMine;
  selectParams.value.status = status === 0 ? null : status;

  // 重置当前模式的分页状态
  currentPagination.value.pageNum = 1;
  getReminderListData(1);
  getMonthDate();
};

const getOnlyMineStatusReq = () => {
  let userAccount = localStorage.getItem('userAccount');
  if (userAccount) {
    getOnlyMineStatus({ account: userAccount, type: 2 }).then((res: any) => {
      if (res.code === 'E000000') {
        localStorage.setItem('onlyMineStatus', String(res.data));
        selectParams.value.onlyMine = Number(res.data);
      }
    });
  }
};

const scorllRef = ref<HTMLElement | null>(null);

//列表操作
const delayReminder = value => {
  getMonthDate();
  toMemoryPostion();

  if (value.type && value.type === 12) close();
};

watch(
  () => props.reminderVisible,
  async value => {
    if (value) {
      reminderStore.showAllUndoRecord = !!reminderNumber.value;
    }
    if (
      value &&
      (!reminderStore.reminderTodoList ||
        reminderStore.reminderTodoList.length === 0)
    ) {
      await getOnlyMineStatusReq();
      currentPagination.value.pageNum = 1;
      getReminderListData(1);
      getMonthDate();
      sessionStorage.removeItem('list-memory');
    } else if (value) {
      getMonthDate();
      toMemoryPostion();
    }
  }
);

interface MemoryData {
  maxPage: number;
  pid: number;
  showAllUndoRecord: boolean;
  backupPosition: number;
}
const toMemoryPostion = async () => {
  let memoryData: MemoryData | null = null;
  try {
    memoryData = JSON.parse(sessionStorage.getItem('list-memory') || '');
  } catch {}
  if (memoryData && memoryData.showAllUndoRecord === showAllUndoRecord.value) {
    pageNum.value = 1;
    // 根据数据量切换两种请求方式
    const overTotalsDataNum = pageSize * memoryData.maxPage > 500;
    if (overTotalsDataNum) {
      // 这个地方用while和promiseAll差别不大，接口时间会随着并发数量增加
      while (pageNum.value <= memoryData.maxPage) {
        await getReminderListData(pageNum.value);
        pageNum.value += 1;
      }
      if (pageNum.value > 1) pageNum.value -= 1;
    } else {
      const { totals } = (await getReminderListData(1, {
        pageSize: pageSize * memoryData.maxPage,
      })) as any;
      pageNum.value = memoryData.maxPage;
      totalNumber.value = Math.ceil(totals / pageSize);
    }

    if (!scorllRef.value) return;
    const pidDoms = Array.from(
      scorllRef.value.querySelectorAll('.patient-card')
    );
    const targetDom = pidDoms.find(
      it => Number(it.getAttribute('data-pid')) === memoryData.pid
    );
    if (targetDom)
      targetDom.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    else scorllRef.value.scrollTop = memoryData.backupPosition;
  } else if (showAllUndoRecord.value) {
    refreshTable();
  }
};

// 关闭抽屉
const close = () => {
  emit('update:reminderVisible', false);
};
</script>
<style scoped lang="less">
:deep(.el-drawer) {
  height: calc(100% - 60px);
  top: 60px;

  .el-drawer__header {
    margin-bottom: 0;
    padding: 12px 16px;
    border-bottom: 1px solid #e9e8eb;
    font-size: 16px;
    font-weight: 600;
    color: #101b25;
  }
  .el-drawer__body {
    padding: 0;
    display: flex;
    flex-direction: column;
    .scroll-box {
      flex: 1;
      background: #fff;
      overflow-y: scroll;
      box-sizing: border-box;
    }

    /*修改滚动条样式*/
    .scroll-box::-webkit-scrollbar {
      width: 0;
      height: 0;
      /**/
    }

    .scroll-box::-webkit-scrollbar-track {
      background: rgb(239, 239, 239);
      border-radius: 2px;
    }

    .scroll-box::-webkit-scrollbar-thumb {
      background: #bfbfbf;
      border-radius: 0;
    }
  }
}
.calender-box {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  padding: 4px 18px;
}
.reminder-card {
  height: 200px;
  background-color: #9ca3af;
}
.scroll-box {
  position: relative;
}
.calenderWeekSimple-wrapper {
  position: sticky;
  top: 0;
  z-index: 1;
}
.expand-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #2e6be6;
  cursor: pointer;
  font-size: 14px;
  padding: 16px 0;
  .el-icon {
    margin-left: 4px;
    transition: transform 0.3s ease;
  }
  .expand {
    transform: rotate(-90deg);
  }
  .shrink {
    transform: rotate(90deg);
  }
}
.bottom-divider {
  color: #7a8599;
}
.loading-icon {
  color: red;
}
.count {
  color: #2e6be6;
  margin-left: 8px;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  } /* 初始状态，角度为0度 */
  100% {
    transform: rotate(360deg);
  } /* 结束状态，角度为360度（完全旋转一周）*/
}
</style>
