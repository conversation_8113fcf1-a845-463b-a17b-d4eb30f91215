<template>
  <div class="header-middle ml-16">
    <div class="check-container">
      <el-checkbox
        v-model="selectData.onlyLookMain"
        label="仅看我的"
        :true-value="1"
        :false-value="0"
        class="look-me"
        @change="changeOnlyMine"
      />
      <div class="refresh-btn" @click="refresh">
        <el-icon :size="14">
          <i-ep-Refresh />
        </el-icon>
        刷新
      </div>
    </div>
    <div class="select-container *:mb-12 pb-12">
      <div class="flex items-center">
        <el-checkbox
          v-model="filterSearchOptions[0]"
          label="质控整改"
          @change="filterSearchChange"
        />
        <el-cascader
          v-model="selectData.caseValue[0]"
          style="width: 240px; margin-left: 16px"
          :options="remindOptions(1)"
          filterable
        />
        <el-select
          v-model="selectData.selectValue[0]"
          placeholder="请选择"
          style="width: 116px; margin-left: 8px"
          :empty-values="['null']"
        >
          <el-option
            v-for="item in selectOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="reset-btn" @click="resetParams(0)">
          <img src="@/assets/imgs/reminder/reset.png" alt="" />
          重置
        </div>
      </div>
      <div class="flex items-center">
        <el-checkbox
          v-model="filterSearchOptions[2]"
          label="待办事项"
          @change="filterSearchChange"
        />
        <el-cascader
          v-model="selectData.caseValue[2]"
          style="width: 240px; margin-left: 16px"
          :options="remindOptions(2)"
          filterable
        />
        <el-select
          v-model="selectData.selectValue[2]"
          placeholder="请选择"
          style="width: 116px; margin-left: 8px"
          :empty-values="['null']"
        >
          <el-option
            v-for="item in selectOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="reset-btn" @click="resetParams(2)">
          <img src="@/assets/imgs/reminder/reset.png" alt="" />
          重置
        </div>
      </div>
      <div class="flex items-center">
        <el-checkbox
          v-model="filterSearchOptions[1]"
          label="科研质疑"
          @change="filterSearchChange"
        />
        <el-cascader
          v-model="selectData.caseValue[1]"
          style="width: 240px; margin-left: 16px"
          :options="remindOptions(3)"
          filterable
        />
        <el-select
          v-model="selectData.selectValue[1]"
          placeholder="请选择"
          style="width: 116px; margin-left: 8px"
          :empty-values="['null']"
        >
          <el-option
            v-for="item in selectOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="reset-btn" @click="resetParams(1)">
          <img src="@/assets/imgs/reminder/reset.png" alt="" />
          重置
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { debounce } from 'lodash-es';
import { getOnlyMineStatus, postOnlyMineStatus } from '@/api/reminder';
import { getUserRoles } from '@/pages/Workbench/Right/components/PatientTodo/index';
import { useUserStore } from '@/store/module/useUserStore';
import { useQualityRecord } from '@/store/module/useQualityRecord';
import { useReminder } from '@/store/module/useReminder';
let userAccount = localStorage.getItem('userAccount') || '';
const reminderStore = useReminder();
const userStore = useUserStore();
const qualityRecordStore = useQualityRecord();
const filterSearchOptions = ref<boolean[]>([true, true, true]); //0 2 1

watch(
  () => reminderStore.showAllUndoRecord,
  val => {
    if (!val) {
      const options = reminderStore.filterOptions;
      filterSearchOptions.value = [false, false, false];
      if (options.includes(1)) {
        filterSearchOptions.value[0] = true;
      }
      if (options.includes(2)) {
        filterSearchOptions.value[2] = true;
      }
      if (options.includes(3)) {
        filterSearchOptions.value[1] = true;
      }
    }
  }
);

const filterSearchChange = () => {
  refresh();
};
let options = ref<any>([]);
const selectOptions = [
  {
    value: null,
    label: '全部',
  },
  {
    value: 1,
    label: '未处理',
  },
  {
    value: 3,
    label: '已逾期',
  },
  {
    value: 2,
    label: '已处理',
  },
  {
    value: 4,
    label: '已超期',
  },
];

const emit = defineEmits(['paramsChange']);

//重置
const resetParams = index => {
  const templateOption = [
    [{ pid: null, name: '全部', type: null, category: 1 }],
    [{ pid: null, name: '全部', type: null, category: 3 }],
    [{ pid: null, name: '全部', type: null, category: 2 }],
  ];
  const templateStatus = [1, 1, 1];
  selectData.caseValue[index] = templateOption[index];
  selectData.selectValue[index] = templateStatus[index];
};
const getReminderListIOptionsReq = () => {
  if (!userStore.userRoles) return;
  qualityRecordStore
    .getReminderListIOptions({
      code: 2,
      all: false,
      userType: userStore.userRoles[0],
    })
    .then(data => {
      const dataArray = Array.isArray(data) ? data : [data];
      options.value = [
        {
          label: '全部',
          category: 1,
          value: {
            pid: null,
            name: '全部',
            type: null,
            category: 1,
          },
          children: [],
        },
        {
          label: '全部',
          category: 3,
          value: {
            pid: null,
            name: '全部',
            type: null,
            category: 3,
          },
          children: [],
        },
        {
          label: '全部',
          category: 2,
          value: {
            pid: null,
            name: '全部',
            type: null,
            category: 2,
          },
          children: [],
        },
        ...arrayToTreeOptions(dataArray),
      ];
    });
};

const remindOptions = computed(() => {
  return type => options.value.filter(item => item.category === type) || [];
});

const getOnlyMineStatusReq = debounce(() => {
  if (!userAccount) return;
  getOnlyMineStatus({ account: userAccount, type: 2 }).then(res => {
    if (res.code === 'E000000') {
      localStorage.setItem('onlyMineStatus', String(res.data));
      selectData.onlyLookMain = Number(res.data);
    }
  });
}, 100);

const changeOnlyMine = () => {
  if (!userAccount) return;
  postOnlyMineStatus({
    account: userAccount,
    userId: userStore.accountId,
    userType: String(getUserRoles()),
    type: 2,
    onlyMine: selectData.onlyLookMain,
  }).then(res => {
    if (res.code === 'E000000') {
      getOnlyMineStatusReq();
    }
  });
};

const arrayToTreeOptions = (items: any[]) => {
  const map: any = {};
  const roots: any[] = [];
  items.forEach(item => {
    map[item.id] = {
      ...item,
      label: item.name,
      value: {
        pid: item.pid,
        name: item.name,
        type: item.type,
        category: item.category,
      },
      children: [],
    };
  });

  items.forEach(item => {
    const pid = item.pid;
    if (pid === null) {
      roots.push(map[item.id]);
    } else {
      if (map[pid]) {
        map[pid].children.push(map[item.id]);
      }
    }
  });

  return roots;
};

const caculaterSelectData = () => {
  const filterCategory: number[] = [];
  if (filterSearchOptions.value[0]) {
    filterCategory.push(1);
  }
  if (filterSearchOptions.value[1]) {
    filterCategory.push(3);
  }
  if (filterSearchOptions.value[2]) {
    filterCategory.push(2);
  }
  let selectDataParams = {
    options: selectData.caseValue
      .map(it => it.flat(Infinity).slice(-1)[0])
      .map((item, index) => ({
        ...item,
        status: selectData.selectValue[index],
      }))
      .filter(it => filterCategory.includes(it.category)),
    onlyMine: selectData.onlyLookMain,
  };
  console.log('selectDataParams:', selectDataParams);
  return selectDataParams;
};
//刷新
const refresh = async () => {
  await getOnlyMineStatusReq();
  let selectDataParams = caculaterSelectData();
  emit('paramsChange', selectDataParams);
};

const selectData = reactive({
  caseValue: [
    [{ pid: null, name: '全部', type: null, category: 1 }],
    [{ pid: null, name: '全部', type: null, category: 3 }],
    [{ pid: null, name: '全部', type: null, category: 2 }],
  ],
  onlyLookMain: parseInt(localStorage.getItem('onlyMineStatus') || '1'),
  selectValue: [1, 1, 1],
});

watch(
  () => selectData,
  async () => {
    await getOnlyMineStatusReq();
    console.log('reminderStore.filterOptions', reminderStore.filterOptions);
    let selectDataParams = caculaterSelectData();

    emit('paramsChange', selectDataParams);
  },
  {
    deep: true,
  }
);
watch(
  filterSearchOptions,
  val => {
    reminderStore.filterOptions = [];
    if (val[0]) {
      reminderStore.filterOptions.push(1);
    }
    if (val[1]) {
      reminderStore.filterOptions.push(3);
    }
    if (val[2]) {
      reminderStore.filterOptions.push(2);
    }
  },
  {
    deep: true,
  }
);
onMounted(() => {
  getReminderListIOptionsReq();
});
</script>

<style scoped lang="less">
.header-middle {
  :deep(.check-container) {
    .look-me {
      .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #3a4762;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner {
        background: #2e6be6;
        border-color: #2e6be6;
      }
    }
  }
  .select-container {
    box-sizing: border-box;
    padding-top: 16px;
    display: flex;
    flex-direction: column;
    .reset-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #2e6be6;
      cursor: pointer;
      margin-left: 16px;
      img {
        margin-right: 4px;
        width: 14px;
        height: 14px;
      }
    }
  }
  .check-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    .refresh-btn {
      margin-right: 16px;
      color: #2e6be6;
      cursor: pointer;
    }
  }
}
</style>
