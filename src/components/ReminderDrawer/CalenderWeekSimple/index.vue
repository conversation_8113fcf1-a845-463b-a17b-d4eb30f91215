<template>
  <div class="body">
    <div class="change-button pre-button">
      <el-icon :size="16" @click="slidePrev">
        <i-ep-ArrowLeft />
      </el-icon>
    </div>
    <div class="change-button next-button">
      <el-icon :size="16" @click="slideNext">
        <i-ep-ArrowRight />
      </el-icon>
    </div>
    <CalenderWeekSwiper
      ref="CalenderWeekSwiperRef"
      v-model:init-date="initDate"
      @get-sunday="getCurrentWeekSunday"
      @change-current-week-data="changeWeekData"
    >
      <template #week-content="{ weekdays }">
        <div
          v-for="(k, j) in weekdays"
          :key="j"
          class="week-block"
          @click="upDateCheckedDate(k.sendDate, weekdays)"
        >
          <div class="date-block">
            <div class="date-number">
              <div
                :class="checkedDate === k.sendDate ? 'is-selected' : ''"
                class="normal"
              >
                {{ k.showDate }}
              </div>
              <div
                v-if="calenderTagMap[dayjs(k.sendDate).format('YYYY-MM-DD')]"
                class="cla-box"
              ></div>
            </div>
          </div>
        </div>
      </template>
    </CalenderWeekSwiper>
  </div>
</template>

<script setup lang="ts">
import CalenderWeekSwiper from '../CalenderWeekSwiper/index.vue';
import dayjs from 'dayjs';

import { useReminder } from '@/store/module/useReminder';

const reminderStore = useReminder();

interface Props {
  date: Date | null;
}

const calenderTagMap = computed(() => reminderStore.calenderTagMap);

const props = defineProps<Props>();

const emit = defineEmits([
  'update:date',
  'getSunday',
  'changeCheckedDate',
  'getMonthDate',
]);

const initDate = ref<Date | null>(null);

const CalenderWeekSwiperRef = ref(null);

const checkedDate = ref<string>('');

const getCurrentWeekSunday = value => {
  emit('getSunday', value);
};

/*更新当前选中时间*/
const upDateCheckedDate = (sendDate: string, weekdays) => {
  checkedDate.value = sendDate;
  emit('getSunday', new Date(weekdays[0].sendDate));
  emit('changeCheckedDate', new Date(checkedDate.value));
};

const slidePrev = () => {
  CalenderWeekSwiperRef.value?.slidePrev();
};
const slideNext = () => {
  CalenderWeekSwiperRef.value?.slideNext();
};

const changeWeekData = value => {
  emit('getMonthDate', new Date(value[0].sendDate));
};

onMounted(() => {
  checkedDate.value = dayjs(props.date ?? new Date()).format('YYYY-M-D');
});
watch(
  () => props.date,
  () => {
    initDate.value = props.date;
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => checkedDate.value,
  () => {
    emit('update:date', new Date(checkedDate.value));
  }
);
</script>

<style scoped lang="less">
.body {
  box-sizing: border-box;
  padding: 0 24px;
  background-color: #f7f8fa;
  position: relative;
}
.change-button {
  width: 30px;
  height: 30px;
  background: #ffffff;
  border: 1px solid #ebedf0;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto 0;
  z-index: 99;
}
.pre-button {
  left: 16px;
}
.next-button {
  right: 16px;
}
.swiper-slide {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  //box-sizing: border-box;
  .week-block {
    flex: 1;
    text-align: center;
    padding: 8px 0;
    color: #939cae;
    font-size: 14px;
    .date-block {
      flex: 1;
      padding: 8px;
      box-sizing: border-box;
      background-color: #f7f8fa;
      .date-number {
        display: flex;
        justify-content: center;
        align-items: center;
        .normal {
          width: 22px;
          height: 22px;
          border-radius: 50%;
          text-align: center;
          font-size: 14px;
          color: #3a4762;
        }
        .is-selected {
          background-color: #0a73e4;
          color: #fff;
        }
        .cla-box {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #0a73e4;
          margin-left: 4px;
        }
      }
    }
  }
}
</style>
