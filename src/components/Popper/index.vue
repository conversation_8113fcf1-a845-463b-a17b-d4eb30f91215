<template>
  <el-popover
    v-model:visible="visible"
    :width="width || 360"
    :placement="placement"
    trigger="click"
  >
    <template #reference>
      <slot name="reference"></slot>
    </template>
    <div class="p-12">
      <div class="title">
        <el-icon style="color: #e6a23c"><WarningFilled /></el-icon>
        {{ title }}
      </div>
      <div class="content">
        <slot></slot>
      </div>
      <div class="btns">
        <el-button @click="cancelHandler">取消</el-button>
        <el-button type="primary" @click="confirmHandler">确定</el-button>
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { WarningFilled } from '@element-plus/icons-vue';

interface IProps {
  title: string;
  placement?: string;
  confirm: () => void;
  width?: number;
}
const props = defineProps<IProps>();
defineOptions({
  name: 'CustomPopper',
});

const visible = ref(false);

const cancelHandler = () => {
  visible.value = false;
};
const confirmHandler = async () => {
  await props.confirm();
  visible.value = false;
};
</script>

<style scoped lang="less">
.title {
  color: #111111;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}
.content {
  color: #7a8599;
  font-size: 14px;
}
.btns {
  text-align: center;
  margin-top: 12px;
  :deep(.el-button) {
    width: 76px;
  }
}
</style>
