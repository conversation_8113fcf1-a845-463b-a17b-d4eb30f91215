<template>
  <div v-if="audioUrl" class="audio-player">
    <!-- 播放进度条 -->
    <div class="progress-container">
      <div class="progress-bar" @click="handleProgressClick">
        <div class="progress-track">
          <div
            class="progress-fill"
            :style="{ width: progressPercent + '%' }"
          ></div>
          <div
            class="progress-thumb"
            :style="{ left: progressPercent + '%' }"
          ></div>
        </div>
      </div>
      <div class="flex justify-between text-sm text-[#939CAE] leading-5">
        <span class="current-time">{{ formatTime(currentTime) }}</span>
        <span class="total-time">
          -{{ formatTime(duration - currentTime) }}
        </span>
      </div>
    </div>

    <!-- 时间显示和控制按钮 -->
    <div class="controls-container">
      <div class="control-buttons">
        <!-- 播放/暂停按钮 -->
        <div class="flex items-center gap-12">
          <button
            v-if="isPlaying"
            class="play-btn"
            :class="{ playing: isPlaying }"
            title="后退5秒"
            type="button"
            @click="rewind"
          >
            <img :src="rewindSvg" class="size-20" />
          </button>
          <button
            class="play-btn"
            :class="{ playing: isPlaying }"
            title="播放/暂停"
            type="button"
            @click="togglePlay"
          >
            <img :src="!isPlaying ? playSvg : pauseSvg" class="size-16" />
          </button>
          <button
            v-if="isPlaying"
            class="play-btn"
            type="button"
            :class="{ playing: isPlaying }"
            title="快进5秒"
            @click="fastForward"
          >
            <img :src="fastForwardSvg" class="size-20" />
          </button>
        </div>

        <!-- 倍速控制 -->
        <div class="speed-controls">
          <span class="speed-label">倍速</span>
          <button
            v-for="speed in speedOptions"
            :key="speed"
            class="speed-btn"
            :class="{ active: playbackRate === speed }"
            @click="setPlaybackRate(speed)"
          >
            {{ speed.toFixed(1) }}
          </button>
        </div>
      </div>
    </div>

    <!-- 隐藏的音频元素 -->
    <audio
      ref="audioRef"
      :src="audioUrl"
      preload="metadata"
      @loadedmetadata="onLoadedMetadata"
      @timeupdate="onTimeUpdate"
      @ended="onEnded"
      @error="onError"
    ></audio>
  </div>
</template>

<script setup lang="ts">
import fastForwardSvg from '@/assets/icons/fast-forward.svg';
import pauseSvg from '@/assets/icons/pause.svg';
import playSvg from '@/assets/icons/play.svg';
import rewindSvg from '@/assets/icons/rewind.svg';
import mitt from '@/lib/bus';
import { onMounted, onUnmounted, ref, useId, useTemplateRef, watch } from 'vue';

interface AudioPlayerProps {
  /** 音频URL */
  audioUrl?: string | null;
}

const props = defineProps<AudioPlayerProps>();

/** 音频元素引用 */
const audioRef = useTemplateRef<HTMLAudioElement>('audioRef');
/** 是否正在播放 */
const isPlaying = ref(false);
/** 当前播放时间（秒） */
const currentTime = ref(0);
/** 音频总时长（秒） */
const duration = ref(0);
/** 播放进度百分比 */
const progressPercent = ref(0);
/** 当前播放速率 */
const playbackRate = ref(1.0);

/**
 * rafId 用于存储 requestAnimationFrame 的动画帧ID，便于后续取消动画
 */
let rafId: number | null = null;

/**
 * 支持的倍速选项
 */
const speedOptions = [0.5, 1.0, 1.5, 2.0];

/**
 * 组件唯一标识，使用 Vue 内置 useId 保证唯一性
 */
const componentId = useId();

/**
 * 监听音频URL变化，切换音频时重置状态
 */
watch(
  () => props.audioUrl,
  /**
   * @param {string} newUrl
   */
  newUrl => {
    if (newUrl && audioRef.value) {
      audioRef.value.load();
      resetState();
    }
  }
);

/**
 * 重置音频播放状态
 */
function resetState() {
  isPlaying.value = false;
  currentTime.value = 0;
  progressPercent.value = 0;
}

/**
 * 启动进度条动画刷新（播放时高频刷新进度条，提升视觉流畅度）
 */
function animateProgress() {
  if (!audioRef.value || !isPlaying.value) return;
  currentTime.value = audioRef.value.currentTime;
  updateProgress();
  rafId = requestAnimationFrame(animateProgress);
}

/**
 * 停止进度条动画刷新（暂停/结束/出错时调用，节省资源）
 */
function stopAnimateProgress() {
  if (rafId !== null) {
    cancelAnimationFrame(rafId);
    rafId = null;
  }
}

/**
 * 播放/暂停切换
 * - 播放时启动动画
 * - 暂停时停止动画
 */
async function togglePlay() {
  if (!audioRef.value) return;

  try {
    if (isPlaying.value) {
      audioRef.value.pause();
      isPlaying.value = false;
      stopAnimateProgress(); // 暂停时停止动画
    } else {
      mitt.emit('audio-play-start', componentId);
      await audioRef.value.play();
      isPlaying.value = true;
      animateProgress(); // 播放时启动动画
    }
  } catch (error) {
    console.error('音频播放失败:', error);
    isPlaying.value = false;
    stopAnimateProgress(); // 出错时停止动画
  }
}

/**
 * 快进5秒
 */
function fastForward() {
  if (audioRef.value && duration.value > 0) {
    let nextTime = audioRef.value.currentTime + 5;
    if (nextTime > duration.value) nextTime = duration.value;
    audioRef.value.currentTime = nextTime;
    currentTime.value = nextTime;
    updateProgress();
  }
}

/**
 * 后退5秒
 */
function rewind() {
  if (audioRef.value && duration.value > 0) {
    let prevTime = audioRef.value.currentTime - 5;
    if (prevTime < 0) prevTime = 0;
    audioRef.value.currentTime = prevTime;
    currentTime.value = prevTime;
    updateProgress();
  }
}

/**
 * 设置播放倍速
 * @param {number} rate - 播放速率
 */
function setPlaybackRate(rate: number) {
  if (audioRef.value) {
    audioRef.value.playbackRate = rate;
    playbackRate.value = rate;
  }
}

/**
 * 处理进度条点击事件，跳转到指定进度
 * @param {MouseEvent} event
 */
function handleProgressClick(event: MouseEvent) {
  if (!audioRef.value || duration.value === 0) return;

  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
  const clickX = event.clientX - rect.left;
  const percent = clickX / rect.width;
  const newTime = percent * duration.value;

  audioRef.value.currentTime = newTime;
  currentTime.value = newTime;
  updateProgress();
}

/**
 * 更新进度条百分比
 */
function updateProgress() {
  if (duration.value > 0) {
    progressPercent.value = (currentTime.value / duration.value) * 100;
  }
}

/**
 * 格式化时间显示（mm:ss）
 * @param {number} seconds
 * @returns {string}
 */
function formatTime(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) return '0:00';

  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 音频元数据加载完成事件，设置音频总时长
 */
function onLoadedMetadata() {
  if (audioRef.value) {
    duration.value = audioRef.value.duration;
  }
}

/**
 * 音频播放进度更新事件处理函数
 * 每当音频播放位置发生变化时触发，更新当前播放时间和进度条
 */
function onTimeUpdate() {
  if (audioRef.value) {
    // 当前播放时间（秒）
    currentTime.value = audioRef.value.currentTime;
    // 更新进度百分比
    updateProgress();
  }
}

/**
 * 音频播放结束事件处理
 */
function onEnded() {
  isPlaying.value = false;
  currentTime.value = 0;
  progressPercent.value = 0;
  stopAnimateProgress();
}

/**
 * 音频加载或播放出错事件处理
 */
function onError(error: Event) {
  console.error('音频加载失败:', error);
  isPlaying.value = false;
  stopAnimateProgress();
}

/**
 * 监听其他音频组件的播放事件，保证同一时间只播放一个音频
 * @param playingComponentId 组件实例🆔
 */
function handleOtherAudioPlay(playingComponentId: string) {
  if (playingComponentId !== componentId && isPlaying.value) {
    if (audioRef.value) {
      audioRef.value.pause();
      isPlaying.value = false;
      stopAnimateProgress();
    }
  }
}

onMounted(() => {
  // 监听其他音频播放事件
  mitt.on('audio-play-start', handleOtherAudioPlay);
});

onUnmounted(() => {
  // 清理事件监听
  mitt.off('audio-play-start', handleOtherAudioPlay);

  // 暂停音频
  if (audioRef.value && isPlaying.value) {
    audioRef.value.pause();
    stopAnimateProgress(); // 组件卸载时停止动画
  }
});
</script>

<style scoped lang="less">
.audio-player {
  width: 100%;
  padding: 4px 8px 8px;
  background: #f7f8fa;
  border-radius: 4px;
  box-sizing: border-box;

  .progress-container {
    margin-bottom: 4px;

    .progress-bar {
      width: 100%;
      height: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;

      .progress-track {
        position: relative;
        width: 100%;
        height: 2px;
        background: #dcdee0;
        border-radius: 2px;

        .progress-fill {
          height: 100%;
          background: #939cae;
          border-radius: 2px;
        }

        .progress-thumb {
          position: absolute;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 6px;
          height: 6px;
          background: #939cae;
          border-radius: 50%;

          &:hover {
            transform: translate(-50%, -50%) scale(1.2);
          }
        }
      }
    }
  }

  .controls-container {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .control-buttons {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 16px;
      width: 100%;

      .play-btn {
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        background: transparent;
      }

      .speed-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        .speed-label {
          font-size: 14px;
          color: #708293;
          white-space: nowrap;
          line-height: 20px;
        }

        .speed-btn {
          min-width: 32px;
          height: 20px;
          padding: 0 12px;
          border: 1px solid #2e6be6;
          border-radius: 12px;
          background: white;
          color: #2e6be6;
          font-size: 14px;
          line-height: 18px;
          cursor: pointer;
          transition: all 200ms ease;

          &.active {
            background: #2e6be6;
            color: white;
          }
        }
      }
    }
  }
}
</style>
