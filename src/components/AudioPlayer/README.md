# AudioPlayer 音频播放组件

一个功能完整的 Vue 3 音频播放组件，支持进度控制、倍速播放和多实例互斥播放。

## 功能特性

- ✅ 音频播放/暂停控制
- ✅ 可拖拽的进度条
- ✅ 时间显示（当前时间/剩余时间）
- ✅ 倍速播放控制（0.5x, 1.0x, 1.5x, 2.0x）
- ✅ 多实例互斥播放（同时只能播放一个音频）
- ✅ 响应式设计，支持移动端
- ✅ 当 audioUrl 为空时自动隐藏组件
- ✅ 错误处理和加载状态

## 使用方法

### 基本用法

```vue
<template>
  <AudioPlayer :audio-url="audioUrl" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AudioPlayer from '@/components/AudioPlayer/index.vue'

const audioUrl = ref('https://example.com/audio.mp3')
</script>
```

### 多个音频播放器

```vue
<template>
  <div>
    <AudioPlayer :audio-url="audio1" />
    <AudioPlayer :audio-url="audio2" />
    <AudioPlayer :audio-url="audio3" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AudioPlayer from '@/components/AudioPlayer/index.vue'

const audio1 = ref('https://example.com/audio1.mp3')
const audio2 = ref('https://example.com/audio2.mp3')
const audio3 = ref('https://example.com/audio3.mp3')
</script>
```

### 动态音频URL

```vue
<template>
  <div>
    <button @click="changeAudio">切换音频</button>
    <button @click="clearAudio">清空音频</button>
    <AudioPlayer :audio-url="currentAudio" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AudioPlayer from '@/components/AudioPlayer/index.vue'

const currentAudio = ref('')

const changeAudio = () => {
  currentAudio.value = 'https://example.com/new-audio.mp3'
}

const clearAudio = () => {
  currentAudio.value = ''
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| audioUrl | string | '' | 音频文件的URL地址，为空时组件不显示 |

## 组件特性

### 互斥播放
当页面上有多个 AudioPlayer 组件时，点击任意一个的播放按钮，其他正在播放的音频会自动暂停。这通过全局事件总线 (mitt) 实现。

### 进度控制
- 点击进度条可以跳转到指定位置
- 进度条会实时显示播放进度
- 支持拖拽操作（通过点击实现）

### 倍速播放
支持 4 种播放速度：
- 0.5x（慢速）
- 1.0x（正常速度）
- 1.5x（快速）
- 2.0x（超快速）

### 时间显示
- 左侧显示当前播放时间
- 右侧显示剩余时间（负数格式）

## 样式定制

组件使用 scoped 样式，如需自定义样式，可以通过以下方式：

```vue
<template>
  <div class="custom-audio-container">
    <AudioPlayer :audio-url="audioUrl" />
  </div>
</template>

<style>
.custom-audio-container .audio-player {
  /* 自定义样式 */
  background: #your-color;
  border-radius: 12px;
}
</style>
```

## 依赖

- Vue 3
- mitt（事件总线，用于组件间通信）

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 注意事项

1. 确保音频文件URL可访问且支持跨域
2. 某些浏览器需要用户交互后才能播放音频
3. 组件会自动处理音频加载失败的情况
4. 建议为音频文件提供适当的 CORS 头部

## 错误处理

组件内置了错误处理机制：
- 音频加载失败时会在控制台输出错误信息
- 播放失败时会自动重置播放状态
- 网络错误不会导致组件崩溃
