<template>
  <div v-if="!isView" class="upload-wrapper" :style="customStyle">
    <el-upload
      ref="elUploadRef"
      v-loading="getVoucherLoading"
      :file-list="selfImgList"
      drag
      :http-request="handleHttpUpload"
      :on-success="uploadSuccess"
      :on-exceed="handleExceed"
      :before-upload="beforeUpload"
      :disabled="selfDisabled"
      :show-file-list="false"
      :limit="limit"
      multiple
      accept="image/*"
    >
      <template #trigger>
        <el-popover
          v-model:visible="popoverVisible"
          placement="top"
          width="280"
          trigger="click"
          :disabled="selfDisabled"
        >
          <div class="chooseImg-btn">
            <el-button text type="primary" @click="chooseMethod(1)">
              本地上传
            </el-button>
            <el-button text type="primary" @click="chooseMethod(2)">
              图片档案上传
            </el-button>
          </div>
          <template #reference>
            <div
              class="flex items-center p-xs"
              :class="[selfDisabled ? 'cursor-not-allowed' : '']"
              @click.stop
              @drop="handleDrop"
            >
              <UploadFilled class="w-20" />
              <div class="pl-sm text-sub-text">
                点击上传，或将图片拖动到此区域，支持jpg、jpeg、png等格式
              </div>
            </div>
          </template>
        </el-popover>
      </template>
    </el-upload>
    <div
      v-if="
        isBatch && taskStatus !== 3 && taskStatus !== 4 && selfImgList.length
      "
      class="batch-scanning w-100 flex items-center"
      @click="handleBatchScanning()"
    >
      <img
        :src="taskStatus === 5 ? ocrAuditResultImg : ocrBatchImg"
        alt=""
        class="w-14 h-14 mr-6"
      />
      {{ taskStatus === 5 ? '审核结果' : '批量扫描' }}
    </div>
  </div>
  <div v-else class="flex">
    <div v-if="!selfImgList.length" class="no-attachment">无附件</div>
    <div v-else class="has-attachment" @click="toggleImgList()">
      <Link class="mr-3xs w-20" />
      {{ selfImgList.length }}个附件
    </div>
  </div>
  <div
    v-show="showImgList && selfImgList?.length"
    ref="imgListRef"
    class="flex items-center pt-sm flex-wrap"
    :class="{ scroll: isScroll }"
  >
    <div v-for="(file, index) in selfImgList" :key="file.url" class="relative">
      <ImgPreview
        class="upload-image"
        :width="80"
        :url="file.url"
        :type="imgPreviewType"
        :show-status-label="showStatusLabel"
        :show-status="showStatus"
        fixed
        :disable-scanning="disabled"
        :thumbnail="false"
      />
      <CircleCloseFilled
        v-if="!selfDisabled"
        class="handle-icon w-20"
        @click.stop="handleRemove(index)"
      />
    </div>
  </div>

  <!-- 图片档案 -->
  <PicArchiveDialog
    v-model:dialogVisible="chooseImgVisible"
    @get-checked-urls="
      (urls: string[]) => updateImgList([...map(selfImgList, 'url'), ...urls])
    "
  />

  <!-- 批量扫描 -->
  <BatchScanning
    v-model:batch-scanning-visible="batchScanningVisible"
    :self-img-list="selfImgList"
    :type="imgPreviewType"
    :show-status-label="showStatusLabel"
    :show-status="showStatus"
    :loading="loading"
    @close="batchScanningVisible = false"
    @submission-recognition="submissionRecognition"
  />

  <!-- 审核结果 -->
  <OcrAuditResult
    v-model:visible="ocrAuditResultVisible"
    :source-type="sourceType"
    :source-id="sourceId"
    :task-method="taskMethod"
    :task-section="taskSection"
    :task-id="taskId"
    @update:visible="ocrAuditResultVisible = false"
  />
</template>

<script setup lang="ts">
import { UploadFile, UploadFiles, UploadProps } from 'element-plus';
import { useToggle } from '@vueuse/core';
import { cloneDeep, map, some, uniqueId } from 'lodash-es';
import ImgPreview from '@/components/ImgPreview/index.vue';
import ocrBatchImg from '@/assets/imgs/ocr/ocr-batch-img.png';
import ocrAuditResultImg from '@/assets/imgs/ocr/ocr-audit-result-img.png';
import PicArchiveDialog from '@/components/PicArchiveDialog/index.vue';
import { CircleCloseFilled, Link, UploadFilled } from '@element-plus/icons-vue';
import { useUpload } from './hooks';
import { NIM_UPLOAD_PERSISTENT_URLS, QINIU_UPLOAD_CONFIG } from '@/constant';
import { useOcrScan } from '@/store/module/useOcrScan';
import BatchScanning from '@/features/BatchScanning/index.vue';
import OcrAuditResult from '@/features/OcrAuditResult/index.vue';
import { ocrBatchScanImageApi } from '@/api/ocr';
import { transcriptionTaskApi } from '@/api/task';
import { ITranscriptionTaskApi } from '@/pages/Workbench/Header/components/TaskAcceptanceDrawer/type';
import useGlobal from '@/store/module/useGlobal';
const { userInfo, currentRole } = useGlobal();
import { SourceTypeValues } from '@/constant';
import { getPatientInfoBase } from '@/api/overview';
import useInternDrawer from '@/store/module/useInternDrawer';
const dStore = useInternDrawer();
import bus from '@/lib/bus';

defineOptions({
  name: 'UploadImages',
});

interface UploadImageProps {
  imgList: string[];
  drag?: boolean; // 是否支持拖拽上传 ==> 非必传（默认为 true）
  disabled?: boolean; // 是否禁用上传组件 ==> 非必传（默认为 false）
  limit?: number; // 最大图片上传数 ==> 非必传（默认为 20张）
  isView?: boolean; // 是否查看状态【通用样式】 ==> 非必传（默认为 false）
  canScan?: boolean; // 是否可以使用OCR扫描 ==> 非必传（默认为 false）
  customStyle?: string;
  showStatusLabel?: boolean;
  showStatus?: boolean;
  fileType?: string[]; // 图片类型限制 ==> 非必传（默认为 ["image/jpeg", "image/png", "image/gif"]）
  isBatch?: boolean; // 是否支持批量扫描
  taskStatus?: number | null;
  sourceId?: number;
  sourceType?: SourceTypeValues;
  taskMethod?: number;
  taskId?: number;
  taskSection?: string;
}

const props = withDefaults(defineProps<UploadImageProps>(), {
  imgList: () => [],
  fileType: () => [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
  ],
  drag: true,
  disabled: false,
  limit: 20,
  canScan: false,
  showStatus: false,
  customStyle: '',
  isBatch: false,
  taskStatus: null,
  sourceId: 0,
  sourceType: 0,
  taskMethod: -1,
  taskId: 0,
  taskSection: '',
});
const customStyle = ref(props.customStyle);

const taskStatus = ref(props.taskStatus);
const taskMethod = ref(props.taskMethod);
const taskSection = ref(props.taskSection);
const taskId = ref(props.taskId);
watch(
  () => props.taskStatus,
  value => {
    taskStatus.value = value;
    taskMethod.value = props.taskMethod;
    taskSection.value = props.taskSection;
    taskId.value = props.taskId;
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => props.customStyle,
  value => {
    if (props.customStyle.includes('%')) customStyle.value = value;
    const digits = props.customStyle.replace(/[^\d.]/g, '');
    const number = parseInt(digits, 10);
    if (props.isBatch && number) customStyle.value = `width:${number - 100}px`;
  },
  {
    deep: true,
    immediate: true,
  }
);

const emit = defineEmits<{
  'update:imgList': [value: string[]];
  onDelete: [val: string];
}>();

const globalImg = useOcrScan();

// 针对查看切换状态
const [showImgList, toggleImgList] = useToggle(true);
watch(
  () => props.isView,
  isView => toggleImgList(!isView)
);

defineExpose({
  toggleImgList,
});
// 判断是否禁用上传和删除
const selfDisabled = computed(() => props.disabled || props.isView);

// 组件图片list
const selfImgList = ref<{ name: string; url: string }[]>([]);

// 图片预览分组
const imgPreviewType = ref(Date.now() + '');
const imgListRef = ref<HTMLElement>();
const isScroll = ref(false);

watch(
  () => props.imgList,
  async list => {
    selfImgList.value =
      list?.map(url => {
        return { name: uniqueId(), url };
      }) || [];

    await nextTick(); // 等待 DOM 更新完成
    if (imgListRef.value) {
      const height = imgListRef.value.offsetHeight;
      isScroll.value = height > 200;
    }
  },
  { immediate: true }
);

const {
  chooseImgVisible,
  elUploadRef,
  chooseMethod,
  handleHttpUpload,
  popoverVisible,
  getVoucherLoading,
} = useUpload();

/**
 * @description 文件上传之前判断
 * @param rawFile 选择的文件
 * */
const beforeUpload: UploadProps['beforeUpload'] = rawFile => {
  const imgType = props.fileType.includes(rawFile.type);
  if (!imgType) {
    ElMessage.warning('上传图片不符合所需的格式!');
  }
  return imgType;
};

/**
 * 图片上传成功
 * @param response 上传响应结果
 * @param uploadFile 上传的文件
 * @param uploadFiles 上传的文件list
 */
const uploadSuccess = (
  response: string | undefined,
  uploadFile: UploadFile,
  uploadFiles: UploadFiles & { response: string }
) => {
  if (!response) return;
  uploadFile.url = response;
  if (uploadFiles.every(item => item.status === 'success' && item.url)) {
    emit('update:imgList', [...(map(uploadFiles, 'url') as Array<string>)]);
  }
};

interface ImageInfo {
  type: string;
  name: string;
}
/**
 * 从URL提取文件名和类型
 */
const commonImageTypes = ['jpg', 'png', 'gif', 'jpeg', 'webp'];
const extractImageInfo = (url: string): ImageInfo => {
  try {
    const { pathname } = new URL(url);
    const fileName = pathname.split('/').pop() || 'untitled';
    let type: string | undefined = 'png'; // 设置默认值
    const parts = fileName.split('.');
    const potentialExtension = cloneDeep(parts).pop()?.toLowerCase();
    if (potentialExtension && commonImageTypes.includes(potentialExtension)) {
      type = potentialExtension;
    }
    // 移除所有可能的扩展名部分以获取纯净文件名
    const pureName = parts.join('.');
    return { type, name: pureName };
  } catch {
    return { type: 'png', name: 'untitled' };
  }
};
const urlToFile = async (url: string) => {
  const { type, name } = extractImageInfo(url);
  const res = await fetch(url);
  const blob = await res.blob();
  return new File([blob], name, { type: `image/${type}` });
};

/** 拖拽网络图片上传 */
const { url, urlHttp, urlQU } = QINIU_UPLOAD_CONFIG;
const handleDrop = async (e: any) => {
  e.preventDefault();
  const imageUrl: string = e.dataTransfer.getData('URL');
  if (imageUrl) {
    if (
      !some([url, urlHttp, urlQU, NIM_UPLOAD_PERSISTENT_URLS], address =>
        imageUrl.startsWith(address)
      )
    ) {
      // todo el-upload drag windows与mac 存在差异
      if (!/Win/i.test(navigator.userAgent)) {
        return ElMessage.error('只支持站内网络图片');
      }
    } else {
      if (selfImgList.value?.length >= props.limit) {
        return handleExceed();
      }
      const fileObj = await urlToFile(imageUrl);
      elUploadRef.value.handleStart(fileObj);
      elUploadRef.value.submit();
      // updateImgList([...map(selfImgList.value, 'url'), imageUrl]);
    }
  }
};

/**
 * @description 文件数超出
 */
const handleExceed = () => {
  ElMessage({
    message: `当前最多只能上传 ${props.limit} 张图片，请移除后上传！`,
    type: 'warning',
  });
};

/**
 * 删除图片
 * @param index 索引
 * */
const handleRemove = (index: number) => {
  const curItem = selfImgList.value[index];
  const list = selfImgList.value.filter((_, i) => i !== index);
  updateImgList(map(list, 'url'));
  globalImg?.deletePicFromPreviewList(imgPreviewType.value, index);
  emit('onDelete', curItem.url);
};

function updateImgList(urls: string[]) {
  if (!urls) urls = [];
  emit('update:imgList', urls);
}

/* 批量扫描 | 审核结果 */
const batchScanningVisible = ref<boolean>(false);
const ocrAuditResultVisible = ref<boolean>(false);
const handleBatchScanning = () => {
  // 当 taskStatus === 5 显示审核结果
  if (props.taskStatus === 5) {
    ocrAuditResultVisible.value = true;
  } else {
    // 除了 taskStatus === 5 显示批量扫描
    batchScanningVisible.value = true;
  }
  globalImg.updateMedicalRecord(false);
};
// 提交识别
const loading = ref(false);
const submissionRecognition = async (urlList: string[]) => {
  const patientInfo = userInfo as any;
  const patientId = patientInfo.patientId;
  const patientName = patientInfo.patientName;

  // 生成转录任务
  const info: ITranscriptionTaskApi = {
    patientId,
    patientName,
    sourceId:
      currentRole === 4 ? Number(dStore.internSubmitBatchId) : props.sourceId,
    taskType: props.sourceType,
    taskMethod: 1,
    sourceType: currentRole === 4 ? 1 : 0,
  };

  if (currentRole === 4) {
    info.patientId = dStore.patientId;
    const data = await getPatientInfoBase({
      patientId: dStore.patientId,
    });
    info.patientName = data.patientName as string;
  }

  const res = await transcriptionTaskApi(info);
  const subTaskId = res.data as number;

  // 批量扫描
  ocrBatchScanImageApi({ urlList, subTaskId }).then((res: any) => {
    if (res.code === 'E000000') {
      ElMessage({
        message: '提交成功',
        type: 'success',
      });
      batchScanningVisible.value = false;
      loading.value = false;
      bus.emit('batch-ocr-success-refresh');
    }
  });
};
</script>
<style lang="less" scoped>
.upload-wrapper {
  position: relative;
  :deep(.el-upload-dragger) {
    padding: 0;
  }
  .batch-scanning {
    font-size: 14px;
    color: #2e6be6;
    cursor: pointer;
    position: absolute;
    top: 14px;
    right: -132px;
  }
}
.scroll {
  height: 200px;
  overflow-y: scroll;
}

.upload-image {
  height: 80px;
  width: 80px;
  cursor: pointer;
  margin: 0 8px 8px 0;
}

.handle-icon {
  border-radius: 100%;
  background: #fff;
  position: absolute;
  top: -6px;
  right: 0;
  color: var(--color-danger);
  cursor: pointer;
}

.no-attachment {
  color: var(--color-sub-text);
  font-size: 14px;
  line-height: 32px;
}

.has-attachment {
  cursor: pointer;
  display: flex;
  align-items: center;
  color: var(--color-primary);
}

.chooseImg-btn {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .el-button {
    width: 108px;
    margin: 8px;
    border: 1px solid #ccc;
  }
}

.dialog-content {
  height: 500px;
}

.dialog-btn-group {
  padding: 24px 88px;
  border-top: 1px solid #e9e8eb;
  .el-button {
    width: 76px;
  }
}
</style>
