<template>
  <div class="rounded-lg box-border relative">
    <slot name="reference">
      <el-image
        :src="currentUrl"
        :lazy="lazy"
        :style="{
          height: fixed ? width + 'px' : height + 'px',
          width: width + 'px',
        }"
        class="object-cover rounded-lg"
        @click.stop="openDialog"
        @error="handleError"
      />
    </slot>

    <el-tooltip
      v-if="
        imgStatusMap[url]?.value === 4 &&
        imgStatusMap[url]?.labelName &&
        showStatusLabel
      "
      class="box-item"
      effect="dark"
      :content="imgStatusMap[url]?.labelName"
      placement="top-start"
    >
      <div class="status-label" :class="{ 'status-label-batch': isBatch }">
        <div class="text-ellipsis overflow-hidden whitespace-nowrap">
          {{ imgStatusMap[url]?.labelName }}
        </div>
      </div>
    </el-tooltip>
    <div
      v-if="showStatus"
      :class="{
        'img-status': true,
        'no-finish-status':
          imgStatusMap[url]?.value === 0 ||
          imgStatusMap[url]?.value === 1 ||
          imgStatusMap[url]?.value === 10,
        'finish-status':
          imgStatusMap[url]?.value !== 0 &&
          imgStatusMap[url]?.value !== 1 &&
          imgStatusMap[url]?.value !== 10,
      }"
    >
      <span v-if="typeof imgStatusMap[url]?.value === 'number'">
        {{ getImgStatusStr(imgStatusMap[url]?.value) }}
      </span>
      <span v-else>获取中</span>
    </div>
    <OcrViewer
      v-if="sacanUrl && showDialog"
      :disable-scanning="disableScanning"
      :curr-userid="1"
      :title="disableScanning ? '查看图片' : ''"
      :component-location="componentLocation"
      :patient-history-id="patientHistoryId"
      :component-location-str="componentLocationStr"
      @close-dialog="closeDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { useOcrScan } from '@/store/module/useOcrScan';

import OcrViewer from '@/features/OcrViewer/index.vue';

import OCR_FILE_TYPES, {
  FILE_TYPE_MAP,
} from '@/features/OcrViewer/constant/ocrFileTypes';
import { handleError } from 'vue';

const slots = useSlots();

const ocrStore = useOcrScan();

const imgStatusMap = computed(() => ocrStore.globalImgInfo.imgStatusMap);

interface Props {
  url: string;
  type?: string;
  showStatus?: boolean;
  showStatusLabel?: boolean;
  fixed?: boolean;
  width?: number;
  disableScanning?: boolean;
  height?: number | string;
  lazy?: boolean;
  // 小图是否显示缩略图版本，默认true
  thumbnail?: boolean;
  isBatch?: boolean; // 是否批量
}

const props = withDefaults(defineProps<Props>(), {
  type: '1',
  showStatus: true,
  showStatusLabel: false,
  fixed: false,
  width: 100,
  height: '',
  disableScanning: false,
  lazy: false,
  thumbnail: true,
  isBatch: false,
});
const emit = defineEmits(['closeOcrDialog']);

const showDialog = ref<boolean>(false);
const currentUrl = ref(
  props.thumbnail
    ? `${props.url}?imageView2/1/w/${~~props.width}/h/${
        props.fixed ? ~~props.width : ~~(props.height || props.width)
      }`
    : props.url
);
const sacanUrl = computed(() => ocrStore.globalImgInfo.currentImgUrl);
const ivData: any = inject('imgViewerData', null);
const componentLocationStr = computed(() => ivData?.value?.locationStr);
const componentLocation = computed(
  () => FILE_TYPE_MAP[ivData?.value?.sourceType] ?? OCR_FILE_TYPES.ALL_RECORDS
);
const patientHistoryId = computed(() => ivData?.value?.sourceId ?? '');
const openDialog = () => {
  const ocrElemnet = document.querySelector('.ocr-dialog');
  if (!ocrElemnet) {
    ocrStore.updatedCurrentImgUrl({
      url: props.url,
      type: props.type,
      showStatus: props.showStatus,
    });
    showDialog.value = true;
  }
};
const closeDialog = (url: null) => {
  ocrStore.updatedCurrentImgUrl({
    url,
    type: props.type,
    showStatus: props.showStatus,
  });
  showDialog.value = false;
  ocrStore.resetOcrState();
  ocrStore.getMsgListStatus();
  //关闭ocr触发
  emit('closeOcrDialog');
};

const handleError = () => {
  if (props.thumbnail) currentUrl.value = props.url;
};

const getImgStatusStr = (status: number) => {
  if (status === 0) {
    return '识别中';
  }
  if (status === 1) {
    return '识别失败';
  }
  if (status === 2) {
    return '入院记录';
  }
  if (status === 3) {
    return '出院记录';
  }
  if (status === 4) {
    return '检验报告';
  }
  if (status === 5) {
    return '门诊报告';
  }
  if (status === 6) {
    return '手术记录';
  }
  if (status === 7) {
    return '12导联心电图';
  }
  if (status === 8) {
    return '动态心电图';
  }
  if (status === 9) {
    return '心脏彩超';
  }
  if (status === 10) {
    return '未识别';
  }
};
onMounted(() => {
  ocrStore.updatedGlobalImgList({
    url: props.url,
    type: props.type,
    showStatus: props.showStatus,
  });
  provide('componentLocation', componentLocation.value);
});
onMounted(() => {
  const refrenceEleId = slots.reference?.()[0].props?.id;
  if (refrenceEleId) {
    const el = document.getElementById(refrenceEleId);
    el?.addEventListener('click', openDialog);
  }
});
onUnmounted(() => {
  const refrenceEleId = slots.reference?.()[0].props?.id;
  if (refrenceEleId) {
    const el = document.getElementById(refrenceEleId);
    el?.removeEventListener('click', openDialog);
  }
});
</script>
<script lang="ts">
export default {
  name: 'ImgPreview',
};
</script>
<style scoped lang="less">
.img-status {
  box-sizing: border-box;
  padding: 4px;
  position: absolute;
  left: 4px;
  top: 4px;
  border-radius: 2px;
  font-size: 12px;
  font-weight: 400;
}
.status-label {
  box-sizing: border-box;
  padding: 4px;
  position: absolute;
  left: 4px;
  bottom: 4px;
  border-radius: 2px;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
  width: 100%;
}
.status-label-batch {
  width: 60px;
  left: 18px;
}
.finish-status {
  color: #111111;
  background-color: white;
}
.no-finish-status {
  color: #ffffff;
  background-color: rgba(65, 64, 62, 0.5);
}
.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: aqua;
}
</style>
