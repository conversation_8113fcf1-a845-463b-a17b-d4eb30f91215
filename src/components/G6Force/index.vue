<template>
  <div ref="g6Container" class="g6-container w-full h-full relative"></div>
</template>

<script setup lang="ts">
import G6, { Graph, GraphData, NodeConfig } from '@antv/g6';

defineOptions({
  name: 'G6Force',
});

const colorsMap = {
  disease: 'l(90) 0:#82E9FE 1:#18A3BF',
  check: 'l(90) 0:#98BAFF 1:#2E6BE6',
  medicine: 'l(90) 0:#FFBB8A 1:#E37221',
  sociology: 'l(90) 0:#FF99EF 1:#CC19AF',
  symptom: 'l(90) 0:#FFD685 1:#E5A82E',
  f: 'l(90) 0:#C195FF 1:#7A2EE5',
};

const graphConfig = {
  small: {
    rootLinkDistance: 160,
    noRootLinkDistance: 130,
    maxNodeSize: 68,
    minNodeSize: 32,
    // node 节点尺寸差值
    nodeSizeGap: 18,
    nodeLabelSize: 12,
    edgeLabelSize: 12,
    edgeLabel: 12,
  },
  normal: {
    rootLinkDistance: 190,
    noRootLinkDistance: 160,
    maxNodeSize: 88,
    minNodeSize: 40,
    // node 节点尺寸差值
    nodeSizeGap: 20,
    nodeLabelSize: 14,
    edgeLabelSize: 12,
  },
};

interface INode extends NodeConfig {
  // 症状类型
  attribute: 'disease' | 'medicine' | 'sociology' | 'symptom' | 'check';
  // 节点类型 0:病人节点，1:推理节点
  type: '0' | '1';
  // 节点名称
  label?: string;
  // 是否是根节点
  isRoot?: boolean;
  // 是否是叶子节点
  isLeaf?: boolean;
}

interface IRelation {
  id?: string;
  source: string;
  target: string;
  // 边描述
  label?: string;
}
interface IG6Props {
  smallLayout?: boolean;
  width?: number;
  height?: number;
  data: {
    roots: string[];
    leafs: string[];
    nodes: INode[];
    relations: IRelation[];
  };
}

const props = withDefaults(defineProps<IG6Props>(), {
  width: undefined,
  height: undefined,
});

let graph: Graph;
let g6Width: number;
let g6Height: number;
/** 处理循环节点指向 */
let tempEdgeIdMap = new Map();
const nodeMap = new Map();
const g6Container = shallowRef<HTMLDivElement | null>(null);
const defGraphCon = computed(() => {
  return props.smallLayout ? graphConfig.small : graphConfig.normal;
});

const initAntvG6Graph = () => {
  const { noRootLinkDistance, rootLinkDistance, nodeLabelSize, edgeLabelSize } =
    defGraphCon.value;

  graph = new G6.Graph({
    container: g6Container.value!,
    width: g6Width,
    height: g6Height,
    layout: {
      type: 'force',
      // alphaDecay: 0.01,
      nodeStrength: -20,
      preventOverlap: true,
      linkDistance: d => {
        return !d.source.isRoot ? noRootLinkDistance : rootLinkDistance;
      },
    },
    modes: {
      default: [
        // 'drag-canvas',
        // 'zoom-canvas',
        {
          type: 'drag-canvas',
          enableOptimize: true,
        },
        {
          type: 'zoom-canvas',
          enableOptimize: true,
        },
        {
          type: 'tooltip',
          formatText(model) {
            return model.oriLabel as string;
          },
        },
        {
          type: 'edge-tooltip',
          formatText(model) {
            return model.oriLabel as string;
          },
        },
      ],
    },
    defaultNode: {
      type: 'bubble',
      labelCfg: {
        style: {
          fill: '#fff',
          fontSize: nodeLabelSize,
          fontWeight: 500,
        },
      },
    },
    defaultEdge: {
      color: '#888',
      style: {
        lineAppendWidth: 2,
        endArrow: {
          // 连线箭头
          path: 'M 0,0 L 4,2 L 3,0 L 4,-2 Z',
          fill: '#e2e2e2',
        },
      },
      labelCfg: {
        autoRotate: true,
        style: {
          fontSize: edgeLabelSize,
          fill: '#3A4762',
          background: {
            radius: 2,
            fill: '#fff',
            padding: [2, 2, 2, 2],
          },
        },
      },
    },
    edgeStateStyles: {
      dark: {
        opacity: 0.2,
        'text-shape': {
          opacity: 0.2,
        },
      },
    },
  });

  // graph.get('canvas').set('localRefresh', false);

  graph.on('node:mouseenter', e => {
    const item = e.item;
    const model: any = item?.getModel();
    if (!item || !model) return;
    graph.setAutoPaint(false);
    const nodeItems = graph.getNodes();
    const edgeItems = graph.getEdges();
    tempEdgeIdMap = new Map();
    const allTags = [
      ...model.tags,
      ...findChildRelationships(props.data.relations, model.id),
    ];

    const relationIds = allTags?.reduce(
      (pre: string[], cur: string) => {
        const ids = cur?.split('-');
        ids.forEach(id => {
          if (!pre.includes(id)) pre.push(id);
        });
        return pre;
      },
      [model.id]
    );

    nodeItems.forEach(node => {
      const curModel = node.getModel();
      if (relationIds.includes(curModel.id)) {
        graph.setItemState(node, 'dark', false);
        curModel.light = true;
      } else {
        graph.setItemState(node, 'dark', true);
        curModel.light = false;
      }
    });

    edgeItems.forEach(item => {
      const source = item.getSource().getModel();
      const target = item.getTarget().getModel();
      if (source.light && target.light) {
        graph.setItemState(item, 'dark', false);
      } else {
        graph.setItemState(item, 'dark', true);
      }
    });

    graph.paint();
    graph.setAutoPaint(true);
  });

  graph.on('node:mouseleave', () => {
    const nodeItems = graph.getNodes();
    const edgeItems = graph.getEdges();
    nodeItems.forEach(item => {
      graph.setItemState(item, 'dark', false);
    });
    edgeItems.forEach(item => {
      graph.setItemState(item, 'dark', false);
    });
  });

  graph.on('node:dragstart', e => {
    graph.layout();
    refreshDragedNodePosition(e);
  });

  graph.on('node:drag', e => {
    refreshDragedNodePosition(e);
  });

  graph.on('node:dragend', e => {
    if (!e.item) return;
    e.item.get('model').fx = null;
    e.item.get('model').fy = null;
  });

  function refreshDragedNodePosition(e) {
    const model = e.item.get('model');
    model.fx = e.x;
    model.fy = e.y;
  }

  //处理数据并渲染
  resetGraph();
};

const resetGraph = () => {
  const { data, smallLayout } = props;
  if (!data) return;
  const { nodeLabelSize, edgeLabelSize, noRootLinkDistance } =
    defGraphCon.value;
  const { leafs, roots, nodes, relations } = data;

  const graphData: GraphData = {
    nodes:
      nodes?.map(node => {
        const isRoot = roots.includes(node.id);
        const isLeaf = leafs.includes(node.id);
        tempEdgeIdMap = new Map();
        const tags = findParRelationships(relations, node.id);
        const size = mapNodeSize(tags, smallLayout);
        const curNode = {
          ...node,
          isRoot,
          isLeaf,
          tags,
          size,
          type: 'bubble',
          oriLabel: node.label,
          label: fittingString(node.label || '', size - 6, nodeLabelSize),
          style: {
            lineWidth: 0,
            fill: colorsMap[node.attribute],
          },
        };
        if (isRoot) {
          curNode.x = Math.random() * (g6Width / 2);
          curNode.y = Math.random() * (g6Height / 3);
        }
        nodeMap.set(curNode.id, curNode);
        return curNode;
      }) || [],
    edges:
      relations?.map(edge => {
        return {
          ...edge,
          oriLabel: edge.label,
          label: fittingString(
            edge.label || '',
            noRootLinkDistance / 2,
            edgeLabelSize
          ),
          color: nodeMap.get(edge.source)?.style?.fill,
          id: `${edge.source}-${edge.target}`,
        };
      }) || [],
  };
  //处理数据并渲染
  graph.clear();
  graph.data(graphData);
  graph.render();
};

/**
 * format the string
 * @param {string} str The origin string
 * @param {number} maxWidth max width
 * @param {number} fontSize font size
 * @return {string} the processed result
 */
const fittingString = (str: string, maxWidth: number, fontSize: number) => {
  const ellipsis = '...';
  const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0];
  let currentWidth = 0;
  let res = str;
  const pattern = new RegExp('[\u4E00-\u9FA5]+');
  str.split('').forEach((letter, i) => {
    if (currentWidth > maxWidth - ellipsisLength) return;
    if (pattern.test(letter)) {
      // Chinese charactors
      currentWidth += fontSize;
    } else {
      // get the width of single letter according to the fontSize
      currentWidth += G6.Util.getLetterWidth(letter, fontSize);
    }
    if (currentWidth > maxWidth - ellipsisLength) {
      res = `${str.substr(0, i)}${ellipsis}`;
    }
  });
  return res;
};

// 节点尺寸
const mapNodeSize = (tags: string[], isSmall = false) => {
  let maxLevel = 0;
  const len = tags?.length || 0;
  for (let i = 0; i < len; i++) {
    const tl = tags[i].split('-')?.length;
    if (tl - 1 > maxLevel) maxLevel = tl - 1;
  }
  const { maxNodeSize, minNodeSize, nodeSizeGap } = isSmall
    ? graphConfig.small
    : graphConfig.normal;
  const size = maxNodeSize - nodeSizeGap * maxLevel;
  return size < minNodeSize ? minNodeSize : size;
};

// 查找父级节点
const findParRelationships = (edges: IRelation[], id: string) => {
  const relationships: string[] = [];
  for (const { source, target } of edges) {
    if (target === id) {
      const idStr = `${target}-${source}`;
      if (tempEdgeIdMap.get(idStr)) break;
      tempEdgeIdMap.set(idStr, true);

      const subRelationships = findParRelationships(edges, source);
      if (subRelationships.length === 0) {
        relationships.push(`${source}-${id}`);
      } else {
        relationships.push(
          ...subRelationships.map(subRelationship => `${subRelationship}-${id}`)
        );
      }
    }
  }
  return relationships;
};

// 查找子级节点
const findChildRelationships = (edges: IRelation[], id: string) => {
  const relationships: string[] = [];

  for (const { source, target } of edges) {
    if (source === id) {
      const idStr = `${target}-${source}`;
      if (tempEdgeIdMap.get(idStr)) break;
      tempEdgeIdMap.set(idStr, true);

      const subRelationships = findChildRelationships(edges, target);
      relationships.push(
        ...subRelationships.map(subRelationship => `${id}-${subRelationship}`)
      );
      if (subRelationships.length === 0) {
        relationships.push(`${id}-${target}`);
      }
    }
  }

  return relationships;
};

G6.registerNode(
  'bubble',
  {
    drawShape(cfg, group) {
      const self = this as any;
      const r = ((cfg!.size || 40) as number) / 2;
      const path = [
        ['M', -r, 0],
        ['C', -r, r / 2, -r / 2, r, 0, r],
        ['C', r / 2, r, r, r / 2, r, 0],
        ['C', r, -r / 2, r / 2, -r, 0, -r],
        ['C', -r / 2, -r, -r, -r / 2, -r, 0],
        ['Z'],
      ];

      const style = Object.assign(
        {},
        {
          x: 0,
          y: 0,
          path: path,
          fill: cfg!.color || 'steelblue',
        },
        cfg!.style
      );

      const keyShape = group!.addShape('path', {
        attrs: {
          ...style,
        },
        draggable: true,
        name: 'path-shape',
      });

      const spNum = 10;
      const directions: any[] = [];
      const rs: any[] = [];
      self.changeDirections(spNum, directions);
      for (let i = 0; i < spNum; i++) {
        const rr = r + directions[i] * ((Math.random() * r) / 1000);
        if (rs[i] < 0.97 * r) rs[i] = 0.97 * r;
        else if (rs[i] > 1.03 * r) rs[i] = 1.03 * r;
        rs.push(rr);
      }
      keyShape?.animate(
        () => {
          const path = self.getBubblePath(r, spNum, directions, rs);
          return { path };
        },
        {
          repeat: true,
          duration: 10000,
        }
      );
      return keyShape;
    },
    changeDirections(num, directions) {
      for (let i = 0; i < num; i++) {
        if (!directions[i]) {
          const rand = Math.random();
          const dire = rand > 0.5 ? 1 : -1;
          directions.push(dire);
        } else {
          directions[i] = -1 * directions[i];
        }
      }
      return directions;
    },
    getBubblePath(r, spNum, directions, rs) {
      const path: any[] = [];
      const cpNum = spNum * 2;
      const unitAngle = (Math.PI * 2) / spNum;
      let angleSum = 0;
      const sps: any[] = [];
      const cps: any[] = [];
      for (let i = 0; i < spNum; i++) {
        const speed = 0.001 * Math.random();
        rs[i] = rs[i] + directions[i] * speed * r;
        if (rs[i] < 0.97 * r) {
          rs[i] = 0.97 * r;
          directions[i] = -1 * directions[i];
        } else if (rs[i] > 1.03 * r) {
          rs[i] = 1.03 * r;
          directions[i] = -1 * directions[i];
        }
        const spX = rs[i] * Math.cos(angleSum);
        const spY = rs[i] * Math.sin(angleSum);
        sps.push({ x: spX, y: spY });
        for (let j = 0; j < 2; j++) {
          const cpAngleRand = unitAngle / 3;
          const cpR = rs[i] / Math.cos(cpAngleRand);
          const sign = j === 0 ? -1 : 1;
          const x = cpR * Math.cos(angleSum + sign * cpAngleRand);
          const y = cpR * Math.sin(angleSum + sign * cpAngleRand);
          cps.push({ x, y });
        }
        angleSum += unitAngle;
      }
      path.push(['M', sps[0].x, sps[0].y]);
      for (let i = 1; i < spNum; i++) {
        path.push([
          'C',
          cps[2 * i - 1].x,
          cps[2 * i - 1].y,
          cps[2 * i].x,
          cps[2 * i].y,
          sps[i].x,
          sps[i].y,
        ]);
      }
      path.push([
        'C',
        cps[cpNum - 1].x,
        cps[cpNum - 1].y,
        cps[0].x,
        cps[0].y,
        sps[0].x,
        sps[0].y,
      ]);
      path.push(['Z']);
      return path;
    },
    setState(name, value, item) {
      const shape = item?.get('keyShape');
      if (name === 'dark') {
        shape.attr('opacity', value ? 0.3 : 1);
      }
    },
  },
  'single-node'
);

onMounted(() => {
  nextTick(() => {
    if (!g6Container.value) return;
    g6Width = props.width || g6Container.value.clientWidth;
    g6Height = props.width || g6Container.value.clientHeight;
    initAntvG6Graph();
  });
});

onBeforeUnmount(() => {
  graph.destroy();
});
</script>
<style scoped lang="less">
.g6-container {
  :deep(.g6-tooltip) {
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #545454;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 6px 8px;
    box-shadow: rgb(174, 174, 174) 0 0 10px;
  }
}
</style>
