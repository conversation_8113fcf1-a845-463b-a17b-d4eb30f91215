<template>
  <div>
    <template v-if="$slots.default">
      <div @click.stop="callValidate">
        <slot></slot>
      </div>
    </template>
    <template v-else>
      <div class="inline-block cursor-pointer" @click.stop="callValidate">
        <img class="size-16" :src="callCenter" />
      </div>
    </template>
    <LineSelectionDialog
      v-if="tel"
      v-model="lineSelectionVisible"
      :call-phone="tel"
      :only-rly="rlyIsLogin && !useCallStore.isCallLogin"
      :patient-id="patientId"
      @confirm="lineSelectionConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { handleRecordApi } from '@/api/addressBook';
import callCenter from '@/assets/imgs/callCenter/call-center.png';
import LineSelectionDialog from '@/components/CallCenter/LineSelectionDialog.vue';
import { useCall, useGlobal, useRLYSoftphone, useUserStore } from '@/store';
import { hasMicrophone } from '@/utils';
import { toRefs } from '@vueuse/core';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';

interface PhoneCallProps {
  name?: string;
  /** 外呼电话号码📱 */
  tel?: string | number;
  needClear?: boolean;
  /** 患者id */
  patientId?: number;
}

const props = defineProps<PhoneCallProps>();
const emit = defineEmits<{
  (e: 'call');
}>();
const lineSelectionVisible = ref(false);
const rlySoftphone = useRLYSoftphone();
const { rlyIsLogin, rlyOutboundCall, rlyIsDialing, rlyIsTalking } =
  toRefs(rlySoftphone);

defineOptions({
  name: 'PhoneCall',
});

const userStore = useUserStore();
let useGlobalInfo = useGlobal();
const useCallStore = useCall();

const showHangup = computed(() => {
  return (
    useCallStore.isCall ||
    useCallStore.isCallInRinging === 1 ||
    rlyIsTalking.value ||
    rlyIsDialing.value
  );
});

const callback = () => {
  if (!props.tel) {
    ElMessage.warning(`${props.name}电话号码为空`);
    return;
  }
  useCallStore.getPhoneInfo(props.tel as string);
  window.ClinkAgent?.previewOutcall({ tel: props.tel as string });
  if (props.needClear) {
    useCallStore.tempCallPhone = props.tel as string;
  }
  handleRecord();
};

let handleRecord = () => {
  // 用户角色  医生：ASSISTANT, 健康管理师：CUSTOMER_SERVER, 运动康复师：REHAB, 实习生：INTERN,
  const userRoles = userStore.userRoles;
  let currentRole = userRoles?.[0];
  if (currentRole !== 'INTERN') {
    let userType =
      currentRole === 'ASSISTANT'
        ? 5
        : currentRole === 'CUSTOMER_SERVER'
          ? 3
          : 9;
    let params = {
      patientId: useGlobalInfo.userId,
      userId: userStore.accountId,
      userType,
      recordTime: Number.parseInt(dayjs().format('YYYYMMDD')),
    };
    handleRecordApi(params);
  }
};

/**
 * 线路选择处理
 * @param data 数据
 */
function lineSelectionConfirm(data: {
  line: 1 | 2;
  phone: string;
  outShow?: string;
}) {
  if (data.line === 1) {
    callback();
  } else if (data.line === 2) {
    rlyOutboundCall.value(data.phone, data.outShow);
  }
  emit('call');
}

/**
 * 外呼拨号校验
 */
async function callValidate() {
  if (!props.tel) {
    ElMessage.warning(`${props.name}电话号码为空`);
    return;
  }
  if (!rlyIsLogin.value && !useCallStore.isCallLogin) {
    ElMessage({
      showClose: true,
      message: '请先登录呼叫中心',
      type: 'warning',
    });
  }
  const hasMic = await hasMicrophone();
  if (!hasMic) {
    ElMessage({
      message: '请接入麦克风后重新呼出!',
      showClose: true,
      type: 'warning',
    });
    return;
  }
  if (showHangup.value) {
    ElMessage({
      showClose: true,
      message: '当前正在通话中，请稍后再试',
      type: 'warning',
    });
    return;
  }
  // 2条线路同时登录展示选择线路弹窗
  if (rlyIsLogin.value && useCallStore.isCallLogin) {
    lineSelectionVisible.value = true;
    return;
  }
  // 仅天润联通登录
  if (useCallStore.isCallLogin) {
    callback();
    return;
  }
  // 仅容联云登录
  if (rlyIsLogin.value) {
    lineSelectionVisible.value = true;
  }
}
</script>
