<script setup lang="ts">
import { computed } from 'vue';
import { Base } from './type';

const {
  placeholder,
  options = [],
  mode,
  value,
} = defineProps<
  Pick<Base, 'mode' | 'placeholder'> & {
    /** 选项数据，支持对象数组或字符串数组 */
    options?: Array<{ value: string | number; label: string; disabled?: boolean }> | string[];
    value?: string[] | string | number[] | number;
  }
>();
const emit = defineEmits<{
  change: [val: string[] | string | number[] | number | undefined];
}>();
const model = defineModel<string[] | number[] | string | number | undefined>();

/** 统一处理选项格式：将字符串数组转换为标准对象格式 */
const selectOptions = computed(() => {
  return options.map((item) => {
    if (typeof item === 'string') {
      return { value: item, label: item };
    } else {
      return item;
    }
  });
});

/** 判断当前是否为查看模式 */
const viewMode = computed(() => {
  return mode === 'view';
});

watch(
  () => value,
  () => {
    model.value = value;
  },
);

watch(model, () => {
  emit('change', model.value);
});

/** 查看模式下显示的文本值计算 */
const textValue = computed(() => {
  if (Array.isArray(model.value)) {
    // 处理多选情况
    const labels = model.value.map((item: string | number) => {
      const option = selectOptions.value.find((selectOption) => selectOption.value === item);
      return option?.label || null;
    });
    return labels.filter(Boolean).join('、');
  } else if (['string', 'number'].includes(typeof model.value)) {
    // 处理单选情况
    const option = selectOptions.value.find(
      (selectOption) => selectOption.value === (model.value as string | number),
    );
    return option?.label || '--';
  } else {
    return '--';
  }
});
</script>

<template>
  <div v-if="viewMode" class="break-all">{{ textValue }}</div>
  <el-select v-else v-model="model" :placeholder="placeholder">
    <el-option
      v-for="item in selectOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>
