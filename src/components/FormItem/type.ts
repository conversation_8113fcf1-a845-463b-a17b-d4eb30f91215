export interface Base {
  mode: 'create' | 'edit' | 'view';
  value: any;
  placeholder?: string;
  config?: any;
  options?: {
    key: string;
    value: number;
    label: string;
    [key: string]: any;
  }[];
}

export interface DrugAmount {
  /** 单次用量含量 */
  value: string;
  /** 单次用量单位 */
  unit: string;
}

export interface DrugSpec {
  /** 成分含量 */
  ingredients: string;
  /** 含量单位 */
  contentUnit: string;
  /** 制剂数量 */
  packageNum: string;
  /** 制剂单位 */
  unit: string;
  /** 包装单位 */
  packageUnit: string;
}

export interface Drug {
  /** 药品名称 */
  drugName: string;
  /** 药品用量信息 */
  singleDose: DrugAmount;
  /** 药品规格信息 */
  drugSpec: DrugSpec;
  /** 服药方法 */
  method: string;
  /** 服药频率 */
  frequency: string;
  /** 服药时间 */
  timing: string;
}
