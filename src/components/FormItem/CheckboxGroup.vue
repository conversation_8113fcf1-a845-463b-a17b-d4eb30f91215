<template>
  <div v-if="mode === 'view'">{{ viewText || '--' }}</div>
  <el-checkbox-group v-else :model-value="value" @change="checkboxChange">
    <el-checkbox
      v-for="v in options"
      :key="v.key"
      :value="v.key"
      @click="() => radioClickHandler(v.key)"
    >
      {{ v.value }}
    </el-checkbox>
  </el-checkbox-group>
</template>

<script setup lang="ts">
import { Base } from './type';

interface IProps extends Base {
  suffixText?: string;
}
const props = defineProps<IProps>();
const emit = defineEmits(['click', 'change']);

const checkboxChange = val => {
  emit('change', val);
};

const viewText = computed(() => {
  const { value, options, suffixText } = props;
  const res = options?.find(v => v.key === value)?.value;
  return suffixText ? res + ' - ' + suffixText : res;
});

const radioClickHandler = val => {
  emit('click', val);
};

defineOptions({
  name: 'CheckboxGroup',
});
</script>
