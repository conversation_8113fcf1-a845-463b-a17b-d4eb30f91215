<template>
  <div v-if="mode === 'view'">
    <div class="break-all">{{ value?.[keys[0]] || '--' }}</div>
    <DiseaseTag :data="viewTags" show-main-flag />
  </div>
  <BaseSelector
    v-else
    ref="baseSelectorRef"
    :title="title ?? 'title'"
    :category="camelToSnake(category ?? 'chiefComplaint') as ICategory"
    :list="formatList"
    :text-content="value?.[keys[0]] ?? ''"
    :selected-data="getFlatIdList(value?.[keys[1]] ?? [])"
    @on-change="getStructuredData"
  />
</template>

<script setup lang="ts">
import DiseaseTag from '@/components/HospitalForm/components/diseaseTag.vue';
import BaseSelector, {
  ICategory,
} from '@/components/HospitalForm/BaseSelector.vue';
import { Base } from './type';
import { transformList } from './utils';
import { idsTransform, getDataMap } from '@/components/StructuredSelector/util';
import { keyBy } from 'lodash-es';
import { formatTreeData, getFlatIdList } from './utils';
interface Props extends Base {
  title?: string;
  category: ICategory;
  list?: any[];
  keys: [string, string];
}
const props = defineProps<Props>();
const emit = defineEmits(['change']);

const formatList = computed(() => transformList(props.list ?? [], 'value'));
const baseSelectorRef = ref<InstanceType<typeof BaseSelector> | null>(null);
const listMap = computed(() => keyBy(props.list ?? [], 'id'));
const getStructuredData = data => {
  const { keys } = props;
  const res = formatTreeData(data.resultTreeData, listMap.value);
  const result = {
    [keys[0]]: data.content,
    [keys[1]]: res,
  };
  emit('change', result);
};

const viewTags = computed(() => {
  const { mode, value, keys } = props;
  if (mode !== 'view') return [];
  const dataMap = getDataMap(formatList.value);
  const ids = getFlatIdList(value?.[keys[1]])?.map(v => v.id);
  const res = idsTransform(ids, dataMap);
  const { showNameList } = res;
  return showNameList.map(v => v.text);
});
const camelToSnake = (camelStr: string) => {
  let res = camelStr.replace(/[A-Z]/g, function (match) {
    return '_' + match.toLowerCase();
  });
  if (res.startsWith('_')) {
    res = res.slice(1);
  }
  return res;
};

defineExpose({
  getDiseaseInfo: () => baseSelectorRef.value?.getDiseaseInfo(),
});

defineOptions({
  name: 'OcrText',
});
</script>

<style scoped lang="less"></style>
