import { keyBy } from 'lodash-es';

const chooseTypeMap = {
  single: 'radio',
  multiple: 'checkbox',
  title: 'title',
};
export const formatTreeData = (list: any = [], listMap) => {
  const res: any[] = [];
  for (const v of list) {
    const cur = listMap[v.id];
    const item = {
      id: v.id,
      key: cur.key,
      value: cur.value,
      old_id: cur?.uiRules?.old_id,
    };
    if (v.children?.length) {
      const child = formatTreeData(v.children, listMap);
      item['items'] = child;
    }
    res.push(item);
  }
  return res;
};

export const getFlatIdList = (treeList: any[] = []) => {
  if (!treeList) return [];
  const res: any[] = [];
  for (const v of treeList) {
    res.push({ id: v.id });
    if (v.items) {
      const child = getFlatIdList(v.items);
      res.push(...child);
    }
  }
  return res;
};

export const convertArrToTree = (arr: any[], pId = null) => {
  const res: any[] = [];
  const map = keyBy(arr, 'id');
  for (const v of arr) {
    if (v.pId === pId) {
      res.push(v);
    } else {
      if (!map[v.pId]) continue;
      if (map[v.pId].children) {
        map[v.pId].children.push(v);
        map[v.pId].originChildren.push(v);
      } else {
        map[v.pId].children = [v];
        map[v.pId].originChildren = [v];
      }
    }
  }
  return res;
};

export const transformList = (list: any[] = [], diseaseNameKey = 'keyword') => {
  const newList = list.map(v => {
    return {
      id: v.id,
      pId: v.id === v.pid ? null : v.pid,
      key: v.key,
      diseaseName: v[diseaseNameKey],
      diseaseId: v.id,
      chooseType: chooseTypeMap[v.uiMethod] ?? 'checkbox',
      children: [],
      originChildren: [],
    };
  });
  return convertArrToTree(newList);
};
