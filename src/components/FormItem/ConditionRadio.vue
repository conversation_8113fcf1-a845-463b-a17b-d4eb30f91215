<template>
  <div v-if="mode === 'view'">{{ viewText || '--' }}</div>
  <div v-else>
    <el-radio-group :model-value="innerValue.value" @change="radioChange">
      <el-radio
        v-for="v in radioOptions"
        :key="v.key"
        :value="v.key"
        @click="() => radioClickHandler(v.key)"
      >
        {{ v.value }}
      </el-radio>
    </el-radio-group>
    <div v-if="conditionOption">
      <DatePicker
        v-if="
          conditionOption.uiMethod === 'date' &&
          innerValue.value === conditionOption.uiRules.ifVisible
        "
        :mode="mode"
        :value="innerValue[conditionOption.key]"
        :type="conditionOption.uiOptions?.type"
        :format="conditionOption.uiRules?.dateFormat"
        :value-format="conditionOption.uiRules?.dateValueFormat"
        enable-future-date
        @change="val => innerDataChange(conditionOption, val)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Base } from './type';
import DatePicker from './DatePicker.vue';
interface IProps extends Base {
  list: any;
}
const props = defineProps<IProps>();
const emit = defineEmits(['click', 'change']);
const innerValue = ref({
  value: '',
});

const radioOptions = computed(() => {
  return props.list?.filter(v => !v.uiRules);
});
const conditionOption = computed(() => {
  return props.list?.filter(v => v.uiRules)?.[0];
});
const emitChange = () => {
  emit('change', { ...innerValue.value });
};
const radioChange = val => {
  innerValue.value.value = val;
  emitChange();
};
const innerDataChange = (item, val) => {
  innerValue.value[item.key] = val;
  emitChange();
};

const viewText = computed(() => {
  const { value } = props;
  if (!value) return '--';
  const res = radioOptions.value?.find(v => v.key === value?.value)?.value;
  return res;
});

const radioClickHandler = val => {
  emit('click', val);
};
watch(
  () => props.value,
  val => {
    innerValue.value = val;
  }
);
defineOptions({
  name: 'ConditionRadio',
});
</script>
