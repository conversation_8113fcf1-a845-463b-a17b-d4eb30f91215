<template>
  <div v-if="mode === 'view'">{{ viewText }}</div>
  <div v-else class="date-box" style="width: 360px">
    <DatePicker
      :placeholder="
        placeholder ?? (type === 'datetime' ? '选择时间' : '选择日期')
      "
      :value="value"
      :type="type ?? 'date'"
      :format="format ?? 'YYYY-MM-DD'"
      :value-format="valueFormat ?? null"
      :disabled-date="enableFutureDate ? null : datePickerOption"
      @change="dateChangeHandler"
    />
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import DatePicker from '@/components/DateTimePicker/index.vue';
import { Base } from './type';

interface IProps extends Base {
  valueFormat?: string;
  format?: string;
  type?: 'date' | 'datetime';
  placeholder?: string;
  enableFutureDate?: boolean;
}
const props = defineProps<IProps>();
const emit = defineEmits(['change']);

const viewText = computed(() => {
  const { format = 'YYYY-MM-DD', value } = props;
  if (value) {
    return dayjs(value).format(format);
  } else {
    return value || '--';
  }
});

const datePickerOption = (time: Date) => {
  return time.getTime() > Date.now();
};

const dateChangeHandler = (val: string | number) => {
  const { valueFormat } = props;
  const newVal = valueFormat ? dayjs(val).format(valueFormat) : val;
  emit('change', newVal);
};

defineOptions({
  name: 'DatePicker',
});
</script>

<style scoped lang="less">
.date-box {
  position: relative;
  :deep(.datepicker) {
    .el-input__prefix {
      display: none;
    }
  }
  :deep(.el-date-editor.el-input__wrapper) {
    width: 100%;
  }
  .change-time-icon {
    position: absolute;
    top: 8px;
    right: 14px;
  }
}
</style>
