<template>
  <div class="w-full">
    <BaseTable
      v-if="visibleColumns?.length"
      :data="tabelFormData"
      :pagination="false"
    >
      <el-table-column
        v-for="(column, colIndex) in visibleColumns"
        :key="column.key"
        :prop="column.key"
        :label="column.value"
        :width="column.uiOptions?.columnWidth"
      >
        <template #default="scope">
          <el-select
            v-if="scope.row[colIndex]?.uiMethod === 'control-select'"
            :model-value="finalData?.[scope.$index]?.[scope.cellIndex]?.value"
            :placeholder="column.uiOptions?.placeholder ?? '请选择'"
            filterable
            :style="{ width: column.uiOptions?.selectWidth }"
            class="control-select"
            @change="val => changeHandler(val, scope.$index, scope)"
          >
            <el-option
              v-for="item in originSelectOptions"
              :key="item.key"
              :disabled="finalData.some(v => v[0].key === item.key)"
              :label="item.label"
              :value="item.key"
            />
          </el-select>
          <el-select
            v-if="column.uiMethod === 'select'"
            :model-value="finalData?.[scope.$index]?.[scope.cellIndex]?.value"
            :placeholder="column.uiOptions?.placeholder ?? '请选择'"
            filterable
            :style="{ width: column.uiOptions?.selectWidth ?? '100px' }"
            class="normal-select"
            @change="val => changeHandler(val, scope.$index, scope)"
          >
            <el-option
              v-for="(item, index) in column.uiOptions?.selectOptions"
              :key="
                column.uiOptions?.valueKey
                  ? item[column.uiOptions?.valueKey]
                  : index
              "
              :label="
                column.uiOptions?.labelKey
                  ? item[column.uiOptions?.labelKey]
                  : item
              "
              :value="
                column.uiOptions?.valueKey
                  ? item[column.uiOptions?.valueKey]
                  : item
              "
            />
          </el-select>
          <TextInput
            v-if="scope.row[colIndex]?.uiMethod === 'input'"
            :mode="props.mode"
            :min="scope.row[colIndex]?.uiRules?.maxlength"
            :placeholder="column.uiOptions?.placeholder ?? '请输入'"
            :value="finalData?.[scope.$index]?.[scope.cellIndex]?.value"
            @change="val => changeHandler(val, scope.$index, scope)"
          />
          <el-checkbox
            v-if="scope.row[colIndex]?.uiMethod === 'checkbox'"
            :model-value="finalData?.[scope.$index]?.[scope.cellIndex]?.value"
            @change="val => changeHandler(val, scope.$index, scope)"
          />
          <InputNumber
            v-if="scope.row[colIndex]?.uiMethod === 'inputNumber'"
            :model-value="scope.row[colIndex].value"
            :max="scope.row[colIndex]?.uiRules?.max"
            :min="scope.row[colIndex]?.uiRules?.min"
            :step="scope.row[colIndex]?.uiRules?.step"
            :precision="scope.row[colIndex]?.uiRules?.precision"
            :control="scope.row[colIndex]?.uiOptions?.control"
            :value="finalData?.[scope.$index]?.[scope.cellIndex]?.value"
            :placeholder="column.uiOptions?.placeholder ?? '请输入'"
            :mode="props.mode"
            @change="val => changeHandler(val, scope.$index, scope)"
          />
          <div
            v-if="scope.row[colIndex]?.uiMethod === 'text'"
            class="normal-select"
          >
            {{ scope.row[colIndex].value }}
          </div>
        </template>
      </el-table-column>
      <el-table-column key="'operation'" label="操作" width="80">
        <template #default="scope">
          <span class="icon" @click="() => addRow(scope)">
            <el-icon>
              <i-ep-plus />
            </el-icon>
          </span>
          <span
            v-if="finalData.length > 1"
            class="icon ml-8"
            @click="() => deleteRow(scope)"
          >
            <el-icon>
              <i-ep-close />
            </el-icon>
          </span>
        </template>
      </el-table-column>
    </BaseTable>
    <el-button
      v-if="finalData.length === 0"
      class="mt-8"
      type="primary"
      link
      @click="() => addEmptyRow()"
    >
      新增+
    </el-button>
  </div>
</template>

<script setup lang="ts">
import BaseTable from '@/components/BaseTable';
import InputNumber from '@/components/FormItem/InputNumber.vue';
import TextInput from '@/components/FormItem/TextInput.vue';
import { ElOption } from 'element-plus';
import { groupBy, isEmpty, isEqual } from 'lodash-es';
import { Column, ItemItem } from '../FormList/types';
import { Base } from './type';

interface IProps extends Base {
  columns: Column[];
  items?: ItemItem[];
  type?: 'drug';
  placeholder?: string;
  /** UI配置 */
  uiOptions?: Record<string, boolean | string>;
}

const props = defineProps<IProps>();
const emit = defineEmits(['change']);

const originSelectOptions = ref<any[]>([]);
const tabelFormData = ref<any[]>([]);
const emptyRow = ref<any>({});
const finalData = ref<Record<string, any>[]>([]);

// 过滤不需要的列,然后根据rowOrder排序
const visibleColumns = computed(() =>
  props.columns
    ?.filter(v => !v?.uiDisable)
    .sort((a, b) => a?.rowOrder - b?.rowOrder)
);
const getEmptyRowValue = () => {
  return Array(visibleColumns.value.length)
    .fill(0)
    .map(() => ({ key: '', value: '' }));
};
const addEmptyRow = (index?: number) => {
  const _emptyRow = [...emptyRow.value];
  const _emptyData = getEmptyRowValue();
  if (index !== undefined) {
    tabelFormData.value.splice(index, 0, _emptyRow);
    finalData.value.splice(index, 0, _emptyData);
  } else {
    tabelFormData.value.push(_emptyRow);
    finalData.value.push(_emptyData);
  }
};

const addRow = row => {
  const { $index } = row;
  addEmptyRow($index + 1);
};
const deleteRow = row => {
  const { $index } = row;
  tabelFormData.value.splice($index, 1);
  finalData.value.splice($index, 1);
};

// 表格数据变化时，更新表单数据
const changeHandler = (val: any, rowIndex: number, row: any) => {
  const { cellIndex } = row;
  // 如果值没有变化，不更新
  if (finalData.value[rowIndex][cellIndex].value !== val) {
    finalData.value[rowIndex][cellIndex].value = val;
    if (cellIndex === 0) {
      finalData.value[rowIndex] = finalData.value[rowIndex].map(v => {
        v.key = val;
        return v;
      });
    } else {
      finalData.value[rowIndex][cellIndex].key =
        finalData.value[rowIndex][0].key;
    }
  }
};

watch(
  finalData,
  () => {
    emit('change', finalData.value);
  },
  { deep: true }
);

const setTableFormData = () => {
  if (!props.value?.length) {
    addEmptyRow();
  } else {
    tabelFormData.value = Array(props.value.length)
      .fill(0)
      .map(() => emptyRow.value);
  }
};
const init = () => {
  const rows = Object.values(groupBy(props.items, 'rowId')).map(row =>
    row.sort((a, b) => (a?.rowOrder ?? 0) - (b?.rowOrder ?? 0))
  );
  const options = rows.map(v => {
    return {
      key: v[0].key,
      label: v[0].value,
    };
  });
  originSelectOptions.value = options;
  if (rows.length) {
    const resFields = rows[0].slice(1);
    emptyRow.value = [
      { uiMethod: 'control-select', options: originSelectOptions },
      ...resFields,
    ];
    setTableFormData();
  }
};
watch(
  () => props.value,
  () => {
    if (!isEqual(finalData.value, props.value)) {
      if (!isEmpty(props.value)) {
        finalData.value = props.value;
        setTableFormData();
      }
    }
  },
  { immediate: true }
);
watch(
  [() => props.items, () => props.columns],
  () => {
    init();
  },
  { immediate: true }
);

defineOptions({
  name: 'AddTableForm',
});
</script>

<style scoped lang="less">
.render-item {
  display: flex;
  justify-content: space-between;

  span {
    flex: 1;
    text-align: left;
  }

  span:not(:first-child) {
    margin-left: 20px;
  }
}
.icon {
  cursor: pointer;
}
</style>
