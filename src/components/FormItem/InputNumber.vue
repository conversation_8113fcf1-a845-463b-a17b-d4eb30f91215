<template>
  <div class="inline-flex">
    <div v-if="mode === 'view'" :style="style" class="break-all">
      {{ value || '--' }}
    </div>
    <el-input
      v-else
      :model-value="value"
      class="data-input"
      :placeholder="placeholder"
      :style="style"
      @input="changeHandler"
      @blur="() => calculate(0)"
    >
      <template v-if="control" #suffix>
        <div class="number-input-icon">
          <div class="icon-block" @click="() => calculate(1)">
            <el-icon class="cursor-pointer">
              <CaretTop />
            </el-icon>
          </div>
          <div class="icon-block" @click="() => calculate(-1)">
            <el-icon class="cursor-pointer">
              <CaretBottom />
            </el-icon>
          </div>
        </div>
      </template>
    </el-input>
    <span v-if="unit" class="ml-12 inline-flex items-center min-w-40">
      {{ unit }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { CaretBottom, CaretTop } from '@element-plus/icons-vue';
import Big from 'big.js';
import { CSSProperties } from 'vue';
import { Base } from './type';

interface IProps extends Base {
  precision?: number;
  min?: number;
  max?: number;
  step?: number;
  /** 是否使用控制按钮 */
  control?: boolean;
  /** 单位 */
  unit?: string;
  /** 自定义样式 */
  style?: CSSProperties;
}

const {
  value,
  min = 0,
  max = 99999,
  step = 1,
  precision,
  control = false,
} = defineProps<IProps>();
const emit = defineEmits(['change']);

function changeHandler(val: string) {
  let _val = val;
  const regStr = `^\\d*(\\.?\\d{0,${precision ?? 3}})`;
  const reg = new RegExp(regStr, 'g');
  if (precision === 0) {
    _val = _val.replace('.', '');
  }
  const res = _val
    ?.replace(/[^\d^.]+/g, '')
    ?.replace(/^0+(\d)/, '$1')
    ?.replace(/^\./, '0.')
    ?.match(reg)?.[0];
  if (res) {
    emit('change', res);
  } else {
    emit('change', undefined);
  }
}

function calculate(op: number) {
  if (op === 0 && !value) return value;
  // 计算步进值
  const val = step * op;
  // 使用 Big.js 处理数值计算，避免 JavaScript 浮点数精度问题
  // 如果 value 为空则默认为 0，然后加上步进值
  const addend = new Big(value || 0).plus(val).toNumber();

  // 确保结果在最小值和最大值的范围内
  const res = Math.max(min, Math.min(max, addend));

  // 触发变更事件，将计算结果传递给父组件
  emit('change', res);
}

defineOptions({
  name: 'InputNumber',
});
</script>

<style lang="less">
.data-input {
  width: 90px;

  .el-input__inner {
    font-size: 14px;
    color: #203549;
    border-radius: 2px;
  }

  .el-input__suffix {
    width: 22px;
    right: 0;
  }
}

.number-input-icon {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 24px;
  margin-left: 12px;

  .icon-block {
    flex: 1;
    width: 100%;
    background-color: #f7f8fa;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border: 1px solid #dcdee0;
  }
}
</style>
