<template>
  <div class="wrap">
    <span v-for="(item, index) in validData" :key="item" class="item">
      <span v-if="showMainFlag && !index" class="main">主</span>
      <span class="max-w-[450px]">
        <Text>{{ item }}</Text>
      </span>
    </span>
  </div>
</template>

<script setup lang="ts">
import Text from '@/components/Text/index.vue';

interface IProps {
  data: string[];
  showMainFlag?: boolean;
}

const props = defineProps<IProps>();
const validData = computed(() => props.data.filter(v => !!v));
defineOptions({
  name: 'DiseaseTag',
});
</script>

<style scoped lang="less">
.wrap {
  margin-top: 4px;
  flex-wrap: wrap;
  display: flex;
}
.item {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  height: 28px;
  background: #f7f8fa;
  border-radius: 2px;
  border: 1px solid #dcdfe6;
  color: #2e6be6;
  margin-right: 8px;
  margin-bottom: 4px;
  padding: 0 8px;
  font-size: 14px;
  .main {
    width: 16px;
    height: 16px;
    background: #2e6be6;
    border-radius: 2px;
    font-size: 8px;
    color: #fff;
    margin-right: 8px;
    text-align: center;
    line-height: 16px;
  }
}
</style>
