//主诉
export interface SymptomInputProps {
  content: string;
  placeholder?: string;
  ocr?: boolean;
  structedDisabled?: boolean;
}
export interface SuitItem {
  time?: string;
  /** 单位 */
  unit?: string;
  /** 症状名称 */
  name?: string;
  /** 症状id */
  id?: number;
  remark?: string;
}

export interface SelectDiseaseItem {
  id: number;
  name: string;
  time?: string;
  unit?: string;
  disabled: boolean;
}

export interface TypeSelectSuitTime {
  value: string;
}
export interface TypeSelectTimeUnit {
  label: string;
}

//个人史禁用选项
export interface TypePersonDisease {
  name: string;
  type: string;
  paramsType: number | null;
  disable: boolean;
  isSmoke?: string;
  isDrink?: string;
}

export interface TypeSmokeInfo {
  /** type:1目前 2既往 */
  type?: number | null;
  /** 年 */
  year?: string;
  /** 吸烟多少支 */
  branch?: string;
}

export interface TypeDrinkInfo {
  /** type: 0既往 1目前 */
  type?: number | null;
  /** 年 */
  year?: string;
  /** 啤酒量 */
  beer?: string;
  /** 白酒量 */
  liquor?: string;
}
export interface TypeAddSmokeInfo {
  isSmoke?: number | null;
  info?: TypeSmokeInfo;
}

export interface TypeAddDrinkInfo {
  isDrink?: number | null;
  info?: TypeDrinkInfo;
}

export interface TypeFamilyDiseaseItem {
  name: string;
  diseaseHistory: number;
  remark: string;
  disable: boolean;
}

export interface TypeAddFamilyDiseaseItem {
  diseaseHistory?: number;
  remark?: string;
}

type BasicInfoKey =
  | 'height'
  | 'weight'
  | 'highPressure'
  | 'lowPressure'
  | 'heartRate';

export interface TypeBodyInfoItem {
  name: string;
  unit: string;
  infoKey: BasicInfoKey;
}

// 使用模板字符串模板类型声明对象
export type TypeBaseInfo = {
  [key in BasicInfoKey]?: string | undefined;
};

export interface TypeHistoryDiseaseItem {
  diseaseId?: number;
  diseaseName?: string;
  pid?: number | null;
  remark?: string | null;
}

export interface IStructItem {
  id: number;
  key: string;
  value: string;
}

export interface BaseProps {}
