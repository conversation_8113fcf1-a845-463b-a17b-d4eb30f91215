<template>
  <div class="nopx-container">
    <el-scrollbar v-if="!hideTags" max-height="120">
      <TagList
        :list="innerShowNameList"
        @delete-item="deleteHandler"
        @sort="sortHandler"
      />
    </el-scrollbar>
    <div v-if="enableFilter" class="search">
      <el-input
        v-model="searchWords"
        size="default"
        placeholder="输入搜索内容"
        @input="searchChange"
      />
    </div>
    <div class="content-wrapper">
      <el-scrollbar v-if="data.length">
        <div :key="refreshKey" class="content">
          <TreeNode
            v-for="(item, index) in data"
            :key="item.diseaseId + '-' + index"
            :search-words="searchWords"
            :selected-ids="choosedIds"
            :node-data="item"
            :radio-clearable="radioClearable"
            :data-map="dataMap"
            :item-layout="itemLayout"
            @on-change="onChange"
          />
        </div>
      </el-scrollbar>
      <div v-else class="empty">暂无数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { cloneDeep, debounce } from 'lodash-es';
import TreeNode from './TreeNode.vue';
import { idsTransform, treeDataFilter, getSortedIds } from '../util';
import {
  ICheckListProps,
  IDiseaseList,
  IShowNameList,
  IPathItem,
} from '../type';
import TagList from './TagList.vue';
defineOptions({
  name: 'CheckList',
});

const props = withDefaults(defineProps<ICheckListProps>(), {
  diseaseList: () => [],
  enableFilter: () => true,
  itemLayout: () => 'block',
  hideTags: () => false,
});
const emit = defineEmits(['onChange', 'onClose']);
const searchWords = ref('');
const originData = ref<IDiseaseList>([]);
const data = ref<IDiseaseList>([]);
const refreshKey = ref(1);
const choosedIds = ref<number[]>([]);
const tempChooseIds = ref<number[]>([]);
const innerShowNameList = ref<IShowNameList['showNameList']>([]);
const innerSelectedPath = ref<IShowNameList['selectedPath']>([]);
const record = ref({
  num: 0,
  sum: 0,
});

const rebuildInnerData = (newIds: number[]) => {
  const { showNameList, selectedPath } = idsTransform(newIds, props.dataMap);
  innerShowNameList.value = showNameList;
  innerSelectedPath.value = selectedPath;
};
watchEffect(() => {
  record.value.num = props.num;
});
watchEffect(() => {
  const { diseaseData } = props;
  const { diseaseIds } = diseaseData;
  if (diseaseIds) {
    tempChooseIds.value = diseaseIds;
    choosedIds.value = diseaseIds;
    rebuildInnerData(diseaseIds);
  }
});
watchEffect(() => {
  const { diseaseList, dataMap } = props;
  if (diseaseList) {
    record.value.sum = Object.keys(dataMap).length;
    originData.value = cloneDeep(diseaseList);
    data.value = diseaseList;
  }
});

const sortHandler = () => {
  choosedIds.value = getSortedIds(
    innerShowNameList.value,
    innerSelectedPath.value
  );
  confirm();
};
const deleteHandler = (item: IPathItem) => {
  const filterPath = innerSelectedPath.value?.filter(
    v => v[v.length - 1].id !== item.id
  );
  const filterPathIds = filterPath?.map(v => v.map(v => v.id)).flat();
  const newIds = [...new Set(filterPathIds)];
  choosedIds.value = newIds;
  rebuildInnerData(newIds);
  confirm();
};
const searchChange = debounce(val => {
  const res = treeDataFilter(originData.value, val);
  data.value = res;
  refreshKey.value += 1;
}, 100);
const onChange = (ids: number[]) => {
  choosedIds.value = ids;
  rebuildInnerData(ids);
  confirm();
};
const confirm = () => {
  const ids = choosedIds.value;
  const { showNameList, selectedPath, resultTreeData } = idsTransform(
    ids,
    props.dataMap
  );
  record.value.num = showNameList.length;
  const params = {
    ids,
    selectedPath,
    showNameList,
    resultTreeData,
  };
  emit('onChange', params);
};
const reset = () => {
  searchWords.value = '';
  searchChange('');
  choosedIds.value = tempChooseIds.value;
  rebuildInnerData(choosedIds.value);
};

defineExpose({
  reset,
});
</script>

<style scoped lang="less">
.nopx-container {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  .header {
    display: flex;
    height: 32px;
    padding-bottom: 12px;
    align-items: center;
    border-bottom: 1px solid #dcdee0;
    font-weight: bold;
    font-size: 14px;
    > div {
      flex: 1;
    }
    > i {
      cursor: pointer;
    }
  }
  .search {
    padding: 0 24px 0;
  }
  .content {
    max-height: calc(100% - 100px);
    padding: 12px 0;
    display: flex;
    padding-bottom: 20px;
    flex-wrap: wrap;
    overflow-y: auto;
    align-items: flex-start;
  }
  .empty {
    padding: 48px 0;
    text-align: center;
  }
  .closeIcon {
    cursor: pointer;
  }
}
.content-wrapper {
  min-height: 200px;
  height: 520px;
}
</style>
