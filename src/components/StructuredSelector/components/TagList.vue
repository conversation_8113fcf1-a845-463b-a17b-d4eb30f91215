<template>
  <div
    class="tag-box box-border flex flex-wrap"
    :style="{ paddingBottom: '8px' }"
  >
    <DraggableList :list="list" @end="sortEnd">
      <template #item="{ item, index }">
        <div class="tag box-border flex items-center py-4 px-8">
          <span v-if="index === 0" class="main-tag">主</span>

          <span>{{ item.text }}</span>

          <el-icon :size="16" class="cursor-pointer ml-8">
            <i-ep-close @click="deleteHandler(item)" />
          </el-icon>
        </div>
      </template>
    </DraggableList>
  </div>
</template>

<script setup lang="ts">
import DraggableList from '@/components/DraggableList/index.vue';
interface IProps {
  list: {
    id: number;
    text: string;
  }[];
}

const emits = defineEmits(['deleteItem', 'sort']);
defineProps<IProps>();

const deleteHandler = item => {
  emits('deleteItem', item);
};

const sortEnd = item => {
  emits('sort', item);
};
defineOptions({
  name: 'TagList',
});
</script>

<style scoped lang="less">
.tag-box {
  width: 100%;
  .tag {
    font-size: 14px;
    font-weight: 400;
    color: #0a73e4;
    background-color: white;
    border-radius: 2px;
    border: 1px solid #dcdee0;
  }
  .main-tag {
    width: 16px;
    height: 16px;
    font-size: 10px;
    color: white;
    box-sizing: border-box;
    background-color: #2e6be6;
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 4px;
  }
}
</style>
