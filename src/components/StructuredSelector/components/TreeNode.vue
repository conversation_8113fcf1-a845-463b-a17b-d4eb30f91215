<template>
  <div
    class="nopx-node-content"
    :class="{ 'has-children': hasChild || checkType('title') }"
    :style="{
      width:
        (checkType('radio') || itemLayout === 'inline') && !hasChild
          ? 'auto'
          : '',
    }"
  >
    <div
      v-if="checkType('title')"
      class="item"
      :style="{
        width: hasChild ? '100%' : 'auto',
        padding: '0 24px',
        background: level === 0 && hasChild ? '#f7f8fa' : '',
      }"
    >
      <div class="title">
        {{ node.diseaseName }}
      </div>
    </div>
    <div
      v-if="checkType('checkbox')"
      class="item"
      :style="{
        width: hasChild ? '100%' : 'auto',
        padding: '0 24px',
        background: level === 0 && hasChild ? '#f7f8fa' : '',
      }"
    >
      <el-checkbox
        :key="node.diseaseId"
        :model-value="selectedIds.includes(node.diseaseId)"
        @change="checkboxChange"
      >
        {{ node.diseaseName }}
      </el-checkbox>
    </div>
    <div
      v-if="checkType('radio')"
      class="item"
      :style="{
        width: hasChild ? '100%' : 'auto',
        'padding-left': '24px',
        background: level === 0 && hasChild ? '#f7f8fa' : '',
      }"
    >
      <el-radio
        :label="node.diseaseId"
        :model-value="
          selectedIds.includes(node.diseaseId) ? node.diseaseId : ''
        "
        @change="() => radioChange(node.diseaseId)"
        @click.stop="() => clickRadioHandler(node.diseaseId)"
      >
        {{ node.diseaseName }}
      </el-radio>
    </div>
    <div class="item-children" :style="{ 'padding-left': 24 + 'px' }">
      <TreeNode
        v-for="(item, index) in node?.children"
        :key="item.diseaseId + '-' + index"
        :search-words="searchWords"
        :selected-ids="selectedIds"
        :node-data="item"
        :radio-clearable="radioClearable"
        :data-map="dataMap"
        :is-first="index === 0"
        :level="level + 1"
        :item-layout="itemLayout"
        @inner-change="onChangeHandler"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep, pullAll } from 'lodash-es';
import {
  getAfterIds,
  getForwardIds,
  getRelativeIds,
  idsTransform,
} from '../util';
import { ITreeNodeProps, IDiseaseItem, IPathItem } from '../type';
defineOptions({
  name: 'TreeNode',
});
const node = ref<IDiseaseItem>({} as IDiseaseItem);
const emit = defineEmits(['onChange', 'innerChange']);
const props = withDefaults(defineProps<ITreeNodeProps>(), {
  isFirst: () => true,
  level: () => 0,
});

watch(
  props.nodeData,
  newVal => {
    node.value = cloneDeep(newVal);
  },
  { immediate: true }
);
const hasChild = computed(
  () => node.value.originChildren && node.value.originChildren.length > 0
);
const checkType = computed(
  () => (type: string) => node.value.chooseType === type
);
const getValidIds = (path: IPathItem[][], val) => {
  const newPath = path.filter(v => v[v.length - 1].id !== val);
  const curPath = path.find(v => v[v.length - 1].id === val) ?? [];
  const curPathPickIds = curPath
    .filter(v => v.id !== val && props.dataMap[v.id].chooseType !== 'title')
    .map(v => v.id);
  const ids = newPath.map(v => v.map(v => v.id)).flat();
  const newIds = [...new Set([...ids, ...curPathPickIds])];
  return newIds;
};
const checkboxChange = (val: boolean) => {
  const { diseaseId } = props.nodeData;
  let ids = [...props.selectedIds];
  if (val) {
    const forwardIds = getForwardIds(diseaseId, props.dataMap);
    const forwardOmitIds: number[] = [];
    for (let v of forwardIds) {
      const item = props.dataMap[v];
      if (item.chooseType === 'radio') {
        forwardOmitIds.push(...getRelativeIds(item, props.dataMap));
      }
    }
    pullAll(ids, forwardOmitIds);
    ids.push(diseaseId, ...forwardIds);
  } else {
    const afterIds = getAfterIds(node.value);
    pullAll(ids, afterIds);
    const { selectedPath } = idsTransform([...ids, diseaseId], props.dataMap);
    const newIds = getValidIds(selectedPath, diseaseId);
    ids = newIds;
  }
  onChangeHandler(ids);
};
const clickRadioHandler = (val: number) => {
  if (props.radioClearable && props.selectedIds.includes(val)) {
    const { selectedPath } = idsTransform(props.selectedIds, props.dataMap);
    const newIds = getValidIds(selectedPath, val);
    setTimeout(() => {
      onChangeHandler(newIds);
    }, 20);
  }
};
const radioChange = (val: number) => {
  const newIds = [...props.selectedIds, val];
  const omitIds = getRelativeIds(node.value, props.dataMap);
  const forwardIds = getForwardIds(val, props.dataMap);
  const forwardOmitIds: number[] = [];
  for (let v of forwardIds) {
    const item = props.dataMap[v];
    if (item.chooseType === 'radio') {
      forwardOmitIds.push(...getRelativeIds(item, props.dataMap));
    }
  }

  pullAll(newIds, [...omitIds, ...forwardOmitIds]);
  onChangeHandler([...newIds, ...forwardIds]);
};
const onChangeHandler = (ids: number[]) => {
  const resIds = [...new Set(ids)];
  if (props.level === 0) {
    emit('onChange', resIds);
  } else {
    emit('innerChange', resIds);
  }
};
</script>

<style scoped lang="less">
.nopx-node-content {
  // width: 50%;
  box-sizing: border-box;
  .title {
    font-size: 14px;
    color: #323233;
    height: 35px;
    line-height: 35px;
    font-weight: bold;
  }
  .item {
    height: 35px;
    line-height: 35px;
  }
  .item-children {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }
}
.has-children {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
</style>
