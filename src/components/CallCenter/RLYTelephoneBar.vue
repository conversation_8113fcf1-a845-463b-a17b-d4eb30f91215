<script setup lang="ts">
import { signInMoor } from '@/api/addressBook';
import callTwo from '@/assets/icons/call-two.svg';
import callSvg from '@/assets/imgs/callCenter/call.svg';
import logoutSvg from '@/assets/imgs/callCenter/logout.svg';
import settingsBlackSvg from '@/assets/imgs/callCenter/settings-black.svg';
import { RLYAgentStatus, RLYLoginType } from '@/constant';
import { useRLYSoftphone } from '@/store/module/useRLYSoftphone';
import { formatSecondsToTime } from '@/utils';
import { toRefs } from '@vueuse/core';
import RLYSettingDialog from './RLYSettingDialog.vue';

const rlySoftphone = useRLYSoftphone();
const {
  rlyIsLogin,
  rlyInit,
  rlyCounter,
  rlyLogout,
  rlyLoginType,
  rlyAgentStatus,
  rlyIsTalking,
  rlyTalkingCounter,
  canLogout,
} = toRefs(rlySoftphone);
const rlySettingsVisible = ref(false);

async function loginTel() {
  rlyInit.value();
}

/**
 * 容联云设置
 * @param data
 */
function rlySettingsChange(data: {
  loginType: RLYLoginType;
  statusValue: RLYAgentStatus;
}) {
  console.log('data', data);
  rlySettingsVisible.value = false;
  if (data.statusValue !== rlyAgentStatus.value) {
    rlySoftphone.rlyChangeAgentStatus(data.statusValue);
  }
  if (data.loginType !== rlyLoginType.value) {
    rlySoftphone.rlyChangeLoginType(data.loginType);
  }
}

window.addEventListener('beforeunload', () => {
  if (rlySoftphone.rlyIsLogin) {
    signInMoor(rlySoftphone.rlyAgentNumber);
  }
});
</script>

<template>
  <div
    v-if="!rlyIsLogin"
    class="login-wrapper"
    role="button"
    @click="loginTel()"
  >
    <img class="size-16 cursor-pointer mr-8" :src="callTwo" alt="" />
    <span class="login-text">登录&nbsp;</span>
    <span class="login-text">线路二</span>
  </div>
  <!-- 线路二已登录 -->
  <el-popover v-else placement="bottom" :width="200">
    <template #reference>
      <div class="flex items-center call whitespace-nowrap">
        <img class="size-16 cursor-pointer mr-8" :src="callTwo" alt="" />
        <span class="text-white cursor-pointer text-sm font-medium">
          线路二
        </span>
        <template v-if="rlyIsTalking">
          <span class="text-white cursor-pointer text-sm font-medium ml-16">
            通话中：
          </span>
          <span class="text-white cursor-pointer text-sm font-medium w-60">
            {{ formatSecondsToTime(rlyTalkingCounter) }}
          </span>
        </template>
        <template v-else>
          <span class="text-white cursor-pointer text-sm font-medium ml-16">
            {{ rlyAgentStatus === RLYAgentStatus.Free ? '空闲' : '忙碌' }}：
          </span>
          <span class="text-white cursor-pointer text-sm font-medium w-60">
            {{ formatSecondsToTime(rlyCounter) }}
          </span>
        </template>
      </div>
    </template>
    <div class="space-y-12 text-[#3A4762] leading-5">
      <p
        class="flex items-center gap-8 cursor-pointer whitespace-nowrap"
        role="button"
      >
        <img class="size-16" :src="callSvg" alt="" />
        容联云
      </p>
      <p
        class="flex items-center gap-8 cursor-pointer"
        role="button"
        @click="rlySettingsVisible = true"
      >
        <img class="size-16" :src="settingsBlackSvg" alt="" />
        设置
      </p>
      <p
        class="flex items-center gap-8"
        :class="{
          'cursor-not-allowed': !canLogout,
          'cursor-pointer': canLogout,
        }"
        role="button"
        @click="rlyLogout"
      >
        <img class="size-16" :src="logoutSvg" alt="" />
        退出
      </p>
    </div>
  </el-popover>
  <RLYSettingDialog
    v-model="rlySettingsVisible"
    :default-values="{
      loginType: rlyLoginType,
      statusValue: rlyAgentStatus,
    }"
    @ok="rlySettingsChange"
  />
</template>

<style lang="less" scoped>
.login-wrapper {
  display: flex;
  align-items: center;
  white-space: nowrap;

  .login-text {
    color: #fff;
    cursor: pointer;
    font-size: 14px;
  }

  &:hover {
    .login-text {
      opacity: 0.85;
      font-weight: 500;
    }
  }

  &:active {
    .login-text {
      opacity: 0.65;
      font-weight: 400;
    }
  }
}
</style>
