<!-- 来电显示弹窗 -->
<script setup lang="ts">
import { queryAddressPhoneNameApi } from '@/api/addressBook';
import callCenter from '@/assets/imgs/callCenter/call-center.png';
import hangup from '@/assets/imgs/callCenter/hangup.png';
import initiative from '@/assets/imgs/callCenter/initiative-incoming-call.png';
import { useAICCStore, useRLYSoftphone } from '@/store';
import { useQuery } from '@tanstack/vue-query';
import { toRef } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

const emit = defineEmits<{
  /** 挂断来电 */
  hangup: [];
  /** 接听来电 */
  answer: [];
}>();
let aicc = useAICCStore();
const moor = useRLYSoftphone();
const { rlyShowBellingDialog, rlyIncomingCallNumber } = storeToRefs(moor);
const aiccIncomingCallNumber = toRef(aicc, 'incomingCallNumber');

const queryEnable = computed(() => !!moor.rlyIncomingCallNumber);

const aiccQueryEnable = computed(() => !!aicc.incomingCallNumber);

const { data } = useQuery({
  queryKey: ['来电号码', rlyIncomingCallNumber],
  queryFn: () =>
    queryAddressPhoneNameApi({ phone: rlyIncomingCallNumber.value }),
  enabled: queryEnable,
});

const { data: aiccData } = useQuery({
  queryKey: ['AICC来电号码', aiccIncomingCallNumber],
  queryFn: () =>
    queryAddressPhoneNameApi({ phone: aiccIncomingCallNumber.value }),
  enabled: aiccQueryEnable,
});

/** 来电列表 */
const bellingList = computed(() => {
  const rlyIncomingData = data.value?.data;
  const aiccIncomingData = aiccData.value?.data;
  /** 容联云来电标题 */
  const rlyIncomingTitle = rlyIncomingData?.name
    ? `${rlyIncomingData.name}(${rlyIncomingData.relation ?? ''})`
    : rlyIncomingCallNumber.value;
  return [
    {
      line: '线路一',
      show: aicc.dialogVisibleTelephoneCalls,
      title: aiccIncomingData?.name
        ? aiccIncomingData.name +
          (aiccIncomingData.relation ? `(${aiccIncomingData.relation})` : '')
        : aiccIncomingCallNumber.value,
      patient: aiccIncomingData?.name
        ? '患者：' + (aiccIncomingData.patientName || aiccIncomingData.name)
        : '患者未知',
    },
    {
      line: '线路二',
      show: rlyShowBellingDialog.value,
      title: rlyIncomingTitle,
      patient: rlyIncomingData?.name
        ? '患者：' + (rlyIncomingData.patientName || rlyIncomingData.name)
        : '患者未知',
    },
  ];
});

/**
 * 处理接听来电
 * @param line 线路
 */
function handleAnswer(line: string) {
  switch (line) {
    case '线路一':
      emit('answer');
      break;
    case '线路二':
      moor.rlyAnswer();
      break;
  }
}

function handleHangup(line: string) {
  switch (line) {
    case '线路一':
      emit('hangup');
      break;
    case '线路二':
      moor.rlyHangup();
      break;
  }
}
</script>

<template>
  <div class="wrapper grid grid-cols-1 gap-16">
    <transition-group name="belling-list">
      <template v-for="item in bellingList" :key="item.line">
        <div v-if="item.show" class="belling-box">
          <div class="flex items-center gap-12">
            <div class="flex items-center flex-col gap-8">
              <img :src="initiative" alt="" class="size-12" />
              <div class="text-sm text-[#939CAE]">{{ item.line }}</div>
            </div>
            <div class="space-y-8">
              <div class="text-base leading-6 font-medium">
                {{ item.title }}
              </div>
              <div class="text-sm leading-5">
                {{ item.patient }}
              </div>
            </div>
          </div>
          <div class="right-call flex items-center gap-8">
            <img
              class="size-26 cursor-pointer"
              :src="hangup"
              alt=""
              @click="handleHangup(item.line)"
            />
            <img
              class="size-26 cursor-pointer"
              :src="callCenter"
              alt=""
              @click="handleAnswer(item.line)"
            />
          </div>
        </div>
      </template>
    </transition-group>
  </div>
</template>

<style lang="css" scoped>
.belling-list-enter-active,
.belling-list-leave-active {
  transition: all 0.5s ease;
}

.belling-list-enter-from,
.belling-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.belling-list-leave-active {
  position: absolute;
}

.wrapper {
  position: fixed;
  right: 16px;
  top: 78px;
  z-index: 99;
}

.belling-box {
  box-shadow: 0px 4px 12px 0px rgba(8, 38, 99, 0.2);
  @apply rounded py-12 pl-8 pr-12 box-border bg-black flex items-center justify-between w-288 h-74;
}
</style>
