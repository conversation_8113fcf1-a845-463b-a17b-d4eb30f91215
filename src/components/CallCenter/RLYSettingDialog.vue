<script setup lang="ts">
import { RLYAgentStatus, RLYLoginType } from '@/constant';
import { ElDialog } from 'element-plus';
import { ref, watch } from 'vue';
const visible = defineModel<boolean>('modelValue', { default: false });

const props = defineProps<{
  defaultValues: { loginType: RLYLoginType; statusValue: RLYAgentStatus };
}>();
const emit = defineEmits<{
  ok: [formData: { loginType: RLYLoginType; statusValue: RLYAgentStatus }];
}>();

const { defaultValues } = toRefs(props);

const freeOptions = [
  {
    value: RLYAgentStatus.Free,
    label: '空闲',
  },
  {
    value: RLYAgentStatus.Busy,
    label: '忙碌',
  },
];

const form = ref<{ loginType: RLYLoginType; statusValue: RLYAgentStatus }>({
  loginType: RLYLoginType.SIP,
  statusValue: RLYAgentStatus.Free,
});

watch(
  () => defaultValues.value,
  val => {
    form.value = val;
  },
  { immediate: true, deep: true }
);

function cancelBtn() {
  visible.value = false;
}

function confirmBtn() {
  emit('ok', {
    loginType: form.value.loginType,
    statusValue: form.value.statusValue,
  });
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="呼叫配置"
    width="410px"
    :modal="false"
    class="dialogVisibleCallCenterBox"
    header-class="header-class"
    body-class="body-class"
  >
    <div class="phoneStyle flex items-center">
      <div class="phoneBox text-sm mr-4">电话类型：</div>
      <el-radio-group v-model="form.loginType">
        <el-radio :value="RLYLoginType.Local" class="phoneRadio text-sm">
          普通电话
        </el-radio>
        <el-radio
          disabled
          :value="RLYLoginType.gateway"
          class="phoneRadio text-sm"
        >
          IP话机
        </el-radio>
        <el-radio :value="RLYLoginType.SIP" class="phoneRadio text-sm">
          软电话
        </el-radio>
      </el-radio-group>
    </div>
    <div class="phoneStyle flex items-center">
      <div class="phoneBox text-sm mr-4">坐席状态：</div>
      <el-radio-group v-model="form.statusValue">
        <el-radio
          v-for="item in freeOptions"
          :key="item.value"
          :value="item.value"
          :label="item.value"
          class="phoneRadio text-sm"
        >
          {{ item.label }}
        </el-radio>
      </el-radio-group>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelBtn()">取消</el-button>
        <el-button
          style="background: #2e6be6; color: #ffffff"
          @click="confirmBtn()"
        >
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.header-class {
  padding: 0;
  height: 50px;
  border-bottom: 1px solid #e9e8eb;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 20px;
}
:deep(.dialogVisibleCallCenterBox) {
  padding: 16px 0;
  .el-dialog__header {
    padding: 0;
    height: 50px;
    border-bottom: 1px solid #e9e8eb;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 20px;
    .el-dialog__headerbtn {
      top: 0;
      right: 16px;
    }
    .el-dialog__title {
      color: #101b25;
      font-weight: 500;
      font-size: 16px;
    }
  }
  .el-dialog__body {
    text-align: left;
    padding: 20px;
    .phoneStyle {
      .phoneBox {
        color: #303133;
      }
      .phoneRadio {
        margin-right: 0;
        margin-left: 30px;
        color: #303133;
      }
    }
    .contentList {
      margin-top: 13px;
      .el-collapse-item {
        .el-collapse-item__arrow {
          display: none;
        }
        .el-collapse-item__header {
          background: #f7f8fa;
        }
      }
      .el-collapse-item__content {
        padding-bottom: 0;
      }
    }
  }
  .el-dialog__footer {
    padding-top: 0;
  }
}
</style>
