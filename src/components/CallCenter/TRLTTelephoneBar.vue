<script setup lang="ts">
import { ClinkAgentLoginParams } from '@/@types/window';
import { callSeatQuery, queryPatientNameApi } from '@/api/addressBook';
import callOne from '@/assets/icons/call-one.svg';
import callSvg from '@/assets/imgs/callCenter/call.svg';
import logoutSvg from '@/assets/imgs/callCenter/logout.svg';
import settingsBlackSvg from '@/assets/imgs/callCenter/settings-black.svg';
import { CallSource } from '@/interface';
import useCall from '@/store/module/useCallCenter';
import useUserStore from '@/store/module/useUserStore';
import { formatSecondsToTime } from '@/utils';
import { useMutation } from '@tanstack/vue-query';
import { useInterval, useStorage } from '@vueuse/core';
import { ElPopover } from 'element-plus';
import { isEmpty } from 'lodash-es';
import { computed } from 'vue';
import TRLTSettingDialog from './TRLTSettingDialog.vue';

const freeOptions = [
  {
    value: 1,
    label: '空闲',
  },
  {
    value: 2,
    label: '忙碌',
  },
];

const dialogVisibleCallCenter = ref<boolean>(false);
let bindType = useStorage('bindType', 3, localStorage);
let useCallStore = useCall();
const userStore = useUserStore();
const { counter: lineOneCounter, reset: lineOneReset } = useInterval(1000, {
  controls: true,
});

const {
  counter: talkCounter,
  reset: talkReset,
  resume: talkResume,
  pause: talkPause,
} = useInterval(1000, {
  controls: true,
});
useCallStore.initCallingState();

const { mutateAsync } = useMutation({
  mutationKey: ['获取AICC登录信息'],
  mutationFn: () => callSeatQuery({ channel: CallSource.AICC }),
});

const { data: phoneList, mutateAsync: fetchPhoneList } = useMutation({
  mutationKey: ['获取电话列表', '/api/address/phone/list'],
  mutationFn: async (cno: string) => {
    if (cno && cno !== 'null') {
      const data = await queryPatientNameApi({ cno });
      return data.clientTelModelList.map(item => ({
        ...item,
        telType: item.telType - 1,
      }));
    }
    return [];
  },
});

/** 线路一状态 */
const lineOneCallTypeText = computed(() => {
  return freeOptions.find(item => item.value === useCallStore.loginStatus)
    ?.label;
});

watch(
  () => useCallStore.isTalking,
  newVal => {
    if (newVal) {
      talkReset();
      talkResume();
    } else {
      talkPause();
    }
  }
);

// 话机设置---修改接听电话方式
function settingsCall() {
  dialogVisibleCallCenter.value = true;
}

// 提醒消息
function getPrompt(msg: string, style: string) {
  let obj = {
    showClose: true,
    message: msg || '请检查是否完成！',
    type: style || 'error',
  };
  let newObj = obj as any;
  ElMessage(newObj);
}

/** 电话登录 */
async function loginTel() {
  const aiccInfo = await mutateAsync();
  if (isEmpty(aiccInfo.cno) || isEmpty(aiccInfo?.loginPassword)) {
    getPrompt('该账号不可用于登录呼叫中心！', 'warning');
    return;
  }
  const aiccPhoneList = await fetchPhoneList(aiccInfo.cno);
  const bindTel = aiccPhoneList?.find(
    item => item.telType === bindType.value
  )?.tel;
  if (isEmpty(aiccPhoneList) || !bindTel) {
    getPrompt('该账号不可用于登录呼叫中心！', 'warning');
    return;
  }
  let password = aiccInfo?.loginPassword;
  let identifier = userStore.enterpriseCode || 'heartmed';
  const params: ClinkAgentLoginParams = {
    identifier, //企业编码 当showEnterprise为false时必填，否则忽略
    cno: aiccInfo.cno, //座席工号 规则同identifier
    password, // 密码 规则同identifier
    bindTel: bindTel, //绑定电话
    bindType: bindType.value, // 绑定类型，1：普通电话、2：IP话机、3：软电话
    loginStatus: useCallStore.loginStatus, //座席初始登录状态 当showAgentInitStatus为false时必填，否则忽略
  };

  window.ClinkAgent?.login(params);
  lineOneReset();
}

// 呼叫中心弹窗确认事件
function confirmBtn(formData: { bindType: number; loginStatus: number }) {
  bindType.value = formData.bindType;
  if (useCallStore.loginStatus !== formData.loginStatus) {
    useCallStore.loginStatus = formData.loginStatus;
  }
  loginTel();
  dialogVisibleCallCenter.value = false;
}
</script>
<template>
  <!-- 线路一未登录 -->
  <div
    v-if="useCallStore.isCallLogin === 0"
    class="login-wrapper"
    role="button"
    @click="loginTel"
  >
    <img class="size-16 cursor-pointer mr-8" :src="callOne" alt="" />
    <span class="login-text font-medium">登录&nbsp;</span>
    <span class="login-text">线路一</span>
  </div>
  <!-- 线路一已登录 -->
  <el-popover v-else placement="bottom" :width="200">
    <template #reference>
      <div class="flex items-center call whitespace-nowrap">
        <img class="size-16 cursor-pointer mr-8" :src="callOne" alt="" />
        <span class="text-white cursor-pointer text-sm font-medium">
          线路一
        </span>
        <template v-if="useCallStore.isTalking">
          <span class="text-white cursor-pointer text-sm font-medium ml-16">
            通话中：
          </span>
          <span class="text-white cursor-pointer text-sm font-medium w-60">
            {{ formatSecondsToTime(talkCounter) }}
          </span>
        </template>
        <template v-else>
          <span class="text-white cursor-pointer text-sm font-medium ml-16">
            {{ lineOneCallTypeText }}：
          </span>
          <span class="text-white cursor-pointer text-sm font-medium w-60">
            {{ formatSecondsToTime(lineOneCounter) }}
          </span>
        </template>
      </div>
    </template>
    <div class="space-y-12 text-[#3A4762] leading-5">
      <p
        class="flex items-center gap-8 cursor-pointer whitespace-nowrap"
        role="button"
      >
        <img class="size-16" :src="callSvg" alt="" />
        天润联通
      </p>
      <p
        class="flex items-center gap-8 cursor-pointer"
        role="button"
        @click="settingsCall()"
      >
        <img class="size-16" :src="settingsBlackSvg" alt="" />
        设置
      </p>
      <p
        class="flex items-center gap-8 cursor-pointer"
        role="button"
        @click="useCallStore.logout"
      >
        <img class="size-16" :src="logoutSvg" alt="" />
        退出
      </p>
    </div>
  </el-popover>
  <!-- 呼叫中心 -->
  <TRLTSettingDialog
    v-model="dialogVisibleCallCenter"
    :phone-list="phoneList || []"
    :default-values="{
      bindType: bindType,
      loginStatus: useCallStore.loginStatus,
    }"
    @ok="confirmBtn"
  />
</template>

<style lang="less" scoped>
.login-wrapper {
  display: flex;
  align-items: center;

  .login-text {
    color: #fff;
    cursor: pointer;
    font-size: 14px;
  }

  &:hover {
    .login-text {
      opacity: 0.85;
      font-weight: 500;
    }
  }

  &:active {
    .login-text {
      opacity: 0.65;
      font-weight: 400;
    }
  }
}
</style>
