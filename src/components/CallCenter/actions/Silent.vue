<!-- 静音 -->
<script setup lang="ts">
import callOne from '@/assets/icons/call-one.svg';
import silence from '@/assets/imgs/callCenter/silence.png';
import unmute from '@/assets/imgs/callCenter/unmute.png';
import { useAICCStore, useRLYSoftphone } from '@/store';
import { ElButton, ElPopover } from 'element-plus';
import { computed } from 'vue';

/** 是否静音 */
let isSilence = ref(false);
const moor = useRLYSoftphone();
const aicc = useAICCStore();

/** 单条线路正在通话中 */
const singleTalking = computed(() => {
  return aicc.isCall;
});

/** 多条线路正在通话中 */
const doubleTalking = computed(() => {
  return aicc.isCall && moor.rlyIsTalking;
});

function silenceCall() {
  // 取消静音
  if (isSilence.value) {
    window.ClinkAgent?.unmute({ direction: 'in' });
  }
  // 静音
  if (!isSilence.value) {
    window.ClinkAgent?.mute({ direction: 'in' });
  }
  isSilence.value = !isSilence.value;
}
</script>

<template>
  <el-popover v-if="doubleTalking" placement="bottom-start" width="170">
    <template #reference>
      <div
        class="cursor-pointer flex flex-col items-center text-xs ml-16 space-y-2"
      >
        <img
          class="w-16 h-16 mb-2"
          :src="isSilence ? unmute : silence"
          alt="call hold"
        />
        <div class="whitespace-nowrap">
          {{ isSilence ? '取消' : '静音' }}
        </div>
      </div>
    </template>
    <div class="space-y-16">
      <div class="flex items-center">
        <img class="size-16 cursor-pointer mr-8" :src="callOne" alt="" />
        <span class="text-[#3A4762] cursor-pointer text-sm font-medium">
          天润联通
        </span>
        <ElButton size="small" class="ml-auto" @click="silenceCall">
          {{ isSilence ? '取消' : '静音' }}
        </ElButton>
      </div>
    </div>
  </el-popover>
  <div
    v-if="singleTalking && !doubleTalking"
    class="cursor-pointer flex flex-col items-center text-xs ml-16 space-y-2"
    @click="silenceCall"
  >
    <img class="w-16 h-16 mb-2" :src="isSilence ? unmute : silence" alt="" />
    <div class="whitespace-nowrap">{{ isSilence ? '取消' : '静音' }}</div>
  </div>
</template>
