<!-- 挂断 -->
<script setup lang="ts">
import callOne from '@/assets/icons/call-one.svg';
import callTwo from '@/assets/icons/call-two.svg';
import hangup from '@/assets/imgs/callCenter/hangup.png';
import { addDBLog } from '@/lib/db';
import { useAICCStore, useRLYSoftphone } from '@/store';
import { useStorage } from '@vueuse/core';
import { ElButton, ElPopover } from 'element-plus';
import { computed } from 'vue';

const moor = useRLYSoftphone();
const aicc = useAICCStore();
let bindType = useStorage('bindType', 3, localStorage);

/** 单条线路正在通话中 */
const singleTalking = computed(() => {
  addDBLog({
    message: '单条线路是否挂断电话计算',
    tag: '容联云',
    extraData: {
      isCall: aicc.isCall,
      isCallInRinging: aicc.isCallInRinging,
      rlyIsTalking: moor.rlyIsTalking,
      rlyIsDialing: moor.rlyIsDialing,
    },
  });
  return (
    aicc.isCall ||
    aicc.isCallInRinging === 1 ||
    moor.rlyIsTalking ||
    moor.rlyIsDialing
  );
});

/** 多条线路正在通话中 */
const doubleTalking = computed(() => {
  return aicc.isCall && moor.rlyIsTalking;
});

/** 挂断电话 */
function hangupNumber() {
  // 如果容联云正在拨号/通话中，挂断容联云通话
  if (moor.rlyIsTalking || moor.rlyIsDialing) {
    moor.rlyHangup();
  } else if (bindType.value === 3) {
    // 如果天润联通正在通话中，挂断天润联通通话
    (window as any).ClinkAgent.sipUnlink();
  } else {
    (window as any).ClinkAgent.previewOutcallCancel({});
  }
}

function rlyHangUp() {
  if (moor.rlyIsTalking || moor.rlyIsDialing) {
    moor.rlyHangup();
  }
}

function aiccHangUp() {
  if (bindType.value === 3) {
    // 如果天润联通正在通话中，挂断天润联通通话
    (window as any).ClinkAgent.sipUnlink();
  } else {
    (window as any).ClinkAgent.previewOutcallCancel({});
  }
}
</script>

<template>
  <el-popover v-if="doubleTalking" placement="bottom-start" width="170">
    <template #reference>
      <img
        v-if="singleTalking"
        class="w-24 h-24 ml-16 cursor-pointer"
        :src="hangup"
      />
    </template>
    <div class="space-y-16">
      <div class="flex items-center">
        <img class="size-16 cursor-pointer mr-8" :src="callOne" alt="" />
        <span class="text-[#3A4762] cursor-pointer text-sm font-medium">
          天润联通
        </span>
        <ElButton
          size="small"
          type="danger"
          class="ml-auto"
          @click="aiccHangUp"
        >
          挂断
        </ElButton>
      </div>
      <div class="flex items-center">
        <img class="size-16 cursor-pointer mr-8" :src="callTwo" alt="" />
        <span class="text-[#3A4762] cursor-pointer text-sm font-medium">
          容联云
        </span>
        <ElButton size="small" type="danger" class="ml-auto" @click="rlyHangUp">
          挂断
        </ElButton>
      </div>
    </div>
  </el-popover>
  <img
    v-if="singleTalking && !doubleTalking"
    class="w-24 h-24 ml-16 cursor-pointer"
    :src="hangup"
    alt=""
    @click="hangupNumber()"
  />
</template>
