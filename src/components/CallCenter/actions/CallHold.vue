<!-- 呼叫保持 -->
<script setup lang="ts">
import callOne from '@/assets/icons/call-one.svg';
import callTwo from '@/assets/icons/call-two.svg';
import hold from '@/assets/imgs/callCenter/hold.png';
import takeBack from '@/assets/imgs/callCenter/take-back.png';
import { useAICCStore, useRLYSoftphone } from '@/store';
import { ElButton, ElPopover } from 'element-plus';
import { computed } from 'vue';

const moor = useRLYSoftphone();
const aicc = useAICCStore();

/** 是否正在保持呼叫 */
const isCallHold = computed(() => moor.rlyIsCallHold || aicc.isCallHold);

/** 单条线路正在通话中 */
const singleTalking = computed(() => aicc.isCall || moor.rlyIsTalking);

/** 多条线路正在通话中 */
const doubleTalking = computed(() => aicc.isCall && moor.rlyIsTalking);

/**
 * 切换呼叫保持
 */
function holdCall() {
  if (moor.rlyIsTalking) {
    moor.rlyToggleHold();
  } else {
    aicc.toggleHold();
  }
}
</script>

<template>
  <el-popover v-if="doubleTalking" placement="bottom-start" width="170">
    <template #reference>
      <div
        class="cursor-pointer flex flex-col items-center text-xs ml-16 space-y-2"
      >
        <img
          class="w-16 h-16 mb-2"
          :src="isCallHold ? takeBack : hold"
          alt="call hold"
        />
        <div class="whitespace-nowrap">
          {{ isCallHold ? '接回' : '保持' }}
        </div>
      </div>
    </template>
    <div class="space-y-16">
      <div class="flex items-center">
        <img class="size-16 cursor-pointer mr-8" :src="callOne" alt="" />
        <span class="text-[#3A4762] cursor-pointer text-sm font-medium">
          天润联通
        </span>
        <ElButton size="small" class="ml-auto" @click="aicc.toggleHold">
          {{ aicc.isCallHold ? '接回' : '保持' }}
        </ElButton>
      </div>
      <div class="flex items-center">
        <img class="size-16 cursor-pointer mr-8" :src="callTwo" alt="" />
        <span class="text-[#3A4762] cursor-pointer text-sm font-medium">
          容联云
        </span>
        <ElButton size="small" class="ml-auto" @click="moor.rlyToggleHold">
          {{ moor.rlyIsCallHold ? '接回' : '保持' }}
        </ElButton>
      </div>
    </div>
  </el-popover>
  <div
    v-else-if="singleTalking"
    class="cursor-pointer flex flex-col items-center text-xs ml-16 space-y-2"
    @click="holdCall"
  >
    <img class="w-16 h-16 mb-2" :src="isCallHold ? takeBack : hold" alt="" />
    <div class="whitespace-nowrap">{{ isCallHold ? '接回' : '保持' }}</div>
  </div>
</template>
