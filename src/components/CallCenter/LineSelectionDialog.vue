<!-- 线路选择弹窗 -->
<script setup lang="ts">
import { fetchCallArea, fetchCallShowList } from '@/api';
import { CallSource } from '@/interface';
import { useCall } from '@/store';
import { useQuery } from '@tanstack/vue-query';
import dayjs from 'dayjs';
import { ElDialog } from 'element-plus';
import { isEmpty } from 'lodash-es';
import { computed, toRef, watch } from 'vue';

const props = withDefaults(
  defineProps<{
    /** 外呼电话号码 */
    callPhone: string;
    /** 仅支持容联云 */
    onlyRly?: boolean;
    /** 默认选择线路 */
    defaultLine?: 1 | 2;
    /** 患者id */
    patientId?: number;
  }>(),
  {
    onlyRly: false,
    defaultLine: 1,
    patientId: undefined,
    callPhone: '',
  }
);

const { defaultLine } = props;
const callPhone = toRef(() => props.callPhone.trim());
const patientId = toRef(() => props.patientId);
const onlyRly = toRef(() => props.onlyRly);
const useCallStore = useCall();

const emit = defineEmits<{
  confirm: [
    data: {
      line: 1 | 2;
      phone: string;
      outShow?: string;
    },
  ];
}>();
const visible = defineModel<boolean>();
/** 呼叫线路 */
const line = ref<1 | 2>(defaultLine || 1);
const phone = ref<string>();
/** 查询号码归属地是否允许 */
const queryEnable = computed(() => !!callPhone.value && !!visible.value);
/** 查询外显号码列表是否允许 */
const callShowListQueryEnable = computed<boolean>(
  () => !!callPhone.value && !!visible.value
);

const { data: phoneArea } = useQuery({
  queryKey: ['号码归属地', callPhone],
  queryFn: () => fetchCallArea({ phone: callPhone.value }),
  enabled: queryEnable,
});

const { data: callShowList } = useQuery({
  queryKey: ['外显号码列表', callPhone, line, patientId],
  queryFn: () => {
    return fetchCallShowList({
      phone: callPhone.value,
      source: line.value === 1 ? CallSource.AICC : CallSource.MOOR,
      patientId: patientId.value,
    });
  },
  enabled: callShowListQueryEnable,
  staleTime: 1000 * 5,
});

const tagPriority = (tags: string[] | null | undefined): number => {
  if (!tags || tags.length === 0) return 4;
  if (tags.includes('LAST_CALL')) return 0;
  if (tags.includes('PHONE_LOCATION')) return 1;
  if (tags.includes('HOSPITAL_LOCATION')) return 2;
  return 3;
};

const orderedCallShowList = computed(() => {
  if (!callShowList.value) return [];
  return callShowList.value.slice().sort((a, b) => {
    const pa = tagPriority(a.tags);
    const pb = tagPriority(b.tags);
    if (pa !== pb) return pa - pb;
    // 同优先级时，按 callTime 降序
    return new Date(b.callTime).getTime() - new Date(a.callTime).getTime();
  });
});

watch(
  [
    () => phoneArea.value?.source,
    () => onlyRly.value,
    () => defaultLine,
    () => visible,
  ],
  ([newVal, only, defaultValue]) => {
    if (defaultValue !== undefined && defaultValue !== null) {
      line.value = defaultValue;
    }
    if (newVal) {
      line.value = newVal === CallSource.MOOR ? 2 : 1;
    }
    if (only) {
      line.value = 2;
    }
  },
  { immediate: true }
);

watch(
  callShowList,
  val => {
    if (!isEmpty(val) && line.value === 2) {
      phone.value = val?.[0].phone;
    }
  },
  { deep: true }
);

/**
 * 校验表单
 */
function validateForm() {
  if (line.value === 2 && !phone.value) {
    ElMessage.warning('请选择外显号码');
    return false;
  }
  return true;
}

function handleCall() {
  if (!validateForm()) {
    return;
  }
  useCallStore.getPhoneInfo(callPhone.value);
  emit('confirm', {
    line: line.value,
    outShow: phone.value,
    phone: callPhone.value,
  });
  visible.value = false;
}

function getTagsText(tags: null | string[]) {
  if (!tags) {
    return '';
  } else {
    if (tags.includes('LAST_CALL')) {
      return '上次呼出';
    } else if (
      tags.includes('PHONE_LOCATION') &&
      tags.includes('HOSPITAL_LOCATION')
    ) {
      return '号码及医院归属地';
    } else if (tags.includes('PHONE_LOCATION')) {
      return '号码归属地';
    } else if (tags.includes('HOSPITAL_LOCATION')) {
      return '医院归属地';
    }
    return '';
  }
}
</script>

<template>
  <ElDialog
    v-model="visible"
    title="请选择呼叫线路"
    :width="400"
    class="line-dialog"
    :append-to-body="true"
  >
    <div class="item">
      <label>被叫号码:</label>
      <span class="font-medium">
        {{ callPhone }}
        {{ phoneArea ? `（${phoneArea.province}-${phoneArea.city}）` : '' }}
      </span>
    </div>
    <div v-if="!onlyRly" class="item">
      <label>呼叫线路:</label>
      <span>
        <el-radio-group v-model="line">
          <el-radio :value="1">
            线路一
            <span
              v-if="phoneArea?.source === CallSource.AICC"
              class="text-[#7A8599]"
            >
              (上次使用)
            </span>
          </el-radio>
          <el-radio :value="2">
            线路二
            <span
              v-if="phoneArea?.source === CallSource.MOOR"
              class="text-[#7A8599]"
            >
              (上次使用)
            </span>
          </el-radio>
        </el-radio-group>
      </span>
    </div>
    <div
      v-if="line === 1"
      class="text-[#7A8599] text-sm leading-5 pl-24 space-y-10"
    >
      <span>固定外显号码：以下号码根据号码所属地区自动匹配</span>
      <div class="grid grid-cols-2 gap-y-10">
        <span v-for="item in callShowList" :key="item.phone">
          {{ item.phone }}({{ item.city }})
        </span>
      </div>
    </div>
    <div v-else class="item">
      <label>外显号码:</label>
      <div v-if="callShowList">
        <el-radio
          v-if="orderedCallShowList?.[0]?.tags?.includes('LAST_CALL')"
          v-model="phone"
          :value="orderedCallShowList?.[0].phone"
        >
          {{ orderedCallShowList?.[0].phone }}({{
            orderedCallShowList?.[0].city
          }})
          <span class="text-[#7A8599] text-sm">(上次呼出)</span>
        </el-radio>
        <div
          v-if="orderedCallShowList?.[0]?.tags?.includes('LAST_CALL')"
          class="text-[#7A8599] text-sm pr-32 leading-5"
        >
          {{ orderedCallShowList?.[0].userName }}
          {{
            dayjs(orderedCallShowList?.[0].callTime).format(
              'YYYY-MM-DD HH:mm:ss'
            )
          }}
          {{ orderedCallShowList?.[0].callStatus }}
        </div>
        <el-radio-group v-model="phone" class="text-sm">
          <el-radio
            v-for="item in orderedCallShowList?.[0]?.tags?.includes('LAST_CALL')
              ? orderedCallShowList?.slice(1)
              : orderedCallShowList"
            :key="item.phone"
            :value="item.phone"
          >
            {{ item.phone }}({{ item.city }})
            <span v-if="item.tags?.length > 0">
              ({{ getTagsText(item.tags) }})
            </span>
          </el-radio>
        </el-radio-group>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-center">
        <el-button type="primary" @click="handleCall">呼叫</el-button>
        <el-button @click="visible = false">取消</el-button>
      </div>
    </template>
  </ElDialog>
</template>

<style lang="css">
.line-dialog {
  padding: 0;
  .el-dialog__header {
    padding: 16px 24px 12px;
    font-size: 16px;
    font-weight: 700;
    color: #101b25;
    border-bottom: 1px solid #e9e8eb;
    margin-right: 0;
    .el-dialog__title {
      font-size: 16px;
    }
  }
  .el-dialog__footer {
    padding-bottom: 24px;
  }
  .el-table__inner-wrapper:before {
    background-color: transparent;
  }
  .el-dialog__headerbtn {
    top: 4px;
    right: 8px;
  }
}
</style>

<style lang="css" scoped>
.item {
  display: flex;
  padding-left: 16px;
  gap: 10px;
  line-height: 32px;
  color: #3a4762;
  font-size: 14px;

  label {
    font-weight: 500;
    flex-shrink: 0;
  }
}
</style>
