<template>
  <div class="flex items-center gap-12">
    <!-- 通话记录入口 -->
    <TelRecordEntry />
    <div class="bg-white w-1 h-12"></div>
    <!-- 天润联通电话条 -->
    <TRLTTelephoneBar />
    <div class="bg-white w-1 h-12"></div>
    <!-- 容联云电话条 -->
    <RLYTelephoneBar />
    <div
      v-if="aicc.isCallLogin === 1 || moor.rlyIsLogin"
      class="call-box flex items-center"
    >
      <el-input
        v-if="
          !(
            aicc.isCall ||
            moor.rlyIsDialing ||
            moor.rlyIsTalking ||
            moor.rlyShowBellingDialog ||
            aicc.dialogVisibleTelephoneCalls
          )
        "
        v-model="aicc.callPhone"
        placeholder="输入电话号码外呼"
        clearable
        :style="{ width: '150px' }"
        class="mr-12"
      />
      <div
        v-if="aicc.isCall || moor.rlyIsDialing || moor.rlyIsTalking"
        class="border border-solid border-[#b8becc] rounded-sm text-[#3a4762] bg-white pl-12 box-border flex items-center mr-12 text-sm w-340 h-32"
      >
        <img class="w-16 h-16 mr-12" :src="exhalation" alt="" />
        <div class="whitespace-nowrap text-ellipsis overflow-hidden">
          {{ callName }}：{{ aicc.callPhone }}
          <span v-if="!!patientName || !!relation">
            （{{ patientName }}{{ relation }}）
          </span>
        </div>
      </div>
      <!-- 外呼按钮 -->
      <img
        v-if="
          !showHangup &&
          !moor.rlyShowBellingDialog &&
          !aicc.dialogVisibleTelephoneCalls
        "
        class="size-24 cursor-pointer"
        :src="callCenter"
        alt=""
        @click="callValidate()"
      />
      <!-- 挂断按钮 -->
      <HungUp />
      <CallHold />
      <Silent />
    </div>
  </div>
  <!-- 15s来电显示 -->
  <IncomingCall @hangup="hangupNumber()" @answer="callNumber()" />
  <LineSelectionDialog
    v-if="aicc.callPhone"
    v-model="lineSelectionVisible"
    :call-phone="aicc.callPhone"
    :only-rly="moor.rlyIsLogin && !aicc.isCallLogin"
    :default-line="2"
    @confirm="lineSelectionConfirm"
  />
</template>
<script setup lang="ts">
import callCenter from '@/assets/imgs/callCenter/call-center.png';
import exhalation from '@/assets/imgs/callCenter/exhalation.png';
import { useAICCStore, useRLYSoftphone } from '@/store';
import { hasMicrophone } from '@/utils';
import { ref } from 'vue';
import { CallHold, HungUp, Silent } from './actions';
import IncomingCall from './IncomingCall.vue';
import LineSelectionDialog from './LineSelectionDialog.vue';
import RLYTelephoneBar from './RLYTelephoneBar.vue';
import TelRecordEntry from './TelRecordEntry.vue';
import TRLTTelephoneBar from './TRLTTelephoneBar.vue';

const aicc = useAICCStore();
const moor = useRLYSoftphone();
const callName = computed(() => aicc.callName);
const patientName = computed(() => aicc.patientName);
const relation = computed(() => aicc.relation);

const lineSelectionVisible = ref(false);
let phoneRadio = ref<any>(3);

/** 是否显示挂断按钮 */
const showHangup = computed(() => {
  return (
    aicc.isCall ||
    aicc.isCallInRinging === 1 ||
    moor.rlyIsTalking ||
    moor.rlyIsDialing
  );
});

/**
 * 外呼拨号校验
 */
async function callValidate() {
  if (!aicc.callPhone) {
    return ElMessage({
      message: '请填写电话号码进行外呼!',
      showClose: true,
      type: 'warning',
      plain: true,
      duration: 2000,
    });
  }
  const hasMic = await hasMicrophone();
  if (!hasMic) {
    return ElMessage({
      message: '请接入麦克风后重新呼出!',
      showClose: true,
      type: 'warning',
    });
  }
  // 2条线路同时登录展示选择线路弹窗
  if (moor.rlyIsLogin && aicc.isCallLogin) {
    lineSelectionVisible.value = true;
    return;
  }
  // 仅天润联通登录
  if (aicc.isCallLogin) {
    callNumber();
    return;
  }
  // 仅容联云登录
  if (moor.rlyIsLogin) {
    lineSelectionVisible.value = true;
  }
}

// 拨打电话
let callNumber = () => {
  aicc.getPhoneInfo(aicc.callPhone);
  if (aicc.isCallInRinging === 1) {
    if (phoneRadio.value == '3') {
      window.ClinkAgent?.sipLink();
    } else {
      return getPrompt('该接听方式不支持软电话接听!', 'warning');
    }
  } else if (aicc.isCallInRinging === 0) {
    if (aicc.callPhone) {
      aicc.initCallingState();
      aicc.tempCallPhone = aicc.callPhone;
      window.ClinkAgent?.previewOutcall({
        tel: aicc.callPhone,
      });
    } else {
      return getPrompt('请填写电话!', 'warning');
    }
  }
};

/** 挂断电话 */
let hangupNumber = () => {
  if (phoneRadio.value === '3') {
    // 如果天润联通正在通话中，挂断天润联通通话
    window.ClinkAgent?.sipUnlink();
  } else {
    window.ClinkAgent?.previewOutcallCancel({});
  }
};

// 提醒消息
let getPrompt = (msg: string, style: string) => {
  let obj = {
    showClose: true,
    message: msg || '请检查是否完成！',
    type: style || 'error',
  };
  let newObj = obj as any;
  ElMessage(newObj);
};

/**
 * 线路选择处理
 * @param data 数据
 */
function lineSelectionConfirm(data: {
  line: 1 | 2;
  phone: string;
  outShow?: string;
}) {
  if (data.line === 1) {
    callNumber();
  } else if (data.line === 2) {
    moor.rlyOutboundCall(data.phone, data.outShow);
  }
}
</script>
