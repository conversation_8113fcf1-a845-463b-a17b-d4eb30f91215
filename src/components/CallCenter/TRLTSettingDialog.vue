<script setup lang="ts">
import { ClinkAgentLoginStatus } from '@/@types/window';
import { ElDialog } from 'element-plus';
import { computed, ref, watch } from 'vue';
const visible = defineModel<boolean>('modelValue', { default: false });

const props = defineProps<{
  /** 绑定电话列表 */
  phoneList: { tel: string; telType: number; isBind: 0 | 1 }[];
  defaultValues: { bindType: number; loginStatus: ClinkAgentLoginStatus };
}>();
const emit = defineEmits<{
  ok: [formData: { bindType: number; loginStatus: ClinkAgentLoginStatus }];
}>();

const { defaultValues } = toRefs(props);

const freeOptions = [
  {
    value: 1,
    label: '空闲',
  },
  {
    value: 2,
    label: '忙碌',
  },
];

const form = ref<{ bindType: number; loginStatus: ClinkAgentLoginStatus }>({
  bindType: 1,
  loginStatus: 1,
});

const phoneNumber = computed(() => {
  return props.phoneList.find(item => item.telType === form.value.bindType)
    ?.tel;
});

watch(
  () => defaultValues.value,
  val => {
    form.value = val;
  },
  { immediate: true, deep: true }
);

function cancelBtn() {
  visible.value = false;
}

function confirmBtn() {
  emit('ok', {
    bindType: form.value.bindType,
    loginStatus: form.value.loginStatus,
  });
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="呼叫配置"
    width="410px"
    :modal="false"
    class="dialogVisibleCallCenterBox"
    header-class="header-class"
    body-class="body-class"
  >
    <div class="phoneStyle flex items-center">
      <div class="phoneBox text-sm mr-4">电话类型：</div>
      <el-radio-group v-model="form.bindType">
        <el-radio :value="1" class="bindType text-sm">普通电话</el-radio>
        <el-radio :value="2" class="bindType text-sm">IP话机</el-radio>
        <el-radio :value="3" class="bindType text-sm">软电话</el-radio>
      </el-radio-group>
    </div>
    <div class="blndPhone mt-4">
      <span class="bindLeft text-sm mr-4">绑定号码：</span>
      <span class="bindRight text-sm">
        {{ phoneNumber }}
      </span>
    </div>
    <div class="phoneStyle flex items-center">
      <div class="phoneBox text-sm mr-4">坐席状态：</div>
      <el-radio-group v-model="form.loginStatus">
        <el-radio
          v-for="item in freeOptions"
          :key="item.value"
          :value="item.value"
          :label="item.value"
          class="bindType text-sm"
        >
          {{ item.label }}
        </el-radio>
      </el-radio-group>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelBtn()">取消</el-button>
        <el-button
          style="background: #2e6be6; color: #ffffff"
          @click="confirmBtn()"
        >
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.header-class {
  padding: 0;
  height: 50px;
  border-bottom: 1px solid #e9e8eb;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 20px;
}
:deep(.dialogVisibleCallCenterBox) {
  padding: 16px 0;
  .el-dialog__header {
    padding: 0;
    height: 50px;
    border-bottom: 1px solid #e9e8eb;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 20px;
    .el-dialog__headerbtn {
      top: 0;
      right: 16px;
    }
    .el-dialog__title {
      color: #101b25;
      font-weight: 500;
      font-size: 16px;
    }
  }
  .el-dialog__body {
    text-align: left;
    padding: 20px;
    .phoneStyle {
      .phoneBox {
        color: #303133;
      }
      .bindType {
        margin-right: 0;
        margin-left: 30px;
        color: #303133;
      }
    }
    .blndPhone {
      span {
        color: #303133;
      }
      .bindLeft {
        font-weight: 400;
      }
      .bindRight {
        font-weight: 500;
      }
    }
    .contentList {
      margin-top: 13px;
      .el-collapse-item {
        .el-collapse-item__arrow {
          display: none;
        }
        .el-collapse-item__header {
          background: #f7f8fa;
        }
      }
      .el-collapse-item__content {
        padding-bottom: 0;
      }
    }
  }
  .el-dialog__footer {
    padding-top: 0;
  }
}
</style>
