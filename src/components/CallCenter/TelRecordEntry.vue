<script lang="ts" setup>
import { fetchMissedCallTotal } from '@/api';
import callPhoneSvg from '@/assets/imgs/callCenter/call-phone.svg';
import { CallStatus } from '@/constant';
import TelRecord from '@/pages/Workbench/Right/components/AddressList/components/TelRecord.vue';
import { useQuery } from '@tanstack/vue-query';
import { ElBadge } from 'element-plus';
import { ref } from 'vue';

const telRecordVisible = ref(false);
const { data: counter, refetch } = useQuery({
  queryKey: ['获取未接来电数量', '/api/call/center/missed/call/total'],
  queryFn: () => fetchMissedCallTotal(),
  refetchInterval: 1000 * 60 * 5, // 每5分钟刷新一次
});

const showTelRecord = () => {
  telRecordVisible.value = true;
};
</script>

<template>
  <el-badge v-if="counter" :value="counter" class="telRecord">
    <img
      class="size-16 cursor-pointer"
      :src="callPhoneSvg"
      alt="通话记录图标"
      @click="showTelRecord"
    />
  </el-badge>
  <img
    v-else
    class="size-16 cursor-pointer"
    :src="callPhoneSvg"
    alt="通话记录图标"
    @click="showTelRecord"
  />
  <TelRecord
    :visible="telRecordVisible"
    :record-type="1"
    :show-to-detail="true"
    :default-status="CallStatus.IC_NotAnswered_NR"
    @close="telRecordVisible = false"
    @clear="refetch"
  />
</template>

<style lang="css" scoped>
.telRecord {
  :deep(.el-badge__content) {
    border: none;
  }
}
</style>
