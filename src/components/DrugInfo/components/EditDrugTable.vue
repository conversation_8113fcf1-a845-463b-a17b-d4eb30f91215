<template>
  <div class="edit-table-box">
    <div
      v-for="(item, index) in editDrugData"
      :key="item.drugName"
      class="item p-12 flex mb-8"
    >
      <div class="item-index mr-6">{{ index + 1 }}.</div>
      <div class="item-main flex-1 flex items-center justify-between">
        <div class="flex-1">
          <div class="flex items-center justify-between">
            <div class="main flex items-center">
              <div class="title mr-6">药品名称</div>
              <div class="flex">
                <el-autocomplete
                  v-model="item.drugName"
                  :popper-append-to-body="false"
                  :fetch-suggestions="querySearchAsync"
                  style="width: 240px"
                  placeholder="药品名称"
                  @select="(value: any) => handleSelectDrugName(value, index)"
                >
                  <template #default="{ item }">
                    <span class="flex justify-between">
                      <span class="flex-1 text-left">
                        {{ item.commonName }}
                      </span>
                      <span class="flex-1 text-left ml-20">
                        {{ item.drugName }}
                      </span>
                      <span class="flex-1 text-left ml-20">
                        {{ item.drugSpecStr }}
                      </span>
                    </span>
                  </template>
                </el-autocomplete>
              </div>
            </div>
            <div class="main flex items-center">
              <div class="title mr-6">药品规格</div>
              <div class="flex">
                <HrtInput
                  v-model="item.drugSpec.ingredients"
                  placeholder="规格"
                  class="data-input"
                  size="mini"
                  oninput="value=value.replace(/[^\d^\.]+/g, '').replace(/^0+(\d)/, '$1').replace(/^\./, '0.').match(/^\d*(\.?\d{0,3})/g)[0] || ''"
                  @blur="item.drugSpec.ingredients = $event.target.value"
                >
                  <template #suffix>
                    <div class="number-input-icon">
                      <div
                        class="icon-block"
                        @click="
                          item.drugSpec.ingredients = String(
                            increase(item.drugSpec.ingredients, 1)
                          )
                        "
                      >
                        <el-icon>
                          <CaretTop />
                        </el-icon>
                      </div>
                      <div
                        class="icon-block"
                        @click="
                          item.drugSpec.ingredients = String(
                            increase(item.drugSpec.ingredients, -1)
                          )
                        "
                      >
                        <el-icon>
                          <CaretBottom />
                        </el-icon>
                      </div>
                    </div>
                  </template>
                </HrtInput>
                <div class="pr-4"></div>
                <HrtSelect
                  v-model="item.drugSpec.contentUnit"
                  filterable
                  size="small"
                  placeholder="单位"
                >
                  <HrtOption
                    v-for="(drugSpec, ind) in ingredientsUnitList"
                    :key="ind"
                    :label="drugSpec"
                    :value="drugSpec"
                  />
                </HrtSelect>
              </div>
            </div>
          </div>
          <div class="flex items-center justify-between mt-8">
            <div class="main flex items-center">
              <div class="title mr-6">单次用量</div>
              <div class="flex">
                <HrtInput
                  v-model="item.drugAmount.ingredients"
                  placeholder="单次用量"
                  class="data-input"
                  size="mini"
                  oninput="value=value.replace(/[^\d^\.]+/g, '').replace(/^0+(\d)/, '$1').replace(/^\./, '0.').match(/^\d*(\.?\d{0,3})/g)[0] || ''"
                  @blur="item.drugAmount.ingredients = $event.target.value"
                >
                  <template #suffix>
                    <div class="number-input-icon">
                      <div
                        class="icon-block"
                        @click="
                          item.drugAmount.ingredients = String(
                            increase(item.drugAmount.ingredients, 1)
                          )
                        "
                      >
                        <el-icon>
                          <CaretTop />
                        </el-icon>
                      </div>
                      <div
                        class="icon-block"
                        @click="
                          item.drugAmount.ingredients = String(
                            increase(item.drugAmount.ingredients, -1)
                          )
                        "
                      >
                        <el-icon>
                          <CaretBottom />
                        </el-icon>
                      </div>
                    </div>
                  </template>
                </HrtInput>
                <div class="pr-8"></div>
                <HrtSelect
                  v-model="item.drugAmount.contentUnit"
                  filterable
                  size="small"
                  placeholder="单位"
                >
                  <HrtOption
                    v-for="(contentUnit, ind) in ingredientsUnitList"
                    :key="ind"
                    :label="contentUnit"
                    :value="contentUnit"
                  />
                </HrtSelect>
              </div>
            </div>
            <div class="main flex items-center">
              <div class="title mr-6">频率</div>
              <div class="flex">
                <HrtSelect
                  v-model="item.drugUsage"
                  placeholder="频率"
                  filterable
                  size="small"
                >
                  <HrtOption
                    v-for="(drugUsage, drugUsageIndex) in drugUsageList"
                    :key="drugUsageIndex"
                    :label="drugUsage"
                    :value="drugUsage"
                  />
                </HrtSelect>
              </div>
            </div>
          </div>
          <div class="flex items-center justify-between mt-8">
            <div class="main flex items-center">
              <div class="title mr-6">服药时间</div>
              <div class="flex">
                <HrtSelect
                  v-model="item.medicineTime"
                  placeholder="服药时间"
                  filterable
                  size="small"
                >
                  <HrtOption
                    v-for="(
                      medicineTime, medicineTimeIndex
                    ) in medicineTimeList"
                    :key="medicineTimeIndex"
                    :label="medicineTime.label"
                    :value="medicineTime.value"
                  />
                </HrtSelect>
              </div>
            </div>
            <div class="main flex items-center">
              <div class="title mr-6">用法</div>
              <div class="flex">
                <el-autocomplete
                  v-model="item.drugMode"
                  style="width: 116px"
                  :popper-append-to-body="false"
                  :fetch-suggestions="querySearchAsyncDrugMode"
                  placeholder="用法"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="delete" @click="deleteDrug(index)">删除</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { CaretBottom, CaretTop } from '@element-plus/icons-vue';
import { cloneDeep } from 'lodash-es';
import { flattenObj } from '@/utils';
import { getDrugSystemList } from '@/api/drug';
import {
  ingredientsUnitList,
  drugModeList,
  drugUsageList,
  medicineTimeList,
  DEFAULT_DRUG_DATA,
} from './constants';
import { Drug, PickPartial } from './types';

defineOptions({
  name: 'EditDrugTable',
});

defineExpose({
  submit,
});

const { paramsData = [] } = defineProps<{
  paramsData: Drug[];
}>();
const emit = defineEmits<{
  change: [value: PickPartial<Drug, 'type'>[]];
}>();

const editDrugData = ref<PickPartial<Drug, 'type'>[]>([]);
const originData = ref<Drug[]>([]);
const addedDrugs = ref<Drug[]>([]);
const removedDrugs = ref<Drug[]>([]);
const modifiedDrugs = ref<Drug[]>([]);
const modifiedPropertiesArray = ref<Partial<Drug>[]>([]);
/** 药品调整方案 */
const drugOperation = ref<string[]>([]);
const dealedParamsData = ref([]);

onMounted(() => {
  //处理渲染参数
  editDrugData.value = JSON.parse(JSON.stringify(paramsData));
  originData.value = JSON.parse(JSON.stringify(paramsData));
  addEmptyRow();
});

watch(
  () => paramsData,
  (value, oldValue) => {
    if (JSON.stringify(value) !== JSON.stringify(oldValue)) {
      editDrugData.value = JSON.parse(JSON.stringify(paramsData));
    }
  },
  {
    deep: true,
  }
);

watch(
  editDrugData,
  () => {
    dealLastRow(editDrugData.value[editDrugData.value.length - 1]);
    emit('change', editDrugData.value.slice(0, -1));
  },
  { deep: true }
);

function addEmptyRow() {
  let isHasEmptyRow = editDrugData.value.some(item => item.type === 1);
  if (!isHasEmptyRow) {
    editDrugData.value.push(cloneDeep(DEFAULT_DRUG_DATA));
  }
}

/**
 * 校验用药的必填项是否都填写
 * @param drug 用药信息
 */
function validateDrug(drug: PickPartial<Drug, 'type'>): boolean {
  if (!drug) return false;
  const requiredFields = [
    drug.drugName,
    drug.drugAmount.ingredients,
    drug.drugAmount.contentUnit,
    drug.drugSpec.ingredients,
    drug.drugSpec.contentUnit,
    drug.drugMode,
    drug.drugUsage,
    drug.medicineTime,
  ];
  return requiredFields.every(field => field); // 所有必填项都填写了
}

/**
 * 判断最后一项是否完成填写所有填写完新增
 * @param obj
 */
function dealLastRow(obj: PickPartial<Drug, 'type'>) {
  if (validateDrug(obj)) {
    const { type, ...lastObj } =
      editDrugData.value[editDrugData.value.length - 1];
    if (type) {
      editDrugData.value[editDrugData.value.length - 1] = { ...lastObj };
    } else {
      //添加新的空白项
      addEmptyRow();
    }
  }
}

function getDrugSpecStr(obj: {
  ingredients: string;
  contentUnit: string;
  packageNum: string;
  unit: string;
  packageUnit: string;
}) {
  let ingredients = obj.ingredients + obj.contentUnit;
  let packageContent = obj.packageNum ? '*' + obj.packageNum + obj.unit : '';
  let packageUnit = obj.packageUnit ? '/' + obj.packageUnit : '';
  return ingredients + packageContent + packageUnit;
}

function getEatDrugTime(value: number | string) {
  const res = medicineTimeList.filter(item => item.value === Number(value));
  return res[0]?.label || '';
}

function querySearchAsync(queryString, cb) {
  let params = {
    keyword: queryString,
    typeId: '',
    limit: false,
  };
  getDrugSystemList(params)
    .then((res: any) => {
      if (res.drugList.length) {
        res.drugList.forEach((item: { [x: string]: string; drugSpec: any }) => {
          item['drugSpecStr'] = getDrugSpecStr(item.drugSpec);
        });
      }
      cb(res.drugList);
    })
    .catch(() => {});
}
function handleSelectDrugName(
  item: {
    commonName: any;
    drugUsage: any;
    drugSpec: any;
    drugName: any;
    drugMode: any;
    takingTime: any;
  },
  index: string | number
) {
  let { commonName, drugUsage, drugSpec, drugName, drugMode, takingTime } =
    item;

  editDrugData.value[index].medicineTime = (medicineTimeList.find(
    v => v.label === takingTime
  )?.value ?? '') as string;
  editDrugData.value[index].commonName = commonName;
  editDrugData.value[index].drugName = drugName;
  editDrugData.value[index].drugUsage = drugUsage;
  editDrugData.value[index].drugMode = drugMode;
  editDrugData.value[index].drugSpec = drugSpec;
  editDrugData.value[index].drugAmount.ingredients = drugSpec.ingredients;
  editDrugData.value[index].drugAmount.contentUnit = drugSpec.contentUnit;
}

function createStateFilter(queryString: string) {
  return (state: { value: string }) => {
    return state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
  };
}

function querySearchAsyncDrugMode(
  queryString: any,
  cb: (arg0: { value: string }[]) => void
) {
  const results = queryString
    ? drugModeList.filter(createStateFilter(queryString))
    : drugModeList;

  setTimeout(() => {
    cb(results);
  });
}

/**
 * 删除项
 * @param index 索引
 */
function deleteDrug(index: number) {
  editDrugData.value.splice(index, 1);
}

/**
 * 安全处理药品规格数值增减
 * @param origin - 原始值（支持字符串/数字/undefined类型，用于处理输入框值）
 * @param opNum - 操作数值（1表示增加，-1表示减少）
 * @returns 计算后的新数值（始终返回数字类型）
 *
 * 特性：
 * 1. 最小值保护：数值最低保持为1（防止0或负数）
 * 2. 初始值处理：当原始值为0时，首次操作设为1
 * 3. 安全递减：当值大于1时才允许减少
 */
function increase(origin: string | number | undefined | null, opNum: number) {
  // 统一转换为数字类型，处理空值情况（undefined/null/空字符串转为0）
  const res = Number(origin ? origin : 0);
  if (res !== 0) {
    if (opNum === 1) {
      return res + opNum;
    } else {
      if (res <= 1) {
        return res;
      }
      if (res > 1) {
        return res + opNum;
      }
    }
  } else {
    return 1;
  }
}

/**
 * 判断两次用药是否相同
 * @param drug1 上次用药方案
 * @param drug2 当前用药方案
 */
function isEqual(drug1: Drug, drug2: Drug): boolean {
  return (
    drug1.drugName === drug2.drugName &&
    drug1.drugAmount.ingredients === drug2.drugAmount.ingredients &&
    drug1.drugAmount.contentUnit === drug2.drugAmount.contentUnit &&
    drug1.drugSpec.ingredients === drug2.drugSpec.ingredients &&
    drug1.drugSpec.contentUnit === drug2.drugSpec.contentUnit &&
    drug1.drugSpec.packageNum === drug2.drugSpec.packageNum &&
    drug1.drugSpec.unit === drug2.drugSpec.unit &&
    drug1.drugSpec.packageUnit === drug2.drugSpec.packageUnit &&
    drug1.drugMode === drug2.drugMode &&
    drug1.drugUsage === drug2.drugUsage &&
    drug1.medicineTime === drug2.medicineTime
  );
}

function compareDrugArrays(
  originalArray: {
    type: number;
    commonName: string;
    drugName: string;
    drugAmount: { ingredients: string; contentUnit: string };
    drugSpec: {
      ingredients: string;
      contentUnit: string;
      packageNum: string;
      unit: string;
      packageUnit: string;
    };
    drugMode: string;
    drugUsage: string;
    medicineTime: string;
  }[],
  modifiedArray: any
) {
  const addedDrugs: Drug[] = [];
  const removedDrugs: Drug[] = [];
  const modifiedDrugs: Drug[] = [];

  // 创建一个 Map 以便根据药品名称和规格进行查找
  const modifiedMap = new Map();

  // 遍历修改后的数组，将药品按照名称和规格存入 Map
  for (const modifiedDrug of modifiedArray) {
    const { drugName, medicineTime } = modifiedDrug;

    const key = `${drugName}-${medicineTime}`;
    modifiedMap.set(key, modifiedDrug);
  }

  // 遍历原始数组
  for (const originalDrug of originalArray) {
    const { drugName, medicineTime } = originalDrug;

    const key = `${drugName}-${medicineTime}`;

    const modifiedDrug = modifiedMap.get(key);
    if (!modifiedDrug) {
      // 如果找不到匹配的药品，则表示被删除了
      removedDrugs.push(originalDrug);
    } else {
      // 如果找到匹配的药品，则表示可能被修改了
      if (!isEqual(originalDrug, modifiedDrug)) {
        modifiedDrugs.push(modifiedDrug);
      }
      // 从 Map 中移除已处理的药品
      modifiedMap.delete(key);
    }
  }

  // 剩下的药品都是新增的
  addedDrugs.push(...modifiedMap.values());

  return {
    addedDrugs,
    removedDrugs,
    modifiedDrugs,
  };
}

//判断是否有相同药品
function hasDuplicateData(array: Drug[]) {
  const seen = new Set();

  for (const item of array) {
    const key = `${item.drugName}-${item.medicineTime}`;
    if (seen.has(key)) {
      return true; // 发现相同药品名称和规格的元素
    }
    seen.add(key);
  }

  return false; // 没有重复的元素
}

//比较被修改过的每一个属性
function getChangedDrugObj(originalArray: Drug[], modifiedArray: Drug[]) {
  //先找到原始数据中的所有修改的数据
  let originModifiedData: Drug[] = [];
  modifiedArray.forEach(item => {
    originalArray.forEach(_item => {
      if (_item.drugName === item.drugName) {
        originModifiedData.push(_item);
      }
    });
  });
  // console.log(
  //   originModifiedData,
  //   '----------originModifiedData-------------'
  // );
  // console.log(modifiedArray, '-------modifiedArray-------------');
  // 创建新数组来存放修改过的属性
  const modifiedPropertiesList: Partial<Drug>[] = [];
  // 遍历修改过的药品数组
  for (const modifiedDrug of modifiedArray) {
    // 找到对应的原始药品对象
    const originalDrug = originModifiedData.find(
      drug => drug.drugName === modifiedDrug.drugName
    );

    // 检查每个属性是否已被修改
    const modifiedProperties: Partial<Drug> = {};
    for (const key in modifiedDrug) {
      if (
        // eslint-disable-next-line no-prototype-builtins
        modifiedDrug.hasOwnProperty(key) &&
        JSON.stringify(modifiedDrug[key]) !==
          JSON.stringify(originalDrug?.[key] || {})
      ) {
        modifiedProperties[key] = modifiedDrug[key];
      }
    }

    // 添加到新数组中
    modifiedPropertiesList.push({
      drugName: modifiedDrug.drugName,
      ...modifiedProperties,
    });
  }
  modifiedPropertiesArray.value = modifiedPropertiesList;
}

/** 处理药物调整方案 */
function dealDrugOperation() {
  let resArr: string[] = [];
  if (removedDrugs.value.length) {
    removedDrugs.value.forEach(item => {
      let removeStr = `停用:${item.drugName}`;
      resArr.push(removeStr);
    });
  }
  if (addedDrugs.value.length) {
    addedDrugs.value.forEach(item => {
      let addedStr = `增加:${item.drugName},${getDrugSpecStr(
        item.drugSpec
      )},单次用量:${item.drugAmount.ingredients}${
        item.drugAmount.contentUnit
      },${item.drugUsage},${getEatDrugTime(
        item.medicineTime
      )},${item.drugMode}`;
      resArr.push(addedStr);
    });
  }
  if (modifiedPropertiesArray.value.length) {
    modifiedPropertiesArray.value.forEach(item => {
      let modifieStr = `修改:${item.drugName},${
        item.drugAmount
          ? '单次用量改为:' +
            item.drugAmount.ingredients +
            item.drugAmount.contentUnit +
            ','
          : ''
      }${item.drugUsage ? '频率改为:' + item.drugUsage + ',' : ''}${
        item.medicineTime
          ? '用药时间改为:' + getEatDrugTime(item.medicineTime) + ','
          : ''
      }${item.drugMode ? '用法改为:' + item.drugMode : ''}`;
      resArr.push(modifieStr);
    });
  }
  drugOperation.value = resArr;
  return resArr;
}

function submit() {
  //这里要先提示是否有相同药品,再进行操作记录比较,不然存在漏洞
  let resData = JSON.parse(JSON.stringify(editDrugData.value));
  //如果最后一项全部为空,则代表前面所有都填写完成
  resData.pop();
  let lastObj = flattenObj(editDrugData.value);
  delete lastObj['type'];
  //判断最后一项是否全部为空,全部为空则代表上一项填完了
  if (Object.values(lastObj).every(item => !item)) {
    // console.log('全部为空正常提交!');
    //还要判断里面每一项必填项是否填写完
    let isFinishedAll = resData.every((item: PickPartial<Drug, 'type'>) =>
      validateDrug(item)
    );

    if (isFinishedAll) {
      if (!hasDuplicateData(resData)) {
        const result = compareDrugArrays(originData.value, resData);
        addedDrugs.value = result.addedDrugs;
        removedDrugs.value = result.removedDrugs;
        modifiedDrugs.value = result.modifiedDrugs;
        //处理修改的属性,具体到每个key
        getChangedDrugObj(originData.value, result.modifiedDrugs);
        //处理调整方案参数为数组
        let drugOperation = dealDrugOperation();
        dealedParamsData.value = Object.freeze(
          JSON.parse(JSON.stringify(resData))
        );
        return { dealedParamsData: dealedParamsData.value, drugOperation };
      } else {
        return ElMessage.warning('存在相同药品!请删除重复药品!');
      }
    } else {
      return ElMessage.warning('请填写完整!');
    }
  } else {
    return ElMessage.warning('请填写完整!');
  }
}
</script>
<style scoped lang="less">
.edit-table-box {
  box-sizing: border-box;
  font-size: 14px;
  font-weight: 400;
  color: #203549;
  margin: 8px 0;
  .item {
    background: #f7f8fa;
    border-radius: 2px;
    .item-index {
      font-size: 14px;
      color: #3a4762;
      line-height: 36px;
    }
  }
}
.item-main {
  .main {
    flex: 1;
    .title {
      color: #3a4762;
      width: 56px;
      text-align: justify;
      text-align-last: justify;
      display: inline-block;
    }
  }
}
.data-input {
  width: 90px;
  .el-input__wrapper {
    box-shadow: none;
    border-radius: 2px;
    border: 1px solid #dcdee0;
    height: 34px;
    padding: 0 0 0 15px;
  }
  .el-input__inner {
    font-size: 14px;
    font-weight: 400;
    color: #203549;
  }
  .el-input__suffix {
    width: 24px;
    right: 0;
  }
}
.delete {
  color: #e63746;
  cursor: pointer;
}
</style>
