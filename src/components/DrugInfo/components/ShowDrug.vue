<template>
  <div class="content-box">
    <div class="show-box">
      <div class="label">
        <span>当前用药</span>
        <span>
          {{
            drugInfos.medicationTime
              ? dayjs(new Date(drugInfos.medicationTime)).format('YYYY-MM-DD')
              : ''
          }}
        </span>
      </div>
      <div class="table-box">
        <el-table
          :data="drugInfos.drugList"
          style="width: 100%"
          header-row-class-name="head-class"
        >
          <el-table-column type="index" label="序号" width="50" />
          <el-table-column
            prop="drugName"
            label="药品名称"
            align="center"
            width="250"
          />
          <el-table-column prop="drugSpec" label="药品规格" align="center">
            <template #default="scope">
              {{ getDrugSpecStr(scope.row.drugSpec) }}
            </template>
          </el-table-column>
          <el-table-column prop="drugAmount" align="center" label="单次剂量">
            <template #default="scope">
              {{ drugAmountText(scope.row.drugAmount) }}
            </template>
          </el-table-column>
          <el-table-column prop="drugUsage" align="center" label="频率" />

          <el-table-column prop="medicineTime" align="center" label="服药时间">
            <template #default="scope">
              <span>
                {{ getEatDrugTime(scope.row.medicineTime) || '--' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="drugMode" align="center" label="用法" />
        </el-table>
      </div>
      <div class="des-box">
        <div class="des-item">
          <div class="des-label">药物调整原因</div>
          <div class="des-details">
            <span v-for="(ite, index) in drugInfos.adjustReason" :key="index">
              {{ index + 1 }}.
              {{ ite === '其他' ? `其他:${drugInfos.adjustReasonOther}` : ite }}
              ;
            </span>
            <span v-if="drugInfos?.adjustReason?.length === 0">--</span>
          </div>
        </div>
        <div class="des-item">
          <div class="des-label">药物调整意见</div>
          <div class="des-details">
            {{ drugInfos.advice || '--' }}
          </div>
        </div>
        <div class="des-item">
          <div class="des-label">药物调整方案</div>
          <div class="des-details">
            <span>
              {{
                drugInfos.createTime
                  ? dayjs(new Date(drugInfos.createTime)).format(
                      'YYYY-MM-DD HH:mm:ss'
                    )
                  : ''
              }}
            </span>
            <div>
              {{
                drugInfos.drugOperation && isJSON(drugInfos.drugOperation)
                  ? JSON.parse(drugInfos.drugOperation).join('；') || '--'
                  : drugInfos.drugOperation || '--'
              }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import { getDrugPatientDetails } from '@/api/drug';
import dayjs from 'dayjs';
import { medicineTimeList } from "./constants";
export default {
  name: 'DrugInfoDrawer',
  computed: {
    dayjs() {
      return dayjs;
    },
  },
  components: {},
  props: {
    //药品id用于查看详情,仅查看
    drugInfoId: {
      type: Number,
    },
    drugInfo: {
      type: Object,
    },
  },
  data() {
    return {
      drugInfos: {},
    };
  },
  mounted() {
    if (this.drugInfoId) {
      this.getDrugPatientDetail(this.drugInfoId);
    } else {
      this.drugInfos = this.drugInfo || {};
    }
  },
  methods: {
    getDrugPatientDetail(drugInfoId) {
      getDrugPatientDetails({ drugInfoId }).then(res => {
        this.drugInfos = res;
      });
    },
    getDrugSpecStr(obj) {
      let ingredients = obj.ingredients + obj.contentUnit;
      let packageContent = obj.packageNum
        ? '*' + obj.packageNum + obj.unit
        : '';
      let packageUnit = obj.packageUnit ? '/' + obj.packageUnit : '';
      return ingredients + packageContent + packageUnit;
    },
    getEatDrugTime(value) {
      const res = medicineTimeList.filter(item => item.value === Number(value));
      return res[0]?.label || '';
    },
    drugAmountText(obj) {
      let text = '';
      if (obj.custom) {
        text = obj.custom;
      } else {
        text = obj.ingredients + obj.contentUnit;
      }
      return text;
    },
    //判断是否为json字符串
    isJSON(str) {
      if (typeof str == 'string') {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
            return false;
          }
        } catch (e) {
          // console.log('error：'+str+'!!!'+e);
          return false;
        }
      }
      // console.log('It is not a string!')
    },
  },
};
</script>

<style scoped lang="less">
:deep(.el-drawer) {
  height: calc(100vh - 60px);
}
:deep(.rtl) {
  top: 60px !important;
  bottom: 0;
}
:deep(.el-drawer__header) {
  padding: 24px 24px 0 24px;
  text-align: left;
  margin: 0;
  .el-drawer__close-btn {
    display: none;
  }
}
.content-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9e8eb;
  .right-line {
    font-size: 16px;
    font-weight: bold !important;
    color: #101b25;
  }
  .el-icon-close {
    font-size: 16px;
  }
}
.content-box {
  box-sizing: border-box;
  padding: 24px;
  height: 100%;
  .show-box {
    box-sizing: border-box;
    padding-right: 84px;
    .label {
      span:first-child {
        font-size: 14px;
        font-weight: bold;
        color: #203549;
      }
      span:last-child {
        margin-left: 8px;
        font-size: 14px;
        font-weight: 400;
        color: #8193a3;
      }
    }
  }
}
:deep(.table-box) {
  margin-top: 8px;
  .head-class {
    .el-table__cell {
      background-color: #f7f8fa !important;
      .cell {
        font-size: 14px;
        font-weight: bold !important;
        color: #203549;
        min-width: 60px;
      }
    }
  }
  .el-table__body-wrapper {
    .el-table__body {
      .el-table__cell {
        font-size: 14px;
        color: #203549;
        border: none;
        .cell {
          line-height: 16px;
        }
      }
    }
  }
  .el-table--border::after,
  .el-table--group::after,
  .el-table::before {
    background-color: transparent;
  }
  .el-table td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    border-bottom: none;
  }
}
.des-box {
  margin-top: 24px;
  .des-item {
    margin-bottom: 24px;
    .des-label {
      font-size: 14px;
      font-weight: bold;
      color: #203549;
      margin-bottom: 6px;
    }
    .des-details {
      font-size: 14px;
      font-weight: 400;
      color: #203549;
      line-height: 20px;
    }
  }
}
.edit-button {
  display: flex;
  justify-content: center;
}
.sub-button {
  width: 76px;
  border-radius: 2px;
  background-color: #0a73e4;
  color: #ffffff;
  font-size: 14px;
}
.edit-box {
  height: 100%;
}
</style>
