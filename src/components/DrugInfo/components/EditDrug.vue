<template>
  <HrtDialog
    v-model="chooseDrugVisibles"
    title="药物调整"
    draggable
    :modal="false"
    size="extraLarge"
    class="drug-dialog"
    @close="emit('update:chooseDrugVisible', false)"
  >
    <div class="page-wrapper">
      <div class="reason-box">
        <div class="reason-label">药物调整原因</div>
        <div class="reason-choose">
          <HrtCheckboxGroup v-model="adjustReason">
            <HrtCheckbox
              v-for="item in checkItemList"
              :key="item.value"
              :label="item.name"
              :value="item.name"
              :class="{ 'other-item': item.value === 16 }"
              @change="(value: any) => changeDrug(item)"
            >
              {{ item.name }}
              <HrtInput
                v-if="item.value === 16"
                v-model="adjustReasonOther"
                size="middle"
                class="other-input"
                :disabled="!isDisabledOther"
              />
            </HrtCheckbox>
          </HrtCheckboxGroup>
        </div>
        <div class="editdes-box">
          <div class="des-label">当前用药</div>
          <EditDrugTable ref="RefEditDrugTable" :params-data="paramsData" />
        </div>
        <div class="editdes-box">
          <div class="des-label">开始用药日期</div>
          <div>
            <HrtDatePicker
              v-model="medicationTime"
              type="date"
              clearable
              placeholder="选择日期时间"
              value-format="x"
              format="YYYY/MM/DD"
              @change="changeDateHanlder"
            />
          </div>
        </div>
        <div class="editdes-box">
          <div class="des-label">药物调整意见</div>
          <div>
            <HrtInput
              v-model="advice"
              placeholder="请输入"
              class="advice-input"
            />
          </div>
        </div>
      </div>
      <HrtDialog
        v-model="isShowChangeDrug"
        :close-on-click-modal="false"
        center
        append-to-body
        class="second-dialog"
      >
        <div v-for="(item, i) in drugOperation" :key="i">{{ item }}</div>
        <div v-if="drugOperation.length === 0">本次未调整具体药物</div>
        <template #footer>
          <span class="dialog-footer">
            <HrtButton class="close-btn" @click="isShowChangeDrug = false">
              取 消
            </HrtButton>
            <HrtButton type="primary" class="sure-btn" @click="confirmDrug">
              确 定
            </HrtButton>
          </span>
        </template>
      </HrtDialog>
    </div>
    <template #footer>
      <div class="btn-footer-box flex justify-between items-end">
        <div class="tracking-box">
          <div>跟踪用药情况</div>
          <div class="flex items-center">
            <HrtSwitch
              v-model="adjustDrugTrack"
              @change="reasonTextarea = ''"
            />
            <span class="ml-8">
              勾选后系统会立即给患者发送调药提醒； 且3日后提醒您跟踪用药情况。
            </span>
          </div>
          <div v-if="!adjustDrugTrack" class="mt-16">
            <div class="mb-8">
              <span class="required">*</span>
              无需跟踪用药原因
            </div>
            <HrtInput
              v-model="reasonTextarea"
              maxlength="100"
              style="width: 400px"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="无需跟踪用药原因(5-100字)"
              show-word-limit
              type="textarea"
              resize="none"
            />
          </div>
        </div>
        <div>
          <HrtButton plain @click="cancelEdit">取消</HrtButton>
          <HrtButton type="primary" plain class="sub-button" @click="submit">
            确定
          </HrtButton>
        </div>
      </div>
    </template>
  </HrtDialog>
</template>

<script setup lang="ts">
import EditDrugTable from './EditDrugTable.vue';
import { IReasonsForDrugAdjustment, Drug } from './types';

const props = defineProps({
  paramsData: {
    type: Array as PropType<Drug[]>,
    default: () => [] as Drug[],
    required: true,
  },
  otherAdjustDrugInfo: {
    type: Object,
    default: () => ({
      adjustReasonList: [],
      advice: '',
      adjustReasonOther: '',
    }),
    required: false,
  },
  chooseDrugVisible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  'update:chooseDrugVisible',
  'confirmDrug',
  'cancelEdit',
]);

const adjustDrugTrack = ref<boolean>(true);
const chooseDrugVisibles = ref<boolean>(false);
const adjustReason = ref([]);
const adjustReasonOther = ref<string>('');
const medicationTime = ref<null | number>(new Date().getTime());
const advice = ref<string>('');
const isShowChangeDrug = ref<boolean>(false);
const drugOperation = ref([]); // 调整方案
const dealedParamsData = ref([]);
const RefEditDrugTable = ref(null);
const reasonTextarea = ref<string>('');

// 药物调整原因
const checkItemList = ref<IReasonsForDrugAdjustment[]>([
  { name: '心绞痛', value: 0 },
  { name: '血压偏高', value: 1 },
  { name: '血压偏低', value: 2 },
  { name: '心率偏快', value: 3 },
  { name: '心率偏慢', value: 4 },
  { name: '血糖偏高', value: 5 },
  { name: '血糖偏低', value: 6 },
  { name: '血脂不达标', value: 7 },
  { name: 'INR不达标', value: 8 },
  { name: '肝功能异常', value: 9 },
  { name: '肌肉损伤', value: 10 },
  { name: '心功能不全', value: 11 },
  { name: '电解质紊乱', value: 12 },
  { name: '消化道出血', value: 13 },
  { name: '出血风险高', value: 14 },
  { name: '糖化血红蛋白不达标', value: 15 },
  { name: '其他', value: 16 },
]);

// 是否显示其他原因输入框
const isDisabledOther = computed(() => {
  return adjustReason.value.some(item => item === '其他');
});

const initData = () => {
  advice.value =
    props.otherAdjustDrugInfo.advice ||
    '按调整后用药方案服药，必要时请在在线咨询窗口联系您的专属医生';
  adjustReasonOther.value = props.otherAdjustDrugInfo.adjustReasonOther;
  adjustReason.value = props.otherAdjustDrugInfo.adjustReasonList;
  chooseDrugVisibles.value = props.chooseDrugVisible;
};

const changeDateHanlder = (val: string | number | Date) => {
  const selectedDate = new Date(val).getTime();
  if (selectedDate > Date.now()) {
    medicationTime.value = null;
    ElMessage.error('不能选择未来时间!');
  }
};

const submit = async () => {
  const result = await RefEditDrugTable.value?.submit();
  const { dealedParamsData: newData, drugOperation: newOperation } = result;

  if (newData) {
    drugOperation.value = newOperation;
    if (
      adjustReason.value.length === 0 ||
      !advice.value ||
      !medicationTime.value
    ) {
      ElMessage.warning('请填药物调整原因、开始用药日期和意见!');
    } else if (!adjustDrugTrack.value && reasonTextarea.value.length < 5) {
      ElMessage.warning('无需跟踪用药原因至少填写5个字!');
    } else {
      dealedParamsData.value = newData;
      if (!dealedParamsData.value.length) {
        ElMessage.warning('当前用药不能为空!');
        return;
      }
      if (isDisabledOther.value && !adjustReasonOther.value) {
        ElMessage.warning('请填写其他药物调整原因!');
        return;
      }
      isShowChangeDrug.value = true;
    }
  }
};

const confirmDrug = () => {
  const params = {
    advice: advice.value,
    type: 3,
    drugOperation: JSON.stringify(drugOperation.value),
    adjustReason: adjustReason.value,
    drugDetailList: dealedParamsData.value,
    adjustReasonOther: adjustReasonOther.value,
    outId: '',
    adjustDrugTrack: adjustDrugTrack.value,
    medicationTime: medicationTime.value,
    untrackReason: reasonTextarea.value,
  };
  isShowChangeDrug.value = false;
  emit('confirmDrug', params);
};

const changeDrug = (item: { value: number }) => {
  if (item.value === 16) adjustReasonOther.value = '';
};

const cancelEdit = () => {
  emit('cancelEdit');
};

watch(
  () => props.chooseDrugVisible,
  val => {
    chooseDrugVisibles.value = val;
  }
);

watch(
  () => chooseDrugVisibles.value,
  val => {
    if (val === false) emit('update:chooseDrugVisible', false);
  }
);

initData();
</script>

<style lang="less">
.drug-dialog {
  .el-dialog {
    min-width: 920px;
    min-height: 700px;
  }
}
</style>

<style scoped lang="less">
.drug-dialog {
  .el-dialog {
    min-width: 920px;
    min-height: 700px;
  }
}
.page-wrapper {
  height: 100%;
  padding: 0 16px;
}
.reason-box {
  box-sizing: border-box;
  min-height: 500px;
  border-bottom: 1px solid #e9e8eb;
  overflow: auto;
  &::-webkit-scrollbar {
    height: 4px;
  }
  &::-webkit-scrollbar-thumb {
    height: 4px;
    background: #bebebe;
    border-radius: 5px;
  }
}
.reason-label {
  font-size: 14px;
  font-weight: 400;
  color: #203549;
  margin: 12px 0;
}
:deep(.reason-choose) {
  .el-checkbox {
    font-size: 14px;
    font-weight: 400;
    color: #203549;
    width: 156px;
    margin-bottom: 8px;
    margin-right: 0;
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      border-color: #0a73e4;
      background-color: #0a73e4;
    }
    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #203549;
    }

    .other-input {
      width: 435px;
      height: 32px !important;
      .el-input__inner {
        height: 34px;
        border-radius: 2px;
      }
    }
  }
  .el-checkbox.other-item {
    width: 490px;
  }
}

.editdes-box {
  margin-top: 24px;
  box-sizing: border-box;
  .des-label {
    font-size: 14px;
    font-weight: 400;
    color: #203549;
    margin-bottom: 8px;
  }
}
.btn-footer-box {
  padding: 16px 24px 24px 24px;
  font-size: 14px;
  color: #3a4762;
  .sub-button {
    background-color: #0a73e4;
    color: #ffffff;
  }
  .el-button {
    width: 76px;
    height: 34px;
    border-radius: 2px;
    line-height: 34px;
    box-sizing: border-box;
    padding: 0;
  }
  .tracking-box {
    text-align: left;
  }
  .required {
    color: #ea1212;
  }
}

:deep(.dialog-footer) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  border-top: 1px solid #e9e8eb;
  padding: 24px 20px;

  .delet-btn {
    font-size: 14px;
    color: #ff595a;
    margin-left: 28px;
    cursor: pointer;
  }
  .close-btn {
    width: 76px;
    height: 32px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0;
    font-size: 14px;
    color: #8193a3;
  }
  .sure-btn {
    margin-left: 14px;
    background: #2c89dc;
    width: 76px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0;
    font-size: 14px;
    color: #ffffff;
  }
}
.second-dialog {
  :deep(.el-dialog--center) {
    margin-top: 25vh !important;
  }

  :deep(.el-dialog__body) {
    padding-bottom: 0;
    font-size: 16px;
  }
  :deep(.dialog-footer) {
    display: flex;
    justify-content: flex-end;
    padding: 24px 0;
    border: none;
    margin: 0;
    .sure-btn {
      margin-left: 18px;
    }
  }
}
.dialog-footers {
  display: flex;
  justify-content: flex-end;
}
</style>
