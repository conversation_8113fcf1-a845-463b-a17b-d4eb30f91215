<template>
  <Dialog
    v-model:visible="drugInfoVisibles"
    :width="1340"
    title="药物调整"
    class="drug-dialog"
  >
    <ShowDrug
      v-if="drugInfoVisible"
      :drug-info-id="drugInfoId"
      :drug-info="drugInfo"
    />
  </Dialog>
</template>
<script lang="ts" setup>
import ShowDrug from './components/ShowDrug.vue';
import Dialog from '@/components/Dialog/index.vue';

interface IProps {
  drugInfoVisible?: boolean;
  drugInfoId?: number;
  drugInfo?: object;
}
const props = withDefaults(defineProps<IProps>(), {
  drugInfoVisible: true,
  drugInfo: () => ({}),
  drugInfoId: 0,
});
const drugInfoVisibles = ref(props.drugInfoVisible);

const emit = defineEmits(['update:drugInfoVisible']);
watch(
  () => props.drugInfoVisible,
  () => {
    drugInfoVisibles.value = props.drugInfoVisible;
  }
);
watch(
  () => drugInfoVisibles.value,
  () => {
    if (drugInfoVisibles.value === false) {
      closeDialog();
    }
  }
);
const closeDialog = () => {
  emit('update:drugInfoVisible', false);
};
</script>

<style lang="less">
.drug-dialog {
  .el-dialog {
    min-width: 920px;
    min-height: 700px;
  }
}
</style>
<style scoped lang="less">
.drug-dialog {
  .el-dialog {
    min-width: 920px;
    min-height: 700px;
  }
}
.page-wrapper {
  height: 100%;
  padding: 0 20px;
}
.reason-box {
  box-sizing: border-box;
  min-height: 500px;
  border-bottom: 1px solid #e9e8eb;
  overflow: auto;
  &::-webkit-scrollbar {
    height: 4px;
  }
  &::-webkit-scrollbar-thumb {
    height: 4px;
    background: #bebebe;
    border-radius: 5px;
  }
}
.reason-label {
  font-size: 14px;
  font-weight: 400;
  color: #203549;
  margin: 12px 0;
}
:deep(.reason-choose) {
  .el-checkbox {
    font-size: 14px;
    font-weight: 400;
    color: #203549;
    width: 156px;
    margin-bottom: 8px;
    margin-right: 0;
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      border-color: #0a73e4;
      background-color: #0a73e4;
    }
    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #203549;
    }

    .other-input {
      width: 435px;
      height: 32px !important;
      .el-input__inner {
        height: 34px;
        border-radius: 2px;
      }
    }
  }
  .el-checkbox.other-item {
    width: 490px;
  }
}
.btn-box {
  text-align: right;
  .sub-button {
    background-color: #0a73e4;
    color: #ffffff;
  }
  .el-button {
    width: 76px;
    height: 34px;
    border-radius: 2px;
    line-height: 34px;
    box-sizing: border-box;
    padding: 0;
    margin-top: 24px;
  }
}

.drug-item {
  display: flex;
  justify-content: space-between;

  span {
    flex: 1;
    text-align: left;
  }

  span:not(:first-child) {
    margin-left: 20px;
  }
}
:deep(.dialog-footer) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  border-top: 1px solid #e9e8eb;
  padding: 24px 20px;

  .delet-btn {
    font-size: 14px;
    color: #ff595a;
    margin-left: 28px;
    cursor: pointer;
  }
  .close-btn {
    width: 76px;
    height: 32px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0;
    font-size: 14px;
    color: #8193a3;
  }
  .sure-btn {
    margin-left: 14px;
    background: #2c89dc;
    width: 76px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0;
    font-size: 14px;
    color: #ffffff;
  }
}
.second-dialog {
  :deep(.el-dialog--center) {
    margin-top: 25vh !important;
  }

  :deep(.el-dialog__body) {
    padding-bottom: 0;
    font-size: 16px;
  }
  :deep(.dialog-footer) {
    display: flex;
    justify-content: flex-end;
    padding: 24px 0;
    border: none;
    margin: 0;
    .sure-btn {
      margin-left: 18px;
    }
  }
}
.dialog-footers {
  display: flex;
  justify-content: flex-end;
}
</style>
