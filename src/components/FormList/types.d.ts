export interface Column {
  key: string;
  value: string;
  enSession: string;
  sessionName: string;
  rowId: number;
  rowOrder: number;
  necessity: boolean;
  uiDisable: boolean;
  uiOptions?: Record<string, any>;
  uiMethod: string;
  version: number;
}

export interface Option {
  id: number;
  pid: number;
  key: string;
  value: string;
  necessity: boolean;
  uiMethod: 'multiple';
  version: number;
}

export interface ItemItem {
  id?: number;
  pid?: number;
  key?: string;
  value?: string;
  necessity?: boolean;
  version: number;
  enSession?: string;
  sessionName?: string;
  ordered?: number;
  uiMethod?:
    | 'input'
    | 'subform_ocr_select'
    | 'text'
    | 'select'
    | 'inputNumber'
    | 'structuredText';
  options?: Option[];
  rowId?: number;
  rowOrder?: number;
  uiDisable?: boolean;
}

export interface DatumItem {
  key: string;
  value: string;
  enSession: string;
  sessionName: string;
  ordered: number;
  uiMethod?: string;
  version: number;
  items: ItemItem[];
  columns: Column[];
  uiDisable?: boolean;
  uiOptions?: Record<string, any>;
  uiRules?: Record<string, any>;
  options?: any[];
}

export interface Datum {
  key: string;
  value: string;
  version: number;
  items: DatumItem[];
  columns?: DatumItem[];
}
