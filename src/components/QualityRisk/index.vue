<template>
  <div class="lost-to-follow-up-dialog py-12 px-24">
    <div class="lost-title flex py-14 px-16">
      <img :src="qualityRiskImg" alt="" class="w-16 h-16" />
      <div class="lost-content ml-7">
        <div class="content-name">质量风险</div>
        <div v-if="type === '1'" class="risk-item flex items-center mt-10">
          <div class="identification w-5 h-5 mr-6"></div>
          入组病历上传附件
        </div>
        <template v-else>
          <div class="risk-item flex items-center mt-10">
            <div class="identification w-5 h-5 mr-6"></div>
            24小时内超2分钟通话记录
          </div>
          <div class="risk-item flex items-center mt-10">
            <div class="identification w-5 h-5 mr-6"></div>
            24小时内咨询聊天记录
          </div>
        </template>
      </div>
    </div>
    <div v-if="type !== '1'" class="situation-description">
      <div class="description-name my-16">情况说明：</div>
      <HrtInput
        v-model="textarea"
        :autosize="{ minRows: 2, maxRows: 6 }"
        type="textarea"
        placeholder="请输入风险说明（5-200字）"
        maxlength="200"
        show-word-limit
        @input="handleTextareaChange"
      />
      <div class="description-tips mt-8">以上说明将提交给质控负责人。</div>
    </div>
  </div>
</template>
<script setup lang="ts">
import qualityRiskImg from '@/assets/imgs/overview/quality-risk.png';

const props = defineProps<{
  situationTextarea?: string;
  type?: string; // 1-确认入组
}>();

const emit = defineEmits<{
  (e: 'update:situationTextarea', value: string): void;
}>();

const textarea = ref(props.situationTextarea);

const handleTextareaChange = () => {
  emit('update:situationTextarea', textarea.value || '');
};

watch(
  () => props.situationTextarea,
  value => {
    textarea.value = value;
  },
  {
    deep: true,
  }
);
</script>
<style scoped lang="less">
.lost-to-follow-up-dialog {
  font-size: 14px;
  .lost-title {
    background: #f7f8fa;
    .lost-content {
      margin-top: -3px;
      .content-name {
        font-weight: bold;
        color: #3a4762;
      }
      .risk-item {
        color: #7a8599;
        .identification {
          border-radius: 50%;
          background: #7a8599;
        }
      }
    }
  }
  .situation-description {
    .description-name {
      font-weight: bold;
      color: #3a4762;
    }
    .description-tips {
      color: #7a8599;
    }
  }
}
</style>
