<template>
  <div class="w-full h-full clear-both relative">
    <div ref="baseChartRef" class="w-full h-full"></div>
    <div
      v-show="isEmptyData || !dataComplete"
      v-loading="!dataComplete"
      :element-loading-text="loadingText"
      class="w-full h-full bg-white flex-c absolute top-0 left-0"
    >
      {{ !dataComplete ? '' : emptyText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import useCharts, { ChartType, IDefaultSize } from './useCharts';
import { Options } from '@/components/BaseChart/type';

const props = defineProps({
  type: {
    type: String as PropType<ChartType>,
    required: true,
  },
  loadingText: {
    type: String,
    default: '数据加载中...',
  },
  emptyText: {
    type: String,
    default: '暂无数据',
  },
  dataComplete: Boolean,
  options: {
    type: Object as PropType<Options>,
    default: () => ({}),
  },
  // 切换tab或者页面(未销毁)后且产生缩放时chart图片的默认尺寸
  defaultSize: {
    type: Object as PropType<IDefaultSize>,
    default: () => ({
      width: 606,
      height: 360,
    }),
    required: false,
  },
});

const { type, options, dataComplete, defaultSize } = toRefs(props);

const baseChartRef = shallowRef<HTMLElement | null>(null);

const { charts, setOptions, initChat } = useCharts({
  el: baseChartRef,
  type,
  defaultSize: defaultSize?.value,
});

const isEmptyData = computed(() => {
  const { options, type } = props;
  // todo ts类型待解决
  const { xAxis, series } = options;
  const hasXAxisData = (xAxis as any)?.data?.length;
  const hasSeriesData = (series as any)?.find(
    (item: any) => item?.data?.length
  );

  return type === 'pie' ? !hasSeriesData : !(hasXAxisData && hasSeriesData);
});

watch(
  options,
  () => {
    setOptions(options.value);
  },
  {
    deep: true,
  }
);

onMounted(() => {
  nextTick(async () => {
    await initChat();
    setOptions(options.value);
  });
});

defineExpose({
  baseChartRef: baseChartRef,
  $charts: charts,
});
</script>
