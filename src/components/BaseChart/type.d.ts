import * as echarts from 'echarts';
import { XAXisComponentOption, YAXisComponentOption } from 'echarts';

import {
  TitleComponentOption,
  TooltipComponentOption,
  GridComponentOption,
  LegendComponentOption,
} from 'echarts/components';
import {
  BarSeriesOption,
  LineSeriesOption,
  PieSeriesOption,
  FunnelSeriesOption,
} from 'echarts/charts';

type BaseOptionType =
  | XAXisComponentOption
  | YAXisComponentOption
  | TitleComponentOption
  | TooltipComponentOption
  | LegendComponentOption
  | GridComponentOption;

type cc = LineSeriesOption['markLine'];

type BaseOption = echarts.ComposeOption<BaseOptionType>;

type LineECOption = echarts.ComposeOption<LineSeriesOption | BaseOptionType>;

type BarECOption = echarts.ComposeOption<BarSeriesOption | BaseOptionType>;

type PieECOption = echarts.ComposeOption<PieSeriesOption | BaseOptionType>;

type FunnelOption = echarts.ComposeOption<FunnelSeriesOption | BaseOptionType>;

type ChartType = 'bar' | 'line' | 'pie';

type EChartsOption = echarts.EChartsOption;

type Options = LineECOption | BarECOption | PieECOption | FunnelOption;

export {
  BaseOption,
  ChartType,
  LineECOption,
  BarECOption,
  Options,
  PieECOption,
  FunnelOption,
  EChartsOption,
};
