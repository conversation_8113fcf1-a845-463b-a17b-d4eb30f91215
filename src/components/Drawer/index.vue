<template>
  <el-drawer
    append-to-body
    :modal="modal"
    :model-value="visible"
    :size="customWidth || sizeMap[size]"
    :destroy-on-close="true"
    :modal-class="drawerModalClass"
    v-bind="attrs"
    @close="onClose"
  >
    <template #header="{ titleId, titleClass }">
      <div class="header-title">
        <h4 :id="titleId" :class="titleClass">{{ title }}</h4>
      </div>
    </template>
    <slot></slot>
    <template #footer>
      <!-- 这里是末尾的元素插槽，比如提交取消按钮-->
      <slot name="footer"></slot>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Drawer',
});

interface IProps {
  visible: boolean;
  title: string;
  size?: 'default' | 'large';
  modal?: boolean;
  customModalClass?: string;
  customWidth?: number | string;
}

const props = withDefaults(defineProps<IProps>(), {
  title: '',
  size: 'default',
  modal: false,
  customWidth: 0,
  customModalClass: '',
});

const attrs = useAttrs();

const sizeMap = {
  default: 600,
  large: 1200,
};

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

const onClose = () => {
  emits('update:visible', false);
};

const drawerModalClass = computed(() => {
  const res =
    props.size === 'large'
      ? ' hrt-drawer-modal large-size'
      : ' hrt-drawer-modal default-size';
  return props.customModalClass + res;
});
</script>

<style lang="less">
.hrt-drawer-modal {
  &.large-size .el-drawer {
    inset: 60px 0 0 calc(100% - 1200px) !important;
  }

  &.default-size .el-drawer {
    inset: 60px 0 0 calc(100% - 600px) !important;
  }

  .el-drawer {
    height: initial;
    display: flex;
    flex-direction: column;
    box-shadow: -4px 2px 7px 0 rgba(200, 201, 204, 0.3);
    border-radius: 6px 0 0 6px;
    .el-drawer__header {
      margin: 0;
      padding: 0 16px;
      border-bottom: 1px solid #e9e8eb;
      .header-title {
        height: 46px;
        font-size: 16px;
        font-weight: bold;
        color: #101b25;
        display: flex;
        align-items: center;
        justify-content: space-between;
        h4 {
          font-size: 16px;
        }
      }
      .el-drawer__close-btn {
        .el-icon {
          font-size: 20px;
        }
      }
    }
    .el-drawer__body {
      padding: 0;
      flex: 1;
      overflow-y: overlay;
      &::-webkit-scrollbar {
        width: 8px;
      }
      &::-webkit-scrollbar-thumb {
        width: 8px;
        background: #bebebe;
        border-radius: 5px;
      }
    }
    .el-drawer__footer {
      padding: 0;
      margin: 0;
    }
  }
}
</style>
