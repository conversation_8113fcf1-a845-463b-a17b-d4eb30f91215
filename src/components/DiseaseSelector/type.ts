export interface IDiseaseData {
  diseaseIds?: number[];
  specialData?: Record<string, string>;
}
export interface ISpecialConfigItem {
  id: number;
  key: string;
  maxLength?: number;
  rows?: number;
  required?: boolean;
}
export interface IDiseaseItem {
  diseaseId: number;
  diseaseName: string;
  diseaseType?: number;
  pId: number | null;
  children: IDiseaseItem[];
  originChildren?: IDiseaseItem[];
  chooseType: 'title' | 'radio' | 'checkbox';
  level?: number;
  isLeaf?: boolean;
  key?: string | null;
}
export type IDiseaseList = IDiseaseItem[];
export type IDiseaseMap = Record<number, IDiseaseItem>;
export interface IProps {
  diseaseList: IDiseaseList;
  width?: string;
  dialogWidth?: string;
  diseaseData: IDiseaseData;
  specialConfig?: ISpecialConfigItem[];
  enableFilter?: boolean;
}
export interface ICheckListProps extends Omit<IProps, 'width' | 'dialogWidth'> {
  dataMap: IDiseaseMap;
  num: number;
  itemLayout?: 'block' | 'inline';
}
export interface ITreeNodeProps {
  selectedIds: number[];
  isFirst?: boolean;
  level?: number;
  nodeData: IDiseaseItem;
  specialInfo: Record<string, string>;
  searchWords: string;
  dataMap: IDiseaseMap;
  specialConfig: ICheckListProps['specialConfig'];
  itemLayout?: ICheckListProps['itemLayout'];
}
export interface IPathItem {
  id: number;
  text: string;
}
export interface IShowNameList {
  ids: number[];
  selectedPath: IPathItem[][];
  showNameList: IPathItem[];
}
