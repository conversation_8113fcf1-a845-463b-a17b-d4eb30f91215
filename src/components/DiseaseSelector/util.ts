import { cloneDeep } from 'lodash-es';
import { IDiseaseList, IDiseaseItem, IDiseaseMap, IPathItem } from './type';

export const getDataMap = (data?: IDiseaseList): IDiseaseMap => {
  if (!data) return {};
  const obj: IDiseaseMap = {};
  const recursion = (node: IDiseaseItem | IDiseaseList, level = 0) => {
    if (!node) return;
    if (Array.isArray(node)) {
      for (const v of node) {
        recursion(v);
      }
    } else {
      obj[node.diseaseId] = node;
      node.level = level;
      node.isLeaf = !node.children?.length;
      node.originChildren = node.children;
      recursion(node.children);
    }
  };
  recursion(data);
  return obj;
};
export const generateShowNameList = (data: IPathItem[][]) => {
  return data.reduce((pre, cur) => {
    pre.push(
      cur.reduce(
        (a, b) => ({
          id: b.id,
          text: a.text ? a.text + '·' + b.text : b.text,
        }),
        {} as IPathItem
      )
    );
    return pre;
  }, [] as IPathItem[]);
};
export const generateFlatData = (data: IDiseaseList) => {
  const result: IPathItem[][] = [];
  const fn = (item: IDiseaseItem, cur: IPathItem[] = []) => {
    if (item.diseaseId > 0) {
      // 手动添加的title类型， id都是小于0
      cur.push({ id: item.diseaseId, text: item.diseaseName });
    }
    if (!item.children?.length) {
      result.push(cur.slice(0));
    } else {
      for (const v of item.children) {
        fn(v, cur);
        cur.pop();
      }
    }
  };
  for (const v of data) {
    fn(v);
  }
  return result;
};
export const filterNode = (node: IDiseaseItem, keywords: string) => {
  if (!node) return false;
  return node.diseaseName.includes(keywords);
};
export const treeDataFilter = (treeData: IDiseaseList, keywords: string) => {
  if (!keywords) return [...treeData];
  const fn = (node: IDiseaseList | IDiseaseItem): IDiseaseList => {
    if (Array.isArray(node)) {
      const arr = [];
      for (const v of node) {
        arr.push(...fn(v));
      }
      return arr;
    } else {
      const isTitleType = node.chooseType === 'title';
      const _child = fn(node.children);
      const res = isTitleType ? false : filterNode(node, keywords);
      if (res || _child.length) {
        node.children = _child;
        return [node];
      }
      return [];
    }
  };
  const filterTreeData = fn(cloneDeep(treeData));
  return filterTreeData;
};
export const genereateResultTreeData = (
  ids: number[],
  dataMap: IDiseaseMap
) => {
  const newIds = ids.slice(0);
  for (const id of ids) {
    const curPId = dataMap[id].pId;
    if (curPId && !newIds.includes(curPId)) {
      newIds.push(curPId);
    }
  }
  const idsToNodes = cloneDeep(newIds.map(id => dataMap[id]));
  const convertArrToTree = (pid: number | null = null) =>
    idsToNodes.reduce((pre, cur) => {
      if (cur.pId === pid) {
        cur.children = convertArrToTree(cur.diseaseId);
        pre.push(cur);
      }
      return pre;
    }, [] as IDiseaseList);
  const result = convertArrToTree();
  return result;
};

export const getTreeDataValuesByKey = (
  data: IDiseaseItem,
  key: 'diseaseId'
) => {
  const res: number[] = [];
  const fn = (node: IDiseaseItem) => {
    if (!node) return;
    res.push(node[key]);
    if (node.children?.length) {
      for (const v of node.children) {
        fn(v);
      }
    }
  };
  fn(data);
  return res;
};

export const getAfterIds = (data: IDiseaseItem) => {
  const ids = getTreeDataValuesByKey(data, 'diseaseId');
  return ids;
};

export const getRelativeIds = (data: IDiseaseItem, dataMap: IDiseaseMap) => {
  if (!data.pId) return [];
  const pNode = dataMap[data.pId];
  if (!pNode) return [];
  const res: number[] = [];
  const fn = (node: IDiseaseItem) => {
    if (!node) return;
    res.push(node.diseaseId);
    if (node.children) {
      for (const v of node.children) {
        fn(v);
      }
    }
  };
  for (const v of pNode.children) {
    if (v.diseaseId !== data.diseaseId) {
      fn(v);
    }
  }
  return res;
};

export const getForwardIds = (id: number, dataMap: IDiseaseMap) => {
  const res = [];
  let pId: number | null = id;
  while (pId && dataMap[pId] && dataMap[pId]?.chooseType !== 'title') {
    res.unshift(dataMap[pId].diseaseId);
    pId = dataMap[pId].pId;
  }
  return res;
};
export const idsTransform = (ids: number[] = [], dataMap: IDiseaseMap) => {
  const resultTreeData = genereateResultTreeData(ids, dataMap);
  const selectedPath = generateFlatData(resultTreeData);
  const showNameList = generateShowNameList(selectedPath);
  return { showNameList, selectedPath };
};
