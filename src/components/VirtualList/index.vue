<template>
  <div v-bind="containerProps as any" :style="{ height: height + 'px' }">
    <div v-bind="wrapperProps">
      <div
        v-for="item in list"
        :key="item.index"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot :item="item"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useVirtualList } from '@vueuse/core';
interface IProps {
  /** 列表数据 */
  data?: any[];
  /** 容器高度 */
  height?: number;
  /** 列表 item 高度, 默认30 */
  itemHeight?: number;
}
const props = withDefaults(defineProps<IProps>(), {
  data: () => [],
  itemHeight: () => 30,
  height: () => 300,
});
const { data: propList } = toRefs(props);

const { list, containerProps, wrapperProps, scrollTo } = useVirtualList(
  propList,
  {
    itemHeight: props.itemHeight,
  }
);

watch(
  () => props.data,
  () => {
    scrollTo(0);
  }
);
defineExpose({
  scrollTo,
});
</script>

<script lang="ts">
export default {
  name: 'VirtualList',
};
</script>

<style scoped lang="less">
// todo
</style>
