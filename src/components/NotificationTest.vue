<template>
  <div class="notification-test">
    <h3>ElNotification 测试</h3>
    <div class="button-group">
      <el-button @click="showSuccess" type="success">成功通知</el-button>
      <el-button @click="showError" type="danger">错误通知</el-button>
      <el-button @click="showWarning" type="warning">警告通知</el-button>
      <el-button @click="showInfo" type="info">信息通知</el-button>
    </div>
    
    <div class="test-info">
      <p>点击按钮测试 ElNotification 是否正常工作</p>
      <p>如果通知没有显示，请检查：</p>
      <ul>
        <li>Element Plus 是否正确安装</li>
        <li>样式是否正确引入</li>
        <li>z-index 是否被覆盖</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElNotification } from 'element-plus'

const showSuccess = () => {
  ElNotification({
    title: '成功',
    message: '这是一个成功的通知',
    type: 'success',
    duration: 3000
  })
}

const showError = () => {
  ElNotification({
    title: '错误',
    message: '这是一个错误的通知',
    type: 'error',
    duration: 3000
  })
}

const showWarning = () => {
  ElNotification({
    title: '警告',
    message: '这是一个警告的通知',
    type: 'warning',
    duration: 3000
  })
}

const showInfo = () => {
  ElNotification({
    title: '信息',
    message: '这是一个信息的通知',
    type: 'info',
    duration: 3000
  })
}
</script>

<style scoped lang="less">
.notification-test {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  
  h3 {
    margin-bottom: 20px;
    color: #333;
  }
  
  .button-group {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }
  
  .test-info {
    background: #f5f7fa;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #409eff;
    
    p {
      margin: 8px 0;
      color: #606266;
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        color: #909399;
      }
    }
  }
}
</style>
