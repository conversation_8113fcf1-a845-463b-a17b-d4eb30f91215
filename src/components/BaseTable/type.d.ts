import { VNode, ComponentPublicInstance } from 'vue';
import { TableColumnCtx } from 'element-plus/es/components/table/src/table-column/defaults';
import BaseTable, { TableProps } from '@/components/BaseTable';

export interface TablePageable {
  pageNum: number;
  pageSize: number;
  total: number;
}

export interface TableStateProps {
  tableData: any[];
  pageable: TablePageable;
  loading: boolean;
  totalParam: {
    [key: string]: any;
  };
}

export type TypeProps = 'index' | 'radio' | 'expand';

// 格式化
export type FormatProps = 'time' | 'date';

export type RenderScope<T> = {
  row: T;
  $index: number;
  column: TableColumnCtx<T>;
  [key: string]: any;
};

export type HeaderRenderScope<T> = {
  $index: number;
  column: TableColumnCtx<T>;
  [key: string]: any;
};

export interface ColumnProps<T = any>
  extends Partial<Omit<TableColumnCtx<T>, 'type'>> {
  /** 列类型 */
  type?: TypeProps;
  /** 列类型 */
  format?: FormatProps;
  /** 自定义单元格内容渲染（tsx语法） */
  render?: (scope: RenderScope<T>) => VNode | string;
  /** 自定义表头内容渲染（tsx语法） */
  headerRender?: (scope: HeaderRenderScope<T>) => VNode;
  /** 自定义规则参数 */
  [key: string]: any;
}

export type ProTableInstance = Omit<
  InstanceType<typeof BaseTable>,
  keyof ComponentPublicInstance | keyof TableProps
>;
