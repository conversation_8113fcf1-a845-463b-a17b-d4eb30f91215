<template>
  <RenderTableColumn v-bind="column" />
</template>

<script setup lang="tsx">
import { ColumnProps, RenderScope, HeaderRenderScope } from '../type';
import { formatValue, handleRowAccordingToProp, handleProp } from '../utils';
import { formatTime } from '@/utils';

defineProps<{ column: ColumnProps }>();

const slots = useSlots();

// 时间格式化
const renderTimeVNode = (time: number, type: ColumnProps['format']) => {
  try {
    const times = formatTime(time, true);
    if (type === 'date') return !times?.length ? '--' : times[0];
    return (
      <div>
        {!times?.length ? '--' : times[0]} <br />
        {times[1]}
      </div>
    );
  } catch {
    return time;
  }
};
// 渲染表格数据
const renderCellData = (item: ColumnProps, scope: RenderScope<any>) => {
  const data = formatValue(handleRowAccordingToProp(scope.row, item.prop!));
  if (['time', 'date'].includes(item.format || '')) {
    return renderTimeVNode(data === '--' ? 0 : data, item.format);
  }
  return data;
};

const RenderTableColumn = (item: ColumnProps) => {
  return (
    <el-table-column
      {...item}
      align={item.align ?? 'center'}
      showOverflowTooltip={
        item.showOverflowTooltip ?? item.prop !== 'operation'
      }
    >
      {{
        default: (scope: RenderScope<any>) => {
          if (item.render) return item.render(scope);
          if (slots[handleProp(item.prop!)]) {
            return slots[handleProp(item.prop!)]!(scope);
          }
          return renderCellData(item, scope);
        },
        header: (scope: HeaderRenderScope<any>) => {
          if (item.headerRender) return item.headerRender(scope);
          if (slots[`${handleProp(item.prop!)}Header`]) {
            return slots[`${handleProp(item.prop!)}Header`]!(scope);
          }
          return item.label;
        },
      }}
    </el-table-column>
  );
};
</script>
