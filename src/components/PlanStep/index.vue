<template>
  <HrtDialog
    v-model="dialogVisible"
    :title="title || 'sss'"
    :width="width || 960"
    class="plan-dialog"
    size="extraLarge"
    @close="close"
  >
    <div v-loading="loading">
      <div v-if="curSteps.length > 1" class="step">
        <div v-for="(item, index) in curSteps" :key="item.val" class="item">
          <div v-if="index === curStep" class="active"></div>
          <span v-else class="circle">{{ index + 1 }}</span>
          <span class="text">{{ item.name }}</span>
        </div>
      </div>
      <div class="detail">
        <div>
          付费时间：
          <span>{{ getTimeStr(timesNode.payTime) }}</span>
        </div>
        <div>
          出院时间：
          <span>{{ getTimeStr(timesNode.outHospitalTime) }}</span>
        </div>
        <div>
          入院时间：
          <span>{{ getTimeStr(timesNode.inHospitalTime) }}</span>
        </div>
      </div>
      <div class="table">
        <ReviewTable
          v-if="types?.includes(1)"
          v-show="curStepValue === 1"
          ref="reviewTableRef"
          :is-key-tracking="isKeyTracking"
          :step-change="stepChange"
        />
        <FollowUpTable
          v-if="types?.includes(2)"
          v-show="curStepValue === 2"
          ref="lifeTableRef"
          :type="2"
          :step-change="stepChange"
        />
        <FollowUpTable
          v-if="types?.includes(3)"
          v-show="curStepValue === 3"
          ref="symptomTableRef"
          :type="3"
          :step-change="stepChange"
        />
      </div>
      <div class="batch">
        <HrtSelect v-model="method" placeholder="请选择">
          <HrtOption label="前移" value="subtract" />
          <HrtOption label="后推" value="add" />
          <!-- <HrtOption label="前移" :value="1" />
          <HrtOption label="后推" :value="2" /> -->
        </HrtSelect>
        <HrtSelect v-model="value" placeholder="请选择">
          <HrtOption v-for="n in 30" :key="n" :label="n" :value="n" />
        </HrtSelect>
        <HrtSelect v-model="unit" placeholder="请选择">
          <HrtOption label="天" value="d" />
          <HrtOption label="周" value="w" />
          <!-- <HrtOption label="月" value="M" /> -->
        </HrtSelect>
        <HrtButton
          :type="isDisabled ? 'info' : 'primary'"
          :disabled="isDisabled"
          @click="confirmBatch"
        >
          调整
        </HrtButton>
        <HrtButton @click="resetBatch">重置</HrtButton>
      </div>
      <div class="btns" :class="{ pending: false }">
        <HrtButton v-if="curStep > 0" class="btn" type="primary" @click="pre">
          上一步
        </HrtButton>
        <HrtButton
          v-if="curStep < curSteps.length - 1"
          class="btn"
          type="primary"
          @click="next"
        >
          下一步
        </HrtButton>
      </div>
    </div>
    <template #footer>
      <div class="right-btns">
        <HrtButton class="btn" @click="cancel">取消</HrtButton>
        <HrtButton
          v-if="curStep === curSteps.length - 1"
          class="btn"
          :loading="confirmLoading"
          type="primary"
          :style="{ width: confirmLoading ? '100px' : '70px' }"
          @click="confirm"
        >
          {{ okText || '确认' }}
        </HrtButton>
      </div>
    </template>
  </HrtDialog>
</template>

<script setup lang="ts">
import ReviewTable from './ReviewTable.vue';
import FollowUpTable from './FollowUpTable.vue';
import dayjs from 'dayjs';
import useGlobal from '@/store/module/useGlobal';
import { reviewBatchModify } from '@/api/review';
import { followBatchModify, getTimesNode } from '@/api/followup';
import bus from '@/lib/bus';
interface IProps {
  visible: boolean;
  title?: string;
  width?: number;
  types?: (1 | 2 | 3)[];
  okText?: string;
  okCallback?: () => Promise<any>;
  isKeyTracking?: boolean;
}

const globalStore = useGlobal();
const props = withDefaults(defineProps<IProps>(), {
  types: () => [1, 2, 3],
  isKeyTracking: true,
});
const emit = defineEmits(['close']);
const dialogVisible = ref(false);
const curStep = ref(0);
const method = ref('');
const value = ref('');
const unit = ref('');
const stepChange = ref(0);
const timesNode = ref({
  inHospitalTime: '',
  outHospitalTime: '',
  payTime: '',
});
const steps = ref([
  { name: '复查方案', val: 1 },
  { name: '症状随访方案', val: 2 },
  { name: '生活方式方案', val: 3 },
]);
const confirmLoading = ref(false);
const loading = ref(false);
const reviewTableRef = shallowRef();
const lifeTableRef = shallowRef();
const symptomTableRef = shallowRef();

const refMap = {
  1: reviewTableRef,
  2: lifeTableRef,
  3: symptomTableRef,
};

const getTimeStr = time => {
  if (!time) return '--';
  return dayjs(time).format('YYYY-MM-DD');
};
const curSteps = computed(() => {
  if (!props.types) return steps.value.slice(0);
  return steps.value.filter(v => props.types?.includes(v.val as any));
});
const curStepValue = computed(() => {
  return curSteps.value[curStep.value].val;
});
const isDisabled = computed(() => {
  return !(method.value && value.value && unit.value);
});

const checkDate = () => {
  const curTableData = refMap[curStepValue.value].value?.getTableData() ?? [];
  const validData = curTableData.filter(v => !v.draft);
  const sortData = validData.sort((a, b) => a.date - b.date);
  if (!validData.length) {
    ElMessage.warning(
      `请添加${curStepValue.value === 1 ? '复查' : '随访'}数据！`
    );
    return false;
  }
  const item = sortData[0];
  const newDate = dayjs(item.date)
    [method.value](value.value, unit.value)
    .valueOf();
  if (newDate < dayjs().valueOf()) {
    ElMessage.warning('批量调整后不能小于当前时间！');
    return false;
  }
  return true;
};
const batchModifySuccess = () => {
  ElMessage.success('调整成功！');
  resetBatch();
  refMap[curStepValue.value].value?.getList();
  if (curStepValue.value === 1) {
    bus.emit('updata-review-list');
  } else {
    bus.emit('refresh-followup-list');
  }
};
const confirmBatch = () => {
  if (!checkDate()) {
    return;
  }
  const params: any = {
    patientId: globalStore.userId,
    day: (value.value as unknown as number) * (unit.value === 'd' ? 1 : 7),
    type: method.value === 'subtract' ? 1 : 2,
  };
  let req = reviewBatchModify;
  if (curStepValue.value !== 1) {
    req = followBatchModify;
    params.followType = curStepValue.value - 1;
  }
  loading.value = true;
  req(params)
    .then(batchModifySuccess)
    .finally(() => {
      loading.value = false;
    });
};
const resetBatch = () => {
  method.value = '';
  value.value = '';
  unit.value = '';
};
const confirm = async () => {
  if (props.okCallback) {
    confirmLoading.value = true;
    props.okCallback?.()?.finally(() => {
      confirmLoading.value = false;
      bus.emit('updete-todo-list');
      close();
    });
  } else {
    close();
  }
};
const pre = () => {
  curStep.value--;
  stepChange.value++;
};
const next = () => {
  curStep.value++;
  stepChange.value++;
};
const cancel = () => {
  close();
};
const close = () => {
  emit('close');
};
const getTimesNodeHandler = async () => {
  const res = await getTimesNode({ patientId: globalStore.userId });
  const { payTime, inHospitalTime, outHospitalTime } = res as any;
  timesNode.value.payTime = payTime;
  timesNode.value.inHospitalTime = inHospitalTime;
  timesNode.value.outHospitalTime = outHospitalTime;
};
defineOptions({
  name: 'PlanStep',
});

watch(
  () => props.visible,
  val => {
    dialogVisible.value = !!val;
    if (val) {
      curStep.value = 0;
      getTimesNodeHandler();
    }
  }
);
</script>

<style scoped lang="less">
.plan-dialog {
  :deep(.el-dialog__body) {
    padding: 0 !important;
  }
}
.step {
  padding: 0 16px;
  height: 56px;
  display: flex;
  justify-content: center;
  .item {
    display: flex;
    align-items: center;
    position: relative;
    &::after {
      position: absolute;
      content: '/';
      font-size: 0;
      height: 2px;
      width: 120px;
      background: #2e6be6;
      right: 16px;
    }
    .text {
      margin-left: 4px;
      margin-right: 152px;
    }
    &:last-child {
      .text {
        margin-right: 0;
      }
      &:after {
        width: 0;
      }
    }
  }
  .active {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: #2e6be6;
    border-radius: 100%;
    position: relative;
    &:after {
      display: inline-block;
      content: '/';
      font-size: 0;
      width: 8px;
      height: 12px;
      border: 2px solid #fff;
      border-top-color: transparent;
      border-left-color: transparent;
      position: absolute;
      left: 8px;
      top: 4px;
      border-radius: 2px;
      transform: rotate(45deg);
    }
  }
  .circle {
    display: inline-block;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 20px;
    font-size: 16px;
    border: 2px solid #2e6be6;
    color: #2e6be6;
    font-weight: bold;
    border-radius: 100%;
  }
}
.detail {
  border-top: 1px solid #e9e8eb;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  color: #7a8599;
  span {
    color: #3a4762;
  }
}
.table {
  width: 100%;
  height: 332px;
  padding: 0 16px;
}
.batch {
  height: 56px;
  padding: 0 16px;
  :deep(.el-select) {
    width: 120px;
    margin-right: 8px;
  }
}
.btns {
  padding: 0 16px;
  border-top: 1px solid #e9e8eb;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  :deep(.HrtButton) {
    width: 70px;
  }
  &.pending {
    justify-content: end;
  }
  .right-btns {
    position: absolute;
    right: 16px;
  }
}
</style>
