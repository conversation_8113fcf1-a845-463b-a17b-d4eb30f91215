<template>
  <div v-loading="loading">
    <HrtTable
      class="follow-table"
      :data="tableData.length ? tableData : emptyTableData"
      max-height="300"
    >
      <HrtTableColumn prop="date" label="名称" width="80">
        <template #default="scope">
          <div class="flex items-center">
            <span>{{ scope.row.name }}</span>
            <img
              v-if="!scope.row.draft"
              class="w-14 h-14 ml-4 cursor-pointer"
              :src="addImg"
              @click="() => addItem(scope.row)"
            />
          </div>
        </template>
      </HrtTableColumn>
      <HrtTableColumn prop="date" label="时间" width="140">
        <template #default="scope">
          <span>
            {{
              scope.row.date ? dayjs(scope.row.date).format('YYYY-MM-DD') : ''
            }}
          </span>
        </template>
      </HrtTableColumn>
      <HrtTableColumn prop="address" label="复查项目" width="340">
        <template #default="scope">
          <div
            v-for="(v, index) in scope.row.reviewCheckList"
            :key="index"
            class="item-card"
          >
            <img class="w-14 h-14" :src="followImg" />
            <span>{{ v.checkName }}{{ v.remark ? '-' + v.remark : '' }}</span>
          </div>
        </template>
      </HrtTableColumn>
      <HrtTableColumn
        v-if="isKeyTracking"
        prop="address"
        label="重点跟进"
        width="80"
      >
        <template #default="scope">
          <HrtSwitch
            v-model="scope.row.keyFollow"
            @change="changeEmphasis(scope.row)"
          />
        </template>
      </HrtTableColumn>
      <HrtTableColumn prop="operation" label="操作" width="120">
        <template #default="scope">
          <span v-if="editItem?.uuId === scope.row.uuId" class="edit">
            确定
          </span>
          <span
            v-if="tableData.length > 0 && editItem?.uuId !== scope.row.uuId"
            class="edit"
            @click="() => editRow(scope.row)"
          >
            编辑
          </span>
          <span
            v-if="editItem?.uuId === scope.row.uuId"
            class="delete cancel"
            @click="cancelEdit"
          >
            取消
          </span>
          <el-popconfirm
            v-if="tableData.length > 0 && editItem?.uuId !== scope.row.uuId"
            title="确认删除此数据？"
            width="180px"
            @confirm="() => deleteHandler(scope.row)"
          >
            <template #reference>
              <span class="delete">删除</span>
            </template>
          </el-popconfirm>
        </template>
      </HrtTableColumn>
    </HrtTable>
    <AddReview
      :dialog-visible="addReviewVisible"
      :independent="false"
      :data="editItem"
      :confirm-callback="addRivewChange"
      @close="closeAddReview"
      @change="() => {}"
    />
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import addImg from '@/assets/imgs/callCenter/add.png';
import followImg from '@/assets/imgs/review/followup.png';
import { getUuid } from '@/utils';
import AddReview from '@/pages/Workbench/Main/Home/Review/components/AddReview.vue';
import {
  getReviewList,
  deleteReview,
  addReviewApi,
  updateReviewInfo,
  updateReviewKeyFollow,
} from '@/api/review';
import useGlobal from '@/store/module/useGlobal';
import bus from '@/lib/bus';

interface IItem {
  uuId: string;
  reviewId?: string;
  name?: string;
  draft?: boolean;
  date?: number | string;
  reviewCheckList: any[];
}
interface IProps {
  stepChange: number;
  isKeyTracking: boolean;
}
const globalStore = useGlobal();
const props = withDefaults(defineProps<IProps>(), {
  isKeyTracking: true,
});
const tableData = ref<IItem[]>([]);
const emptyTableData = ref([
  {
    uuId: getUuid(),
    name: '复查',
    reviewCheckList: [],
  },
]);
const loading = ref(false);
const editItem = ref();
const addReviewVisible = ref(false);

const editRow = item => {
  const { date, reviewCheckList } = item;
  editItem.value = {
    ...item,
    date,
    value: reviewCheckList,
  };
  addReviewVisible.value = true;
};
const cancelEdit = () => {
  editItem.value = null;
};
const clearDraft = () => {
  tableData.value = tableData.value.filter(v => !v.draft);
};
const closeAddReview = () => {
  addReviewVisible.value = false;
  editItem.value = null;
  clearDraft();
};
const successCallback = () => {
  ElMessage.success('操作成功！');
  getList();
  bus.emit('updata-review-list');
};
const addRivewChange = async data => {
  const { date, reportList } = data;
  const params = {
    patientId: globalStore.userId,
    date,
    reportList,
    reviewId: editItem.value.reviewId,
  };
  if (editItem.value.reviewId) {
    return updateReviewInfo(params).then(successCallback);
  } else {
    return addReviewApi(params).then(successCallback);
  }
};
const getEmptyItemData = () => {
  return {
    uuId: getUuid(),
    date: '',
    draft: true,
    name: '自定义复查',
    reviewCheckList: [],
  };
};
const getIndex = id => {
  return tableData.value.findIndex(v => v.uuId === id);
};
const addItem = item => {
  clearDraft();
  const index = getIndex(item.uuId);
  const newItem = getEmptyItemData();
  tableData.value.splice(index + 1, 0, newItem);
  editRow(newItem);
};
const deleteHandler = async item => {
  const { reviewId } = item;
  const params = {
    reviewId,
  };
  loading.value = true;

  await deleteReview(params).finally(() => {
    bus.emit('updata-review-list');
    loading.value = false;
  });
  ElMessage.success('删除成功！');
  getList();
};
const getList = async () => {
  loading.value = true;
  const res = await getReviewList({ patientId: globalStore.userId });
  loading.value = false;
  tableData.value = res?.map(v => ({
    ...v,
    uuId: getUuid(),
    name: v.times === 0 ? '自定义复查' : '复查',
    beforeTime: v.date,
    keyFollow: v.keyFollow,
  }));
};
// 重点跟进
const changeEmphasis = async (row: any) => {
  try {
    const params = {
      reviewId: row.reviewId,
      keyFollow: row.keyFollow,
    };
    await updateReviewKeyFollow(params);
    ElMessage.success('操作成功！');
  } catch (err) {
    console.log('[ err ] >', err);
    ElMessage.success('操作失败！');
    row.keyFollow = !row.keyFollow;
  }
};
onMounted(() => {
  getList();
});

defineExpose({
  getTableData: () => tableData.value,
  getList: () => getList(),
});

watch(
  () => props.stepChange,
  () => {
    cancelEdit();
  }
);
defineOptions({
  name: 'ReviewTable',
});
</script>

<style scoped lang="less">
.follow-table {
  width: 736px;
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      color: #15233f;
      background-color: #ebedf0;
      padding: 14px 0;
    }
  }
  :deep(.el-table__body-wrapper) {
    width: 928px;
    .el-table__cell {
      padding: 4px 0;
      height: 48px;
    }
  }
  :deep(.el-table__empty-block) {
    width: 928px !important;
  }
}
.edit {
  position: relative;
  cursor: pointer;
  color: #2e6be6;
  margin-right: 18px;
}
.delete {
  position: relative;
  cursor: pointer;
  color: #e63746;
  &:after {
    position: absolute;
    content: '/';
    font-size: 0;
    width: 1px;
    height: 12px;
    background: #e1e5ed;
    left: -9px;
    top: 4px;
  }
  &.cancel {
    color: #939cae;
  }
}
.item-card {
  display: inline-flex;
  align-items: center;
  margin-right: 4px;
  margin-bottom: 4px;
  background-color: #e1e5ed;
  border-radius: 2px;
  padding: 2px 4px;
}
</style>
