import { ElNotification } from 'element-plus'

/**
 * 统一的通知工具函数
 * 解决在某些环境下 ElNotification 不生效的问题
 */

export interface NotificationOptions {
  title?: string
  message: string
  type?: 'success' | 'warning' | 'info' | 'error'
  duration?: number
  showClose?: boolean
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

/**
 * 显示成功通知
 */
export const showSuccessNotification = (message: string, title = '成功') => {
  ElNotification({
    title,
    message,
    type: 'success',
    duration: 3000,
    showClose: true,
  })
}

/**
 * 显示错误通知
 */
export const showErrorNotification = (message: string, title = '错误') => {
  ElNotification({
    title,
    message,
    type: 'error',
    duration: 4000,
    showClose: true,
  })
}

/**
 * 显示警告通知
 */
export const showWarningNotification = (message: string, title = '警告') => {
  ElNotification({
    title,
    message,
    type: 'warning',
    duration: 3000,
    showClose: true,
  })
}

/**
 * 显示信息通知
 */
export const showInfoNotification = (message: string, title = '提示') => {
  ElNotification({
    title,
    message,
    type: 'info',
    duration: 3000,
    showClose: true,
  })
}

/**
 * 通用通知函数
 */
export const showNotification = (options: NotificationOptions) => {
  const defaultOptions = {
    duration: 3000,
    showClose: true,
    position: 'top-right' as const,
    type: 'info' as const,
  }

  ElNotification({
    ...defaultOptions,
    ...options,
  })
}

/**
 * 呼叫中心专用通知函数
 * 针对呼叫中心的通知需求进行优化
 */
export const showCallCenterNotification = {
  loginSuccess: () => showSuccessNotification('登录成功！'),
  loginFailed: (msg: string) => showErrorNotification(`登录失败：${msg}`),
  callSuccess: () => showSuccessNotification('呼叫成功'),
  callFailed: (msg: string) => showErrorNotification(`呼叫失败：${msg}`),
  pauseSuccess: () => showSuccessNotification('置忙成功'),
  pauseFailed: () => showErrorNotification('置忙失败'),
  unpauseSuccess: () => showSuccessNotification('置闲成功'),
  unpauseFailed: () => showErrorNotification('置闲失败'),
  muteSuccess: () => showSuccessNotification('静音成功'),
  muteFailed: () => showErrorNotification('静音失败'),
  unmuteSuccess: () => showSuccessNotification('取消静音成功'),
  unmuteFailed: () => showErrorNotification('取消静音失败'),
  holdSuccess: () => showSuccessNotification('保持成功'),
  holdFailed: () => showErrorNotification('保持失败'),
  unholdSuccess: () => showSuccessNotification('保持接回成功'),
  unholdFailed: () => showErrorNotification('保持接回失败'),
  sipLinkSuccess: () => showSuccessNotification('软电话接听成功'),
  sipLinkFailed: () => showErrorNotification('软电话接听失败'),
  sipUnlinkSuccess: () => showSuccessNotification('软电话挂断成功'),
  sipUnlinkFailed: () => showErrorNotification('软电话挂断失败'),
  transferSuccess: () => showSuccessNotification('电话转移成功'),
  transferFailed: () => showErrorNotification('电话转移失败'),
  investigationSuccess: () => showSuccessNotification('满意度设置成功'),
  investigationFailed: () => showErrorNotification('满意度设置失败'),
}
