/**
 * 检测是否有麦克风设备
 * @returns {Promise<boolean>}
 */
export async function hasMicrophone() {
  // 1. 检查浏览器是否支持媒体设备
  if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
    return false;
  }

  try {
    // 2. 获取所有媒体设备
    const devices = await navigator.mediaDevices.enumerateDevices();

    // 3. 筛选音频输入设备
    const audioInputs = devices.filter(device => device.kind === 'audioinput');
    // 4. 返回检测结果
    return audioInputs.length > 0;
  } catch (err) {
    return false;
  }
}
