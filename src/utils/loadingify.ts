type LoadingifyCallback<T, Argument extends unknown[]> = (
  ...args: Argument
) => Promise<T>;

export default function loadingify<T, Argument extends unknown[]>(
  fn: LoadingifyCallback<T, Argument>
): LoadingifyCallback<T, Argument> {
  let loading: Promise<T> | false = false;
  return async (...args: Argument) => {
    if (loading) {
      return loading;
    } else {
      try {
        return await (loading = fn(...args));
      } finally {
        loading = false;
      }
    }
  };
}
