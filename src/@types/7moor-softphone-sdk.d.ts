/**
 * 7moor-softphone-sdk 类型声明
 * 容联云呼叫中心SDK
 * @see https://ccjs.7moor.com/
 */
declare module '7moor-softphone-sdk' {
  import { RLYAgentStatus, RLYAttachEventType, RLYLoginType } from '@/constant';

  /** 回调函数类型 */
  interface CallbackFunction {
    (): void;
  }

  /** 错误回调函数类型 */
  interface ErrorCallbackFunction {
    (error: any): void;
  }

  interface SoftphoneBaseParams {
    /** 成功回调 */
    success?: CallbackFunction;
    /** 失败回调 */
    fail?: ErrorCallbackFunction;
  }

  /** Softphone 初始化参数 */
  interface SoftphoneInitParams extends Pick<SoftphoneBaseParams, 'success'> {
    /** 账户编号 */
    accountId: string;
    /** 座席账号 */
    agentNumber: string;
    /** 座席密码 */
    password: string;
    /** 登陆类型 */
    loginType?: RLYLoginType;
    /** pbx地址 */
    proxy_url?: string;
    /** 失败回调 */
    error?: (err: { success: boolean; message: string }) => void;
  }

  /** 外呼参数 */
  interface DialoutParams extends SoftphoneBaseParams {
    /** 被叫号码 */
    calleeNumber: string;
  }

  interface TransferParams extends SoftphoneBaseParams {
    /** 转接号码 */
    TransferNumber: string;
    /** 转接类型：in-代表转内线；out-代表转外线 */
    type?: 'in' | 'out';
  }

  interface ConsultParams extends SoftphoneBaseParams {
    /** 咨询号码 */
    ConsultNumber: string;
    /** 咨询类型：in-代表转内线；out-代表转外线 */
    type?: 'in' | 'out';
  }

  /** 呼叫API接口 */
  interface CallApi {
    /** 外呼方法 */
    dialout(params: DialoutParams): void;
    /** 挂断电话 */
    hangup(params: SoftphoneBaseParams): void;
    /** 保持通话 */
    hold(params: SoftphoneBaseParams): void;
    /** 取消保持 */
    unhold(params: SoftphoneBaseParams): void;
    /** 转接 */
    transfer(params: TransferParams): void;
    /** 取消转接 */
    canceltransfer(params: SoftphoneBaseParams): void;
    /** 咨询 */
    consult(params: ConsultParams): void;
    /** 取消咨询 */
    cancelconsult(params: SoftphoneBaseParams): void;
    /** 结束咨询 */
    endconsult(params: SoftphoneBaseParams): void;
    /** 转移咨询 */
    transferconsult(params: SoftphoneBaseParams): void;
    /** 三方通话 */
    threewaycall(params: SoftphoneBaseParams): void;
    /** 获取三方通话用户 */
    getthreewaycalluser(params: SoftphoneBaseParams): void;
    /** 获取转满意度列表 */
    getInvestigateList(params: SoftphoneBaseParams): void;
    /** 转满意度评价 */
    transferSatisfaction(params: SoftphoneBaseParams): void;
    /** 获取IVR列表 */
    getIvrMenuList(params: SoftphoneBaseParams): void;
    /** 转IVR */
    toIvrMenu(params: SoftphoneBaseParams): void;
  }

  interface AgentPhoneBarListParams extends Pick<SoftphoneBaseParams, 'fail'> {
    success?: (res: {
      /** 执行是否成功 */
      success: boolean;
      /** 座席状态数组 */
      data: any[];
    }) => void;
  }

  interface UpdateAgentStatusParams extends Pick<SoftphoneBaseParams, 'fail'> {
    /** 切换状态值 */
    statusValue: string;
    success?: (res: {
      /** 执行是否成功 */
      success: boolean;
    }) => void;
  }

  interface LogoutParams extends Pick<SoftphoneBaseParams, 'fail'> {
    /** 是否离线接听: true-不离线接听; false-离线接听; */
    offlineAnswering?: boolean;
    success?: (res: {
      /** 执行是否成功 */
      success: boolean;
    }) => void;
  }

  /** 座席API */
  interface AgentApi {
    /** 获取座席状态列表 */
    getAgentPhoneBarList(params: AgentPhoneBarListParams): void;
    /** 更新座席电话条状态 */
    updateAgentStatus(UpdateAgentStatusParams): void;
    /** 座席退出登录 */
    Logout(params: LogoutParams): void;
    /** 切换登录类型 */
    changeLoginType(params: {
      /** 登录类型 */
      loginType: RLYLoginType;
      success?: CallbackFunction;
      fail?: ErrorCallbackFunction;
    }): void;
    /** 获取座席信息 */
    getAgentInfo(params: SoftphoneBaseParams): void;
  }

  interface RLYAttachEventData {
    /** 来电号码 */
    FromCid: string;
    /** 座席登录类型 */
    ExtenType: RLYLoginType;
    /** 来电城市 */
    CallerCity: string;
    /** 来电城市编码 */
    CallerCityCode: string;
    /** 来电省份 */
    CallerProvince: string;
    /** 来电省份编码 */
    CallerProvinceCode: string;
  }

  interface WebRTCApi {
    answer(params: SoftphoneBaseParams): void;
  }

  /** Softphone 实例接口 */
  interface SoftphoneInstance {
    /** 呼叫API */
    callApi?: CallApi;
    /** 座席API */
    agentApi?: AgentApi;
    /** WebRTC API */
    webrtcApi?: WebRTCApi;
    /** 绑定通话事件 */
    attachEvent?(event: {
      /** 事件绑定通道建立成功回调 */
      success: () => void;
      /** 事件绑定异常回调 主要是底层链接ws的异常回调 */
      error: () => void;
      /** 通话事件回调 */
      message: (res: {
        /** 事件类型 */
        type: RLYAttachEventType;
        /** 座席状态值 0:空闲、1:忙碌 */
        typeValue: RLYAgentStatus;
        /** 通话状态数据 */
        LinkedChannel: object;
        /** 事件数据 */
        eventData: RLYAttachEventData;
      }) => void;
    }): void;
  }

  /** Softphone 构造函数 */
  interface SoftphoneConstructor {
    new (params: SoftphoneInitParams): SoftphoneInstance;
  }

  /** 默认导出 */
  const Softphone: SoftphoneConstructor;
  export default Softphone;

  /** 导出类型 */
  export type {
    AgentApi,
    AgentPhoneBarListParams,
    CallApi,
    CallbackFunction,
    ConsultParams,
    DialoutParams,
    ErrorCallbackFunction,
    LogoutParams,
    SoftphoneBaseParams,
    SoftphoneConstructor,
    SoftphoneInitParams,
    SoftphoneInstance,
    TransferParams,
    UpdateAgentStatusParams,
  };
}
