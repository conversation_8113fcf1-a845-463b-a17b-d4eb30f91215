import { RLYLoginType } from '@/constant';
/**
 * 容联呼叫中心回调请求参数
 */
export interface AddressCallDialoutMoorRequest {
  /** 坐席工号 */
  fromExten: string;
  /** 被叫号码 */
  exten: string;
  /** 被叫类型\nLocal/sip/gateway */
  extenType: RLYLoginType;
  /** 唯一字符串 */
  actionID?: string;
  /** 外呼自定义参数 */
  dialoutStrVar?: string;
  /** 指定外呼外显号码 */
  outShow?: string;
}

/**
 * 容联呼叫中心回调响应参数
 */
export interface AddressCallDialoutMoorResponse {
  /** 响应 */
  response: string;
  /** actionID */
  actionID: string;
  /** 是否成功 */
  succeed: boolean;
  /** 消息 */
  message: string;
}
