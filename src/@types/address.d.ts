import { CallSource } from '@/interface';

/**
 * 查询通话记录参数
 */
export interface AddressCallListApiRequest {
  /** 患者id */
  patientId?: number;
  /** 患者姓名或手机号 */
  keyword?: string;

  /** 接听时间 开始 */
  startTime?: number;
  /** 接听时间 结束 */
  endTime?: number;
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 当前页 */
  page?: number;
  /** 页面大小 */
  pageSize?: number;
  pageNumber?: number;
  callStatus?: {
    /** 呼叫类型 1 呼入 2 呼出 */
    type?: number;
    /** 接听状态 */
    status?: number;
    /** 是否已读：0-未读；1-已读 */
    isRead?: 0 | 1;
  }[];
}

/**
 * 通话记录单项
 */
export interface AddressCallListApiItem {
  /** 通话记录id */
  id: number;
  /** 患者id */
  patientId: number;
  /** 用户id */
  userId: number;
  /** 用户类型 */
  userType: number;
  /** 手机号 */
  customerNumber: string;
  /** 通话类型 1 呼入 2 呼出 */
  type: 1 | 2;
  /** 接听状态 */
  callStatus: number;
  /** 响铃时间 */
  callTime: number;
  /** 坐席号 */
  cno: string;
  /** 客户省份 */
  customerProvince: string;
  /** 客户城市 */
  customerCity: string;
  /** 接通时长 */
  bridgeDuration: number;
  /** 通话记录唯一标识 */
  uniqueId: string;
  /** 挂机方 */
  endReason: string;
  /** 接通时间 */
  bridgeTime: number;
  /** 呼叫中心返回 call_status 中文 */
  status: string;
  /** 呼叫中心返回 type 中文 */
  callType: string;
  /** 机主名称 */
  name: string;
  /** 关系 */
  relation: string;
  /** 通话记录录音文件地址 */
  fileUrl: string | null;
  /** 是否已读：0-未读；1-已读； */
  isRead: 0 | 1;
  /** 通话来源 */
  source: CallSource;
  /** 座席名称 */
  agentName: null | string;
  /** 座席电话 */
  agentPhone: null | string;
  /** 来电热线号码 */
  hotline: string;
  /** 等待时长 */
  waitDuration: number;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
}

/**
 * 查询通话记录响应
 */
export interface AddressCallListApiResponse {
  /** 响应码 */
  code: string;
  /** 响应消息 */
  message: string;
  /** 错误数据 */
  errorData?: Record<string, any>;
  /** 数据体 */
  data: {
    /** 总条数 */
    total: number;
    /** 通话记录列表 */
    contents: AddressCallListApiItem[];
  };
}

/**
 * 查询通话记录详情参数
 */
export interface QueryCallDetailsApiRequest {
  /** 呼叫状态 :  1 呼入 2 呼出 */
  type: number;
  /** 通话记录唯一标识 */
  mainUniqueId: string;
}

/**
 * 查询通话记录详情响应
 */
export interface QueryCallDetailsApiResponse {
  /** 响应码 */
  code: string;
  /** 响应消息 */
  message: string;
  /** 错误数据 */
  errorData?: Record<string, any>;
  /** 数据体 */
  data: {
    /** 通话详情 */
    callBackInfo: {
      /** 客户来电号码，带区号 */
      customerNumber: string;
      /** 来电热线号码 */
      hotline: string;
      /** 呼入类型 */
      callType: string;
      /** 接听状态 */
      status: string;
      /** 总时长 */
      totalDuration: string;
      /** 开始时间 */
      startTime: string;
      /** 结束时间 */
      endTime: string;
      /** 接通时间 */
      bridgeTime: string;
      /** 队列号 */
      qno: string;
      /** 队列名称 */
      qname?: string;
      /** 坐席号 */
      cno: string;
      /** 座席电话 */
      clientNumber: string;
    };
    /** 呼叫坐席列表 */
    callAgentList: Array<{
      /** 座席电话 */
      clientName: string;
      /** 队列号 */
      qno: string;
      /** 座席号 */
      cno: string;
      /** 呼叫类型 */
      callType: string;
      /** 呼叫结果 */
      status: string;
      /** 呼叫情况 */
      sipCause: string;
      /** 是否开启主叫记忆 */
      remember: string;
      /** 接起时间 */
      startTime: string;
      /** 接听时间 */
      answerTime: string;
      /** 通话时长 */
      totalDuration: string;
      /** 通话记录主通道唯一标识 */
      mainUniqueId: string;
      /** 通话记录详情唯一标识 */
      uniqueId: string;
    }>;
  };
}
