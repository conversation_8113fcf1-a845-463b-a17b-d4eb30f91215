// ClinkAgent 呼叫中心相关类型定义

/** 天润联通座席初始状态 */
export enum ClinkAgentLoginStatus {
  /** 空闲 */
  Free = 1,
  /** 忙碌 */
  Busy = 2,
}

interface ClinkAgentEventType {
  STATUS: string;
  PREVIEW_OUTCALL_RINGING: string;
  PREVIEW_OUTCALL_BRIDGE: string;
}

interface ClinkAgentResponseType {
  LOGIN: string;
  LOGOUT: string;
  PREVIEW_OUTCALL: string;
  PREVIEW_OUTCALL_CANCEL: string;
  PAUSE: string;
  UNPAUSE: string;
  REFUSE: string;
  UNLINK: string;
  MUTE: string;
  UNMUTE: string;
  INVESTIGATION: string;
  HOLD: string;
  UNHOLD: string;
  SIP_LINK: string;
  SIP_UNLINK: string;
  TRANSFER: string;
}

export interface ClinkAgentSetupOptions {
  sipPhone?: boolean;
  debug?: boolean;
}

export interface ClinkAgentLoginParams {
  /** 企业编码 */
  identifier: string;
  /** 座席工号 */
  cno: string;
  /** 密码 */
  password: string;
  /** 绑定电话 */
  bindTel: string;
  /** 绑定类型，1：普通电话、2：IP话机、3：软电话 */
  bindType: number;
  /** 座席初始登录状态 */
  loginStatus: ClinkAgentLoginStatus;
}

export interface ClinkAgentCallParams {
  /** 电话号码 */
  tel: string;
  /** 呼叫座席超时，默认：30秒 */
  timeout?: number;
  /** 呼叫客户超时，默认：45秒 */
  dialTelTimeout?: number;
  /** 客户侧外显号码 */
  obClid?: string;
  /** 外显规则名称 */
  obClidGroupName?: string;
}

export interface ClinkAgentMuteParams {
  /** 静音方向: in-座席侧；out-客户侧；all-双方 */
  direction: 'in' | 'out' | 'all';
}

interface ClinkAgentStatusEvent {
  /** 状态码，如 'IDLE', 'CALLING', 'BUSY' */
  code: string;
  /** 客户号码 */
  customerNumber?: string;
  action?: string;
}

interface ClinkAgentCallEvent {
  code: string;
  action: string;
  /** 客户号码 */
  customerNumber?: string;
}

interface ClinkAgentResponse {
  /** 0 表示成功 */
  code: number;
  /** 响应消息 */
  msg?: string;
}

// Window 对象类型扩展
declare global {
  interface Window {
    /**
     * ClinkAgent 呼叫中心
     * @see https://develop.clink.cn/develop/web/cc_toolbar.html
     */
    ClinkAgent?: {
      // 事件类型枚举
      EventType: ClinkAgentEventType;

      // 响应类型枚举
      ResponseType: ClinkAgentResponseType;

      /** 初始化设置 */
      setup: (options: ClinkAgentSetupOptions, callback: () => void) => void;

      /** 登录 */
      login: (params: ClinkAgentLoginParams) => void;

      /**
       * 座席退出系统，可以选择退出方式以及是否解绑电话
       * @param params 参数
       * @see https://develop.clink.cn/develop/web/cc_toolbar.html#logout
       */
      logout: (params: {
        /** 退出方式: 0-后台在线; 1-完全退出 */
        logoutMode: 0 | 1;
        /** 解绑电话: 0-不解绑; 1-解绑 */
        removeBinding: 0 | 1;
      }) => void;

      /** 预览外呼 */
      previewOutcall: (params: ClinkAgentCallParams) => void;

      /** 取消预览外呼 */
      previewOutcallCancel: (params?: any) => void;

      /** 置忙 */
      pause_client: (params: { cno: string }) => void;

      /** 置闲 */
      unpause_client: (params: { cno: string }) => void;

      /** 静音 */
      mute: (params: ClinkAgentMuteParams) => void;

      /** 取消静音 */
      unmute: (params: ClinkAgentMuteParams) => void;

      /** 保持通话 */
      hold: (params?: any) => void;

      /** 保持接回 */
      unhold: (params?: any) => void;

      // 软电话接听
      sipLink: () => void;

      /** 软电话挂断 */
      sipUnlink: () => void;

      // 转移
      transfer: (params?: any) => void;

      // 满意度调查
      investigation: (params?: any) => void;

      // 注册事件监听器
      registerListener: (
        eventType: string,
        callback: (event: ClinkAgentStatusEvent | ClinkAgentCallEvent) => void
      ) => void;

      // 注册回调函数
      registerCallback: (
        responseType: string,
        callback: (response: ClinkAgentResponse) => void
      ) => void;
    };
  }

  // 如果需要扩展其他全局对象
  interface Navigator {
    // 添加 Navigator 的自定义属性
    standalone?: boolean;
  }

  // 扩展 Document 接口
  interface Document {
    // 添加 Document 的自定义属性
    webkitHidden?: boolean;
    mozHidden?: boolean;
    msHidden?: boolean;
  }
}

// 确保这个文件被当作模块处理
export {};
