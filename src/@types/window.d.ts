// Window 对象类型扩展
declare global {
  interface Window {
    // 应用配置
    __APP_CONFIG__?: {
      apiUrl?: string;
      version?: string;
      env?: 'development' | 'test' | 'uat' | 'production';
      [key: string]: any;
    };

    // 语音识别相关
    webkitSpeechRecognition?: any;
    SpeechRecognition?: any;
    webkitSpeechGrammarList?: any;
    SpeechGrammarList?: any;

    // 地图相关
    AMap?: any;
    BMap?: any;
    TMap?: any;

    // 第三方库
    echarts?: any;
    G6?: any;
    
    // 微信相关
    wx?: any;
    WeixinJSBridge?: any;

    // 支付相关
    AlipayJSBridge?: any;

    // 埋点统计
    gtag?: (...args: any[]) => void;
    ga?: (...args: any[]) => void;
    _hmt?: any[];

    // 其他常用的全局变量
    __DEV__?: boolean;
    __PROD__?: boolean;
    __TEST__?: boolean;

    // 允许任意属性（谨慎使用）
    [key: string]: any;
  }

  // 如果需要扩展其他全局对象
  interface Navigator {
    // 添加 Navigator 的自定义属性
    standalone?: boolean;
  }

  // 扩展 Document 接口
  interface Document {
    // 添加 Document 的自定义属性
    webkitHidden?: boolean;
    mozHidden?: boolean;
    msHidden?: boolean;
  }
}

// 确保这个文件被当作模块处理
export {};
