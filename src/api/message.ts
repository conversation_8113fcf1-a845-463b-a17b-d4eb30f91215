import { http } from '@/network';
import {
  IApiMessageCenterListParams,
  IApiMessageCenterList,
  IApiMessageCenterNum,
  IApiMessageCenterReadParams,
  IApiMessageCenterRead,
  IApiMessageCenterClear,
} from '@/interface/type';
/** 消息中心列表查询 */
export function getMessageList(params: IApiMessageCenterListParams) {
  return http.post<IApiMessageCenterList>({
    url: '/api/message/center/list',
    data: params,
  });
}

/** 查询第一页未读消息数（用于前端展示消息红点） */
export function getMessageNum() {
  return http.post<IApiMessageCenterNum>({
    url: '/api/message/center/num',
  });
}

/** 单个消息已读 */
export function readMessage(params: IApiMessageCenterReadParams) {
  return http.post<IApiMessageCenterRead>({
    url: '/api/message/center/read',
    data: params,
  });
}

/** 清空消息 */
export function clearMessage(data) {
  return http.post<IApiMessageCenterClear>({
    url: '/api/message/center/clear',
    data,
  });
}
