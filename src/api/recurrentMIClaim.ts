import { http } from '@/network';
import type {
  IApiMiClaimApplyCheckParams,
  IApiMiClaimApplyCheck,
  IApiMiClaimListParams,
  IApiMiClaimList,
  IApiMiClaimDetailParams,
  IApiMiClaimDetail,
  IApiMiClaimApplyParams,
  IApiMiClaimApply,
  IApiMiClaimQueryUserParams,
  IApiMiClaimQueryUser,
  IApiMiClaimQueryPatientProductParams,
  IApiMiClaimQueryPatientProduct,
  IApiMiClaimFlowAttachUploadParams,
  IApiMiClaimFlowAttachUpload,
  IApiMiClaimRefundCheckParams,
  IApiMiClaimRefundCheck,
} from '@/interface/type';

// 去申请-校验再梗理赔状态 /api/mi/claim/apply/check

export const checkReinfarctionClaimStatus = (
  data: IApiMiClaimApplyCheckParams
) => {
  return http.post<IApiMiClaimApplyCheck>({
    url: '/api/mi/claim/apply/check',
    data,
  });
};

// 获取再梗理赔列表 /api/mi/claim/list
export const getReinfarctionClaimList = (data: IApiMiClaimListParams) => {
  return http.post<IApiMiClaimList>({
    url: '/api/mi/claim/list',
    data,
  });
};

// 查询再梗理赔详情 /api/mi/claim/detail
export const getReinfarctionClaimDetail = (data: IApiMiClaimDetailParams) => {
  return http.post<IApiMiClaimDetail>({
    url: '/api/mi/claim/detail',
    data,
  });
};

// 申请再梗理赔 /api/mi/claim/apply
export const applyReinfarctionClaim = (data: IApiMiClaimApplyParams) => {
  return http.post<IApiMiClaimApply>({
    url: '/api/mi/claim/apply',
    data,
  });
};

// 查询公司员工 /api/mi/claim/query/user
export const queryCompanyUser = (data: IApiMiClaimQueryUserParams) => {
  return http.post<IApiMiClaimQueryUser>({
    url: '/api/mi/claim/query/user',
    data,
  });
};

// 查询患者服务包信息 /api/mi/claim/query/patient/product
export const queryPatientProduct = (
  data: IApiMiClaimQueryPatientProductParams
) => {
  return http.post<IApiMiClaimQueryPatientProduct>({
    url: '/api/mi/claim/query/patient/product',
    data,
  });
};

// 上传流程附件文件并获取信息

export const uploadProcessAttachment = (
  data: IApiMiClaimFlowAttachUploadParams
) => {
  return http.post<IApiMiClaimFlowAttachUpload>({
    url: '/api/mi/claim/flow/attach/upload',
    data,
  });
};

// 退款检验再梗理赔状态

export const checkRefundReinfarctionClaimStatus = (
  data: IApiMiClaimRefundCheckParams
) => {
  return http.post<IApiMiClaimRefundCheck>({
    url: '/api/mi/claim/refund/check',
    data,
  });
};
