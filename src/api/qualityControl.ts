import { http } from '@/network';
import type {
  IApiQualityRecordCheckPointParams,
  IApiQualityRecordCheckPointItem,
  IApiQualityRecordCreateParams,
  IApiQualityRecordCreate,
  IApiQualityRecordPointPersonParams,
  IApiQualityRecordPointPerson,
  IApiQualityRecordQueryPageParams,
  IApiQualityRecordQueryPage,
  IApiQualityRecordIgnoreParams,
  IApiQualityRecordIgnore,
  IApiQualityRecordRevampParams,
  IApiQualityRecordRevamp,
  IApiQualityRecordRetractParams,
  IApiQualityRecordRetract,
  IApiQualityRecordQueryUnprocessedCountParams,
  IApiQualityRecordQueryUnprocessedCount,
} from '@/interface/type';

// 查询待办的质量检查点
export function qualityCheck(params: IApiQualityRecordCheckPointParams) {
  return http.post<IApiQualityRecordCheckPointItem>({
    url: '/api/quality/record/check/point',
    data: params,
  });
}

// 创建质量质控记录

export function createQualityRecord(params: IApiQualityRecordCreateParams) {
  return http.post<IApiQualityRecordCreate>({
    url: '/api/quality/record/create',
    data: params,
  });
}

// 查询质控点，质控人
export function getQualityPointPerson(
  params: IApiQualityRecordPointPersonParams
) {
  return http.post<IApiQualityRecordPointPerson>({
    url: '/api/quality/record/point/person',
    data: params,
  });
}

// 分页查询质控点、被质控人
export function getQualityPointPersonPage(
  params: IApiQualityRecordQueryPageParams
) {
  return http.post<IApiQualityRecordQueryPage>({
    url: '/api/quality/record/query/page',
    data: params,
  });
}

// 忽略、全部忽略
export function ignoreQualityRecord(params: IApiQualityRecordIgnoreParams) {
  return http.post<IApiQualityRecordIgnore>({
    url: '/api/quality/record/ignore',
    data: params,
  });
}

// 整改、全部整改
export function revampQualityRecord(params: IApiQualityRecordRevampParams) {
  return http.post<IApiQualityRecordRevamp>({
    url: '/api/quality/record/revamp',
    data: params,
  });
}

// 撤回质控整改
export function retractQualityRecord(params: IApiQualityRecordRetractParams) {
  return http.post<IApiQualityRecordRetract>({
    url: '/api/quality/record/retract',
    data: params,
  });
}

// 查询当前用户未处理质控数量
export function countQualityRecord(
  params: IApiQualityRecordQueryUnprocessedCountParams
) {
  return http.post<IApiQualityRecordQueryUnprocessedCount>({
    url: '/api/quality/record/query/unprocessed/count',
    data: params,
  });
}
