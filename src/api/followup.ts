import { http } from '@/network';
import {
  IApiFollowRecordsParams,
  IApiFollowRecords,
  IApiFollowLifestyleDetailParams,
  IApiFollowLifestyleDetail,
  IApiFollowLifestyleEditParams,
  IApiFollowSymptomDetailParams,
  IApiFollowSymptomDetail,
  IApiFollowSymptomEditParams,
  IApiFollowLifestyleRuleInfoParams,
  IApiFollowLifestyleRuleInfo,
  IApiFollowDealSymptomParams,
  IApiFollowStatisticsDiet,
  IApiFollowStatisticsDietParams,
  IApiFollowStatisticsNumberQuestionParams,
  IApiFollowStatisticsNumberQuestion,
} from '@/interface/type';

// 获取患者随访记录
export function getFollowRecords(params: IApiFollowRecordsParams) {
  return http.post<IApiFollowRecords>({
    url: '/api/follow/records',
    data: params,
  });
}

// 获取生活方式随访详情
export function getFollowLifestyleDetail(
  params: IApiFollowLifestyleDetailParams
) {
  return http.post<IApiFollowLifestyleDetail>({
    url: '/api/follow/lifestyle/detail',
    data: params,
  });
}
// 查询患者只填写一次的相关问卷问题数据
export function getFollowLifestyleRuleInfo(
  params: IApiFollowLifestyleRuleInfoParams
) {
  return http.post<IApiFollowLifestyleRuleInfo>({
    url: '/api/follow/lifestyle/rule/info',
    data: params,
  });
}
// 编辑生活方式随访
export function saveFollowLifestyleEdit(params: IApiFollowLifestyleEditParams) {
  return http.post({
    url: '/api/follow/lifestyle/edit',
    data: params,
  });
}
//获取症状随访详情
export function getFollowSymptomDetail(params: IApiFollowSymptomDetailParams) {
  return http.post<IApiFollowSymptomDetail>({
    url: '/api/follow/symptom/detail',
    data: params,
  });
}

// 编辑症状随访
export function saveFollowSymptomEdit(params: IApiFollowSymptomEditParams) {
  return http.post({
    url: '/api/follow/symptom/edit',
    data: params,
  });
}
// 处理症状随访事件
export function updateFollowDealSymptom(params: IApiFollowDealSymptomParams) {
  return http.post({
    url: '/api/follow/deal/symptom',
    data: params,
  });
}

/** 获取饮食记录列表 */
export function getFollowDiet(params: IApiFollowStatisticsDietParams) {
  return http.post<IApiFollowStatisticsDiet>({
    url: '/api/follow/statistics/diet',
    data: params,
  });
}

/** 获取吸烟、饮酒、bmi、运动记录列表 */
export function getFollowNumQuestion(
  params: IApiFollowStatisticsNumberQuestionParams
) {
  return http.post<IApiFollowStatisticsNumberQuestion>({
    url: '/api/follow/statistics/number/question',
    data: params,
  });
}

/**查询症状随访、生活方式随访列表 */
export function getFollowList(params) {
  return http.post({
    url: '/api/follow/query/list',
    data: params,
  });
}
/**查询当前患者可供选择的问卷 */
export function getQuestionnaire(params) {
  return http.post({
    url: '/api/follow/adjust/query/questionnaire',
    data: params,
  });
}
/**新增随访 */
export function addFollow(params) {
  return http.post({
    url: '/api/follow/adjust/insert/follow',
    data: params,
  });
}
/**随访调整计划列表--症状随访查询 */
export function getSymptomList(params) {
  return http.post({
    url: '/api/follow/adjust/symptom/list',
    data: params,
  });
}
/**随访调整计划列表--生活方式随访 */
export function getLifeList(params) {
  return http.post({
    url: '/api/follow/adjust/life/list',
    data: params,
  });
}
/**删除随访 */
export function deleteFollow(params) {
  return http.post({
    url: '/api/follow/adjust/delete',
    data: params,
  });
}
/**修改随访 */
export function updateFollowInfo(params) {
  return http.post({
    url: '/api/follow/update/info',
    data: params,
  });
}
/**批量移动天数 */
export function followBatchModify(params) {
  return http.post({
    url: '/api/follow/batch/modify',
    data: params,
  });
}
/**查询患者时间节点 */
export function getTimesNode(params) {
  return http.post({
    url: '/api/patient/info/query/patient/times/node',
    data: params,
  });
}
