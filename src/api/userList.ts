import { http } from '@/network';
import {
  IApiPatientFullParams,
  IApiPatientInfoParams,
  IApiPatientPhoneParams,
  IApiPatientChatParams,
  IApiPatientTagParams,
  IApiAssistantCurrentPatientParams,
  IApiAssistantManagePatientParams,
  IApiReviewNotUrgedPatientParams,
  IApiReviewUrgedPatientParams,
  IApiCustomerCurrentPatientParams,
  IApiCustomerManagePatientParams,
  IApiFollowUpNotUrgedPatientParams,
  IApiFollowUpUrgedPatientParams,
  IApiRehabilitationCurrentPatientParams,
  IApiRehabilitationManagePatientParams,
  IApiRehabilitationManageAssessmentPatientParams,
  IApiPatientInfoAttentionParams,
  IApiSearchRegionHospitalGroupParams,
  IApiSearchRegionHospitalGroup,
  IApiPatientChatListDetailParams,
  IApiPatientChatListDetail,
  IApiOrprojectQuerySimple,
} from '@/interface/type';

/** 查询用户的科研项目信息 */
export function searchOrProject() {
  return http.post<IApiOrprojectQuerySimple>({
    url: '/api/or-project/query/simple',
    method: 'post',
    data: {},
  });
}

/** 查询角色下的地区-医院-工作室 */
export function searchRegionHospitalGroup(
  params: IApiSearchRegionHospitalGroupParams
) {
  return http.post<IApiSearchRegionHospitalGroup>({
    url: '/api/search/region/hospital/group',
    method: 'post',
    data: params,
  });
}

/** 模糊搜索 */
export function getPatientByKeywords(params: IApiPatientFullParams) {
  return http.post({
    url: '/api/patient/full',
    method: 'post',
    data: params,
  });
}
/** 模糊搜索 - 患者 */
export function getPatientInfo(params: IApiPatientInfoParams) {
  return http.post({
    url: '/api/patient/info',
    method: 'post',
    data: params,
  });
}
/** 模糊搜索 - 电话号码 */
export function getPatientPhone(params: IApiPatientPhoneParams) {
  return http.post({
    url: '/api/patient/phone',
    method: 'post',
    data: params,
  });
}
/** 模糊搜索 - 聊天记录 */
export function getPatientChat(params: IApiPatientChatParams) {
  return http.post({
    url: '/api/patient/chat',
    method: 'post',
    data: params,
  });
}
/** 模糊搜索 - 标签 */
export function getPatientTag(params: IApiPatientTagParams) {
  return http.post({
    url: '/api/patient/tag',
    method: 'post',
    data: params,
  });
}
/** 根据群聊编号和模糊文本查询聊天列表详情*/
export function getPatientChatList(params: IApiPatientChatListDetailParams) {
  return http.post<IApiPatientChatListDetail>({
    url: '/api/patient/chat/list/detail',
    method: 'post',
    data: params,
  });
}

/** 城市-医院-工作室 */
export function getRegions(params: any) {
  return http.post({
    url: '/api/patient/tag',
    method: 'post',
    data: params,
  });
}
/** 关注，取消关注 */
export function attention(params: any) {
  return http.post({
    url: '/api/patient/tag',
    method: 'post',
    data: params,
  });
}
/** 医生工作台 - 今日 */
export function getDoctorCurrent(params: IApiAssistantCurrentPatientParams) {
  return http.post({
    url: '/api/assistant/current/patient',
    method: 'post',
    data: params,
  });
}
/** 医生工作台 - 管理中 */
export function getDoctorManage(params: IApiAssistantManagePatientParams) {
  return http.post({
    url: '/api/assistant/manage/patient',
    method: 'post',
    data: params,
  });
}
/** 医生工作台 - 复查 - 未催办 */
export function getDcotorNoUrged(params: IApiReviewNotUrgedPatientParams) {
  return http.post({
    url: '/api/review/not/urged/patient',
    method: 'post',
    data: params,
  });
}
/** 医生工作台 - 部门列表 */
export function getDoctorDepartmentList(
  params: IApiReviewNotUrgedPatientParams
) {
  return http.post({
    url: '/api/assistant/dept/patient',
    method: 'post',
    data: params,
  });
}
/** 医生工作台 - 查询登录账号的全部下属账号 */
export function getUserSubordinateAccountsList(params: any) {
  return http.post({
    url: '/api/health/user/subordinate/accounts',
    method: 'post',
    data: params,
  });
}
/** 医生工作台 - 复查 - 已催办 */
export function getDoctorUrged(params: IApiReviewUrgedPatientParams) {
  return http.post({
    url: '/api/review/urged/patient',
    method: 'post',
    data: params,
  });
}
/** 健康管理师 - 今日 */
export function getCustomerCurrent(params: IApiCustomerCurrentPatientParams) {
  return http.post({
    url: '/api/customer/current/patient',
    method: 'post',
    data: params,
  });
}
/** 健康管理师 - 团队 */
export function getCustomerManage(params: IApiCustomerManagePatientParams) {
  return http.post({
    url: '/api/customer/manage/patient',
    method: 'post',
    data: params,
  });
}
/** 健康管理师 - 部门  */
export function getCustomerDepartmentList(
  params: IApiFollowUpNotUrgedPatientParams
) {
  return http.post({
    url: '/api/customer/dept/patient',
    method: 'post',
    data: params,
  });
}
/** 健康管理师 - 随访 - 未催办 */
export function getCustomerNoUrged(params: IApiFollowUpNotUrgedPatientParams) {
  return http.post({
    url: '/api/followUp/not/urged/patient',
    method: 'post',
    data: params,
  });
}
/** 健康管理师 - 随访 - 已催办 */
export function getCustomerUrged(params: IApiFollowUpUrgedPatientParams) {
  return http.post({
    url: '/api/followUp/urged/patient',
    method: 'post',
    data: params,
  });
}

/** 运动康复师 - 今日  */
export function getRehabilitationCurrent(
  params: IApiRehabilitationCurrentPatientParams
) {
  return http.post({
    url: '/api/rehabilitation/current/patient',
    method: 'post',
    data: params,
  });
}

/** 运动康复师 - 部门  */
export function getRehabilitationDepartmentList(
  params: IApiRehabilitationCurrentPatientParams
) {
  return http.post({
    url: '/api/rehabilitation/dept/patient',
    method: 'post',
    data: params,
  });
}

/** 运动康复师 - 团队  */
export function getRehabilitationManage(
  params: IApiRehabilitationManagePatientParams
) {
  return http.post({
    url: '/api/rehabilitation/manage/patient',
    method: 'post',
    data: params,
  });
}

/** 运动康复师 - 管理列表  */
export function getRehabilitationList(
  params: IApiRehabilitationManageAssessmentPatientParams
) {
  return http.post({
    url: '/api/rehabilitation/manage/assessment/patient',
    method: 'post',
    data: params,
  });
}

/** 关注或取消关注患者  */
export function patientAttention(params: IApiPatientInfoAttentionParams) {
  return http.post({
    url: '/api/patient/info/attention',
    method: 'post',
    data: params,
  });
}

/** 统计医生患者列表和复查列表总数  */
export function getAssistantNum(params: any) {
  return http.post({
    url: '/api/assistant/statistics/num',
    method: 'post',
    data: params,
  });
}

/** 统计医生部门列表总数  */
export function getAssistantDepartmentNum(params: any) {
  return http.post({
    url: '/api/assistant/dept/num',
    method: 'post',
    data: params,
  });
}

/** 统计健管师患者列表和随访列表总数  */
export function getCustomerNum(params: any) {
  return http.post({
    url: '/api/customer/statistics/num',
    method: 'post',
    data: params,
  });
}
/** 统计康复师患者列表和管理评估列表总数  */
export function getRehabilitationNum(params: any) {
  return http.post({
    url: '/api/rehabilitation/statistics/num',
    method: 'post',
    data: params,
  });
}
/** 统计康复师患者部门列表总数  */
export function getRehabilitationDepartmentNum(params: any) {
  return http.post({
    url: 'api/rehabilitation/dept/num',
    method: 'post',
    data: params,
  });
}
/** 统计健管师部门列表总数  */
export function getCustomerDepartmentNum(params: any) {
  return http.post({
    url: '/api/customer/dept/num',
    method: 'post',
    data: params,
  });
}

/** 新增搜索排序数据  */
export function sortEventSubmit(params: any) {
  return http.post({
    url: '/api/submit/sort/event/submit',
    method: 'post',
    data: params,
  });
}

/** 查询患者运动康复管理状态、风险等级  */
export function getRehabilitationManageStatus(params: any) {
  return http.post({
    url: '/api/rehabilitation/manage/status',
    method: 'post',
    data: params,
  });
}
