import { http } from '@/network';
import {
  IApiInternTaskSubmitParams,
  IApiInternTranscriptionParams,
  IApiInternTaskCompleteParams,
  IApiInternExistGroupDoctorList,
  IApiInternTaskListParams,
  IApiInternTaskList,
  IApiInternTaskLockParams,
  IApiInternReleaseParams,
} from '@/interface/type';

/** 医生及工作室查询 */
export function getExistGroupDoctorList(params = {}) {
  return http.post<IApiInternExistGroupDoctorList>({
    url: '/api/intern/exist/group/doctor/list',
    data: params,
  });
}
/** 任务列表查询 */
export function getInterTaskList(params: IApiInternTaskListParams) {
  return http.post<IApiInternTaskList>({
    url: '/api/intern/task/list',
    data: params,
  });
}
/** 锁单 */
export function lockTask(params: IApiInternTaskLockParams) {
  return http.post({
    url: '/api/intern/task/lock',
    data: params,
  });
}
/** 释放任务 */
export function releaseTask(params: IApiInternReleaseParams) {
  return http.post({
    url: '/api/intern/release',
    data: params,
  });
}
/** 确认完成 */
export function completeTask(params: IApiInternTaskCompleteParams) {
  return http.post({
    url: '/api/intern/task/complete',
    data: params,
  });
}

/** 生成转录任务 */
export const generateTranscription = (data: IApiInternTranscriptionParams) => {
  return http.post({
    url: '/api/intern/transcription',
    data,
  });
};

/** 生成转录任务 */
export const submitTask = (data: IApiInternTaskSubmitParams) => {
  return http.post({
    url: '/api/intern/task/submit',
    data,
  });
};

/** 患者类型筛选条件查询 */
export const patientTypeApi = () => {
  return http.post({
    url: '/api/intern/task/patient/type',
  });
};
