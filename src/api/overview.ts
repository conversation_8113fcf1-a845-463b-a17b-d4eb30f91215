import { http } from '@/network';
import {
  IApiPatientInfoBase,
  IApiPatientInfoBaseParams,
} from '@/interface/type';

/** 获取患者基本信息 */
export function getPatientInfoBase(params: IApiPatientInfoBaseParams) {
  return http.post<IApiPatientInfoBase>({
    url: '/api/patient/info/base',
    data: params,
    customConfig: { repeatRequestCancel: false },
  });
}
/** 更新患者地址 */
export function updatePatientAddress(params: any) {
  return http.post({
    url: '/api/patient/info/update/address',
    method: 'post',
    data: params,
  });
}

/** 创建订单 */
export function createOrderApi(params: any) {
  return http.post({
    url: '/api/order/create',
    method: 'post',
    data: params,
    customConfig: {
      reductDataFormat: false,
      repeatRequestCancel: false,
      codeMessageShow: false,
    },
  });
}

// 查询退费流程对应的待支付补差价订单信息
export function queryOrderByApi(data) {
  return http.post({
    url: '/api/order/refund/query/order/by/refund',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

/** 推送设备邮寄地址 */
export function pushAddressApi(params: any) {
  return http.post({
    url: '/api/order/push/address',
    method: 'post',
    data: params,
  });
}

/** 获取患者风险等级 */
export function getPatientRisk(params: any) {
  return http.post({
    url: '/api/patient/info/risk/grade',
    method: 'post',
    data: params,
  });
}
/** 获取患者危险因素、依从性 */
export function getPatientFactor(params: any) {
  return http.post({
    url: '/api/patient/info/risk/factor',
    method: 'post',
    data: params,
  });
}
/** 获取患者临床诊断、既往史 */
export function getPatientClinical(params: any) {
  return http.post({
    url: '/api/patient/info/clinical',
    method: 'post',
    data: params,
  });
}
/** 获取患者手术信息 */
export function getPatientSurgery(params: any) {
  return http.post({
    url: '/api/patient/info/surgery',
    method: 'post',
    data: params,
  });
}
/** 获取系统标签列表 */
export function getPatientTagSystem(params: any) {
  return http.post({
    url: '/api/patient/info/tag/system',
    method: 'post',
    data: params,
  });
}
/** 获取用户历史标签 */
export function getPatientTagHistory() {
  return http.post({
    url: '/api/patient/info/tag/history',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}
/** 获取患者标签列表 */
export function getPatientTag(params: any) {
  return http.post({
    url: '/api/patient/info/tag/list',
    method: 'post',
    data: params,
  });
}

/** 新增患者标签 */
export function addPatientTag(params: any) {
  return http.post({
    url: '/api/patient/info/tag/add',
    method: 'post',
    data: params,
  });
}
/** 删除患者标签 */
export function deletePatientTag(params: any) {
  return http.post({
    url: '/api/patient/info/tag/delete',
    method: 'post',
    data: params,
  });
}
/** 修改患者备注信息 */
export function updatePatientInfoRemarks(params: any) {
  return http.post({
    url: '/api/patient/info/remarks',
    method: 'post',
    data: params,
  });
}

/** 获取患者硬件设备列表 */
export function getPatientDeviceList(params: any) {
  return http.post({
    url: '/api/patient/device/list',
    method: 'post',
    data: params,
  });
}
/** 患者硬件设备绑定、换绑 */
export function updatePatientDevice(params: any) {
  return http.post({
    url: '/api/patient/device/change',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

/** 患者硬件设备解绑 */
export function deletePatientDevice(params: any) {
  return http.post({
    url: '/api/patient/device/delete',
    method: 'post',
    data: params,
  });
}
//获取截断列表
export function getSegmentLists(params: any) {
  return http.post({
    url: '/api/case/history/segment',
    method: 'post',
    data: params,
  });
}

//硬件设备列表
export function getDevicetListApi(data: any) {
  return http.post({
    url: '/api/patient/device/query/bind/record',
    method: 'post',
    data,
  });
}
