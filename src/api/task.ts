import { http } from '@/network';
import {
  ITaskSearch,
  IRejectTaskApi,
  ICaseTaskApi,
} from '@/pages/Workbench/Header/components/TaskAcceptanceDrawer/type';

// 驳回任务
export function rejectTaskApi(data: IRejectTaskApi) {
  return http.post({
    url: '/api/audit/task/reject/task',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 审核任务列表
export function taskListApi(data: ITaskSearch) {
  return http.post({
    url: '/api/audit/task/page',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 待审核任务数量查询
export function waitAuditNumApi() {
  return http.post({
    url: '/api/audit/task/wait/audit/task/num',
    method: 'post',
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 撤销任务
export function quashTaskApi(data: IRejectTaskApi) {
  return http.post({
    url: '/api/audit/task/quash/task',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 病历任务查询
export function queryCaseTaskApi(data: ICaseTaskApi) {
  return http.post({
    url: '/api/audit/task/case/task/status',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 生成转录任务
export function transcriptionTaskApi(data: ICaseTaskApi) {
  return http.post({
    url: '/api/intern/transcription',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 通过任务
export function completedTaskApi(data: { taskId: number }) {
  return http.post({
    url: '/api/audit/task/completed/audit/task ',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

/**
 * 检验content
 * @param taskId 任务🆔
 */
export function checkTaskContent(taskId: number): Promise<boolean> {
  return http.post({
    url: '/api/audit/task/check/task/content',
    method: 'post',
    data: { taskId },
    customConfig: {
      repeatRequestCancel: false,
    },
  });
}
