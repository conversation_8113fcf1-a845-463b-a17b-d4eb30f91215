import { http } from '@/network';
import {
  IApiIndexQueryParams,
  IApiRiskTargetValue,
  IApiRiskDeviceExDataParams,
  IApiRiskDeviceExData,
  IApiRiskDeviceDataParams,
  IApiRiskDeviceData,
  IApiRiskDeviceViewParams,
  IApiRiskDeviceView,
  IApiRiskBloodDataParams,
  IApiRiskBloodData,
  IApiRiskBloodViewParams,
  IApiRiskBloodView,
  IApiRiskHandleEventParams,
  IApiRiskHandleErrorParams,
} from '@/interface/type';

/** 查询患者存在的指标项 */
export function getRiskTargetValue(params: IApiIndexQueryParams) {
  return http.post<IApiRiskTargetValue>({
    url: '/api/risk/target/value',
    data: params,
  });
}

/** 查询血压异常栏数据 */
export function getRiskDeviceExData(params: IApiRiskDeviceExDataParams) {
  return http.post<IApiRiskDeviceExData>({
    url: '/api/risk/device/ex/data',
    data: params,
  });
}
/** 查询血压数据 */
export function getRiskDeviceData(params: IApiRiskDeviceDataParams) {
  return http.post<IApiRiskDeviceData>({
    url: '/api/risk/device/data',
    data: params,
    customConfig: { repeatRequestCancel: false },
  });
}
export function getRiskHeartData(params: IApiRiskDeviceDataParams) {
  return http.post<IApiRiskDeviceData>({
    url: '/api/risk/heart/data',
    data: params,
    customConfig: { repeatRequestCancel: false },
  });
}
/** 查询异常图表数据 */
export function getRiskDeviceView(params: IApiRiskDeviceViewParams) {
  return http.post<IApiRiskDeviceView>({
    url: '/api/risk/device/view',
    data: params,
    customConfig: { repeatRequestCancel: false },
  });
}
/** 查询异常图表数据 */
export function getRiskBloodData(params: IApiRiskBloodDataParams) {
  return http.post<IApiRiskBloodData>({
    url: '/api/risk/blood/data',
    data: params,
  });
}
/** 查询异常图表数据 */
export function getRiskBloodView(params: IApiRiskBloodViewParams) {
  return http.post<IApiRiskBloodView>({
    url: '/api/risk/blood/view',
    data: params,
  });
}
/** 处理事件 */
export function riskHandleEvent(params: IApiRiskHandleEventParams) {
  return http.post({
    url: '/api/risk/handle/event',
    data: params,
  });
}

/** 处理事件 */
export function riskHandlePartEvent(params: IApiRiskHandleErrorParams) {
  return http.post({
    url: '/api/risk/handle/error',
    data: params,
  });
}

/** 风险管理体重风险数据 */
export function riskWeightEvent(params: IApiRiskHandleEventParams) {
  return http.post({
    url: '/api/risk/weight/data',
    data: params,
  });
}
