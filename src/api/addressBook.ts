import type {
  AddressCallListApiRequest,
  AddressCallListApiResponse,
  QueryCallDetailsApiRequest,
  QueryCallDetailsApiResponse,
} from '@/@types/address';
import type {
  AddressCallDialoutMoorRequest,
  AddressCallDialoutMoorResponse,
} from '@/@types/addressBook';
import { CallSource } from '@/interface';
import { http } from '@/network';
/**
 * 容联呼叫中心回调
 * @param params 请求参数
 * @returns Promise<{ code: string; message: string; data: AddressCallDialoutMoorResponse }>
 */
export function addressCallDialoutMoorApi(
  params: AddressCallDialoutMoorRequest
): Promise<AddressCallDialoutMoorResponse> {
  return http.post({
    url: '/api/address/call/dialout/moor',
    method: 'post',
    data: params,
  });
}

// 查询通讯录
export function patientList(params: any) {
  return http.post({
    url: '/api/address/patient/list',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 新增编辑通讯录
export function updateApi(params: any) {
  return http.post({
    url: '/api/address/update',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

/**
 * 查询通话记录列表
 * @param params 查询参数
 * @returns Promise<CallListQueryResponseDTO>
 */
export function addressCallListApi(
  params: AddressCallListApiRequest
): Promise<AddressCallListApiResponse> {
  return http.post({
    url: '/api/address/call/list',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

/**
 * 查询坐席电话列表
 * @param params 查询参数
 * @returns
 */
export function queryPatientNameApi(params: {
  /** 座席工号 */
  cno: string;
}): Promise<{
  clientTelModelList: {
    tel: string;
    /** 是否绑定 */
    isBind: 0 | 1;
    /** 电话类型 */
    telType: number;
  }[];
}> {
  return http.post({
    url: '/api/address/phone/list',
    method: 'post',
    data: params,
  });
}

// 记录呼叫事件
export function queryCallItemApi(params: any) {
  return http.post({
    url: '/api/address/call/item',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

/**
 * 查询通话记录详情
 * @param params 查询通话记录详情参数
 */
export function queryCallDetailsApi(
  params: QueryCallDetailsApiRequest
): Promise<QueryCallDetailsApiResponse> {
  return http.post({
    url: '/api/address/call/details',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 通过手机号返回来电姓名
export function queryAddressPhoneNameApi(params: {
  /** 手机号 */
  phone: string;
}): Promise<{
  code: string;
  data: {
    name: string | null;
    relation: string | null;
    patientName: string | null;
  };
}> {
  return http.post({
    url: '/api/address/seats/phone',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 通过姓名和手机号搜索符合要求的患者及患者家属
export function queryPatientRelevanceMsgApi(params: { keyword: string }) {
  return http.post({
    url: '/api/address/patient/keyword',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 新增实质性事务数据
export function handleRecordApi(params: any) {
  return http.post({
    url: '/api/submit/handle/record/submit',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 当前角色未接来电数
export function addressMissCalls(): Promise<number> {
  return http.post({
    url: '/api/address/miss/calls',
    method: 'post',
    data: {},
  });
}

/**
 * 查询线路登陆账号
 */
export function callSeatQuery(params: {
  /** 线路: AICC-天润联通 MOOR-容联云 */
  channel?: CallSource;
}): Promise<{
  cno: string;
  loginPassword: string;
  accountId: string;
}> {
  return http.post({
    url: '/api/address/call/seat/query',
    method: 'post',
    data: params,
  });
}

/**
 * 容联云登录
 * @param cno 座席工号
 * @returns  登录成功返回座席信息
 */
export function signInMoor(cno: string | number) {
  fetch('/health-manage/api/address/call/sign/in/moor', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      authorization: `${localStorage.getItem('TOKEN__')}`,
    },
    body: JSON.stringify({ cno }),
    // 因为需要关闭标签后继续请求，需要下面配置
    keepalive: true,
  });
}
