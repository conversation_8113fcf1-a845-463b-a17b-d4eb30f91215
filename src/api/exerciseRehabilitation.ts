import { http } from '@/network';

//查询患者运动状态变更历史及详情
export function getRehabManageMotionHistory(params) {
  return http.post({
    url: '/api/rehab/manage/motion/history',
    data: params,
  });
}
// 编辑患者运动状态
export function getRehabManageEditMotion(params) {
  return http.post({
    url: '/api/rehab/manage/edit/motion',
    data: params,
  });
}

//查询患者风险评估变更历史及详情
export function getRehabManageRiskHistory(params) {
  return http.post({
    url: '/api/rehab/manage/risk/history',
    data: params,
  });
}

// 编辑患者风险评估
export function getRehabManageEditRisk(params) {
  return http.post({
    url: '/api/rehab/manage/edit/risk',
    data: params,
  });
}
