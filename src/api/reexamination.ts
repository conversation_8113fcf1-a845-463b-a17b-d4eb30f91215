import { http } from '@/network';
import {
  IApiPatientReviewConclusionParams,
  IApiPatientReviewDetail,
} from '@/interface/type';

interface ICommonParams {
  /** 复查id */
  reviewId: number;
}

/** 获取复查详情 */
export const getReviewDetail = (data: ICommonParams) => {
  return http.post<IApiPatientReviewDetail>({
    url: '/api/patient/review/detail',
    data,
  });
};

/** 删除自定义复查 */
export const delReview = (data: ICommonParams) => {
  return http.post({
    url: '/api/patient/review/delete',
    data,
  });
};

/** 填写复查结论、建议 */
export const updateReviewConclusion = (
  data: IApiPatientReviewConclusionParams
) => {
  return http.post({
    url: '/api/patient/review/conclusion',
    data,
    customConfig: { codeMessageShow: false, reductDataFormat: false },
  });
};

/** 生成复查结论 */
export const createSummaryTemplate = (data: any) => {
  return http.post({
    url: '/api/patient/review/review/conclusion',
    data,
  });
};

/** 生成复查结论 */
export const queryReviewReductionMsgApi = (data: any) => {
  return http.post({
    url: '/api/patient/review/query/reduction/msg',
    data,
  });
};
