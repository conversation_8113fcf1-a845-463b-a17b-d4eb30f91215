import { http } from '@/network';

// 待办列表
export function todoListApi(params: any) {
  return http.post({
    url: '/api/backlog/page/query',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 新增待办
export function addTodoApi(params: any) {
  return http.post({
    url: '/api/backlog/create',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 延迟待办
export function delayTodoApi(params: any) {
  return http.post({
    url: '/api/backlog/delay',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 删除待办
export function deteleTodoApi(params: any) {
  return http.post({
    url: '/api/backlog/remove',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 处理待办
export function handleTodoApi(params: any) {
  return http.post({
    url: '/api/backlog/deal',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}
