import { http } from '@/network';
import {
  IApiRiskStatisticsAci,
  IApiRiskStatisticsAcsAcute,
  IApiRiskStatisticsAcsCha,
  IApiRiskStatisticsAcsSyn,
  IApiRiskStatisticsAscvd,
  IApiRiskStatisticsBled,
  IApiRiskStatisticsDeath,
  IApiRiskStatisticsRiskLevel,
} from '@/interface/type';
// ------------------风险评估------------------

export interface IPatientId {
  /** 患者id	 */
  patientId: number;
}
/** 获取预后风险-急性ACS风险统计  */
export function getGraRiskData(params: IPatientId) {
  return http.post<IApiRiskStatisticsAcsAcute>({
    url: '/api/risk/statistics/acs/acute',
    data: params,
  });
}

/** 获取预后风险-非瓣膜性房颤脑卒中风险统计 */
export function getChaRiskData(params: IPatientId) {
  return http.post<IApiRiskStatisticsAcsCha>({
    url: '/api/risk/statistics/acs/cha',
    data: params,
  });
}

/** 获取预后风险-复杂ACS风险统计  */
export function getSynRiskData(params: IPatientId) {
  return http.post<IApiRiskStatisticsAcsSyn>({
    url: '/api/risk/statistics/acs/syn',
    data: params,
  });
}

/** 获取出血风险-ACS出血风险统计  */
export function getAciRiskData(params: IPatientId) {
  return http.post<IApiRiskStatisticsAci>({
    url: '/api/risk/statistics/aci',
    data: params,
  });
}

/** 获取出血风险-房颤抗凝出血风险统计  */
export function getBledRiskData(params: IPatientId) {
  return http.post<IApiRiskStatisticsBled>({
    url: '/api/risk/statistics/bled',
    data: params,
  });
}

/** 获取ASCVD总体风险  */
export function getAscvdRiskData(params: IPatientId) {
  return http.post<IApiRiskStatisticsAscvd>({
    url: '/api/risk/statistics/ascvd',
    data: params,
  });
}

/** 获取猝死风险  */
export function getDeathRiskData(params: IPatientId) {
  return http.post<IApiRiskStatisticsDeath>({
    url: '/api/risk/statistics/death',
    data: params,
  });
}

/** 获取风险等级 */
export function getRiskLevel(params: IPatientId) {
  return http.post<IApiRiskStatisticsRiskLevel>({
    url: '/api/risk/statistics/risk/level',
    data: params,
  });
}
