import type {
  CallAreaQueryParams,
  CallAreaResponse,
  CallRecordReadParams,
  CallRecordReadResponse,
  CallShowListQueryParams,
  CallShowListResponse,
  MissedCallPageQueryParams,
  MissedCallPageResponse,
  PatientAddressBookItem,
  PatientAddressBookQueryParams,
  PatientUserOpenItem,
  PatientUserOpenQueryParams,
} from '@/interface';
import { http } from '@/network';

/**
 * 查询患者通讯录列表
 * @param params 查询参数
 * @returns 患者通讯录列表
 */
export async function fetchPatientAddressBookList(
  params: PatientAddressBookQueryParams
) {
  const res: PatientAddressBookItem[] = await http.post({
    url: '/api/call/center/patient/address-book/tag/list',
    method: 'post',
    data: params,
  });
  return res.map(item => ({
    ...item,
    isEdit: false,
    isNewAdd: false,
  }));
}

/**
 * 查询患者亲情账号列表
 * @param params 查询参数
 * @returns 患者亲情账号列表
 */
export function fetchPatientUserOpenList(
  params: PatientUserOpenQueryParams
): Promise<PatientUserOpenItem[]> {
  return http.post({
    url: '/api/call/center/patient/user-open/tag/list',
    method: 'post',
    data: params,
  });
}

/**
 * 查询外显号码
 * @param params 查询参数
 * @returns 外显号码列表
 */
export function fetchCallShowList(
  params: CallShowListQueryParams
): Promise<CallShowListResponse> {
  return http.post({
    url: '/api/call/center/call/show/list',
    method: 'post',
    data: params,
  });
}

/**
 * 查询被叫号码归属地
 * @param params 查询参数
 * @returns 号码归属地信息
 */
export function fetchCallArea(
  params: CallAreaQueryParams
): Promise<CallAreaResponse> {
  return http.post({
    url: '/api/call/center/area/get',
    method: 'post',
    data: params,
  });
}

/**
 * 未接来电数量统计
 * @param params 查询参数
 * @returns 未接来电数量
 */
export function fetchMissedCallTotal(): Promise<number> {
  return http.post({
    url: '/api/call/center/missed/call/total',
    method: 'post',
    data: {},
  });
}

/**
 * 查询未读未接来电
 * @param params 查询参数
 * @returns 未接来电列表
 */
export function fetchMissedCallPage(
  params: MissedCallPageQueryParams
): Promise<MissedCallPageResponse> {
  return http.post({
    url: '/api/call/center/missed/call/page/query',
    method: 'post',
    data: params,
  });
}

/**
 * 通话记录已读标记
 * @param params 标记参数
 * @returns 操作结果
 */
export function markCallRecordRead(
  params: CallRecordReadParams
): Promise<CallRecordReadResponse> {
  return http.post({
    url: '/api/call/center/missed/call/read',
    method: 'post',
    data: params,
  });
}
