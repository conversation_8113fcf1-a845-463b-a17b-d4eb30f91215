import { http } from '@/network';
import {
  IApiPatientConversationTeamListParams,
  IApiPatientConversationTeamList,
  IApiPatientConversationQuestionType,
  IApiPatientConversationQuestionAnswerParams,
  IApiPatientConversationQuestionAnswer,
  IApiPatientConversationSpeechcraftAddParams,
  IApiPatientConversationSpeechcraftListParams,
  IApiPatientConversationSpeechcraftList,
  IApiDrugSystemListParams,
  IApiDrugSystemList,
  IApiDrugSystemDetailParams,
  IApiDrugSystemDetail,
  IApiCaseHistoryDiagnosisAccessoryParams,
  IApiCaseHistoryDiagnosisAccessory,
  IApiPatientConversationSpeechcraftDelete,
  IApiPatientConversationSpeechcraftDeleteParams,
  IApiPatientConversationSpeechcraftEdit,
  IApiPatientConversationSpeechcraftEditParams,
} from '@/interface/type';

/** 获取当前角色的患者群聊列表 */
export function getTeamList(params: IApiPatientConversationTeamListParams) {
  return http.post<IApiPatientConversationTeamList>({
    url: '/api/patient/conversation/team/list',
    data: params,
  });
}

/** 查询问题分类 */
export function getQuestionType() {
  return http.post<IApiPatientConversationQuestionType>({
    url: '/api/patient/conversation/question/type',
    data: {},
  });
}

/** 查询问题答复列表 */
export function getQuestinAnwser(
  params: IApiPatientConversationQuestionAnswerParams
) {
  return http.post<IApiPatientConversationQuestionAnswer>({
    url: '/api/patient/conversation/question/answer',
    data: params,
  });
}

/** 添加个人常用语 */
export function addSpeechCraft(
  params: IApiPatientConversationSpeechcraftAddParams
) {
  return http.post({
    url: '/api/patient/conversation/speechcraft/add',
    data: params,
  });
}

/** 分页获取常用语列表 */
export function getSpeechCraftList(
  params: IApiPatientConversationSpeechcraftListParams
) {
  return http.post<IApiPatientConversationSpeechcraftList>({
    url: '/api/patient/conversation/speechcraft/list',
    data: params,
  });
}
/** 获取系统药品列表 */
export function getDrugSystemType() {
  return http.post<{ typeList: { typeId: number; name: string }[] }>({
    url: '/api/drug/system/type',
    data: {},
  });
}

/** 获取系统药品列表 */
export function getDrugSystemList(params: IApiDrugSystemListParams) {
  return http.post<IApiDrugSystemList>({
    url: '/api/drug/system/list',
    data: params,
  });
}
/** 获取系统药品详情 */
export function getDrugSystemDetail(params: IApiDrugSystemDetailParams) {
  return http.post<IApiDrugSystemDetail>({
    url: '/api/drug/system/detail',
    data: params,
  });
}

/** 通过类型获取图片选择记录 */
export function getDiagnosisAccessory(
  params: IApiCaseHistoryDiagnosisAccessoryParams
) {
  return http.post<IApiCaseHistoryDiagnosisAccessory>({
    url: '/api/case/history/diagnosis/accessory',
    data: params,
  });
}

/** 删除个人常用语 */

export function deleteSpeechCraft(
  params: IApiPatientConversationSpeechcraftDeleteParams
) {
  return http.post<IApiPatientConversationSpeechcraftDelete>({
    url: '/api/patient/conversation/speechcraft/delete',
    data: params,
  });
}

/** 编辑个人常用语 */

export function editSpeechCraft(
  params: IApiPatientConversationSpeechcraftEditParams
) {
  return http.post<IApiPatientConversationSpeechcraftEdit>({
    url: '/api/patient/conversation/speechcraft/edit',
    data: params,
  });
}
