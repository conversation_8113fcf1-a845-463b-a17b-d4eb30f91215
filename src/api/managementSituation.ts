import { http } from '@/network';
import {
  IApiRiskTargetValue,
  IApiStructuredReportingGroupChat,
  IApiStructuredReportingIndexDetail,
  IApiStructuredReportingPatientThreshold,
  IApiStructuredReportingPushPatientWxParams,
  IApiOrderSelectReductionListParams,
} from '@/interface/type';

// 阶段性总结报告列表查询
export function getReportingListApi(params: any) {
  return http.post({
    url: '/api/structured/reporting/list',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 阶段性总结报告列表查询
export function addReportingApi(params: any) {
  return http.post({
    url: '/api/structured/reporting/save/structured',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询患者订单管理周期
export function queryPatientOrderApi(params: any) {
  return http.post({
    url: '/api/patient/order/manager/period',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 阶段性总结报告患者信息
export function queryPatientBaseMsgApi(params: any) {
  return http.post({
    url: '/api/structured/reporting/patient/info',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 所有指标数据
export function queryStructuredApi(params: any) {
  return http.post({
    url: '/api/structured/reporting/all/index',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 单个指标详细数据
export function queryStructuredDetailApi(params: any) {
  return http.post<IApiStructuredReportingIndexDetail>({
    url: '/api/structured/reporting/index/detail',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 心电图结论及时间
export function queryStructuredEcgDetailApi(params: any) {
  return http.post({
    url: '/api/structured/reporting/ecg/detail',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 心电图
export function queryStructuredEcgDataApi(params: any) {
  return http.post({
    url: '/api/structured/reporting/ecg/data',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 用户拥有群聊查询
export function queryChatApi(params: any) {
  return http.post<IApiStructuredReportingGroupChat>({
    url: '/api/structured/reporting/group/chat',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 阶段性总结报告详情查询
export function queryReportingDetailsApi(params: any) {
  return http.post({
    url: '/api/structured/reporting/detail',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 患者血压、心率阈值查询
export function queryThresholdApi(params: any) {
  return http.post<IApiStructuredReportingPatientThreshold>({
    url: '/api/structured/reporting/patient/threshold',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 患者行为数据
export function queryPatientInfoApi(params: any) {
  return http.post({
    url: '/api/manage/info/behavior',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 血压测量统计次数--折线图
export function queryPressureNumApi(params: any) {
  return http.post({
    url: '/api/manage/info/pressure/num',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 问诊时间段分布--饼图图
export function queryPressureTimeNumApi(params: any) {
  return http.post({
    url: '/api/manage/info/time/slot',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 血压测量统计次数--折线图
export function queryConsultNumApi(params: any) {
  return http.post({
    url: '/api/manage/info/consult/num',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 患者管理效果数据
export function queryEffectNumApi(params: any) {
  return http.post({
    url: '/api/manage/info/effect',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取患者目标值
export function queryTargetValueApi(params: any) {
  return http.post<IApiRiskTargetValue>({
    url: '/api/risk/target/value',
    method: 'post',
    data: params,
  });
}

// 患者随访复查完成率
export function queryCompleteApi(params: any) {
  return http.post({
    url: '/api/manage/info/complete/rate',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询患者订单服务信息
export function queryOrderApi(params: any) {
  return http.post({
    url: '/api/patient/order/service/info',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询患者订单服务信息
export function queryOrderRecordListApi(params: any) {
  return http.post({
    url: '/api/patient/order/record/list',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 查询患者订单服务信息
export function queryOrderRefundApi(params: any) {
  return http.post({
    url: '/api/order/refund/query/apply/info',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询退费流程对应的待支付补差价订单信息
export function queryOrderByApi(data: any) {
  return http.post({
    url: '/api/order/refund/query/order/by/refund',
    method: 'post',
    data,
  });
}

// 获取公司列表
export function queryCompanyListApi() {
  return http.post({
    url: '/api/order/company/list',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 提交退款申请
export function querySubmitRefundApi(params: any) {
  return http.post({
    url: '/api/order/refund/submit/device/apply',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 微信申请退款
export function queryInitiateWxRefundApi(params: any) {
  return http.post({
    url: '/api/order/refund/initiate/wx',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 取消退款申请
export function queryCancelOrderApi(params: any) {
  return http.post({
    url: '/api/order/cancel',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 撤销审批
export function queryProcessInstancesTerminateApi(params: any) {
  return http.post({
    url: '/api/order/refund/processInstancesTerminate',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 医生订单续费
export function queryRenewOrderApi(params: any) {
  return http.post({
    url: '/api/order/asst/renew',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 医生线下支付订单续费
export function queryOfflineRenewOrderApi(params: any) {
  return http.post({
    url: '/api/order/asst/offline/renew',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取患者会员到期时间
export function queryExpirationDateApi(params: any) {
  return http.post({
    url: '/api/patient/order/expirationDate',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 免费订单解除会员
export function queryTerminateApi(params: any) {
  return http.post({
    url: '/api/order/refund/terminate/free/vip',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询患者最近一条订单信息
export function queryOrderLastApi(params: any) {
  return http.post({
    url: '/api/patient/order/last/info',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取患者绑定工作室的产品列表
export function queryOrderProductApi(params: any) {
  return http.post({
    url: '/api/patient/order/product',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 患者群聊
export function queryConversationTeamListApi(params: any) {
  return http.post({
    url: '/api/patient/conversation/team/list',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

/** 结构化报告推送--针对患者端 */
export function pushSubscribeMsg(
  data: IApiStructuredReportingPushPatientWxParams
) {
  return http.post({
    url: '/api/structured/reporting/push/patient/wx',
    data,
  });
}

/** 查询订单减免列表 */
export function queryOrderSelectReductionListApi(
  data: IApiOrderSelectReductionListParams
) {
  return http.post({
    url: '/api/order/select/reduction/list',
    data,
  });
}
