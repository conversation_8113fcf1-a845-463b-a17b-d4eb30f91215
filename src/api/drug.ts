import { http } from '@/network';
import {
  IApiDrugSystemListParams,
  IApiDrugPatientCurrentParams,
  IApiDrugPatientHistoryParams,
  IApiDrugPatientAdjustParams,
  IApiDrugPatientStopParams,
  IApiDrugPatientAdjust,
  IApiDrugPatientCurrent,
} from '@/interface/type';

/** 获取系统药品列表 */
export function getDrugSystemList(params: any) {
  return http.post<IApiDrugSystemListParams>({
    url: '/api/drug/system/list',
    method: 'post',
    data: params,
  });
}

/** 获取患者当前用药 */
export function getDrugPatientCurrent(params: IApiDrugPatientCurrentParams) {
  return http.post<IApiDrugPatientCurrent>({
    url: '/api/drug/patient/current',
    method: 'post',
    data: params,
  });
}
/** 获取患者历史用药列表 */
export function getDrugPatientHistory(params: any) {
  return http.post<IApiDrugPatientHistoryParams>({
    url: '/api/drug/patient/history',
    method: 'post',
    data: params,
  });
}
/** 调整患者用药 */
export function getDrugPatientAdjust(params: IApiDrugPatientAdjustParams) {
  return http.post<IApiDrugPatientAdjust>({
    url: '/api/drug/patient/adjust',
    method: 'post',
    data: params,
  });
}
/** 停止患者用药 */
export function getDrugPatientStop(params: any) {
  return http.post<IApiDrugPatientStopParams>({
    url: '/api/drug/patient/stop',
    method: 'post',
    data: params,
  });
}

/** 根据id获取患者用药详情 */
export function getDrugPatientDetails(params: any) {
  return http.post({
    url: '/api/drug/patient/details',
    method: 'post',
    data: params,
  });
}
