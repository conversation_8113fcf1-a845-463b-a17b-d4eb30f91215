import { keyBy } from 'lodash-es';

const LOCAL_KEY = 'CHAT_FAIL_MSG';
const LIMIT = 200;

let failTime = 0;

window.addEventListener('load', () => {
  insertMsgSession({});
});
export const insertMsg = (list: any) => {
  localStorage.setItem(LOCAL_KEY, JSON.stringify(list));
};

export const insertMsgSession = (data: any) => {
  sessionStorage.setItem(LOCAL_KEY, JSON.stringify(data));
};

export const addMsg = (msg: any) => {
  const msgs = getMsg();
  const msgSession = getMsgSession();
  if (!msgs.find((v: any) => v.idClient === msg.idClient)) {
    msgs.push(msg);
    msgSession[msg.idClient] = 1;
    if (msgs.length > LIMIT) {
      const _msg = msgs.shift();
      delete msgSession[_msg.idClient];
    }
    failTime = Date.now();
    insertMsg(msgs);
    insertMsgSession(msgSession);
  }
};

export const getFailTime = () => failTime;

export const getMsg = () => {
  const str = localStorage.getItem(LOCAL_KEY);
  const msgs = str ? JSON.parse(str) : [];
  return msgs;
};
export const getMsgSession = () => {
  const str = sessionStorage.getItem(LOCAL_KEY);
  const msgSession = str ? JSON.parse(str) : {};
  return msgSession;
};

export const removeMsg = (msg: any) => {
  const msgs = getMsg();
  const msgSession = getMsgSession();
  const _msgs = msgs.filter((v: any) => {
    return !(v.to === msg.to && v.text === msg.text);
  });
  delete msgSession[msg.idClient];
  insertMsg(_msgs);
  insertMsgSession(msgSession);
};
export const removeMsgById = (id: string) => {
  const msgs = getMsg();
  const msgSession = getMsgSession();
  const _msgs = msgs.filter((v: any) => {
    return v.idClient !== id;
  });
  delete msgSession[id];
  insertMsg(_msgs);
  insertMsgSession(msgSession);
};
export const getMsgByTeamNo = (no: string) => {
  const msgs = getMsg();
  return msgs.filter((item: any) => item.to === no);
};

export const clearSessionMsg = () => {
  const msgSession = getMsgSession();
  const keys = Object.keys(msgSession);
  const msgs = getMsg();
  const _msgs = msgs.filter((v: any) => !keys.includes(v.idClient));
  insertMsg(_msgs);
  insertMsgSession({});
};

export const mergeMsgshandler = (msgs: any = []) => {
  const hisMsgs = msgs;
  const hisMsgsMap = keyBy(hisMsgs, 'idClient');
  const msgSession = getMsgSession();
  const localMsgs = getMsgByTeamNo(hisMsgs?.[0]?.to);
  for (const v of localMsgs) {
    if (hisMsgsMap[v.idClient]) {
      removeMsgById(v.idClient);
    }
  }
  const _localMsgs = localMsgs.filter(
    (v: any) => !msgSession[v.idClient] && !hisMsgsMap[v.idClient]
  );
  const mergeMsgs = [...hisMsgs, ..._localMsgs].sort(
    (a, b) => a?.time - b?.time
  );
  return mergeMsgs;
};
