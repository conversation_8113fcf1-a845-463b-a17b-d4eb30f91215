//处理时间戳（直接调用最后的transTime函数）
export const _$encode = (_map: any, _content?: string) => {
  _content = String(_content);
  if (!_map || !_content) {
    return _content || '';
  }
  return _content.replace(_map.r, function ($1) {
    const _result = _map[!_map.i ? $1.toLowerCase() : $1];
    return typeof _result !== 'undefined' ? _result : $1;
  });
};
export const dateFormat = (
  time: number,
  _format: string,
  _12time?: boolean
) => {
  const _map: Record<string, any> = {
    i: !0,
    r: /\byyyy|yy|MM|cM|eM|M|dd|d|HH|H|mm|ms|ss|m|s|w|ct|et\b/g,
  };
  const _12cc = ['上午', '下午'];
  const _12ec = ['A.M.', 'P.M.'];
  const _week = ['日', '一', '二', '三', '四', '五', '六'];
  const _cmon = [
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
    '七',
    '八',
    '九',
    '十',
    '十一',
    '十二',
  ];
  const _emon = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sept',
    'Oct',
    'Nov',
    'Dec',
  ];
  const _fmtnmb = function (_number: string) {
    const res = parseInt(_number) || 0;
    return (res < 10 ? '0' : '') + _number;
  };
  const _fmtclc = function (_hour: number) {
    return _hour < 12 ? 0 : 1;
  };
  if (!time || !_format) return '';
  const _time = new Date(time);
  _map.yyyy = _time.getFullYear();
  _map.yy = String(_map.yyyy).substr(2);
  _map.M = _time.getMonth() + 1;
  _map.MM = _fmtnmb(_map.M);
  _map.eM = _emon[_map.M - 1];
  _map.cM = _cmon[_map.M - 1];
  _map.d = _time.getDate();
  _map.dd = _fmtnmb(_map.d);
  _map.H = _time.getHours();
  _map.HH = _fmtnmb(_map.H);
  _map.m = _time.getMinutes();
  _map.mm = _fmtnmb(_map.m);
  _map.s = _time.getSeconds();
  _map.ss = _fmtnmb(_map.s);
  _map.ms = _time.getMilliseconds();
  _map.w = _week[_time.getDay()];
  const _cc = _fmtclc(_map.H);
  _map.ct = _12cc[_cc];
  _map.et = _12ec[_cc];
  if (_12time) {
    _map.H = _map.H % 12;
  }
  return _$encode(_map, _format);
};

//会话列表的时间显示
export const transTime = (time: number) => {
  const check = getDayPoint(new Date());
  if (time >= check[0]) {
    return dateFormat(time, 'HH:mm');
  } else if (time >= check[0] - 60 * 1000 * 60 * 24) {
    return '昨天';
  } else if (time >= check[0] - 2 * 60 * 1000 * 60 * 24) {
    return '前天';
  } else if (time >= check[0] - 7 * 60 * 1000 * 60 * 24) {
    // return "星期" + this.dateFormat(time, "w");
    return dateFormat(time, 'MM-dd');
  } else if (time >= check[1]) {
    return dateFormat(time, 'MM-dd');
  } else {
    return dateFormat(time, 'yyyy-MM-dd');
  }
};
export const getDayPoint = (time: Date) => {
  time.setMinutes(0);
  time.setSeconds(0);
  time.setMilliseconds(0);
  time.setHours(0);
  const today = time.getTime();
  time.setMonth(1);
  time.setDate(1);
  const yearDay = time.getTime();
  return [today, yearDay];
};
export const _$escape = (content: string) => {
  const _reg = /<br\/?>$/;
  return content.replace(_reg, '<br/><br/>');
};

// 会话列表最后一条信息展示处理
export const buildSessionMsg = (msg: any) => {
  if (!msg) return '';
  let text = msg.from === localStorage.getItem('currentIMId') ? '你：' : '';
  const type = msg.type;
  if (!/text|image|file|audio|video|geo|custom|tip|notification/i.test(type))
    return '';
  switch (type) {
    case 'text':
      text += _$escape(msg.text);
      break;
    case 'image':
      text += '[图片]';
      break;
    case 'file':
      if (!/exe|bat/i.test(msg.file.ext)) {
        text += '[文件]';
      } else {
        text += '[非法文件，已被本站拦截]';
      }
      break;
    case 'audio':
      text += '[语音]';
      break;
    case 'video':
      text += '[视频]';
      break;
    case 'geo':
      text += '[位置]';
      break;
    case 'tip':
      text = '[提醒消息]';
      break;
    case 'robot':
      text = '[机器人消息]';
      break;
    case 'custom':
      const content = JSON.parse(msg.content);
      if (content.type === 1) {
        text += '[猜拳]';
      } else if (content.type === 2) {
        text += '[阅后即焚]';
      } else if (content.type === 3) {
        text += '[贴图]';
      } else if (content.type === 4) {
        text += '[白板]';
      } else {
        text += '[自定义消息]';
      }
      break;
    case 'notification':
      text =
        msg.attach.type == 'addTeamMembers'
          ? '[通知信息]'
          : '[系统通知]QWERTYU';
      break;
    default:
      text += '[未知消息类型]';
      break;
  }
  if (msg.status === 'fail') {
    text = '<i class="icon el-icon-warning-outline"></i>' + ' ' + text;
  }
  return text;
};
//聊天框中时间显示
export const transTime2 = (time: number) => {
  const check = getDayPoint(new Date());
  if (time >= check[0]) {
    return dateFormat(time, 'HH:mm');
  } else if (time >= check[0] - 60 * 1000 * 60 * 24) {
    return '昨天' + dateFormat(time, 'HH:mm');
  } else if (time >= check[0] - 2 * 60 * 1000 * 60 * 24) {
    return '前天' + dateFormat(time, 'HH:mm');
  } else if (time >= check[0] - 7 * 60 * 1000 * 60 * 24) {
    return dateFormat(time, 'MM-dd HH:mm');
  } else if (time >= check[1]) {
    return dateFormat(time, 'MM-dd HH:mm');
  } else {
    return dateFormat(time, 'yyyy-MM-dd HH:mm');
  }
};

export const processMsg = (v: any) => {
  let istofrom = 'left';
  let content = '';
  let dur = 0;
  if (v.flow === 'out') {
    istofrom = 'right';
  }
  // if (v.from.indexOf('assistant@') != -1) {
  //   istofrom = 'right';
  // }
  // if (v.from.indexOf('doctor@') != -1) {
  //   istofrom = 'left';
  // }
  // if (v.from.indexOf('user@') != -1) {
  //   istofrom = 'left';
  // }
  if (v.type == 'tip') {
    content = v.tip;
  }
  if (v.type == 'text') {
    content = v.text;
  }
  if (v.type == 'image') {
    content = v.file.url;
  }
  if (v.type == 'video') {
    content = v.file.url;
  }
  if (v.type == 'audio') {
    content = (window as any).nim.audioToMp3({
      url: v.file.url,
    });
    dur = v.file.dur;
  }
  if (v.type == 'file' && v.file.ext == 'amr') {
    content = (window as any).nim.audioToMp3({
      url: v.file.url,
    });
    v.type = 'audio';
    dur = v.file.dur;
  }
  if (v.type == 'file' && v.file.ext == 'jpeg') {
    content = v.file.url;
    v.type = 'image';
  }
  if (v.type == 'file' && v.file.ext == 'mp4') {
    content = v.file.url;
    v.type = 'video';
    dur = v.file.dur;
  }
  if (v.type == 'custom') {
    const obj = JSON.parse(v.content);
    if (obj.type == '6' && obj.answerId) {
      obj.answerId = JSON.parse(obj.answerId);
    }
    v.customContent = obj;
    v.customType = obj.type;
  }
  return {
    ...v,
    text: content,
    avatar: '',
    dur,
    timeText: transTime2(v.time),
    timestap: v.time,
    sender: v.from,
    istofrom,
    msg: v,
    custom: isJSON(v.custom) ? JSON.parse(v.custom) : v.custom,
    customName:
      v.custom && !isJSON(v.custom)
        ? v.custom
        : v.custom && isJSON(v.custom)
          ? JSON.parse(v.custom).doctorName
          : null,
    customAvator:
      v.custom && !isJSON(v.custom)
        ? null
        : v.custom && isJSON(v.custom)
          ? JSON.parse(v.custom).doctorAvatar
          : null,
    userIsRead: !!v.userIsRead,
  };
};
//处理历史记录
export const history = (msgs: any) => {
  let hisMsg = [];
  hisMsg = msgs.reverse().map((v: any) => processMsg(v));
  return hisMsg;
};

//判断是否为json字符串
export const isJSON = (str: string) => {
  if (typeof str == 'string') {
    try {
      const obj = JSON.parse(str);
      if (typeof obj == 'object' && obj) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }
};
