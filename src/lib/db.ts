import dayjs from 'dayjs';
import <PERSON><PERSON>, { type EntityTable } from 'dexie';

type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [P in K]?: T[P];
};

export interface Log {
  id: number;
  /** 日志信息 */
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  timestamp: number;
  createTime: string;
  tag: string;
  extraData: any;
}

export const db = new Dexie('logsDB') as <PERSON><PERSON> & {
  logs: EntityTable<Log, 'id'>;
};
/** 是否清除历史数据 */
let hasClearOldData = false;

db.version(1).stores({
  logs: '++id, message, type, timestamp, createTime, tag, extraData', // Primary key and indexed props
});

/**
 * 添加日志记录📝
 * @param log 日志信息
 */
export async function addDBLog(
  log: MakeOptional<
    Pick<Log, 'message' | 'type' | 'tag' | 'extraData'>,
    'type' | 'extraData'
  >
) {
  try {
    if (!hasClearOldData) {
      await deleteOldLogs();
      hasClearOldData = true;
    }
    await db.logs.add({
      type: 'info',
      extraData: '',
      timestamp: Date.now(),
      createTime: new Date().toLocaleString(),
      ...log,
    });
  } catch (e) {
    console.error(e);
  }
}

/**
 * 批量删除3天前的日志记录🗑️
 * @param days 删除多少天前的日志，默认当天
 * @returns 删除的日志数量
 */
export async function deleteOldLogs(
  days: number = 0
): Promise<number | undefined> {
  try {
    // 计算指定天数前的日期0点时间戳
    const cutoffTimestamp = dayjs()
      .subtract(days, 'day')
      .startOf('day')
      .valueOf();
    console.log(new Date(cutoffTimestamp).toLocaleString());

    // 查询指定天数前的日志数量（用于返回删除数量）
    const oldLogsCount = await db.logs
      .where('timestamp')
      .below(cutoffTimestamp)
      .count();

    // 批量删除指定天数前的日志
    if (oldLogsCount > 0) {
      await db.logs.where('timestamp').below(cutoffTimestamp).delete();
    }

    console.log(
      `成功删除 ${oldLogsCount} 条${new Date(cutoffTimestamp).toLocaleString()}之前的日志记录`
    );
    return oldLogsCount;
  } catch (error) {
    console.error('删除旧日志失败:', error);
  }
}

/**
 * 获取日志统计信息📊
 * @returns 日志统计信息
 */
export async function getLogStats(): Promise<
  | {
      total: number;
      byType: Record<Log['type'], number>;
      oldLogsCount: number;
    }
  | undefined
> {
  try {
    const total = await db.logs.count();
    // 计算3天前的日期0点时间戳
    const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() - 3);
    targetDate.setHours(0, 0, 0, 0); // 设置为当天0点
    const cutoffTimestamp = targetDate.getTime();
    const oldLogsCount = await db.logs
      .where('timestamp')
      .below(cutoffTimestamp)
      .count();

    // 按类型统计
    const byType: Record<Log['type'], number> = {
      info: 0,
      warning: 0,
      error: 0,
      success: 0,
    };

    const logs = await db.logs.toArray();
    logs.forEach(log => {
      byType[log.type]++;
    });

    return {
      total,
      byType,
      oldLogsCount,
    };
  } catch (error) {
    console.error('获取日志统计失败:', error);
  }
}

/**
 * 清空所有日志记录🧹
 * @returns 删除的日志数量
 */
export async function clearAllLogs() {
  try {
    const count = await db.logs.count();
    await db.logs.clear();
    console.log(`成功清空所有 ${count} 条日志记录`);
    return count;
  } catch (error) {
    console.error('清空日志失败:', error);
  }
}
