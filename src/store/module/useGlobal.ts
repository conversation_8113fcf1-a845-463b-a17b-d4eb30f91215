import { getRehabilitationManageStatus } from '@/api/userList';
import RoleEnum from '@/constant/role';
import STORE_NAMES from '@/constant/storeNames';
import { defineStore } from 'pinia';
interface IState {
  /** 当前选中用户 userId */
  userId?: number;
  /* 当前角色列表 */
  userRoles: (keyof typeof RoleEnum)[] | null;
  /* 1, 2, 3, 4 */
  currentRole: 1 | 2 | 3 | 4;
  userInfo: {
    patientId: number;
    patientName: string;
    gender: number;
    age: number;
    hospitalName: string;
  };
  /** 跟患者相关模块刷新 key */
  rerenderKey: string;
  /**管理状态 （1 待管理、2 管理中、3 结束管理、4 管理中断） */
  manageStatus?: 1 | 2 | 3 | 4;
  /**患者类型 （1 会员、  2科研干预、3 科研对照、0 非会员） */
  patientType?: 1 | 2 | 3 | 0;
  manageTabReady?: boolean;
  /** 是否处于质疑模式 */
  isDoubting?: boolean;
  manageTabReadyCallback?: () => void;
  /**服务病种 ('HEART_FAILURE' 心衰、 'CORONARY_HEART_DISEASE' 冠心病)*/
  diseaseType?: 'HEART_FAILURE' | 'CORONARY_HEART_DISEASE';
  disableAutoRefresh?: boolean;
}

export const useGlobal = defineStore(STORE_NAMES.GLOBAL, {
  state: () =>
    ({
      userRoles: [],
      currentRole: 1,
      userInfo: {},
      rerenderKey: '1',
      disableAutoRefresh: false,
    }) as IState,
  actions: {
    setUserId(userId: number | undefined) {
      if (!userId) {
        this.rerenderKey = Math.random() + '';
        this.manageTabReady = false;
        this.manageTabReadyCallback = undefined;
      }
      this.userId = userId;
    },
    setUserRoles(userRoles: IState['userRoles']) {
      this.userRoles = userRoles;
      if (userRoles?.length) {
        const role = userRoles[0];
        this.setCurrentRole(RoleEnum[role] as IState['currentRole']);
      }
    },
    setCurrentRole(role: IState['currentRole']) {
      this.currentRole = role;
    },
    setUserInfo(obj: object) {
      this.userInfo = obj;
    },
    setManageStatus(patientId: number) {
      if (!patientId) return;
      if (this.currentRole === 3) {
        getRehabilitationManageStatus({ patientIds: [patientId] }).then(
          (res: any) => {
            this.manageStatus = res?.[0]?.manageStatus;
          }
        );
      }
    },
  },
});

export default useGlobal;
