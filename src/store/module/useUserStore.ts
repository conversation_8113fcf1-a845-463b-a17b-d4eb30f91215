import { defineStore } from 'pinia';
import { useLocalStorage, useSessionStorage } from '@vueuse/core';
import useGlobal from '@/store/module/useGlobal';
import RoleEnum from '@/constant/role';
import STORE_NAMES from '@/constant/storeNames';
import { TOKEN_KEY } from '@/constant/cache';
import { login, LoginParams } from '@/api/login';
import router from '@/router';
import routes from '@/router/route';
import PageEnum from '@/constant/pageEnum';

export interface UserState {
  cno?: string;
  accountId?: number;
  /** 是否支持登录呼叫中心 */
  isLogin?: boolean;
  isPopup?: boolean;
  userName?: string;
  imToken?: string;
  imAccid?: string;
  assistantRole?: string;
  seatsPassword?: string;
  enterpriseCode?: string;
  userRoles?: (keyof typeof RoleEnum)[];
  token?: string;
}

export const useUserStore = defineStore(STORE_NAMES.APP_USER, {
  state: (): UserState => ({
    // localStorage
    token: useLocalStorage(TOKEN_KEY, '').value,
    isPopup: useLocalStorage('isPopup', false).value,
    accountId: useLocalStorage('accountId', 0).value,
    userName: useLocalStorage('userName', '').value,
    imToken: useLocalStorage('imToken', '').value,
    imAccid: useLocalStorage('imAccid', '').value,
    assistantRole: useLocalStorage('assistantRole', '').value,
    userRoles: useLocalStorage('userRoles', []).value,
    // sessionStorage
    cno: useSessionStorage('cno', '').value,
    isLogin: useSessionStorage('isLogin', false).value,
    seatsPassword: useSessionStorage('seatsPassword', '').value,
    enterpriseCode: useSessionStorage('enterpriseCode', '').value,
  }),
  actions: {
    setLocalValue(key: string, val: any) {
      this[key] = val;
      useLocalStorage(key, val).value = val ?? '';
    },
    setSessionValue(key: string, val: any) {
      this[key] = val;
      useSessionStorage(key, val).value = val ?? '';
    },
    //sessionStorage
    setCno(cno?: string) {
      this.setSessionValue('cno', cno);
    },
    setIsLogin(isLogin?: boolean) {
      this.setSessionValue('isLogin', isLogin);
    },
    setSeatsPassword(seatsPassword?: string) {
      this.setSessionValue('seatsPassword', seatsPassword);
    },
    setEnterpriseCode(enterpriseCode?: string) {
      this.setSessionValue('enterpriseCode', enterpriseCode);
    },
    // localStorage
    setAccountId(accountId) {
      this.setLocalValue('accountId', accountId);
    },
    setIsPopup(isPopup?: boolean) {
      this.setLocalValue('isPopup', isPopup);
    },
    setUserName(userName?: string) {
      this.setLocalValue('userName', userName);
    },
    setImToken(imToken?: string) {
      this.setLocalValue('imToken', imToken);
    },
    setImAccId(imAccid?: string) {
      this.setLocalValue('imAccid', imAccid);
    },
    setAssistantRole(assistantRole?: string) {
      this.setLocalValue('assistantRole', assistantRole);
    },
    setUserRoles(userRoles: string[]) {
      this.setLocalValue('userRoles', userRoles);
    },
    setToken(token: string | undefined) {
      this.token = token ? token : '';
      useLocalStorage(TOKEN_KEY, token).value = token;
    },
    setUserAccount(account?: string) {
      useLocalStorage('userAccount', account);
    },
    resetState() {
      this.token = '';
      localStorage.clear();
      sessionStorage.clear();
    },
    /**
     * @description: login
     */
    async login(params: LoginParams, goHome?: boolean) {
      const useGlobalInfo = useGlobal();

      try {
        const res = await login(params);
        const { data, code } = res;
        console.log('$debug: data', data);
        if (code === 'E000000') {
          this.setCno(data.cno);
          this.setIsLogin(!!data.cno); // 医助是否可登录呼叫中心
          this.setIsPopup(true);
          this.setUserName(data.userName);
          this.setAccountId(data.userId);
          this.setToken(data.token);
          this.setImToken(data.imToken);
          this.setImAccId(data.imAccid);
          this.setAssistantRole(data.doctorRole);
          this.setSeatsPassword(data.seatsPassword);
          this.setEnterpriseCode(data.enterpriseCode);
          this.setUserRoles(data.userRoles);
          this.setUserAccount(params.loginAccount);
          useGlobalInfo.setUserRoles(data.userRoles as any);
          ElMessage({
            message: '登录成功！',
            type: 'success',
          });
          routes
            .filter((v: any) =>
              v.meta?.role?.includes(useGlobalInfo.currentRole)
            )
            .forEach(v => {
              router.addRoute(v);
            });
          setTimeout(() => {
            if (useGlobalInfo.currentRole === 4) {
              router.push('/intern');
            } else {
              router.push('/workbench');
            }
          });
        } else {
          ElMessage.error(res.message);
        }
        // this.setToken(token);
        // this.setRoleList(isArray(role) ? role : [role]);
        // this.setUserInfo(data);
        goHome && (await router.replace(PageEnum.BASE_HOME));
      } catch (error) {
        return Promise.reject(error);
      }
    },
    /**
     * @description: logout
     */
    async logout() {
      /*     if (this.token) {
        try {
          // todo
        } catch {
          console.log('注销Token失败');
        }
      }*/
      this.resetState();
      await router.push(PageEnum.BASE_LOGIN);
      setTimeout(() => {
        window.location.reload();
      }, 100);
    },
  },
});

export default useUserStore;
