import { defineStore } from 'pinia';
import STORE_NAMES from '@/constant/storeNames';
import {
  ItemType,
  IApiResearchDoubtInfo,
  IApiResearchDoubtLocationItem,
  IApiResearchDoubtLocation,
} from '@/interface/type';
import {
  getResearchDoubtInfo,
  recieveDoubt,
  rejectDoubt,
  doubtReply,
  doubtSubmit,
  getAnotherDoubt,
  doubtWithdraw,
  getDoubtLocation,
  getDoubtSnapshot,
} from '@/api/doubt';
import useGlobal from './useGlobal';
import { FormCategory } from '@/constant';
import { DoubtStatus, DoubtType } from '@/constant/doubt';
import useComponentsTabAction, {
  IAction,
} from '@/store/module/useComponentsTabAction';
import bus from '@/lib/bus';
import dayjs from 'dayjs';

type ITabParams = IAction & { mode?: string };
interface IState {
  backlogId?: number;
  locationSourceId?: number;
  unitData?: ItemType<IApiResearchDoubtLocation>;
  loading: boolean;
  doubtId?: number;
  doubtDetail: IApiResearchDoubtInfo;
}

const tabAction = useComponentsTabAction();
const globalStore = useGlobal();

const getDateTitle = (time: string | number, name: string) => {
  if (!time) return name;
  return dayjs(time).format('YYYY-MM-DD') + ' ' + name;
};
const openTabs = (params: ITabParams) => {
  const { componentType, name, key, data, mode } = params;
  tabAction.setAction({
    componentType,
    name,
    key,
    mode: !mode ? 'new' : mode,
    data,
  });
  bus.emit('open-component-tab');
};
const tabActionHandler = (data: IApiResearchDoubtLocationItem) => {
  const _names = ['住院', '门诊', '复查', '入组'];
  openTabs({
    componentType: data.sourceType,
    name: getDateTitle(data.occurredTime!, _names[data.sourceType!]),
    mainTabCode: 2,
    data: {
      id: data.sourceId!,
      recordActionType: { actionType: 'view', sourceType: data.sourceType },
    },
  } as ITabParams);
};

const useDoubt = defineStore(STORE_NAMES.DOUBT, {
  state: () =>
    ({
      loading: false,
      doubtDetail: {},
    }) as IState,
  actions: {
    async doubtCheck(id: number) {
      const res = await this.getDoubtDetail(id);
      if (res.doubtStatus === DoubtStatus.WAIT_RECEIVE) {
        await this.acceptHandler();
        globalStore.isDoubting = true;
      } else if (res.doubtStatus === DoubtStatus.INSPECTING) {
        globalStore.isDoubting = true;
      } else {
        this.reset();
        return;
      }
      if (res.doubtType === DoubtType.DATA_INSPECT) {
        await this.getLocation();
      } else {
        this.clearLocation();
      }
      return res;
    },
    async getLocation() {
      const res = await getDoubtLocation({ doubtId: this.doubtId! });
      //  打开 tab, 定位, 住院 门诊 住院 复查 入组
      if (res?.length === 1) {
        const curItem = res[0];
        if (curItem.sourceId) {
          if (![0, 1, 2, 3].includes(curItem?.sourceType as number)) return;
          tabActionHandler(curItem);
          this.unitData = curItem;
          this.locationSourceId = curItem.sourceId;
          setTimeout(() => {
            bus.emit('doubt-location');
          }, 600);
        }
      }
    },
    async getSnapshot(id) {
      const res = await getDoubtSnapshot({ doubtReplyId: id });
      return res;
    },
    async getDoubtDetail(id: number) {
      this.doubtId = id;
      this.loading = true;
      const res = await getResearchDoubtInfo({ doubtId: id }).finally(() => {
        this.loading = false;
      });
      this.doubtDetail = res;
      return res;
    },
    async acceptHandler() {
      await recieveDoubt({
        doubtId: this.doubtId!,
      });
    },
    async rejectHandler() {
      await rejectDoubt({
        doubtId: this.doubtDetail.doubtId!,
      } as any);
    },
    async submitHandler(data) {
      const loadingInstance = ElLoading.service({ fullscreen: true });
      const { unitType, unitCategory, sourceId, sourceType } =
        this.unitData ?? {};
      const lowercaseUt = unitType?.toLowerCase();
      const lowercaseUc = unitCategory?.toLowerCase();
      const isDiagnoseReport = lowercaseUc === FormCategory.DIAGNOSE_REPORT;
      const wrapDetail = {
        detail: { ...data, source_id: sourceId, source_type: sourceType },
        firstUnitKey: isDiagnoseReport ? lowercaseUc : lowercaseUt,
        secondUnitKey: isDiagnoseReport ? lowercaseUt : undefined,
      };
      const params = {
        doubtId: this.doubtId,
        data: JSON.stringify(wrapDetail),
      };
      await doubtSubmit(params).finally(() => {
        loadingInstance.close();
      });
      ElMessage.success('定向提交成功');
      this.getDoubtDetail(this.doubtId!);
    },
    async replyHandler(data) {
      const params = {
        doubtId: this.doubtId,
        ...data,
      };
      this.loading = true;
      await doubtReply(params).finally(() => {
        this.loading = false;
      });
      return await this.checkNextDoubt();
    },
    async checkNextDoubt() {
      const { patientId = 0 } = this.doubtDetail;
      this.loading = true;
      const res = await getAnotherDoubt({
        doubtId: this.doubtId!,
        patientId,
        type: 'NEXT',
        doubtStatuses: [DoubtStatus.WAIT_RECEIVE, DoubtStatus.INSPECTING],
      }).finally(() => {
        this.loading = false;
      });
      if (res) {
        this.clearLocation();
        this.doubtCheck(res.doubtId!);
        return true;
      }
      return false;
    },
    async getAnotherHandler(type: 'PREVIOUS' | 'NEXT') {
      const { patientId = 0 } = this.doubtDetail;
      this.loading = true;
      const res = await getAnotherDoubt({
        doubtId: this.doubtId!,
        patientId,
        type,
        doubtStatuses: [DoubtStatus.INSPECTING],
      }).finally(() => {
        this.loading = false;
      });
      if (res) {
        this.clearLocation();
        this.doubtDetail = res;
        this.doubtId = res.doubtId;
        if (res.doubtType === DoubtType.DATA_INSPECT) {
          await this.getLocation();
        }
      } else {
        ElMessage.warning('没有数据了！');
      }
    },
    async withDrawHandler() {
      const params = { doubtId: this.doubtId };
      await doubtWithdraw(params as any);
    },
    clearLocation() {
      this.unitData = undefined;
      this.locationSourceId = undefined;
    },
    reset() {
      this.loading = false;
      this.doubtId = undefined;
      this.doubtDetail = {};
      this.clearLocation();
    },
  },
});

export default useDoubt;
