import { defineStore } from 'pinia';
import STORE_NAMES from '@/constant/storeNames';
import { getReminderList, getReminderCount } from '@/api/reminder';
import dayjs from 'dayjs';
export interface UseReminderState {
  calenderTagMap: {
    [key: string]: {
      revampCount?: number;
      backlogCount?: number;
      doubtCount?: number;
    };
  };
  reminderList: any[];
  reminderTodoList: any[];
  showAllUndoRecord: boolean;
  totals: number;
  reminderTypeMap: any;
  filterOptions: number[];
}

export const useReminder = defineStore(STORE_NAMES.REMINDER_EVENT, {
  state: (): UseReminderState => {
    return {
      calenderTagMap: {},
      reminderList: [],
      reminderTodoList: [],
      showAllUndoRecord: true,
      totals: 0,
      reminderTypeMap: {},
      filterOptions: [1, 2, 3],
    };
  },
  actions: {
    getReminderListFnc(data) {
      // 异步请求完成之后也更新自己列表数据
      const cachedKey = this.showAllUndoRecord
        ? 'reminderTodoList'
        : 'reminderList';
      if (data.pageNum === 1) {
        this.reminderTypeMap = {};
        this[cachedKey] = [];
      }
      return new Promise((resolve, reject) => {
        getReminderList(data)
          .then(res => {
            if (res.code === 'E000000') {
              this[cachedKey] = (res.data.backlogResponseList || []).reduce(
                (pre, cur) => {
                  cur.paginationInfo = {
                    pageNum:
                      data.pageSize > 50
                        ? Math.ceil((res.data.totals || 50) / 50)
                        : data.pageNum,
                  };
                  const exist = pre.find(
                    item => item.patientId === cur.patientId
                  );
                  if (exist) {
                    const existItem = exist.children.findIndex(
                      it => it.backlogId === cur.backlogId
                    );
                    if (existItem > -1) {
                      exist.children.splice(existItem, 1, cur);
                    }
                    exist.children.push(cur);
                  } else {
                    pre.push({
                      ...cur,
                      patientId: cur.patientId,
                      children: [cur],
                    });
                  }
                  return pre;
                },
                this[cachedKey]
              );
              if (cachedKey === 'reminderList') {
                this.totals = res.data.totals || 0;
              }
              resolve(res.data);
            }
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    getReminderCountFnc(data) {
      getReminderCount(data).then(res => {
        this.calenderTagMap = {};
        res.data.forEach(item => {
          this.calenderTagMap[dayjs(item.date).format('YYYY-MM-DD')] = {
            ...item,
          };
        });
      });
    },
  },
});
