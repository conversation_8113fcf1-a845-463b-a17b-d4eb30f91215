import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import useIM from './useIM';
import useGlobal from './useGlobal';
import useAIConversation from './useAIConversation';
import { IApiMessageCenterListResponse } from '@/interface/type';
import { messageType } from '@/constant/message';
import { readMessage } from '@/api/message';
import bus from '@/lib/bus';
import { TOKEN_KEY } from '@/constant/cache';

interface IState {
  data: any[];
  isConnecting: boolean;
}
const HEART_TYPE = 'HEART_BEAT_OK_REPLY';
const REFRESH_TYPE = 'CURRENT_TASK_REFRESH';
const REFRESH_MESSAGE = 'MESSAGE_LIST_REFRESH';
const TASK_REFRESH = 'PATIENT_DATA_TASK_REFRESH';
const heartBeat = () => ({
  type: HEART_TYPE,
  timestamp: Date.now(),
});
/** 心跳时间 */
const heartTime = 1000 * 30;

let ws: any = null;
let clientTimer: any;
let serverTimer: any;
let retryTimes = 10;
const baseUrl =
  import.meta.env.VITE_APP_WS_Url ??
  'wss://www.hrttest.cn/health-manage/ws/message';
const URL = baseUrl + '?token=';
const imStore = useIM();
const globalStore = useGlobal();
const useMessage = defineStore('SOCKET_MESSAGE', {
  state: () =>
    ({
      data: [],
      isConnecting: false,
    }) as IState,
  actions: {
    init() {
      this.create();
      window.onbeforeunload = function () {
        ws?.close();
      };
    },
    create() {
      try {
        if ('WebSocket' in window) {
          const token = localStorage.getItem(TOKEN_KEY);
          ws = new WebSocket(URL + token);
          this.initWsEvent();
        } else {
          ElMessage.warning('当前浏览器不支持 Websocket！');
        }
      } catch (error) {
        console.log('【websocket】: error', error);
      }
    },
    initWsEvent() {
      ws.onopen = () => {
        console.log('【websocket】: 连接成功');
        this.heartCheckReset();
        this.heartCheck();
        retryTimes = 10;
      };
      ws.onclose = err => {
        console.log('【websocket】: 连接关闭', err);
        if (retryTimes) {
          this.reconnect();
        }
      };
      ws.onerror = (err: any) => {
        console.log('【websocket】: 连接发生错误:', err);
        if (retryTimes) {
          this.reconnect();
        }
      };
      ws.onmessage = (e: any) => {
        this.heartCheckReset();
        this.heartCheck();
        const data = JSON.parse(e.data);
        this.messageHandler(data);
      };
    },
    messageHandler(data: any) {
      if (data.sys) {
        // 系统消息
        if (data.data.opType === REFRESH_TYPE) {
          if (globalStore.disableAutoRefresh) {
            console.log('【websocket】: 列表自动刷新已关闭');
          } else {
            console.log('【websocket】: 刷新患者列表');
            // 刷新患者列表
            bus.emit('refresh-patient-list');
          }
        } else if (data.data.opType === REFRESH_MESSAGE) {
          console.log('【websocket】: 刷新消息中心，消息总数');
          // 刷新患者列表
          bus.emit('refresh-message');
        } else if (
          data.data.opType === messageType.RECOMMEND_CONVERSATION_REFRESH
        ) {
          bus.emit('ai-notification-message-refresh');
        } else if (data.data.opType === TASK_REFRESH) {
          // 更新任务数量
          bus.emit('batch-ocr-success-refresh');
        }
      } else {
        console.log('$debugZ【websocket】: 收到 toast 消息', data);
        // 提示消息
        const { msgType, sourceId } = data as IApiMessageCenterListResponse;
        const chatTypes = [
          messageType.PATIENT_CHAT_RECORD,
          messageType.TEAM_CHAT_RECORD,
          messageType.EXPERT_CHAT_RECORD,
        ];

        // Ai消息通知
        if (msgType === messageType.RECOMMEND_CONVERSATION) {
          const { msgSimpleContent = '', patientId, sourceId } = data || {};
          useAIConversation().setActiveAiConversation({
            outputDialogue: msgSimpleContent,
            patientId,
            recommendId: Number(sourceId),
          });
          bus.emit('ai-notification-message-refresh');
          return;
        }

        if (
          chatTypes.includes(msgType as messageType) &&
          imStore.curTeamId === sourceId
        ) {
          console.log(
            '【websocket】: 收到当前聊天群聊消息，不更新 toast, 调用已读接口'
          );
          // 聊天类型消息，并且是发给当前患者， 则不 toast 提示，并且清除消息
          readMessage({ msgId: data.msgId });
          // bus.emit('refresh-message');
        } else {
          this.data.push(data);
          imStore.isPlaying = true; // 播放 IM 声音
          if (!globalStore.isDoubting) {
            bus.emit('notification-message');
          }
        }
      }
    },
    reconnect() {
      if (this.isConnecting) return;
      ws = null;
      retryTimes -= 1;
      this.isConnecting = true;
      const waitTime = 2 + (10 - retryTimes);
      setTimeout(() => {
        this.create();
        this.isConnecting = false;
      }, waitTime * 1000);
    },
    heartCheckReset() {
      clearTimeout(clientTimer);
      clearTimeout(serverTimer);
    },
    heartCheck() {
      clientTimer = setTimeout(() => {
        if (ws?.readyState === 1) {
          ws.send(JSON.stringify(heartBeat()));
        }
        serverTimer = setTimeout(() => {
          // 检测后端是主动否断开连接
          ws.close();
        }, heartTime);
      }, heartTime);
    },
  },
});

export default useMessage;
