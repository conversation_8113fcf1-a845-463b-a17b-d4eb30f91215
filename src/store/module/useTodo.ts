import { defineStore } from 'pinia';

interface infoType {
  backlogId: number;
  content: string;
  headId: number;
  headRole: number;
  patientId: number;
  headName: string;
  imageUrl: string;
  overdueTime: string;
  term: string;
  type: number;
}
const useTodo = defineStore('todo', {
  state: () => {
    return {
      status: 0,
      todoInfo: {
        backlogId: 0,
        content: '',
        headId: 0,
        headRole: 0,
        groupId: 0,
        patientId: 0,
        headName: '',
        imageUrl: '',
        overdueTime: '',
        term: '',
        type: 0,
      },
      lookMySelf: false,
      category: <number[]>[1, 2, 3], // 大类：1整改、2待办、3质疑
      qualityRecord: [],
      rectificationReply: '',
    };
  },
  getters: {
    setStatus(state) {
      return (num: number) => (state.status = num);
    },
    setTodoInfo(state) {
      return (info: infoType) => (state.todoInfo = info);
    },
    setQualityRecord(state) {
      return record => (state.qualityRecord = record);
    },
    setRectificationReply(state) {
      return reply => (state.rectificationReply = reply);
    },
  },
});

export default useTodo;
