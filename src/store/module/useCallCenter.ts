import { queryAddressPhoneNameApi, queryCallItemApi } from '@/api/addressBook';
import { ElNotification } from 'element-plus';
import { defineStore } from 'pinia';

/**
 * 天润联通控制中心
 * @see https://develop.clink.cn/develop/web/cc_toolbar.html
 */
export const useCall = defineStore('callCenter', {
  state: () => {
    return {
      /** 天润融通线路是否登录：0-未登录；1-登录； */
      isCallLogin: 0,
      /** 天润融通是否在响铃中：0-未响铃; 1-未响中; */
      isCallInRinging: 0,
      callType: '',
      isCall: false,
      callPhone: '',
      tempCallPhone: '',
      /** 是否展示来电弹窗 */
      dialogVisibleTelephoneCalls: false,
      timmer: 1,
      callName: '',
      patientName: '',
      relation: '',
      /** 坐席状态：1-空闲；2-忙碌 */
      loginStatus: 1,
      /** 呼叫是否保持 */
      isCallHold: false,
      /** 来电号码 */
      incomingCallNumber: '',
      isTalking: false,
      /** 最后一次通话时间 */
      lastCallTime: new Date().getTime(),
    };
  },
  actions: {
    initCallingState() {
      (window as any).ClinkAgent.setup({ sipPhone: true, debug: false }, () => {
        (window as any).ClinkAgent.registerListener(
          (window as any).ClinkAgent.EventType.STATUS,
          async (e: any) => {
            console.log('$debug: registerListener EventType.STATUS', e);
            console.log('//坐席状态', e);
            this.callPhone = e.customerNumber;

            if (e.code === 'IDLE') {
              this.isCall = false;
              this.callType = '2';
              this.loginStatus = 1;
              this.isCallInRinging = 0;
              this.dialogVisibleTelephoneCalls = false;
              this.isCallHold = false;
              this.isTalking = false;
              clearTimeout(this.timmer);
              this.lastCallTime = new Date().getTime();
            }
            if (e.code === 'CALLING') {
              this.callType = '呼叫中';
            }
            if (e.code === 'RINGING' && e.action === 'ringingAgentOb') {
              this.callType = '外呼响铃';
            }
            if (
              e.code === 'RINGING' &&
              (e.action === 'ringingIb' || e.action === 'ringingTransfer')
            ) {
              this.callType = '呼入响铃';
              this.isCallInRinging = 1;
              this.incomingCallNumber = e.customerNumber;
              this.isCallHold = false;
              console.log('呼入响铃');
              this.getPhoneInfo(e.customerNumber);
              //15秒后弹屏
              this.timmer = setTimeout(() => {
                console.log('执行弹屏');
                this.dialogVisibleTelephoneCalls = true;
              }, 3000);
            }
            if (e.code === 'BUSY' && e.action === 'busyAgentOb') {
              this.callType = '外呼通话';
              this.isCall = true;
            }
            if (
              e.code === 'BUSY' &&
              (e.action === 'busyIb' || e.action === 'busyTransfer')
            ) {
              //记录呼入接听状态
              queryCallItemApi({
                callPhone: e.customerNumber,
                callType: 2,
              });

              this.callType = '呼入通话';
              this.isCall = true;
              this.isCallInRinging = 0;
              this.dialogVisibleTelephoneCalls = false;
              this.isTalking = true;
              clearTimeout(this.timmer);
            }
            if (e.code === 'WRAPUP') {
              this.callType = '整理中';
              this.isCall = false;
              this.isCallInRinging = 0;
              this.isTalking = false;
              this.lastCallTime = new Date().getTime();
            }
            if (e.code === 'BREAK_LINE') {
              console.log('$debug: 断线了', e);
            }
          }
        );

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.PREVIEW_OUTCALL,
          (result: { code: number; msg: string }) => {
            console.log('$debug: ResponseType.PREVIEW_OUTCALL', result);
            if (result.code === 0) {
              if (this.tempCallPhone) {
                this.tempCallPhone = '';
              }
              console.log('电话呼出成功', result);
            } else {
              ElNotification({
                title: '错误',
                message: '电话呼出失败：' + result.msg,
                type: 'error',
              });
            }
          }
        );

        //预览外呼响铃
        (window as any).ClinkAgent.registerListener(
          (window as any).ClinkAgent.EventType.PREVIEW_OUTCALL_RINGING,
          (event: { code: string; action: string }) => {
            console.log('预览外呼响铃', event);
            if (event.code === 'BUSY' && event.action === 'ringingCustomerOb') {
              this.callType = '响铃中...';
              this.isCall = true;
            }
          }
        );

        (window as any).ClinkAgent.registerListener(
          (window as any).ClinkAgent.EventType.PREVIEW_OUTCALL_BRIDGE,
          (event: { customerNumber: any; code: string; action: string }) => {
            console.log('预览外呼客户接听', event);

            //记录接听状态
            queryCallItemApi({
              callPhone: event.customerNumber,
              callType: 2,
            });

            if (event.code === 'BUSY' && event.action === 'busyOb') {
              this.callType = '通话中';
              this.isCall = true;
              this.isTalking = true;
            }
          }
        );

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.LOGIN,
          (result: { code: number; msg: any }) => {
            console.log('$debug: ResponseType.LOGIN', result);
            if (result.code === 0) {
              // 登录成功
              console.log('电话登录成功', result);
              this.isCallLogin = 1;

              ElNotification({
                title: '成功',
                message: '登录成功！',
                type: 'success',
              });
            } else {
              ElNotification({
                title: '错误',
                message: result.msg,
                type: 'error',
              });
              console.log('电话登录失败', result);
              // 登录失败
            }
          }
        );

        // 拨打电话
        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.PREVIEW_OUTCALL_CANCEL,
          function (event: { code: number }) {
            if (event.code === 0) {
              console.log('预览外呼成功', event);
            } else {
              console.log('预览外呼失败', event);
            }
          }
        );

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.LOGOUT,
          function (event: any) {
            console.log('//退出', event);
          }
        ); //退出

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.PAUSE,
          function (event: { code: number }) {
            if (event.code === 0) {
              ElNotification({
                title: '成功',
                message: '置忙成功',
                type: 'success',
              });
            } else {
              ElNotification({
                title: '错误',
                message: '置忙失败',
                type: 'error',
              });
              console.log('置忙失败', event);
            }
          }
        ); // 置忙

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.UNPAUSE,
          function (event: { code: number }) {
            if (event.code === 0) {
              ElNotification({
                title: '成功',
                message: '置闲成功',
                type: 'success',
              });
            } else {
              ElNotification({
                title: '错误',
                message: '置闲失败',
                type: 'error',
              });
              console.log('置忙失败', event);
            }
          }
        ); // 置闲

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.REFUSE,
          (event: any) => {
            console.log('//拒接', event);
            this.clearPhoneInfo();
          }
        ); // 拒接

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.UNLINK,
          (event: any) => {
            console.log('//挂断', event);
            this.clearPhoneInfo();
          }
        ); // 挂断
        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.MUTE,
          function (event: { code: number }) {
            if (event.code === 0) {
              ElNotification({
                title: '成功',
                message: '静音成功',
                type: 'success',
              });
            } else {
              ElNotification({
                title: '错误',
                message: '静音失败',
                type: 'error',
              });
              console.log('静音失败', event);
            }
          }
        ); // 静音

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.UNMUTE,
          function (event: { code: number }) {
            if (event.code === 0) {
              ElNotification({
                title: '成功',
                message: '取消静音成功',
                type: 'success',
              });
            } else {
              ElNotification({
                title: '错误',
                message: '取消静音失败',
                type: 'error',
              });
            }
          }
        ); // 取消静音

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.INVESTIGATION,
          function (event: { code: number }) {
            if (event.code === 0) {
              ElNotification({
                title: '成功',
                message: '满意度设置成功',
                type: 'success',
              });
            } else {
              ElNotification({
                title: '错误',
                message: '满意度设置失败',
                type: 'error',
              });
            }
          }
        ); // 满意度调查

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.HOLD,
          (event: { code: number }) => {
            if (event.code === 0) {
              ElNotification({
                title: '成功',
                message: '保持成功',
                type: 'success',
              });
              this.isCallHold = true;
            } else {
              ElNotification({
                title: '错误',
                message: '保持失败',
                type: 'error',
              });
            }
          }
        ); // 保持

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.UNHOLD,
          (event: { code: number }) => {
            if (event.code === 0) {
              ElNotification({
                title: '成功',
                message: '保持接回成功',
                type: 'success',
              });
              this.isCallHold = false;
            } else {
              ElNotification({
                title: '错误',
                message: '保持接回失败',
                type: 'error',
              });
            }
          }
        ); // 保持接回

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.SIP_LINK,
          (event: { code: number }) => {
            this.isTalking = true;
            if (event.code === 0) {
              ElNotification({
                title: '成功',
                message: '软电话接听成功',
                type: 'success',
              });
            } else {
              ElNotification({
                title: '错误',
                message: '软电话接听失败',
                type: 'error',
              });
            }
          }
        ); // 软电话接听

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.SIP_UNLINK,
          (event: { code: number }) => {
            if (event.code === 0) {
              ElNotification({
                title: '成功',
                message: '软电话挂断成功',
                type: 'success',
              });
              this.clearPhoneInfo();
            } else {
              ElNotification({
                title: '错误',
                message: '软电话挂断失败',
                type: 'error',
              });
            }
          }
        ); // 软电话挂断

        (window as any).ClinkAgent.registerCallback(
          (window as any).ClinkAgent.ResponseType.TRANSFER,
          function (event: { code: number }) {
            if (event.code === 0) {
              ElNotification({
                title: '成功',
                message: '电话转移成功',
                type: 'success',
              });
            } else {
              ElNotification({
                title: '错误',
                message: '电话转移失败',
                type: 'error',
              });
            }
          }
        ); // 转移
      });
    },
    /**
     * 天润联通退出登录
     */
    logout() {
      window.ClinkAgent?.logout({ logoutMode: 1, removeBinding: 0 });
      this.isCallLogin = 0;
      this.clearPhoneInfo();
    },
    /** 切换保持/取消保持通话 */
    toggleHold() {
      if (this.isCallHold) {
        window?.ClinkAgent?.unhold();
      } else {
        window.ClinkAgent?.hold();
      }
    },
    /**
     * 获取电话对应的会员信息
     * @param phoneNumber 电话号码
     */
    getPhoneInfo(phoneNumber: string) {
      queryAddressPhoneNameApi({
        phone: phoneNumber,
      })
        .then((res: { code: string; data: any }) => {
          console.info('更新电话信息', phoneNumber, res);
          const { code, data } = res;
          if (code == 'E000000') {
            this.callName = data.name;
            this.patientName = data.patientName;
            this.relation = data.relation;
          }
        })
        .catch((err: any) => {
          console.log(err);
        });
    },
    /**
     * 清除电话信息
     */
    clearPhoneInfo() {
      this.callName = '';
      this.patientName = '';
      this.relation = '';
    },
  },
});

export const useAICCStore = useCall;

export default useCall;
