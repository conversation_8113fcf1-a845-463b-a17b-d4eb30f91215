import { defineStore } from 'pinia';
import STORE_NAMES from '@/constant/storeNames';
import {
  getPatientByKeywords,
  getPatientChat,
  getPatientInfo,
  getPatientPhone,
  getPatientTag,
} from '@/api/userList';
import { getEndTimeStamp } from '@/utils';
import { IList } from '@/@types/global';

export interface IPhone {
  name: string;
  phone: string;
  relation: string;
}
export interface ITag {
  tagId: number;
  tagName: string;
}
export interface IChats {
  chatDetail: string;
  chatId: string;
  chatTime: string;
  sendUserName: string;
  sendUserType: 'PATIENT' | 'HRT_EXPERT' | 'HRT_DOCTOR';
  chatType: 'TEXT' | 'IMAGE' | 'AUDIO' | 'VIDEO' | 'CUSTOM' | 'NOTIFICATION';
}
export interface ICard {
  patientId: number;
  patientAge?: number;
  patientAvator?: string;
  patientGender: number;
  patientType: 0 | 1 | 2 | 3;
  patientName: string;
  doctorName?: string;
  teamType: 1 | 2 | 4; //医助-专家 2患者大群 4 哈瑞特团队
  teamId: string;
  currentTodoNum?: number;
  totalTodoNum?: number;
  totalPatientChats?: number;
  isUpload?: boolean;
  expireDays?: number;
  isConfirmEnrollment?: boolean;
  isMarkPatient?: boolean;
  patientChats?: IChats[];
  patientChat?: IChats;
  patientPhones?: IPhone[];
  patientTags?: ITag[];
  tagList?: ITag[];
  remainValidDays?: number;
  patientReview?: {
    reviewId?: number;
    reviewDate?: string;
  };
  patientFollowUp?: {
    followUpId?: number;
    followUpDate?: string;
  };
  manageStatus?: number;
  riskLevel?: number;
  serviceDiseaseType?: 'HEART_FAILURE' | 'CORONARY_HEART_DISEASE';
  heartFailureLevel?: string;
  isVulnerablePhase?: boolean;
  died?: boolean;

}
export type IType = number;
export type IsearchItem = IList<ICard>;
export interface IState {
  keywords: string;
  activeTab: IType;
  advancedData: {
    regionId?: number;
    hospitalId?: number;
    groupId?: number;
    patientTypes?: (0 | 1 | 2 | 3)[];
    diseaseTypes?: (
      | 'CORONARY_HEART_DISEASE'
      | 'HEART_FAILURE'
      | 'CARDIOVASCULAR'
    )[];
    orProjectId?: number;
    transformDate?: number[];
  };
  searchResult: {
    info: IsearchItem;
    chat: IsearchItem;
    tag: IsearchItem;
    phone: IsearchItem;
  };
  tabCount: {
    [key: number]: number;
  };
  tabRendered: {
    [key: number]: boolean;
  };
  keepUser: boolean;
  userChoosed: boolean;
}
const searchRequestMap = {
  info: getPatientInfo,
  chat: getPatientChat,
  tag: getPatientTag,
  phone: getPatientPhone,
};
const defaultSearchResult: IState['searchResult'] = {
  info: { totals: 0, data: [] },
  chat: { totals: 0, data: [] },
  tag: { totals: 0, data: [] },
  phone: { totals: 0, data: [] },
};
const defaultAdvancedData = {
  regionId: undefined,
  hospitalId: undefined,
  groupId: undefined,
  patientTypes: [],
  orderEnrolStartTime: null,
  orderEnrolEndTime: null,
};
const searchPageNumber = {
  info: 1,
  chat: 1,
  tag: 1,
  phone: 1,
};
const searchKeysMap = {
  info: 'patientName',
  chat: 'patientChat',
  phone: 'patientPhone',
  tag: 'patientTag',
};
const searchPageSize = 8;
const useUserList = defineStore(STORE_NAMES.USER_LIST, {
  state: (): IState => ({
    keywords: '',
    activeTab: 0,
    keepUser: false,
    userChoosed: false,
    tabCount: {
      0: 0,
      1: 0,
    },
    tabRendered: {
      0: false,
      1: false,
    },
    advancedData: { ...defaultAdvancedData },
    searchResult: { ...defaultSearchResult },
  }),
  actions: {
    resetAdvancedData() {
      this.advancedData = { ...defaultAdvancedData };
    },
    resetSearchResult() {
      this.searchResult = { ...defaultSearchResult };
      const keys = Object.keys(searchPageNumber);
      keys.map(
        key => (searchPageNumber[key as keyof typeof searchPageNumber] = 1)
      );
    },
    getAdvancedFormatData() {
      const params: any = {
        ...this.advancedData,
        isNeedRegHosGroup: ['regionId', 'hospitalId', 'groupId'].some(
          (v: string) => this.advancedData[v]
        ),
      };
      if (params.transformDate) {
        params.orderEnrolStartTime = params.transformDate[0] ?? null;
        params.orderEnrolEndTime = params.transformDate[1]
          ? getEndTimeStamp(params.transformDate[1])
          : null;
        delete params.transformDate;
      }
      return params;
    },
    async startSearch() {
      const data = (await getPatientByKeywords({
        queryText: this.keywords,
        pageNumber: 1,
        pageSize: searchPageSize,
      })) as IState['searchResult'];
      this.searchResult = data ?? {};
    },
    async loadSearchMore(type: keyof IState['searchResult']) {
      searchPageNumber[type]++;
      const params = {
        [searchKeysMap[type]]: this.keywords,
        pageNumber: searchPageNumber[type],
        pageSize: searchPageSize,
      };
      const data = (await searchRequestMap[type](params)) as IsearchItem;
      this.searchResult[type].data.push(...data.data);
      // this.searchResult[type].totals = data.totals;
    },
  },
});

export default useUserList;
