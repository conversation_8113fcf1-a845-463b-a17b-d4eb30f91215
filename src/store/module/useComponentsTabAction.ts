import { defineStore } from 'pinia';
import STORE_NAMES from '@/constant/storeNames';
import { IPatientTabAddItem } from '@/store/module/useTabs';
import { isNil, isPlainObject } from 'lodash-es';

/** 组件类型 0住院；1门诊；2复查；3入组；4批量异常处理； 5:生活方式; 6:症状随访; 7:阶段性总结报告 */
export type IComponentType = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7;

/** 来源类型 0住院；1门诊；2复查；3入组*/
export type IRecordSourceType = 0 | 1 | 2 | 3;

/** 操作类型 新增 查看 编辑 */
export type IRecordActionType = 'add' | 'view' | 'edit';

export interface IAction
  extends Omit<IPatientTabAddItem, 'data' | 'component' | 'closeable'> {
  componentType: IComponentType;
  closeable?: boolean;
  /**组件内传参数据 */
  data?: {
    id?: string | number;
    // tab 名称
    tabName?: string;
    /**复查/诊疗-行为 */
    recordActionType?: {
      sourceType: IRecordSourceType;
      actionType: IRecordActionType;
    };
    taskInfo?: {
      taskMethod: number;
      taskId: number;
    };
    [key: string]: any;
  };
}

/**
 *  外部打开门诊、住院、批量异常处理等行为信息
 *  参数mode -> mode ?? data?.id ? 'new' : 'reuse';
 *  参数key -> key ?? data?.id ? String(data?.id) : undefined;
 *  参数type -> type ?? ...;
 */
export const useComponentsTabAction = defineStore(
  STORE_NAMES.Component_Tabs_Action,
  {
    state: (): IAction => ({
      componentType: 0,
      name: '',
      closeable: true,
      mode: 'reuse',
      data: {
        tabName: '',
        recordActionType: {
          sourceType: 0,
          actionType: 'add',
        },
      },
    }),
    actions: {
      setAction({
        name,
        componentType,
        data,
        group,
        mode,
        key,
        closeable = true,
        commonSign,
        mainTabCode,
        type,
      }: IAction) {
        if (!name || isNil(componentType)) return;
        const { id, recordActionType } = data || {};
        this.componentType = componentType;
        this.name = name;
        this.group = group;
        this.mainTabCode = mainTabCode;
        this.closeable = closeable;
        this.commonSign = commonSign;
        this.mode = mode ?? (id ? 'new' : 'reuse');
        this.key = key ?? (id ? String(id) : undefined);
        this.data = isPlainObject(data)
          ? { ...this.data, ...data, tabName: name }
          : { tabName: name };
        this.type =
          type ??
          (['edit', 'add'].includes(String(recordActionType?.actionType))
            ? 'edit'
            : 'view');
      },
    },
  }
);

export default useComponentsTabAction;
