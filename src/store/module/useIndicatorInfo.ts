import { defineStore } from 'pinia';
import STORE_NAMES from '@/constant/storeNames';
interface IState {
  /** 血压监测 */
  bloodPressureMonitoring?: {
    patientId: number;
    indexTermId: number;
  };
  /** 是否有指标分析项目风险 */
  hasAnalysisRisk?: boolean;
}
const useIndicatorInfo = defineStore(STORE_NAMES.Indicator_Info, {
  state: (): IState => ({
    bloodPressureMonitoring: undefined,
    hasAnalysisRisk: false,
  }),
  actions: {},
  getters: {},
});

export default useIndicatorInfo;
