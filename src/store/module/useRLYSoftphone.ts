import Softphone, {
  SoftphoneInitParams,
  SoftphoneInstance,
} from '7moor-softphone-sdk';
import {
  addressCallDialoutMoorApi,
  callSeatQuery,
  queryAddressPhoneNameApi,
  signInMoor,
} from '@/api/addressBook';
import {
  RLY_SUFFIX,
  RLYAgentStatus,
  RLYAttachEventType,
  RLYLoginType,
  STORE_NAMES,
} from '@/constant';
import { CallSource } from '@/interface';
import { addDBLog } from '@/lib/db';
import { useMutation } from '@tanstack/vue-query';
import { useInterval, useIntervalFn } from '@vueuse/core';
import { ElMessage } from 'element-plus';
import { defineStore } from 'pinia';
import { ref } from 'vue';
import useCallCenter from './useCallCenter';

/**
 * 容联云呼叫中心集成
 * @see https://ccjs.7moor.com/
 */
export const useRLYSoftphone = defineStore(STORE_NAMES.RLYSoftphone, () => {
  /** 容联云是否登录 */
  const rlyIsLogin = ref(false);
  /** Softphone实例 */
  const moorSimpleSoftphone = ref<SoftphoneInstance | null>(null);
  /** 展示来电弹窗 */
  const rlyShowBellingDialog = ref(false);
  /** 容联云登录类型 */
  const rlyLoginType = ref<RLYLoginType>(RLYLoginType.SIP);
  /** 容联云坐席状态 */
  const rlyAgentStatus = ref<RLYAgentStatus>(RLYAgentStatus.Free);
  /** 容联云是否在通话中 */
  const rlyIsTalking = ref(false);
  /** 容联云是否在呼叫中 */
  const rlyIsDialing = ref(false);
  /** 容联云来电号码 */
  const rlyIncomingCallNumber = ref<string>('');
  /** 容联云坐席工号 */
  const rlyAgentNumber = ref<string>('');
  /** 容联云是否在呼叫保持中 */
  const rlyIsCallHold = ref(false);
  const useCallStore = useCallCenter();
  const lastCallTime = ref(new Date().getTime());
  const canLogout = ref(false);
  /** 事件绑定是否成功 */
  const isAttachEventSuccess = ref(false);

  /** 计时器 */
  const {
    counter: rlyCounter,
    reset: rlyCounterReset,
    resume: rlyCounterResume,
    pause: rlyCounterPause,
  } = useInterval(1000, {
    controls: true,
  });

  const {
    counter: rlyTalkingCounter,
    reset: rlyTalkingCounterReset,
    pause: rlyTalkingCounterPause,
    resume: rlyTalkingCounterResume,
  } = useInterval(1000, { controls: true });

  const { pause: attachEventPause, resume: attachEventResume } = useIntervalFn(
    () => {
      console.log('检查绑定是否成功');
      // 每2秒检查一下事件绑定是否成功
      if (moorSimpleSoftphone.value && !isAttachEventSuccess.value) {
        attachEvent();
      }
    },
    2000,
    { immediate: false }
  );

  const { mutateAsync } = useMutation({
    mutationKey: ['查询线路登录账号'],
    mutationFn: () => callSeatQuery({ channel: CallSource.MOOR }),
  });

  const { mutateAsync: fetchAddressInfo } = useMutation({
    mutationKey: ['查询外呼号码'],
    mutationFn: (phone: string) => queryAddressPhoneNameApi({ phone }),
  });

  /**
   * 设置容联云登录状态
   * @param login 是否登录
   */
  function setIsLogin(login: boolean) {
    rlyIsLogin.value = login;
  }

  /**
   * 初始化容联云实例
   * @param params 初始化参数
   */
  async function rlyInit(
    params?: Omit<SoftphoneInitParams, 'success' | 'fail' | 'proxy_url'>
  ) {
    if (moorSimpleSoftphone.value === null) {
      const data = await mutateAsync();
      if (!data || !data.cno || !data.loginPassword) {
        addDBLog({
          type: 'error',
          message: '您尚未绑定呼叫中心！',
          tag: '容联云',
        });
        ElMessage.warning({
          message: '您尚未绑定呼叫中心！',
          showClose: true,
        });
        return;
      }
      rlyAgentNumber.value = data.cno;
      moorSimpleSoftphone.value = new Softphone({
        ...params,
        accountId: data.accountId,
        password: data.loginPassword,
        agentNumber: data.cno + RLY_SUFFIX,
        loginType: rlyLoginType.value,
        success: () => {
          setIsLogin(true);
          rlyCounterReset();
          rlyCounterResume();
          attachEvent();
          canLogout.value = false;
          addDBLog({
            type: 'success',
            message: '初始化成功',
            tag: '容联云',
          });
          attachEventResume();
        },
        error: () => {
          ElMessage.warning({
            message: '您尚未绑定呼叫中心！',
            showClose: true,
          });
          addDBLog({
            type: 'success',
            message: '初始化失败',
            tag: '容联云',
          });
          setIsLogin(false);
          moorSimpleSoftphone.value = null;
          isAttachEventSuccess.value = false;
          attachEventPause();
        },
      });
    }
  }

  /**
   * 绑定容联云事件监听器
   */
  function attachEvent() {
    console.log('绑定事件');
    moorSimpleSoftphone.value?.attachEvent?.({
      success: () => {
        addDBLog({
          type: 'success',
          message: '事件绑定通道建立成功回调',
          tag: '容联云',
        });
        isAttachEventSuccess.value = true;
      },
      error: () => {
        addDBLog({
          type: 'error',
          message: '事件绑定异常回调',
          tag: '容联云',
        });
        isAttachEventSuccess.value = false;
      },
      message: res => {
        const type = res.type;
        const eventData = res.eventData;
        isAttachEventSuccess.value = true;
        addDBLog({
          type: 'info',
          message: '通话事件回调' + type,
          tag: '容联云',
          extraData: res,
        });

        switch (type) {
          /** 来电 */
          case RLYAttachEventType.belling:
            if (eventData.ExtenType === RLYLoginType.SIP) {
              rlyShowBellingDialog.value = true;
              rlyIncomingCallNumber.value = eventData.FromCid;
              useCallStore.callPhone = eventData.FromCid;
              fetchCallInfo();
            }
            break;
          case RLYAttachEventType.peerstate:
            rlyAgentStatus.value = res.typeValue;
            rlyIsTalking.value = false;
            rlyIsDialing.value = false;
            rlyShowBellingDialog.value = false;
            rlyIsCallHold.value = false;
            rlyTalkingCounterPause();
            canLogout.value = true;
            break;
          case RLYAttachEventType.unregister:
            isAttachEventSuccess.value = false;
            break;
          case RLYAttachEventType.dialing:
            rlyIsDialing.value = true;
            fetchCallInfo();
            break;
          case RLYAttachEventType.innerTalking:
            break;
          case RLYAttachEventType.dialTalking:
            rlyIsTalking.value = true;
            rlyIsDialing.value = false;
            setTalkingBusy();
            break;
          case RLYAttachEventType.dialTransfer:
            break;
          case RLYAttachEventType.threeWayTalking:
            break;
          case RLYAttachEventType.talking:
            rlyIsTalking.value = true;
            rlyIsDialing.value = false;
            setTalkingBusy();
            break;
          case RLYAttachEventType.kick:
            rlyIsCallHold.value = false;
            rlyIsLogin.value = false;
            rlyShowBellingDialog.value = false;
            rlyIsTalking.value = false;
            rlyIsDialing.value = false;
            moorSimpleSoftphone.value = null;
            isAttachEventSuccess.value = false;
            ElMessage({
              type: 'error',
              message: '容联云账号在其他地方登录！',
              showClose: true,
            });
            break;
        }
      },
    });
  }

  /**
   * 通话中时将状态临时设置为忙碌
   */
  function setTalkingBusy() {
    rlyTalkingCounterReset();
    rlyTalkingCounterResume();
  }

  function fetchCallInfo() {
    fetchAddressInfo(useCallStore.callPhone).then(
      (res: { code: any; data: any }) => {
        const { code, data } = res;
        if (code == 'E000000') {
          useCallStore.callName = data.name;
          useCallStore.patientName = data.patientName;
          useCallStore.relation = data.relation;
        }
      }
    );
  }

  /**
   * 容联云外呼
   * @param calleeNumber 被叫号码
   */
  function rlyOutboundCall(calleeNumber: string, outShow?: string) {
    const phoneNumber = calleeNumber.trim();
    addDBLog({
      message: '外呼号码：' + phoneNumber,
      tag: '容联云',
    });
    // 事件绑定失败时重新绑定事件
    if (!isAttachEventSuccess.value) {
      attachEvent();
    }
    useCallStore.callPhone = calleeNumber;
    if (!outShow) {
      moorSimpleSoftphone.value?.callApi?.dialout({
        calleeNumber: phoneNumber,
        success: () => {
          addDBLog({ type: 'success', message: '外呼成功', tag: '容联云' });
        },
        fail: (err: any) => {
          addDBLog({
            type: 'error',
            message: '外呼失败',
            tag: '容联云',
            extraData: err || '',
          });
        },
      });
    } else {
      const params = {
        fromExten: rlyAgentNumber.value,
        exten: phoneNumber,
        outShow: outShow,
        extenType: rlyLoginType.value,
      };
      addressCallDialoutMoorApi(params).then(data => {
        if (!data.succeed) {
          ElMessage({
            type: 'error',
            message: data.message,
            showClose: true,
          });
          addDBLog({
            message: '外呼失败',
            tag: '容联云',
            type: 'error',
            extraData: params,
          });
        } else {
          addDBLog({
            message: '外呼成功',
            tag: '容联云',
            type: 'success',
            extraData: params,
          });
        }
      });
    }
  }
  /**
   * 挂断电话
   */
  function rlyHangup() {
    addDBLog({
      message: '挂断电话',
      tag: '容联云',
    });
    moorSimpleSoftphone.value?.callApi?.hangup({
      success: () => {
        addDBLog({
          type: 'success',
          message: '挂断电话成功',
          tag: '容联云',
        });
        rlyShowBellingDialog.value = false;
        rlyIsTalking.value = false;
        useCallStore.callPhone = '';
        lastCallTime.value = new Date().getTime();
      },
      fail: (err: any) => {
        addDBLog({
          type: 'error',
          message: '挂断电话失败',
          tag: '容联云',
          extraData: err,
        });
      },
    });
  }

  /** 保持电话☎️ */
  function rlyHold() {
    addDBLog({
      message: '保持电话',
      tag: '容联云',
    });
    moorSimpleSoftphone.value?.callApi?.hold({
      success: () => {
        addDBLog({
          type: 'success',
          message: '保持电话成功',
          tag: '容联云',
        });
        rlyShowBellingDialog.value = false;
        rlyIsDialing.value = false;
        rlyIsCallHold.value = true;
      },
      fail: (err: any) => {
        addDBLog({
          type: 'error',
          message: '保持电话失败',
          tag: '容联云',
          extraData: err,
        });
      },
    });
  }

  /** 取消保持电话☎️ */
  function rlyUnHold() {
    addDBLog({
      message: '取消保持电话',
      tag: '容联云',
    });
    moorSimpleSoftphone.value?.callApi?.unhold({
      success: () => {
        addDBLog({
          type: 'success',
          message: '取消保持电话成功',
          tag: '容联云',
        });
        rlyShowBellingDialog.value = false;
        rlyIsDialing.value = false;
        rlyIsCallHold.value = false;
      },
      fail: (err: any) => {
        addDBLog({
          type: 'error',
          message: '取消保持电话失败',
          tag: '容联云',
          extraData: err,
        });
      },
    });
  }

  /** 切换保持/取消保持通话 */
  function rlyToggleHold() {
    if (rlyIsCallHold.value) {
      rlyUnHold();
    } else {
      rlyHold();
    }
  }

  /** 接听电话☎️ */
  function rlyAnswer() {
    addDBLog({
      message: '接听电话',
      tag: '容联云',
    });
    moorSimpleSoftphone.value?.webrtcApi?.answer({
      success: () => {
        addDBLog({
          type: 'success',
          message: '接听电话成功',
          tag: '容联云',
        });
        rlyShowBellingDialog.value = false;
        rlyIsDialing.value = false;
      },
      fail: (err: any) => {
        addDBLog({
          type: 'error',
          message: '接听电话失败',
          tag: '容联云',
          extraData: err,
        });
      },
    });
  }

  /**
   * 退出登录
   */
  function rlyLogout() {
    if (!canLogout.value) {
      return;
    }
    addDBLog({
      message: '退出登录',
      tag: '容联云',
    });
    moorSimpleSoftphone.value?.agentApi?.Logout({
      success: () => {
        addDBLog({
          type: 'success',
          message: '退出登录成功',
          tag: '容联云',
        });
        moorSimpleSoftphone.value = null;
        isAttachEventSuccess.value = false;
        setIsLogin(false);
        rlyCounterReset();
        rlyCounterPause();
        signInMoor(rlyAgentNumber.value);
        attachEventPause();
      },
      fail: err => {
        addDBLog({
          type: 'error',
          message: '退出登录失败',
          tag: '容联云',
          extraData: err,
        });
        ElMessage({
          type: 'error',
          message: '退出失败' + err.message,
          showClose: true,
        });
      },
    });
  }

  /**
   * 更新坐席登录类型
   * @param type 登录类型
   */
  function rlyChangeLoginType(type: RLYLoginType) {
    moorSimpleSoftphone.value?.agentApi?.changeLoginType({
      loginType: type,
      success: () => {
        rlyLoginType.value = type;
      },
      fail: (err: { success: boolean; message: string }) => {
        ElMessage({
          message: err.message,
          type: 'error',
        });
      },
    });
  }

  /**
   * 更新坐席状态
   * @param status 座席状态
   */
  function rlyChangeAgentStatus(status: RLYAgentStatus) {
    moorSimpleSoftphone.value?.agentApi?.updateAgentStatus({
      statusValue: status,
      success: () => {
        console.log('切换座席状态成功', status);
        rlyAgentStatus.value = status;
        rlyCounterReset();
      },
      fail: (err: { success: boolean; message: string }) => {
        console.log('切换座席状态失败', err);
        ElMessage({
          message: '切换坐席状态失败',
          type: 'error',
        });
      },
    });
  }

  return {
    /** 容联云是否登录 */
    rlyIsLogin,
    /** 展示来电弹窗 */
    rlyShowBellingDialog,
    /** 计时器 */
    rlyCounter,
    /** 容联云登录类型 */
    rlyLoginType,
    /** 容联云坐席状态 */
    rlyAgentStatus,
    /** 容联云是否在通话中 */
    rlyIsTalking,
    /** 容联云是否在呼叫中 */
    rlyIsDialing,
    /** 容联云来电号码 */
    rlyIncomingCallNumber,
    /** 容联云通话计时器 */
    rlyTalkingCounter,
    setIsLogin,
    /** 容联云坐席号码 */
    rlyAgentNumber,
    rlyInit,
    /** 容联云是否保持通话 */
    rlyIsCallHold,
    /** 容联云-外呼电话 */
    rlyOutboundCall,
    /** 容联云-挂断电话 */
    rlyHangup,
    /** 容联云-接听电话 */
    rlyHold,
    /** 容联云-取消保持电话 */
    rlyUnHold,
    /** 容联云-退出登录 */
    rlyLogout,
    /** 容联云-接听电话 */
    rlyAnswer,
    rlyCounterReset,
    rlyCounterResume,
    rlyCounterPause,
    /** 容联云-切换登录类型 */
    rlyChangeLoginType,
    /** 容联云-切换座席状态 */
    rlyChangeAgentStatus,
    /** 容联云-切换保持/取消保持通话 */
    rlyToggleHold,
    /** 容联云-最后一次通话时间 */
    lastCallTime,
    /** 容联云-是否可以退出登录 */
    canLogout,
  };
});
