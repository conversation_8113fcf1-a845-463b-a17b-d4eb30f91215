import { defineStore } from 'pinia';

const useReport = defineStore('report', {
  state: () => {
    return {
      reportInfo: {
        endDate: 0,
        orderId: '',
        productName: 0,
        startDate: 0,
        type: 0,
        reportId: 0,
      },
    };
  },
  getters: {
    setRseportInfo(state) {
      return (info: any) => (state.reportInfo = info);
    },
  },
});

export default useReport;
