import {
  countQualityRecord,
  getQualityPointPerson,
} from '@/api/qualityControl';
import { getReminderCount, getReminderListIOptions } from '@/api/reminder';
import type {
  IApiBacklogQueryOptionsItem,
  IApiQualityRecordPointPersonPersons,
  IApiQualityRecordPointPersonPoints,
} from '@/interface/type';
import dayjs from 'dayjs';
import { defineStore } from 'pinia';

export const useQualityRecord = defineStore('qualityRecord', {
  state: () => {
    return {
      qualityPoint: [] as IApiQualityRecordPointPersonPoints[],
      qualityPointPerson: [] as IApiQualityRecordPointPersonPersons[],
      backlogOptions: [] as IApiBacklogQueryOptionsItem[],
      reminderNumber: 0,
      shielderNumber: 0,
      lastTodoTime: 0,
      typedTodoNum: [0, 0, 0],
    };
  },
  getters: {
    getBacklogOptions: state => {
      return state.backlogOptions;
    },
    getTodoType: state => {
      return type => state.backlogOptions.find(it => it.type === type);
    },
  },
  actions: {
    getQualityPointPerson(data) {
      return getQualityPointPerson(data).then(res => {
        console.log('搜索条件', res);
        this.qualityPoint =
          res.points?.map(it => {
            return { ...it, label: it.description, value: it.id };
          }) ?? [];
        this.qualityPointPerson =
          res.persons?.map(it => {
            return { ...it, label: it.qualityName, value: it.qualityId };
          }) ?? [];
        return res;
      });
    },
    getReminderListIOptions(data) {
      return getReminderListIOptions(data).then(res => {
        if (data.all) {
          this.backlogOptions = res;
        }
        return res;
      });
    },
    getReminderNum(data) {
      return getReminderCount({
        ...data,
        options: [
          { pid: null, name: '全部', type: null, category: 1 },
          { pid: null, name: '全部', type: null, category: 2 },
          { pid: null, name: '全部', type: null, category: 3 },
        ],
        overdueTime: dayjs(new Date()).endOf('day').valueOf(),
        endTime: dayjs(new Date()).endOf('day').valueOf(),
        onlyMine: 1,
        untreated: 1,
        all: false,
      })
        .then(res => {
          this.typedTodoNum = [0, 0, 0];
          if (!res.data.length) {
            this.lastTodoTime = undefined;
            this.reminderNumber = 0;
          } else {
            this.lastTodoTime = res.data.sort(
              (a, b) => a.date - b.date
            )[0].date;
            this.reminderNumber = res.data.reduce((pre, cur) => {
              this.typedTodoNum[0] += Number(cur.revampCount);
              this.typedTodoNum[1] += Number(cur.backlogCount);
              this.typedTodoNum[2] += Number(cur.doubtCount);
              return (
                pre +
                Number(cur.backlogCount) +
                Number(cur.doubtCount) +
                Number(cur.revampCount)
              );
            }, 0);
          }
          console.log('获取待办条数', this.reminderNumber);
          return res.data.length;
        })
        .catch(err => console.log(err));
    },
    getQualityNum(data) {
      return countQualityRecord(data).then(res => {
        console.log('获取质控条数', res);
        this.shielderNumber = res;
        return res;
      });
    },
  },
});

export default useQualityRecord;
