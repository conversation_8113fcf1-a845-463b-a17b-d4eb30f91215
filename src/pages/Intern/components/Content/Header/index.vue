<template>
  <div class="header">
    <el-form ref="formRef" :inline="true" :model="formData" class="flex">
      <el-form-item label="患者" prop="patientName">
        <el-input
          v-model="formData.patientName"
          placeholder="患者姓名"
          clearable
          style="width: 160px"
        />
      </el-form-item>
      <el-form-item label="患者类型" prop="patientType">
        <el-select
          v-model="formData.patientType"
          placeholder="请选择"
          filterable
          clearable
          style="width: 100px"
        >
          <el-option
            v-for="item in PATIENT_TYPE"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="工作室" prop="workRoomId">
        <el-select
          v-model="formData.workRoomId"
          placeholder="请选择或输入搜索"
          filterable
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="item in groupList"
            :key="item.workRoomId"
            :label="item.workRoomName"
            :value="item.workRoomId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="医生" prop="doctorId">
        <el-select
          v-model="formData.doctorId"
          placeholder="请选择"
          filterable
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="item in doctorSelectList"
            :key="item.doctorId"
            :label="item.doctorName"
            :value="item.doctorId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-select
          v-model="formData.taskType"
          placeholder="请选择"
          filterable
          style="width: 100px"
        >
          <el-option
            v-for="v in TASK_TYPE"
            :key="v.value"
            :label="v.name"
            :value="v.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="最后提交日期" prop="createTime">
        <el-date-picker
          v-model="formData.createTime"
          type="daterange"
          start-placeholder="请选择"
          end-placeholder=" "
          value-format="x"
          :range-separator="formData.createTime?.[0] ? '-' : ' '"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="taskStatus">
        <el-select
          v-model="formData.taskStatus"
          placeholder="请选择"
          style="width: 100px"
        >
          <el-option
            v-for="v in TASK_STATUS"
            :key="v.value"
            :label="v.name"
            :value="v.value"
          />
        </el-select>
      </el-form-item>
      <div class="inline">
        <el-button type="primary" @click="submit">搜索</el-button>
        <el-button type="" @click="reset">重置</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { FormInstance } from 'element-plus';
import { getExistGroupDoctorList, patientTypeApi } from '@/api/intern';
import { TASK_STATUS, TASK_TYPE } from '@/constant/intern';
import {
  IApiInternExistGroupDoctorListDoctorList,
  IApiInternExistGroupDoctorListWorkRoomList,
} from '@/interface/type';
defineOptions({
  name: 'FormHeader',
});
const emits = defineEmits(['search']);
const groupList = ref<IApiInternExistGroupDoctorListWorkRoomList[]>([]);
const doctorSelectList = ref<IApiInternExistGroupDoctorListDoctorList[]>([]);
const formRef = ref<FormInstance>();
const formData = ref({
  patientName: '',
  workRoomId: null,
  doctorId: null,
  taskType: -999,
  taskStatus: -999,
  createTime: null,
  patientType: null,
});

const emitHandler = () => {
  emits('search', { ...formData.value });
};
const submit = () => {
  emitHandler();
};
const reset = () => {
  formRef.value?.resetFields();
  emitHandler();
};

const getDoctorGroupHandler = async () => {
  const res = await getExistGroupDoctorList({});
  const { doctorList = [], workRoomList = [] } = res;
  groupList.value = workRoomList;
  doctorSelectList.value = doctorList;
};

// 患者类型筛选条件查询
interface IPatientType {
  name: string;
  value: string;
}
interface IRes {
  typeName: string;
  patientIds: number[];
}
const PATIENT_TYPE = ref<IPatientType[]>([]);
const getPatientType = async () => {
  const res: IRes[] = (await patientTypeApi()) as IRes[];
  PATIENT_TYPE.value = res.map((item: IRes) => {
    return {
      name: item.typeName,
      value: JSON.stringify(item.patientIds),
    };
  });
};

onMounted(() => {
  emitHandler();
  getDoctorGroupHandler();
  getPatientType();
});
</script>

<style scoped lang="less">
.header {
  height: 64px;
  width: 100%;
  overflow: hidden;
  padding: 16px;
  background: #fff;
  box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
  border-radius: 0px 0px 6px 6px;
  :deep(.el-input) {
    width: 240px;
  }
  :deep(.el-select) {
    width: 240px;
  }
  :deep(.el-date-editor--daterange) {
    width: 240px;
  }
  :deep(.el-button) {
    width: 76px;
  }
  :deep(.el-form-item__label) {
    color: #111111;
    font-weight: 400;
  }
}
</style>
