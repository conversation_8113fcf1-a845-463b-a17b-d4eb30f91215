<template>
  <div class="h-full flex flex-col bg-[#F6F8FB]">
    <WorkBenchHeader />
    <div class="flex flex-1">
      <Content />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useLocalStorage } from '@vueuse/core';
import Content from './components/Content/index.vue';
import useGlobal from '@/store/module/useGlobal';

const WorkBenchHeader = defineAsyncComponent(
  () => import('../Workbench/Header/index.vue')
);
const globalStore = useGlobal();
const userRoles = JSON.parse(useLocalStorage('userRoles', '[]').value) ?? [];
if (userRoles?.length) {
  globalStore.setUserRoles(userRoles as any);
}
</script>
<style scoped lang="less">
.welComePage {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  .welcomeImg {
    width: 62.5%;
    cursor: pointer;
  }
}
</style>
