<template>
  <div class="chart pb-xl">
    <Echarts :index-info="indexInfo" />
  </div>
  <BaseTable :height="304" :data="tableData" :pagination="false">
    <el-table-column prop="generateTime" label="测量日期" width="180">
      <template #default="scope">
        {{ formatDate(scope.row.generateTime) }}
      </template>
    </el-table-column>
    <el-table-column prop="source" label="数据来源" align="center" />
    <el-table-column prop="weight" label="数值" align="center" width="180">
      <template #default="scope">
        <span :class="scope.row.errorLevel === 1 ? 'error-data' : ''">{{
          scope.row.weight
        }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="treatmentMethod" label="处理结果">
      <template #default="scope">
        <span
          >{{ getTreatmentMethod(scope.row.treatmentMethod) }}
          {{ formatDate(scope.row.processingTime) }}</span
        >
      </template>
    </el-table-column>
  </BaseTable>
</template>

<script setup lang="ts">
import BaseTable from '@/components/BaseTable';
import Echarts from './components/Echarts.vue';
import { riskWeightEvent } from '@/api/exceptions';
import store from '@/store';
const globalStore = store.useGlobal();
import dayjs from 'dayjs';
import { formatDate } from '@/utils/index';
import { cloneDeep} from 'lodash-es';

let tableData = ref([]);
let indexInfo = ref<any>({
  nuit: '公斤',
  yData: [],
  xData: [],
});

onMounted(() => {
  getData();
});

let getData = async () => {
  const params = { patientId: globalStore.userId! };
  const data: any = await riskWeightEvent(params);
  tableData.value = cloneDeep(data.weightResponse);
  let xData: any = [];
  let yData: any = [];
  data.weightResponse = data.weightResponse.sort(
    (a, b) => a.generateTime - b.generateTime
  );
  data.weightResponse.forEach(
    (item: {
      generateTime: string | number | Date | dayjs.Dayjs | null | undefined;
      weight: any;
    }) => {
      const targetDate = dayjs(item.generateTime).format('YYYY-MM-DD');
      xData.push(targetDate);
      yData.push(item.weight);
    }
  );
  indexInfo.value.xData = xData;
  indexInfo.value.yData = [yData];
};

// 获取处理结果
let getTreatmentMethod = computed(() => {
  return function (num) {
    let str = '';
    let list = [
      '电话沟通',
      '在线沟通',
      '3待观察',
      '观察',
      '调药',
      '就诊',
      '住院',
    ];
    if (!num) {
      str = '--';
    } else {
      str = list[num - 1];
    }

    return str;
  };
});
</script>

<style scoped lang="less">
.chart {
  height: 360px;
}
.error-data {
  color: red;
}
</style>
