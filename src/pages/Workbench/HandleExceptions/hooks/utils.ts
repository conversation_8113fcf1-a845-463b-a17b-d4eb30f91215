import dayjs from 'dayjs';
import {
  IApiRiskBloodView,
  IApiRiskDeviceView,
  IApiRiskTargetValue,
} from '@/interface/type';
export const typeMap = {
  1: '空腹',
  2: '餐后2小时',
  3: '随机',
};

export const eventTreatMap = {
  4: '观察',
  5: '调药',
  6: '门诊',
  7: '住院',
};

// 开启 dataZoom 数据量
const dataZoomLimit = 50;

export const eventTreatmethodOptions = Object.keys(eventTreatMap).map(
  (key: string) => ({
    value: Number(key),
    name: eventTreatMap[key as unknown as keyof typeof eventTreatMap],
  })
);
export const generateSeriesItem = (config: any) => {
  const { color, name, markLine, data = [], markArea } = config;
  return {
    name,
    data,
    type: 'line',
    connectNulls: true,
    // showSymbol: false,
    lineStyle: { color },
    itemStyle: { color },
    markLine: markLine && {
      // animation: false,
      emphasis: {
        disabled: true,
      },
      symbol: 'none',
      data: [
        {
          name: markLine.name ?? name,
          yAxis: markLine.yAxis,
          symbol: 'none',
          label: {
            color,
            position: 'end',
            fontSize: 12,
            padding: 12,
            formatter: (params: any) => params.name,
          },
        },
      ],
    },
    markArea,
  };
};

export const getEchartsOptions = (config: any) => {
  const {
    xAxisData,
    seriesConfig,
    yAxis,
    dataZoom = { enable: false },
    grid = {},
  } = config;

  return {
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      top: 30,
      left: 50,
      right: 60,
      bottom: 58,
      ...grid,
    },
    yAxis: {
      axisLabel: {
        formatter: (value: number) => {
          if (value === 0) return '';
          return value;
        },
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
      ...yAxis,
    },
    dataZoom: dataZoom.enable
      ? [
          {
            brushSelect: false,
            // showDataShadow: false,
            borderColor: 'transparent',
            backgroundColor: '#ebf6ff',
            handleStyle: {
              color: '#0a73e4',
              borderWidth: 0,
            },
            show: true,
            realtime: true,
            start: 0,
            end: 100,
            xAxisIndex: [0, 1],
            bottom: 10,
            ...dataZoom,
          },
        ]
      : null,
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
    },
    legend: {
      bottom: 0,
      icon: 'stack',
      itemWidth: 10,
      itemHeight: 10,
    },
    series: seriesConfig.map((item: any) => generateSeriesItem(item)),
  };
};

export const formateTime = (time?: string | number) => {
  if (!time) return '--';
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

export const getTreatmentName = (type: number) => {
  const treatmentMap = {
    1: '电话沟通',
    2: '在线沟通',
    3: '待观察',
    4: '观察',
    5: '用药调整',
    6: '门诊',
    7: '住院',
  };
  return treatmentMap[type as keyof typeof treatmentMap];
};

export const getBloodPressureEchartsConfig = (
  _data: IApiRiskDeviceView,
  targetValue: IApiRiskTargetValue = {},
  dataType: string = 'blood'
) => {
  const { bloodPressure, hearRate } = targetValue;
  const data = _data.filter(
    item =>
      Number(item.highPressure) > 0 ||
      Number(item.heartRate) > 0 ||
      Number(item.highPressure) > 0
  );
  const xAxisData = data.map(item => item.date);
  const yValues = data
    .map(item => [item.lowPressure, item.heartRate, item.highPressure])
    .flat();
  const yMax = Math.max(...(yValues as number[]));
  const yAxis = yMax > 150 ? {} : { min: 0, max: 150 };
  const dataZoom = { enable: xAxisData.length >= dataZoomLimit, height: 20 };
  const getCurrentData = (key: string) =>
    data.map(item => item[key as keyof typeof item]);
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  const [_, lowPressure, highPressure] =
    bloodPressure!.match(/(\d+)\/(\d+)/) ?? [];
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  const [__, rateMin, rateMax] = hearRate!.match(/(\d+)~(\d+)/) ?? [];
  const seriesConfigMap = {
    blood: [
      {
        name: '收缩压',
        color: '#E58B48',
        markLine: {
          yAxis: highPressure ? Number(highPressure) : 0,
          name: `收缩压(${highPressure})`,
        },
        data: getCurrentData('highPressure'),
      },
      {
        name: '舒张压',
        color: '#0A73E4',
        markLine: {
          yAxis: lowPressure ? Number(lowPressure) : 0,
          name: `舒张压(${lowPressure})`,
        },
        data: getCurrentData('lowPressure'),
      },
    ],
    heart: [
      {
        name: '心率',
        color: '#2DA641',
        markArea: {
          emphasis: {
            disabled: true,
          },
          data: [
            [
              {
                yAxis: rateMin ? Number(rateMin) : 0,
                itemStyle: {
                  color: '#2DA641',
                  opacity: 0.06,
                },
                name: `心率(${rateMin}-${rateMax})`,
                symbol: 'none',
                label: {
                  color: '#2DA641',
                  position: 'right',
                  fontSize: 12,
                  padding: 12,
                },
              },
              {
                yAxis: rateMax ? Number(rateMax) : 0,
              },
            ],
          ],
        },
        data: getCurrentData('heartRate'),
      },
    ],
  };
  return {
    grid: {
      right: 85,
    },
    yAxis,
    dataZoom,
    xAxisData,
    seriesConfig: seriesConfigMap[dataType],
  };
};

export const getBloodGlucoseEchartsConfig = (data: IApiRiskBloodView) => {
  const _data = data.map(item => {
    item.blood = JSON.parse(item.blood ?? '');
    return item;
  });
  const xAxisData =
    (_data?.[0]?.blood as any)?.map((item: any) =>
      item.date?.replace(' ', '\n')
    ) ?? [];
  const dataZoom = { enable: xAxisData.length >= dataZoomLimit, height: 20 };
  const getCurrentData = (type: number) => {
    const filterData = data.filter(item => item.type === type);
    return (filterData?.[0]?.blood as any)?.map((item: any) => item.giu);
  };
  return {
    grid: {
      right: 80,
    },
    dataZoom,
    xAxisData,
    seriesConfig: [
      {
        name: typeMap[1],
        color: '#363687',
        // markLine: {
        //   yAxis: 90,
        // },
        data: getCurrentData(1),
      },
      {
        name: typeMap[2],
        color: '#8A3FD4 ',
        // markLine: {
        //   yAxis: 3,
        // },
        data: getCurrentData(2),
      },
      {
        name: typeMap[3],
        color: '#11A0AD',
        // markLine: {
        //   yAxis: 50,
        // },
        data: getCurrentData(3),
      },
    ],
  };
};

export const getBloodPressureTotalConfig = () => {
  const count = [
    { key: 'deviceNum', label: '测量总次数', unit: '次' },
    { key: 'deviceExceptionNum', label: '异常预警总次数', unit: '次' },
    { key: 'maxExceptionLevel', label: '最高异常等级', unit: '' },
  ];
  // 收缩压
  const systolic = [
    { key: 'maxHighPressure', label: '最高', unit: 'mmHg' },
    { key: 'minHighPressure', label: '最低', unit: 'mmHg' },
    { key: 'avgHighPressure', label: '平均', unit: 'mmHg' },
  ];
  // 舒张压
  const diastolic = [
    { key: 'maxLowPressure', label: '最高', unit: 'mmHg' },
    { key: 'minLowPressure', label: '最低', unit: 'mmHg' },
    { key: 'avgLowPressure', label: '平均', unit: 'mmHg' },
  ];
  // 心率
  const heartRate = [
    { key: 'maxHeartRate', label: '最高', unit: '次/分' },
    { key: 'minHeartRate', label: '最低', unit: '次/分' },
    { key: 'avgHeartRate', label: '平均', unit: '次/分' },
  ];
  return {
    count,
    systolic,
    diastolic,
    heartRate,
  };
};

export const errorLevelTextMap = {
  1: '偏低',
  2: '一级',
  3: '二级',
  4: '三级',
};

/** 血糖异常文案 */
export const errorLevelRiskBloodMap = {
  1: '偏低',
  2: '偏高',
};
