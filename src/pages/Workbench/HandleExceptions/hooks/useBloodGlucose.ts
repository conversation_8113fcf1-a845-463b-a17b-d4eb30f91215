import type { TableColumnCtx } from 'element-plus/es/components/table/src/table-column/defaults';
import { CSSProperties } from 'vue';
import { getRiskBloodData, getRiskBloodView } from '@/api/exceptions';
import store from '@/store';
import {
  getTreatmentName,
  formateTime,
  getBloodGlucoseEchartsConfig,
  getEchartsOptions,
  typeMap,
  errorLevelRiskBloodMap,
} from './utils';
interface ICellStyle<T> {
  row: T;
  rowIndex: number;
  column: TableColumnCtx<T>;
  columnIndex: number;
}

/** @description 血糖异常 */
export default function useBloodGlucose() {
  const options = ref({});
  const tableData = ref<any>([]);
  const loading = ref(true);
  const globalStore = store.useGlobal();

  const cellClassNameHandler = (data: ICellStyle<any>) => {
    const styleObj: CSSProperties = {};
    const { row, columnIndex } = data;
    const { treatmentMsg, errorLevel } = row;
    if (columnIndex === 2 && errorLevel > 0) {
      styleObj.color = 'red';
    }
    if (treatmentMsg) {
      styleObj.borderTop = '1px solid #0A73E4';
    }
    return styleObj;
  };

  const getBloodExDataHandler = async () => {
    const params = { patientId: globalStore.userId! };
    const data = await getRiskBloodData(params);
    if (data.bloodDataList) {
      tableData.value = data.bloodDataList.map(item => {
        const {
          treatmentMethod,
          processingTime,
          abnormalLevel,
          uploadTime,
          type,
        } = item;
        const treatmentMsg = treatmentMethod
          ? `${getTreatmentName(treatmentMethod)} ${formateTime(
              processingTime
            )}`
          : null;
        return {
          ...item,
          typeName: typeMap[type as keyof typeof typeMap],
          errorLevelText:
            (abnormalLevel && errorLevelRiskBloodMap[abnormalLevel]) || '',
          treatmentMsg,
          date: formateTime(uploadTime),
        };
      });
    }
  };

  const getBloodExViewHandler = async () => {
    loading.value = true;
    const params = { patientId: globalStore.userId! };
    const data = await getRiskBloodView(params);
    loading.value = false;
    const config = getBloodGlucoseEchartsConfig(data);
    options.value = getEchartsOptions(config);
  };

  const init = () => {
    getBloodExDataHandler();
    getBloodExViewHandler();
  };
  onMounted(() => {
    init();
  });

  return {
    options,
    tableData,
    loading,
    cellClassNameHandler,
  };
}
