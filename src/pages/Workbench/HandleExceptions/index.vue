<template>
  <div class="handle-exceptions-wrapper flex flex-col">
    <div class="flex-1 pb-2xs overflow-auto">
      <Header :target-value="targetValue" />
      <CardWrapper
        v-if="riskType === 1 || riskType === 5"
        class="mt-2xs"
        title="血压异常"
      >
        <BloodPressureAbnormal v-if="targetValue" :target-value="targetValue" />
      </CardWrapper>
      <CardWrapper
        v-if="riskType === 2 || riskType === 5"
        class="mt-2xs"
        title="心率异常"
      >
        <HeartRateAbnormal v-if="targetValue" :target-value="targetValue" />
      </CardWrapper>
      <CardWrapper
        v-if="riskType === 3 || riskType === 5"
        class="mt-2xs"
        title="血糖异常"
      >
        <BloodGlucoseAbnormal v-if="targetValue" />
      </CardWrapper>
      <CardWrapper
        v-if="riskType === 4 || riskType === 5"
        class="mt-2xs"
        title="体重异常"
      >
        <BloodWeightAbnormal v-if="targetValue" />
      </CardWrapper>
    </div>
    <HrtDialog v-model="dialogVisible" title="处理意见" :width="600">
      <div class="content py-16 px-24">
        <div class="title">办理方式</div>
        <div class="processing-method">
          <HrtCheckboxGroup v-model="checkProcessingMethodList">
            <HrtCheckbox
              v-for="(item, index) in processingMethodList"
              :key="index"
              :label="item"
              :value="index"
            />
          </HrtCheckboxGroup>
        </div>
        <div class="title mt-22">触达用户</div>
        <HrtRadioGroup
          v-model="reachUsers"
          class="treat-method"
          @change="reachUsersChange"
        >
          <HrtRadio
            v-for="(item, index) in reachUsersList"
            :key="index"
            :label="item"
            :value="index"
          >
            <div class="font-sm">
              {{ item }}
            </div>
          </HrtRadio>
        </HrtRadioGroup>
        <div class="title mt-22">办理结果</div>
        <HrtRadioGroup
          v-if="reachUsers === 0"
          v-model="treatMethod"
          direction="vertical"
          class="treat-method"
        >
          <HrtRadio
            v-for="item in eventTreatList"
            :key="item.value"
            :label="item.value"
          >
            <div class="font-sm">
              {{ item.name }}
              <span>{{ item.tips }}</span>
            </div>
          </HrtRadio>
        </HrtRadioGroup>
        <div
          v-else
          class="processing-result p-16 mt-12 flex items-center justify-between"
        >
          <HrtCheckboxGroup
            v-model="checkProcessingResultList"
            @change="changeProcessingResultHandler"
          >
            <HrtCheckbox
              v-for="(item, index) in processingResultList"
              :key="index"
              :label="item"
              :value="index"
            />
          </HrtCheckboxGroup>
          <HrtInput
            v-if="checkProcessingResultList.includes(2)"
            v-model="otherText"
            style="width: 200px"
            maxlength="100"
            placeholder="请输入原因（100以内）"
            type="text"
            clearable
          />
        </div>
      </div>
      <QualityRisk
        v-if="isHaveQualityControl"
        v-model:situation-textarea="situationTextarea"
      />
      <template #footer>
        <div class="p-sm pr-lg border-t">
          <HrtButton @click="handleCancel">取消</HrtButton>
          <HrtButton
            :loading="loadingDrugTableData || handleEventLoading"
            type="primary"
            @click="handleSubmit"
          >
            继续完成
          </HrtButton>
        </div>
      </template>
    </HrtDialog>
    <div class="footer shrink-0 flex items-center justify-end px-sm bg-white">
      <HrtButton
        type="primary"
        :disabled="!canDealExceptions"
        @click="dialogVisible = true"
      >
        处理
      </HrtButton>
    </div>

    <EditDrug
      v-if="chooseDrugVisible"
      v-model:choose-drug-visible="chooseDrugVisible"
      :params-data="drugTableData"
      @confirm-drug="handleAdjustDrug"
      @cancel-edit="chooseDrugVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import Header from './Header.vue';
import BloodPressureAbnormal from './BloodPressureAbnormal.vue';
import BloodGlucoseAbnormal from './BloodGlucoseAbnormal.vue';
import BloodWeightAbnormal from './BloodWeightAbnormal.vue';
import HeartRateAbnormal from './HeartRateAbnormal.vue';
import CardWrapper from '@/components/CardWrapper/index.vue';
import { useToggle } from '@vueuse/core';
import store from '@/store';
import {
  getRiskTargetValue,
  riskHandleEvent,
  riskHandlePartEvent,
  getBacklogInfo,
} from '@/api/exceptions';
import {
  IApiDrugPatientCurrentDrugList,
  IApiRiskTargetValue,
} from '@/interface/type';
import EditDrug from '@/components/DrugInfo/components/EditDrug.vue';
import { getDrugPatientAdjust, getDrugPatientCurrent } from '@/api/drug';
import bus from '@/lib/bus';
import { ITabProps } from '@/store/module/useTabs';
import QualityRisk from '@/components/QualityRisk/index.vue';
import { qualityCheck, createQualityRecord } from '@/api/qualityControl';

defineOptions({
  name: 'HandleExceptions',
});
const props = defineProps<Partial<ITabProps>>();

const riskType = computed(() => props.data?.riskType);

const globalStore = store.useGlobal();
const handleEventLoading = ref(false);
const canDealExceptions = ref(true);
const [dialogVisible, setDialogVisible] = useToggle();
const treatMethod = ref(4);
const targetValue = ref<IApiRiskTargetValue>();
const eventTreatList = [
  {
    value: 4,
    name: '观察',
    tips: '（不生成后续待办）',
  },
  {
    value: 5,
    name: '调药',
    tips: '（调药操作后3日，系统提醒您跟踪患者调药）',
  },
  {
    value: 6,
    name: '门诊',
    tips: '（系统3日后自动提醒您跟踪患者门诊情况）',
  },
  {
    value: 7,
    name: '住院',
    tips: '（系统3日后自动提醒您跟踪患者住院情况）',
  },
];
const riksTypeMap = {
  1: 'BLOOD_PRESSURE',
  2: 'HEART_RATE',
  3: 'BLOOD_SUGAR',
  4: 'WEIGHT',
};
const processingMethodList = ref<string[]>(['电话', '在线咨询', '其他']);
const checkProcessingMethodList = ref([]);
const reachUsers = ref(0);
const reachUsersList = ref(['成功', '失败']);
const processingResultList = ref<string[]>(['无法联系', '拒绝沟通', '其他']);
const checkProcessingResultList = ref<number[]>([]);
const otherText = ref<string>('');
const situationTextarea = ref<string>('');
const isHaveQualityControl = ref<boolean>(false);
const qualityControlInfo = ref({});

/* 抵达用户 */
const reachUsersChange = () => {
  treatMethod.value = 4;
  checkProcessingResultList.value = [];
  otherText.value = '';
};
/* 办理结果 */
const changeProcessingResultHandler = () => {
  if (!checkProcessingResultList.value.includes(2)) {
    otherText.value = '';
  }
};

const getTargetValue = async () => {
  const params = { patientId: globalStore.userId! };
  targetValue.value = await getRiskTargetValue(params);
};
onMounted(() => {
  getTargetValue();
  queryHandleTodoInfo();
});

// ‘处理’提交成功后需完成的事项
const handleDealOnSubmit = () => {
  bus.emit('updete-todo-list');
  bus.emit('update-analysis-indicator');
  canDealExceptions.value = false;
};
const treatmentMethodReq = async () => {
  const params = {
    patientId: globalStore.userId!,
    treatmentMethod: treatMethod.value,
    type: riskType.value,
    reachUser: reachUsers.value === 1 ? 0 : 1,
    checkResult: checkProcessingResultList.value.join(','),
    checkResultInput: otherText.value,
    contactType: checkProcessingMethodList.value.join(','),
  };
  try {
    handleEventLoading.value = true;
    await handlePartOrAllReq(params);
    ElMessage.success('处理成功');
    setDialogVisible(false);
    handleDealOnSubmit();
    createQualityCheck();
  } finally {
    handleEventLoading.value = false;
  }
};
const handleSubmit = async () => {
  if (!checkProcessingMethodList.value.length) {
    ElMessage.warning('请选择处理方式！');
  } else if (situationTextarea.value.length < 5) {
    ElMessage.warning('风险说明不能少于5个字!');
  } else if (reachUsers.value === 0) {
    if (treatMethod.value === 5) {
      await getDrugPatientCurrents();
      chooseDrugVisible.value = true;
    } else {
      await treatmentMethodReq();
    }
  } else if (
    reachUsers.value === 1 &&
    !checkProcessingResultList.value.length
  ) {
    ElMessage.warning('请选择办理结果！');
  } else if (
    reachUsers.value === 1 &&
    checkProcessingResultList.value.includes(2) &&
    !otherText.value
  ) {
    ElMessage.warning('请填写办理结果其他原因！');
  } else {
    await treatmentMethodReq();
  }
};

/* 取消 */
const handleCancel = () => {
  dialogVisible.value = false;
  checkProcessingMethodList.value = [];
  reachUsers.value = 0;
  treatMethod.value = 4;
  checkProcessingResultList.value = [];
  otherText.value = '';
  situationTextarea.value = '';
};

/*判断处理的事风险待办还是其他*/
const handlePartOrAllReq = async params => {
  if (riskType.value === 5) {
    delete params.type;
    await riskHandleEvent(params);
  } else {
    await riskHandlePartEvent(params);
  }
};

/* 查询待办信息 */
const queryHandleTodoInfo = async () => {
  const params = {
    patientId: globalStore.userId!,
    type: 'RISK_HANDLE',
    riskType: riksTypeMap[riskType.value],
  };
  const res: any = await getBacklogInfo(params);
  if (res?.backlogId) getQualityCheck(res);
};
// 查询待办的质量检查点
const getQualityCheck = async (res: any) => {
  const params = {
    userId: res.headId,
    userType: String(res.headRole),
    source: 2,
    dicType: res.type,
    backlogId: res.backlogId,
    patientId: res.patientId,
  };
  const data: any = await qualityCheck(params);
  isHaveQualityControl.value = data?.length > 0;
  const checkPoint = data?.map((item: { point: string; complete: boolean }) => {
    return {
      point: item.point,
      complete: item.complete,
    };
  });
  qualityControlInfo.value = {
    ...params,
    sourceId: res.backlogId,
    sourceCode: 2,
    groupId: res.groupId,
    checkPoint,
  };
};
// 创建质控
const createQualityCheck = async () => {
  await createQualityRecord(qualityControlInfo.value);
};

/** 调药 */
const loadingDrugTableData = ref(false);
const imStore = store.useIM();
const chooseDrugVisible = ref(false);
const drugTableData = ref<IApiDrugPatientCurrentDrugList[]>([]);
const getDrugPatientCurrents = async () => {
  loadingDrugTableData.value = true;
  try {
    const res = await getDrugPatientCurrent({ patientId: globalStore.userId! });
    drugTableData.value =
      res.drugList?.map(v => {
        if (v.drugAmount) delete v.drugAmount['custom'];
        return v;
      }) || [];
  } finally {
    loadingDrugTableData.value = false;
  }
};
const handleAdjustDrug = async (obj: any) => {
  const { sourceType, sourceId } = props.data || {};
  try {
    const res = await getDrugPatientAdjust({
      ...obj,
      patientId: globalStore.userId!,
      sourceId,
      sourceType,
      riskType: riksTypeMap[riskType.value],
    });
    if (obj.adjustDrugTrack) {
      // 跟踪用药需要发送药物调整消息卡片
      imStore.sendPatientCustomMsg({
        content: {
          name: '药物调整',
          id: res.drugInfoId,
          type: 7,
        },
      });
    }
    await treatmentMethodReq();
  } catch {
    ElMessage.error('调整用药失败!');
  } finally {
    chooseDrugVisible.value = false;
    setDialogVisible(false);
  }
};
</script>

<style scoped lang="less">
.handle-exceptions-wrapper {
  padding: 0 2px;
  height: calc(100vh - 162px);
  .treat-method {
    span {
      color: #7a8599;
    }
  }
  .el-button {
    width: 76px;
  }
  .title {
    color: #3a4762;
    font-weight: bold;
  }
  .processing-result {
    background: #f7f8fa;
  }
}
.footer {
  height: 56px;
  border-top: 1px solid #e9e8eb;
}
</style>
