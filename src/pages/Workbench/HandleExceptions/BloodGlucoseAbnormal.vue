<template>
  <div class="chart pb-xl">
    <BaseChart type="line" :data-complete="!loading" :options="options" />
  </div>
  <BaseTable
    :height="304"
    :data="tableData"
    :pagination="false"
    :cell-style="cellClassNameHandler"
  >
    <el-table-column prop="date" label="测量日期" width="180" />
    <el-table-column prop="typeName" label="类型" width="120" align="right" />
    <el-table-column prop="giu" label="数值" width="110" align="right" />
    <el-table-column
      prop="errorLevelText"
      label="等级"
      width="110"
      align="right"
    />
    <el-table-column label="" align="center">
      <template #default="scope">
        <span class="mark"> {{ scope.row.treatmentMsg }} </span>
      </template>
    </el-table-column>
  </BaseTable>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import useBloodGlucose from './hooks/useBloodGlucose';
import BaseChart from '@/components/BaseChart';
import BaseTable from '@/components/BaseTable';

defineProps({
  targetValue: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
});

const { options, tableData, loading, cellClassNameHandler } = useBloodGlucose();
</script>

<style scoped lang="less">
.chart {
  height: 360px;
}
.mark {
  color: #0a73e4;
}
</style>
