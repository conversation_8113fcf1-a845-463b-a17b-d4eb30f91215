<template>
  <div class="chart mb-5xl">
    <div class="chart-switch">
      <el-radio-group v-model="switchType" size="small">
        <el-radio-button :label="1">周平均</el-radio-button>
        <el-radio-button :label="2">月平均</el-radio-button>
        <el-radio-button :label="3">全部</el-radio-button>
      </el-radio-group>
    </div>
    <BaseChart
      type="line"
      :data-complete="loading"
      :options="bloodPressureOptions"
    />
  </div>
  <BaseTable
    :height="304"
    :data="data"
    :pagination="false"
    :cell-style="cellClassNameHandler"
  >
    <el-table-column prop="testTime" label="测量日期" width="180" />
    <el-table-column prop="heartRate" label="心率" width="120" align="right" />
    <el-table-column label="" align="center">
      <template #default="scope">
        <span class="mark">{{ scope.row.treatmentMsg }} </span>
      </template>
    </el-table-column>
  </BaseTable>
</template>

<script setup lang="ts">
import useBloodPressure from './hooks/useBloodPressure';
import BaseChart from '@/components/BaseChart';
import BaseTable from '@/components/BaseTable';
const props = defineProps({
  targetValue: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
  dataType: {
    type: String,
    default: 'heart',
  },
});
const {
  bloodPressureOptions,
  tableData,
  switchType,
  loading,
  cellClassNameHandler,
} = useBloodPressure(props);
const data = computed(() => tableData.value.filter(item => item.heartRate));
</script>
<style scoped lang="less">
.chart {
  height: 360px;
  &-switch {
    :deep(.el-radio-button__original-radio + .el-radio-button__inner) {
      width: 72px;
      padding-top: 8px;
      padding-bottom: 8px;
      box-sizing: border-box;
    }
    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      border-color: #0a73e4;
      background: #ebf6ff;
      color: #0a73e4;
    }
  }
}
.detail {
  &-info {
    background: #f7f8fa;
    border-radius: 2px;
  }
  .value {
    padding-right: 6px;
    color: var(--color-danger);
  }
}
.mark {
  color: #0a73e4;
}
</style>
