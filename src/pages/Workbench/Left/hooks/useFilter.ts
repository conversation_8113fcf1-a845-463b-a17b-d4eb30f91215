import { ref } from 'vue';
import { filterConfigMap, filterDataMap } from './config';
import { IType } from '@/store/module/useUserList';

const useFilter = (type: IType, role: keyof typeof filterConfigMap) => {
  const filterConfigs = ref({ ...filterConfigMap[role][type] });
  const filterValue = ref({ ...filterDataMap[role][type] });

  const setFilterValues = (data: any) => {
    filterValue.value = { ...filterValue.value, ...data };
  };
  const resetFilterValues = () => {
    filterValue.value = { ...filterDataMap[role][type] };
  };
  return {
    filterConfigs,
    filterValue,
    setFilterValues,
    resetFilterValues,
  };
};
export default useFilter;
