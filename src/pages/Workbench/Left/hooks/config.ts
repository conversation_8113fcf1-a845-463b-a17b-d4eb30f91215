import today from '@/assets/imgs/userList/today.png';
import manage from '@/assets/imgs/userList/manage.png';
import press from '@/assets/imgs/userList/press.png';
import unpress from '@/assets/imgs/userList/unpress.png';
import {
  getDoctorCurrent,
  getDoctorManage,
  getDcotorNoUrged,
  getDoctorUrged,
  getCustomerCurrent,
  getCustomerManage,
  getCustomerUrged,
  getCustomerNoUrged,
  getRehabilitationCurrent,
  getRehabilitationManage,
  getRehabilitationList,
  getAssistantNum,
  getCustomerNum,
  getRehabilitationNum,
  getDoctorDepartmentList,
  getAssistantDepartmentNum,
  getCustomerDepartmentList,
  getCustomerDepartmentNum,
  getRehabilitationDepartmentList,
  getRehabilitationDepartmentNum,
} from '@/api/userList';
import { ICard } from '@/store/module/useUserList';
interface IbaseItem {
  count: number;
  data: ICard[];
}
// 医生工作台配置  0.我的患者  1.部门  2.复查解读
export const doctorFilterConfigs = {
  0: [
    {
      type: 'checkbox-expand',
      options: [
        {
          label: '今日待办',
          key: 'isNeedCurrentTodo',
        },
        {
          label: '重点关注',
          key: 'isNeedMarkPatient',
        },
        {
          label: '未入组',
          key: 'isNeedUnAccess',
        },
        {
          label: '超30天未联系',
          key: 'isNeedUnContact',
        },
        {
          label: '续费管理患者',
          key: 'isNeedRenew',
        },
        {
          label: '需续费',
          key: 'isNeedPendingFee',
        },
      ],
    },
    {
      type: 'checkbox-expand',
      titleName: '患者状态',
      options: [
        {
          label: '心衰易损期',
          key: 'isNeedHeartFailurePeriod',
        },
        {
          label: '有风险预警',
          key: 'isNeedDangerFactor',
        },
        {
          label: '超30天未测血压',
          key: 'isNeedBloodPressure',
        },
      ],
    },
  ],
  1: [
    {
      type: 'checkbox-expand',
      options: [
        {
          label: '今日待办',
          key: 'isNeedCurrentTodo',
        },
        {
          label: '重点关注',
          key: 'isNeedMarkPatient',
        },
        {
          label: '未入组',
          key: 'isNeedUnAccess',
        },
        {
          label: '超30天未联系',
          key: 'isNeedUnContact',
        },
        {
          label: '续费管理患者',
          key: 'isNeedRenew',
        },
        {
          label: '需续费',
          key: 'isNeedPendingFee',
        },
      ],
    },
    {
      type: 'checkbox-expand',
      titleName: '患者状态',
      options: [
        {
          label: '心衰易损期',
          key: 'isNeedHeartFailurePeriod',
        },
        {
          label: '有风险预警',
          key: 'isNeedDangerFactor',
        },
        {
          label: '超30天未测血压',
          key: 'isNeedBloodPressure',
        },
      ],
    },
    {
      type: 'checkbox-expand',
      titleName: '责任医生',
      options: [],
    },
  ],
  2: [
    {
      type: 'checkbox-expand',
      options: [
        {
          label: '未上传',
          key: 'isNeedNotUploaded',
        },
        {
          label: '已上传',
          key: 'isNeedUploaded',
        },
        {
          label: '不足7天将失效',
          key: 'isNeedExpire',
        },
        {
          label: '科研对照',
          key: 'isNeedScientificCompare',
        },
        {
          label: '到期续管',
          key: 'isNeedExpireRenew',
        },
      ],
    },
  ],
};
// 医生工作台搜索条件默认值 0.我的患者  1.部门  2.复查解读
export const doctorFilterDatas = {
  0: {
    isNeedCurrentTodo: false,
    isNeedDangerFactor: false,
    isNeedMarkPatient: false,
    isNeedUnAccess: false,
    isNeedUnContact: false,
    isNeedRenew: false,
    isNeedPendingFee: false,
    isNeedHeartFailurePeriod: false,
    isNeedBloodPressure: false,
  },
  1: {
    isNeedNotUploaded: false,
    isNeedUploaded: false,
    isNeedExpire: false,
    isNeedScientificCompare: false,
    isNeedExpireRenew: false,
  },
  2: {
    isNeedAboutExpire: false,
  },
};
// 健康管理师配置  0.随访  1：我的  2： 部门
export const healthyManagerFilterConfigs = {
  0: [
    {
      type: 'checkbox',
      options: [
        {
          label: '复查',
          key: 'isNeedAboutExpire',
        },
        // {
        //   label: '七日内过期',
        //   key: 'isNeedAboutExpire',
        // },
        // {
        //   label: '科研对照',
        //   key: 'isNeedScientificCompare',
        // },
        // {
        //   label: '到期续管',
        //   key: 'isNeedExpireRenew',
        // },
        {
          label: '症状随访',
          key: 'isNeedSymptomFollowUp',
        },
        {
          label: '生活方式随访',
          key: 'isNeedLifestyleFollowUp',
        },
      ],
    },
  ],
  1: [
    {
      type: 'checkbox-expand',
      options: [
        {
          label: '今日待办',
          key: 'isNeedCurrentTodo',
        },
        {
          label: '重点关注',
          key: 'isNeedMarkPatient',
        },
        {
          label: '需续费',
          key: 'isNeedPendingFee',
        },
        {
          label: '超30天未联系',
          key: 'isNeedUnContact',
        },
        {
          label: '超30天未测血压',
          key: 'isNeedBloodPressure',
        },
      ],
    },
    {
      type: 'checkbox-expand',
      titleName: '患者情况筛选',
      options: [
        {
          label: '心衰易损期',
          key: 'isNeedHeartFailurePeriod',
        },
      ],
    },
  ],
  2: [
    {
      type: 'checkbox-expand',
      options: [
        {
          label: '今日待办',
          key: 'isNeedCurrentTodo',
        },
        {
          label: '重点关注',
          key: 'isNeedMarkPatient',
        },
        {
          label: '需续费',
          key: 'isNeedPendingFee',
        },
        {
          label: '超30天未联系',
          key: 'isNeedUnContact',
        },
        {
          label: '超30天未测血压',
          key: 'isNeedBloodPressure',
        },
      ],
    },
    {
      type: 'checkbox-expand',
      titleName: '患者情况筛选',
      options: [
        {
          label: '心衰易损期',
          key: 'isNeedHeartFailurePeriod',
        },
      ],
    },
    {
      type: 'checkbox-expand',
      titleName: '责任医生',
      options: [
        {
          label: '心衰易损期',
          key: 'isNeedHeartFailurePeriod1',
        },
        {
          label: '有风险预警',
          key: 'isNeedDangerFactor1',
        },
        {
          label: '超30天未测血压',
          key: 'isNeedBloodPressure1',
        },
      ],
    },
  ],
};
// 健康管理师搜索条件默认值  0.部门  1：近期随访  2： 我的患者
export const healthyManagerFilterDatas = {
  0: {
    isNeedAboutExpire: false,
    isNeedScientificCompare: false,
    isNeedExpireRenew: false,
    isNeedHeartFailurePeriod: false,
    isNeedSymptomFollowUp: false,
    isNeedLifestyleFollowUp: false,
  },
  1: {
    isNeedCurrentTodo: false,
    isNeedMarkPatient: false,
    isNeedPendingFee: false,
    isOverdue: false,
  },
  2: {
    isNeedNotUploaded: false,
    isNeedUploaded: false,
    isNeedExpire: false,
    isNeedScientificCompare: false,
    isNeedExpireRenew: false,
  },
};
// 运动康复师配置  0.患者列表  1.管理中  2.部门
export const sportTherapistFilterConfigs = {
  0: [
    {
      type: 'checkbox',
      options: [
        {
          label: '今日待办',
          key: 'isNeedCurrentTodo',
        },
        {
          label: '重点关注',
          key: 'isNeedMarkPatient',
        },
        {
          label: '需续费',
          key: 'isNeedPendingFee',
        },
      ],
    },
    {
      type: 'group',
      children: [
        {
          label: '管理状态',
          type: 'select',
          key: 'manageStatus',
          options: [
            {
              label: '全部',
              value: 0,
            },
            {
              label: '待管理',
              value: 1,
            },
            {
              label: '管理中',
              value: 2,
            },
            {
              label: '结束管理',
              value: 3,
            },
            {
              label: '管理中断',
              value: 4,
            },
          ],
        },
        {
          label: '入组日期',
          type: 'daterange',
          key: ['enrollmentStartDate', 'enrollmentEndDate'],
        },
      ],
    },
  ],
  1: [
    {
      type: 'checkbox',
      options: [
        {
          label: '今日待办',
          key: 'isNeedCurrentTodo',
        },
        {
          label: '重点关注',
          key: 'isNeedMarkPatient',
        },
        {
          label: '需续费',
          key: 'isNeedPendingFee',
        },
      ],
    },
    {
      type: 'group',
      children: [
        {
          label: '管理状态',
          type: 'select',
          key: 'manageStatus',
          options: [
            {
              label: '全部',
              value: 0,
            },
            {
              label: '待管理',
              value: 1,
            },
            {
              label: '管理中',
              value: 2,
            },
            {
              label: '结束管理',
              value: 3,
            },
            {
              label: '管理中断',
              value: 4,
            },
          ],
        },
        {
          label: '入组日期',
          type: 'daterange',
          key: ['enrollmentStartDate', 'enrollmentEndDate'],
        },
      ],
    },
  ],
  2: [
    {
      type: 'group',
      children: [
        {
          label: '风险等级',
          type: 'select',
          key: 'riskLevel',
          options: [
            {
              label: '全部',
              value: -1,
            },
            {
              label: '未知',
              value: 0,
            },
            {
              label: '高危',
              value: 3,
            },
            {
              label: '中危',
              value: 2,
            },
            {
              label: '低危',
              value: 1,
            },
          ],
        },
        {
          label: '管理评估日期',
          type: 'daterange',
          key: ['startTime', 'endTime'],
        },
      ],
    },
  ],
};
export const sportTherapistFilterDatas = {
  0: {
    isNeedCurrentTodo: false,
    isNeedMarkPatient: false,
    isNeedPendingFee: false,
    isNeedRehabManageStatus: false,
    isNeedEnrollmentDate: false,
    manageStatus: 0,
    enrollmentStartDate: null,
    enrollmentEndDate: null,
  },
  1: {
    isNeedRehabRiskLevel: false,
    isNeedAssessmentDate: false,
    riskLevel: -1,
    startTime: null,
    endTime: null,
  },
};
export const baseConfig = {
  visible: true,
  titleColor: '#101B25',
  height: 300,
};
export const baseListData: IbaseItem = {
  count: 0,
  data: [],
};
// 医生工作台列表配置  0.我的患者  1.部门  2.复查
export const doctorListConfigs = {
  0: [
    {
      title: '今日',
      icon: today,
      ...baseConfig,
    },
    {
      title: '管理中',
      icon: manage,
      ...baseConfig,
    },
  ],
  1: [
    {
      title: '',
      icon: '',
      ...baseConfig,
    },
  ],
  2: [
    {
      title: '未催办',
      icon: unpress,
      ...baseConfig,
    },
    {
      title: '已催办',
      icon: press,
      ...baseConfig,
    },
  ],
  // 2: [
  //   {
  //     title: '未催办',
  //     icon: unpress,
  //     ...baseConfig,
  //   },
  //   {
  //     title: '已催办',
  //     icon: press,
  //     ...baseConfig,
  //   },
  // ],
};
export const healthyManagerListConfigs = {
  0: [
    {
      title: '未催办',
      icon: unpress,
      ...baseConfig,
    },
    {
      title: '已催办',
      icon: press,
      ...baseConfig,
    },
  ],
  1: [
    {
      title: '今日',
      icon: today,
      ...baseConfig,
    },
    {
      title: '管理中',
      icon: manage,
      ...baseConfig,
    },
  ],
  2: [
    {
      title: '',
      icon: '',
      ...baseConfig,
    },
  ],
};
export const sportTherapistListConfigs = {
  0: [
    {
      title: '今日',
      icon: today,
      ...baseConfig,
    },
    {
      title: '团队',
      icon: manage,
      ...baseConfig,
    },
  ],
  1: [
    {
      title: '',
      icon: '',
      ...baseConfig,
    },
  ],
  2: [
    {
      title: '',
      icon: '',
      ...baseConfig,
    },
  ],
};
// 医生工作台列表数据
export const doctorListDatas = {
  0: [{ ...baseListData }, { ...baseListData }],
  1: [{ ...baseListData }],
  2: [{ ...baseListData }, { ...baseListData }],
};
export const healthyManagerListDatas = {
  0: [{ ...baseListData }, { ...baseListData }],
  1: [{ ...baseListData }, { ...baseListData }],
  2: [{ ...baseListData }],
};
export const sportTherapistListDatas = {
  0: [{ ...baseListData }, { ...baseListData }],
  1: [{ ...baseListData }],
  2: [{ ...baseListData }],
};
const DOCTOR = 1;
const HEALTHY = 2;
const SPORT = 3;

export const filterConfigMap = {
  [DOCTOR]: doctorFilterConfigs,
  [HEALTHY]: healthyManagerFilterConfigs,
  [SPORT]: sportTherapistFilterConfigs,
};
export const filterDataMap = {
  [DOCTOR]: doctorFilterDatas,
  [HEALTHY]: healthyManagerFilterDatas,
  [SPORT]: sportTherapistFilterDatas,
};
export const listConfigMap = {
  [DOCTOR]: doctorListConfigs,
  [HEALTHY]: healthyManagerListConfigs,
  [SPORT]: sportTherapistListConfigs,
};
export const listDataMap = {
  [DOCTOR]: doctorListDatas,
  [HEALTHY]: healthyManagerListDatas,
  [SPORT]: sportTherapistListDatas,
};

// 不同角色不同tab下请求 api
export const listRequestMap = {
  [DOCTOR]: {
    0: {
      first: getDoctorCurrent,
      second: getDoctorManage,
    },
    1: {
      first: getDoctorDepartmentList,
    },
    2: {
      first: getDcotorNoUrged,
      second: getDoctorUrged,
    },
  },
  [HEALTHY]: {
    0: {
      first: getCustomerNoUrged,
      second: getCustomerUrged,
    },
    1: {
      first: getCustomerCurrent,
      second: getCustomerManage,
    },
    2: {
      first: getCustomerDepartmentList,
    },
  },
  [SPORT]: {
    0: {
      first: getRehabilitationCurrent,
      second: getRehabilitationManage,
    },
    1: {
      first: getRehabilitationDepartmentList,
    },
    2: {
      first: getRehabilitationList,
    },
  },
};

const defaultCountParams = {
  pageSize: 1,
  pageNumber: 1,
};
// 列表总数统计配置
export const listCountMap = {
  [DOCTOR]: {
    0: {
      url: getAssistantNum,
      params: {
        asstParam: defaultCountParams,
      },
      paramsKey: 'asstParam',
      resultKey: 'patientListNum',
    },
    1: {
      url: getAssistantDepartmentNum,
      params: {
        asstDeptParam: defaultCountParams,
      },
      paramsKey: 'asstDeptParam',
      resultKey: 'deptListNum',
    },
    2: {
      url: getAssistantNum,
      params: {
        reviewParam: defaultCountParams,
      },
      paramsKey: 'reviewParam',
      resultKey: 'reviewListNum',
    },
  },
  [HEALTHY]: {
    0: {
      url: getCustomerNum,
      params: {
        fuParam: defaultCountParams,
      },
      paramsKey: 'fuParam',
      resultKey: 'followUpListNum',
    },
    1: {
      url: getCustomerNum,
      params: {
        cusParam: defaultCountParams,
      },
      paramsKey: 'cusParam',
      resultKey: 'patientListNum',
    },
    2: {
      url: getCustomerDepartmentNum,
      params: {
        cusDeptParam: defaultCountParams,
      },
      paramsKey: 'cusDeptParam',
      resultKey: 'deptListNum',
    },
  },
  [SPORT]: {
    0: {
      url: getRehabilitationNum,
      params: {
        rehabParam: defaultCountParams,
      },
      paramsKey: 'rehabParam',
      resultKey: 'patientListNum',
    },
    1: {
      url: getRehabilitationDepartmentNum,
      params: {
        rehabDeptParam: defaultCountParams,
      },
      paramsKey: 'rehabDeptParam',
      resultKey: 'deptListNum',
    },
    2: {
      url: getRehabilitationNum,
      params: {
        maParam: defaultCountParams,
      },
      paramsKey: 'maParam',
      resultKey: 'manageAssessmentListNum',
    },
  },
};


