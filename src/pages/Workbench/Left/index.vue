<template>
  <div class="left flex flex-col w-[392px] relative bg-white">
    <Search />
    <div class="p-16 pb-0 flex-1">
      <el-tabs
        v-model="userListStore.activeTab"
        type="border-card"
        class="h-full"
        :class="{ ['length_' + curTabsConfig.length]: true }"
      >
        <el-tab-pane
          v-for="tab in curTabsConfig"
          :key="tab.code"
          :name="tab.code"
          lazy
        >
          <template #label>
            <span class="title">{{ getTitle(tab.code) }}</span>
            <span v-if="userListStore.tabCount[tab.code]" class="badge">
              ({{ userListStore.tabCount[tab.code] }})
            </span>
          </template>
          <div
            v-if="tab.title === '部门'"
            class="hrt-whitespace-nowrap hrt-w-[300px] hrt-bg-blue-100 mb-8"
          >
            <HrtOverflowTooltip class="text" :content="getGroupText" />
          </div>
          <div class="h-full">
            <Content :type="tab.code" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="globalStore.isDoubting" class="doubt-mask"></div>
  </div>
</template>

<script setup lang="ts">
import Search from './components/Search/index.vue';
import Content from './components/Content/index.vue';
import store from '@/store';
import { getPatientInfoBase } from '@/api/overview';
import { listCountMap } from './hooks/config';
import useMeta from '@/store/module/useMeta';
defineOptions({
  name: 'WorkBenchLeft',
});
const globalStore = store.useGlobal();
const userListStore = store.useUserList();
const tabsConfig = ref({
  1: [
    { title: '我的', code: 0 },
    { title: '部门', code: 1 },
    { title: '复查', code: 2 },
  ],
  2: [
    { title: '随访', code: 0 },
    { title: '我的', code: 1 },
    { title: '部门', code: 2 },
  ],
  3: [
    { title: '我的', code: 0 },
    { title: '部门', code: 1 },
    { title: '康复中', code: 2 },
  ],
});
const getTitle = (i: number) => {
  const role = store.useGlobal().currentRole;
  const obj = tabsConfig.value[role as keyof typeof tabsConfig.value].find(
    item => item.code === i
  );
  return obj?.title;
};

const HEAD_REQUIRED_TABS = ['部门'] as const;
const curTabsConfig = computed(() => {
  const role = store.useGlobal().currentRole;
  const isHead = JSON.parse(
    localStorage.getItem('isHead') || 'false'
  ) as boolean;
  const currentTabs = tabsConfig.value[role as keyof typeof tabsConfig.value];
  const tabList = currentTabs.filter(
    item =>
      !HEAD_REQUIRED_TABS.includes(
        item.title as (typeof HEAD_REQUIRED_TABS)[number]
      ) || isHead
  );
  return tabList;
});

const getNum = async (i: number) => {
  const role = store.useGlobal().currentRole;
  const { url: req, params, paramsKey, resultKey } = listCountMap[role]?.[i];
  const advacnedParams = userListStore.getAdvancedFormatData();
  const newParams = {
    [paramsKey]: { ...advacnedParams, ...params[paramsKey] },
  };
  if (req) {
    const res: any = await req(newParams);
    if (res) {
      userListStore.tabCount[i] = res[resultKey] ?? 0;
    }
  }
};
const getNumHandler = () => {
  const restConfig = curTabsConfig.value.slice(1);
  for (let i = 0; i < restConfig.length; i++) {
    getNum(i + 1);
  }
};
onMounted(() => {
  getNumHandler();
});

const getPatientBaseInfo = async (id: number) => {
  const res: any = await getPatientInfoBase({ patientId: id });
  globalStore.setUserInfo(res);
};
watch(
  () => userListStore.advancedData,
  (newVal, oldVal) => {
    if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
      if (!userListStore.tabRendered[1]) {
        getNumHandler();
      }
    }
  }
);
watch(
  () => globalStore.userId,
  val => {
    if (val) {
      getPatientBaseInfo(val);
      useMeta().getFollwUpOptions(val);
    } else {
      globalStore.setUserInfo({ patientName: '' });
    }
  },
  { immediate: true }
);

const getGroupText = computed(() => {
  const deptNames = JSON.parse(localStorage.getItem('deptNames') || '[]');
  return deptNames.join('，');
});
</script>
<style scoped lang="less">
.left {
  :deep(.el-tabs__item) {
    width: 180px;
    .el-badge__content {
      top: 0;
      background: #dc0101;
    }
    &.is-active {
      border-right-color: transparent;
    }
    &:first-child {
      border-top-left-radius: 6px;
      &.is-active {
        border-right-color: inherit;
      }
    }
    &:last-child {
      border-top-right-radius: 6px;
    }
  }
  :deep(.el-tabs__header) {
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
    border: 1px solid #ccc;
    .title {
      font-size: 14px;
      color: #3a4762;
    }
    .badge {
      font-size: 16px;
      font-weight: bold;
      color: #2e6be6;
    }
    .is-active {
      .title {
        font-weight: bold;
        color: #3a4762;
      }
    }
  }
  :deep(.el-tabs--border-card) {
    border: 0;
  }
  :deep(.el-tabs__content) {
    height: calc(100% - 40px);
    padding: 0;
    padding-top: 8px;
    overflow: visible;
    .el-tab-pane {
      height: 100%;
    }
  }
  .length_3 {
    :deep(.el-tabs__item) {
      width: 120px;
    }
  }
}
.doubt-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 98;
  left: 0;
  top: 0;
  background: #f6f8fb;
  opacity: 0.5;
}
</style>
