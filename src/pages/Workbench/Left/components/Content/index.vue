<template>
  <div v-loading="loading" class="flex h-full flex-col">
    <div ref="filterRef" class="relative">
      <Filter
        :filter-configs="filterConfigs"
        :filter-value="filterValue"
        @change="filterChange"
      />
      <div class="shadow"></div>
    </div>
    <div ref="listRef" class="p-16 pt-0 list-wrapper flex flex-col flex-1">
      <List
        v-for="(config, index) in listConfigs"
        :key="config.title"
        :config="config"
        :data="listData[index]"
        :type="type"
        :list-index="index"
        :to-tab-handler="toTabHandler"
        @toggle="(val: boolean) => toggleHanlder(index, val)"
        @load="() => loadHandler(index as any)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useElementSize } from '@vueuse/core';
import Filter from '../Filter/index.vue';
import List from '../List/index.vue';
import useList from '../../hooks/useList';
import store from '@/store';
interface IProps {
  type: number;
}
const props = defineProps<IProps>();
const filterRef = shallowRef();
const listRef = shallowRef<HTMLElement>();
const maxHeight = ref(700);
const { height } = useElementSize(filterRef);
const {
  filterConfigs,
  filterValue,
  setFilter,
  listConfigs,
  listData,
  getListMore,
  loading,
  toTabHandler,
} = useList(props.type);
const userListStore = store.useUserList();
defineOptions({
  name: 'ListContent',
});
onMounted(() => {
  caculateHeight();
});
watch(
  [() => userListStore.advancedData, height, () => listData.value],
  () => {
    caculateHeight();
  },
  {
    deep: true,
  }
);
const loadHandler = (i: 0 | 1) => {
  getListMore(i);
};
const filterChange = (data: any) => {
  setFilter(data);
};
const getItemHeight = () => {
  const visibleConfigs = listConfigs.value.filter(v => v.visible);
  const hiddenConfigs = listConfigs.value.filter(v => !v.visible);
  const len = visibleConfigs.length;
  const itemHeight = (maxHeight.value - hiddenConfigs.length * 40) / len;
  return itemHeight;
};
const caculateHeight = () => {
  if (listRef.value) {
    const rect = listRef.value.getBoundingClientRect();
    const clientHeight = document.body.clientHeight;
    maxHeight.value = clientHeight - rect.top - 32;
    const itemHeight = getItemHeight();
    listConfigs.value.forEach(item => {
      item.height = itemHeight;
    });
    reCaculateHeight(itemHeight);
  }
};
const toggleHanlder = (index: number, val: boolean) => {
  const curConfig = listConfigs.value[index];
  curConfig.visible = val;
  const visibleConfigs = listConfigs.value.filter(v => v.visible);
  const itemHeight = getItemHeight();
  visibleConfigs.map(v => (v.height = itemHeight));
  reCaculateHeight(itemHeight);
};
const reCaculateHeight = (itemHeight: number) => {
  if (listConfigs.value.filter(v => v.visible).length > 1) {
    const _data = listData.value;
    const emptyHeight = 125;
    if (!_data[0].count && _data[1].count) {
      listConfigs.value[0].height = emptyHeight;
      listConfigs.value[1].height = itemHeight * 2 - emptyHeight;
    } else if (_data[0].count && !_data[1].count) {
      listConfigs.value[0].height = itemHeight * 2 - emptyHeight;
      listConfigs.value[1].height = emptyHeight;
    } else if (_data[0].count < 3) {
      const minHeight = _data[0].count * 108 + 50;
      listConfigs.value[0].height = minHeight;
      listConfigs.value[1].height = itemHeight * 2 - minHeight;
    } else if (_data[1].count < 3) {
      const minHeight = _data[1].count * 108 + 50;
      listConfigs.value[0].height = itemHeight * 2 - minHeight;
      listConfigs.value[1].height = minHeight;
    }
  }
};
</script>

<style scoped lang="less">
.list-wrapper {
  width: 392px;
  margin-left: -16px;
  background: #f6f8fb;
}
.shadow {
  position: absolute;
  bottom: 0;
  left: -16px;
  width: calc(100% + 32px);
  height: 1px;
  box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
}
</style>
