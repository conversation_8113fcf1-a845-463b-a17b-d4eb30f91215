<template>
  <div ref="resultRef" class="result p-16 pt-0 pb-0">
    <el-scrollbar :height="height">
      <ResultCard
        v-if="isShow('info')"
        type="info"
        title="患者"
        :data="userList.searchResult.info"
        @load="() => load('info')"
      />
      <ResultCard
        v-if="isShow('chat')"
        type="chat"
        title="聊天记录"
        :data="userList.searchResult.chat"
        @load="() => load('chat')"
      />
      <ResultCard
        v-if="isShow('tag')"
        type="tag"
        title="患者标签"
        :data="userList.searchResult.tag"
        @load="() => load('tag')"
      />
      <ResultCard
        v-if="isShow('phone')"
        type="phone"
        title="电话号码"
        :data="userList.searchResult.phone"
        @load="() => load('phone')"
      />
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import ResultCard from './ResultCard.vue';
import store from '@/store';

const userList = store.useUserList();
const emits = defineEmits(['load']);
type IResultType = keyof typeof userList.searchResult;
const height = ref(0);
const resultRef = shallowRef<HTMLElement>();
defineOptions({
  name: 'SearchResult',
});
const load = (type: IResultType) => {
  emits('load', type);
};
const isShow = (type: IResultType) => {
  return userList.searchResult[type].totals > 0;
};
onMounted(() => {
  const clientHeight = document.body.clientHeight;
  height.value = clientHeight - 110;
});
</script>

<style scoped lang="less">
.result {
  position: absolute;
  z-index: 999;
  background: #fff;
  width: 360px;
  top: 108px;
  left: 16px;
  border-radius: 4px;
  height: calc(100vh - 110px);
  overflow: auto;
  box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
}
</style>
