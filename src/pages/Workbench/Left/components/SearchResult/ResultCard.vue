<template>
  <div>
    <div class="text-[14px] pb-8 pt-16 h-44 font-bold text-[#101B25]">
      {{ title }}
    </div>
    <div class="bg-[#F7F8FA] rounded-[6px]">
      <SearchCard
        v-for="(item, index) in currentData"
        :key="index"
        :data="item"
        :type="type"
        class="card-item"
      >
        <div class="flex-1 items-center ml-16 overflow-hidden">
          <BasicInfo
            :type="type"
            :data="item"
            :highlight-key="getHilightkey(['info'])"
          >
            <span
              class="text-[#2E6BE6] cursor-pointer"
              @click="() => clickHandler(item)"
            >
              {{ getText(type, item) }} &gt;
            </span>
          </BasicInfo>
          <ChatRecord
            v-if="type === 'chat' || type === 'phone'"
            :text="getRenderText(type, item)"
            :show-times="false"
          />
          <Tag
            v-if="type === 'tag'"
            :data="item.patientTags"
            :highlight-key="getHilightkey(['tag'])"
          />
        </div>
      </SearchCard>
      <div v-if="showResidues" class="card-item justify-center">
        <span
          class="flex items-center cursor-pointer text-[#2E6BE6]"
          @click="expandAll"
        >
          展开全部（{{ data.totals - (currentData.length ?? 0) }}）
          <el-icon style="transform: rotate(90deg)">
            <i-ep-d-arrow-right />
          </el-icon>
        </span>
      </div>
      <el-dialog
        v-model="dialogVisible"
        class="search-result-dialog"
        title="聊天记录"
        width="600"
        append-to-body
      >
        <DetailDialog
          v-if="dialogVisible"
          :highlight-key="userListStore.keywords"
          :data="dialogData"
          @click-item="clickHandler"
          @load-detail="loadmoreDetail"
        />
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchCard from '../Card/SearchCard.vue';
import BasicInfo from '../Card/BasicInfo.vue';
import ChatRecord from '../Card/ChatRecord.vue';
import Tag from '../Card/Tag.vue';
import DetailDialog from './DetailDialog.vue';
import { IsearchItem, ICard } from '@/store/module/useUserList';
import { getChatText } from '../../utils';
import store from '@/store';
import { getHilightStr } from '../../utils';
import { getPatientChatList, sortEventSubmit } from '@/api/userList';
interface IProps {
  title: string;
  type: 'info' | 'chat' | 'tag' | 'phone';
  data: IsearchItem;
}
defineOptions({
  name: 'ResultCard',
});
const globalStore = store.useGlobal();
const userListStore = store.useUserList();
const imStore = store.useIM();
const emits = defineEmits(['load']);
const props = defineProps<IProps>();
const showAll = ref(false);
const dialogVisible = ref(false);
const dialogData = ref<ICard[]>([]);
const chatDetailPageNumber = ref(1);
const chatDetailtemp = ref();
const chatDetailTotals = ref(0);
const expandAll = () => {
  if (showAll.value) {
    emits('load');
  } else {
    showAll.value = true;
  }
};

const getHilightkey = (
  typeList: IProps['type'][],
  item: ICard = {} as ICard
) => {
  if (typeList.includes(props.type)) {
    const {
      totalPatientChats = 0,
      patientChats = [],
      patientPhones = [],
      patientTags = [],
    } = item;
    if (
      totalPatientChats > 1 ||
      patientPhones?.length > 1 ||
      patientTags?.length > 1
    ) {
      return '';
    }
    if (
      props.type === 'chat' &&
      patientChats?.length === 1 &&
      patientChats[0].chatType !== 'TEXT'
    ) {
      return '';
    }
    return userListStore.keywords;
  }
  return '';
};
const showResidues = computed(() => {
  const { data } = props;
  if (!showAll.value) return data.totals > 2;
  return data.data.length && data.data.length < data.totals;
});
const currentData = computed(() => {
  const { data } = props;
  if (!showAll.value) return data.data.slice(0, 2);
  return data.data;
});
const getRenderText = (type: IProps['type'], item: ICard) => {
  if (type === 'phone') {
    const phones = item.patientPhones ?? [];
    if (phones.length > 1) {
      return `${phones.length}个号码匹配`;
    }
    const phone = phones[0] ?? {};
    return `${phone.name}（${phone.relation}）： ${getHilightStr(phone.phone + '', userListStore.keywords)}`;
  } else if (type === 'chat') {
    const chats = item.patientChats ?? [];
    if ((item.totalPatientChats ?? 0) > 1) {
      return `${item.totalPatientChats}条聊天记录匹配`;
    }
    const chat = chats[0] ?? {};
    return getChatText(chat, false);
  }
};
const getText = (type: IProps['type'], item: ICard) => {
  if (['phone', 'tag', 'info'].includes(type)) return '进入聊天';
  if (type === 'chat' && (item.totalPatientChats ?? 0) > 1) return '详情';
  return '查看上下文';
};
const resetPatientHandler = (val: any) => {
  if (!val.patientId) {
    ElMessage.warning('PatientId 不存在！');
    return;
  }
  globalStore.setManageStatus(val.patientId);
  imStore.hasMore = true;
  if (globalStore.userId === val.patientId && imStore.findMsgData) {
    imStore.curTeamId = val.teamId;
    imStore.findMsg(imStore.findMsgData as any);
    imStore.findMsgData = null;
    imStore.findTeamId = '';
  }
  userListStore.keywords = '';
  userListStore.keepUser = true;
  globalStore.setUserId(val.patientId);
  sortEventSubmit({ patientId: val.patientId, eventType: 3 });
};
const loadmoreDetail = async () => {
  if (chatDetailTotals.value <= dialogData.value.length) return;
  const result = await getChatDetailHandler(chatDetailtemp.value);
  dialogData.value.push(...(result as any));
};
const getChatDetailHandler = async (val: ICard) => {
  const resDetail = await getPatientChatList({
    teamNumber: val.teamId,
    patientChat: userListStore.keywords,
    pageSize: 8,
    pageNumber: chatDetailPageNumber.value,
  });
  const result = [...(resDetail.patientChats ?? [])].map(v => ({
    ...v,
    ...val,
    patientChats: [{ ...v }],
    totalPatientChats: 0,
  }));
  chatDetailTotals.value = resDetail.totalPatientChats ?? 0;
  chatDetailPageNumber.value += 1;
  return result;
};
const clickHandler = async (val: ICard) => {
  if (props.type === 'chat') {
    if ((val.totalPatientChats ?? 0) > 1) {
      chatDetailPageNumber.value = 1;
      chatDetailTotals.value = 0;
      chatDetailtemp.value = val;
      const result = await getChatDetailHandler(val);
      dialogData.value = result as any;
      dialogVisible.value = true;
    } else {
      const chatItem = val.patientChats?.[0];
      if (!chatItem?.chatId) {
        ElMessage.warning('消息 ID 不存在！');
        return;
      }
      if (!val.teamId) {
        ElMessage.warning('群聊 ID 不存在！');
        return;
      }
      imStore.findMsgData = {
        idServer: chatItem.chatId,
        time: chatItem.chatTime,
      };
      imStore.findTeamId = val.teamId;
      resetPatientHandler(val);
    }
  } else {
    resetPatientHandler(val);
  }
};
</script>

<style lang="less">
.search-result-dialog {
  border-radius: 6px;
  .el-dialog__header {
    font-weight: bold;
    padding-top: 10px;
    font-size: 16px;
    border-bottom: 1px solid #e9e8eb;
  }
  .el-dialog__title {
    font-size: 16px;
  }
  .el-dialog__headerbtn {
    top: 0;
  }
  .el-dialog__body {
    padding: 0 16px;
  }
}
</style>
<style scoped lang="less">
.card-item {
  font-size: 14px;
  padding: 12px;
  background: unset;
  height: 62px;
  margin-bottom: 0;
  box-shadow: unset;
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding-right: 2px;
  &:hover {
    background: #ecf4fc;
  }
}
</style>
