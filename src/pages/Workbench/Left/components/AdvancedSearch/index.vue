<template>
  <div>
    <div class="flex p-16 h-50 items-center border">
      <span class="flex-1 text-16 text-[#101B25] font-bold">高级搜索</span>
      <i-ep-close class="cursor-pointer" @click="cancel" />
    </div>
    <div class="p-16">
      <div class="item">
        <span>科研项目</span>
        <HrtSelect
          v-model="project"
          size="large"
          filterable
          clearable
          :teleported="false"
          placeholder="请选择"
        >
          <HrtOption
            v-for="item in projectList"
            :key="item.projectId"
            :label="item.projectName"
            :value="item.projectId"
          />
        </HrtSelect>
      </div>
      <div class="item">
        <span>地区</span>
        <HrtSelect
          v-model="regionId"
          size="large"
          filterable
          clearable
          :teleported="false"
          placeholder="请选择"
          @change="(val: number) => changeHandler('regionId', val)"
        >
          <HrtOption
            v-for="item in regionList"
            :key="item.id"
            :label="item.regionName"
            :value="item.id"
          />
        </HrtSelect>
      </div>
      <div class="item">
        <span>医院</span>
        <HrtSelect
          v-model="hospitalId"
          size="large"
          filterable
          clearable
          :teleported="false"
          placeholder="请选择"
          @change="(val: number) => changeHandler('hospitalId', val)"
        >
          <HrtOption
            v-for="item in getCurrentOptions(
              hospitalList,
              'hospital'
            ) as IApiSearchRegionHospitalGroup['hospitalList']"
            :key="item.id"
            :label="item.hospitalName"
            :value="item.id"
          />
        </HrtSelect>
      </div>
      <div class="item">
        <span>工作室</span>
        <HrtSelect
          v-model="groupId"
          size="large"
          filterable
          clearable
          :teleported="false"
          placeholder="请选择"
          @change="(val: number) => changeHandler('groupId', val)"
        >
          <HrtOption
            v-for="item in getCurrentOptions(
              groupList,
              'group'
            ) as IApiSearchRegionHospitalGroup['groupList']"
            :key="item.id"
            :label="item.groupName"
            :value="item.id"
          />
        </HrtSelect>
      </div>
      <div class="item">
        <span>主要服务病种</span>
        <HrtSelect
          v-model="diseaseTypes"
          size="large"
          multiple
          :teleported="false"
          placeholder="请选择"
        >
          <HrtOption
            v-for="item in diseaseTypeOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </HrtSelect>
      </div>
      <div class="item">
        <span>转化日期</span>
        <HrtDatePicker
          v-model="transformDate"
          type="daterange"
          :teleported="false"
          start-placeholder="请选择"
          value-format="x"
          style="width: 364px"
          :range-separator="transformDate?.[0] ? '-' : ' '"
        />
      </div>
      <div class="item">
        <span>患者类型</span>
        <HrtSelect
          v-model="patientTypes"
          size="large"
          multiple
          :teleported="false"
          placeholder="请选择"
        >
          <HrtOption
            v-for="item in curPatientTypeOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </HrtSelect>
        <!-- <el-checkbox-group v-model="patientTypes" class="patientTyhpes">
          <el-checkbox
            v-for="item in patientTypeOptions"
            :key="item.value"
            :value="item.value"
            >{{ item.name }}</el-checkbox
          >
        </el-checkbox-group> -->
      </div>
    </div>
    <div class="flex pl-156 mb-24 advance-search">
      <HrtButton class="sure-btn" type="primary" @click="confirm">
        确定
      </HrtButton>
      <HrtButton class="cancel-btn" @click="cancel">取消</HrtButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import store from '@/store';
import dayjs from 'dayjs';
import { keyBy } from 'lodash-es';
import { searchRegionHospitalGroup, searchOrProject } from '@/api/userList';
import {
  IApiSearchRegionHospitalGroup,
  IApiOrprojectQuerySimple,
} from '@/interface/type';
const userList = store.useUserList();
const globalStore = store.useGlobal();
const regionList = ref<IApiSearchRegionHospitalGroup['regionList']>([]);
const hospitalList = ref<IApiSearchRegionHospitalGroup['hospitalList']>([]);
const groupList = ref<IApiSearchRegionHospitalGroup['groupList']>([]);
const projectList = ref<IApiOrprojectQuerySimple>([]);
const regionListMap = ref<Record<number, any>>({});
const hospitalListMap = ref<Record<number, any>>({});
const groupListMap = ref<Record<number, any>>({});
const projectListMap = ref<Record<number, any>>({});
defineOptions({
  name: 'AdvancedSearch',
});

type DiseaseType =
  | 'CORONARY_HEART_DISEASE'
  | 'HEART_FAILURE'
  | 'CARDIOVASCULAR';
const diseaseTypeOptions = [
  { value: 'HEART_FAILURE', name: '心力衰竭' },
  { value: 'CORONARY_HEART_DISEASE', name: '冠心病' },
  { value: 'CARDIOVASCULAR', name: '其他心血管病' },
  // { value: 'NONE', name: '无' },
];
const patientTypeOptions = [
  { value: 1, name: '会员在管', role: [1, 2, 3] },
  { value: 2, name: '数字化管理干预组', role: [1, 2, 3] },
  { value: 3, name: '数字化管理对照组', role: [2] },
  { value: 0, name: '非会员', role: [2] },
  { value: 0, name: '到期30天内', role: [1] },
];
const regionId = ref();
const hospitalId = ref();
const groupId = ref();
const project = ref();
const patientTypes = ref<(0 | 1 | 2 | 3)[]>([]);
const transformDate = ref();
const diseaseTypes = ref<DiseaseType[]>([]);

const curPatientTypeOptions = computed(() => {
  const role = globalStore.currentRole;
  return patientTypeOptions.filter(v => v.role.includes(role));
});
watch(
  () => userList.advancedData,
  val => {
    project.value = val.orProjectId;
    regionId.value = val.regionId;
    hospitalId.value = val.hospitalId;
    groupId.value = val.groupId;
    patientTypes.value = val.patientTypes ?? [];
    diseaseTypes.value = val.diseaseTypes ?? [];
    transformDate.value = val.transformDate;
  }
);
const emits = defineEmits(['close', 'change']);
const close = () => {
  emits('close');
};

const getCurrentOptions = (
  data:
    | IApiSearchRegionHospitalGroup['hospitalList']
    | IApiSearchRegionHospitalGroup['groupList'] = [],
  type: 'hospital' | 'group'
) => {
  if (type === 'hospital') {
    return data.filter(v => !regionId.value || v.pid === regionId.value);
  }
  if (type === 'group') {
    return data.filter(v => !hospitalId.value || v.pid === hospitalId.value);
  }
};
const cancel = () => {
  project.value = userList.advancedData.orProjectId;
  regionId.value = userList.advancedData.regionId;
  hospitalId.value = userList.advancedData.hospitalId;
  groupId.value = userList.advancedData.groupId;
  patientTypes.value = userList.advancedData.patientTypes ?? [];
  diseaseTypes.value = userList.advancedData.diseaseTypes ?? [];
  transformDate.value = userList.advancedData.transformDate;
  close();
};
const setParentValue = (id: number, type: 'region' | 'hospital') => {
  if (type === 'hospital') {
    const curItem = groupListMap.value[id];
    const newItem = hospitalListMap.value[curItem?.pid];
    hospitalId.value = newItem?.id;
    setParentValue(hospitalId.value, 'region');
  } else {
    const curItem = hospitalListMap.value[id];
    const newItem = regionListMap.value[curItem?.pid];
    regionId.value = newItem?.id;
  }
};
const changeHandler = (key: string, val: number) => {
  if (!val) return;
  if (key === 'regionId') {
    hospitalId.value = '';
    groupId.value = '';
  } else if (key === 'hospitalId') {
    groupId.value = '';
    setParentValue(val, 'region');
  } else if (key === 'groupId') {
    setParentValue(val, 'hospital');
  }
};
const confirm = () => {
  const params = {
    regionId: regionId.value,
    hospitalId: hospitalId.value,
    groupId: groupId.value,
    patientTypes: patientTypes.value,
    diseaseTypes: diseaseTypes.value,
    transformDate: transformDate.value,
    orProjectId: project.value,
  };
  emits('change', params);
  close();
};

const queryProjectOptions = async () => {
  const data = await searchOrProject();
  projectList.value = data;
  projectListMap.value = keyBy(projectList.value, 'projectId') as any;
};
const queryOptions = async () => {
  const data = await searchRegionHospitalGroup({});
  regionList.value = data.regionList ?? [];
  hospitalList.value = data.hospitalList ?? [];
  groupList.value = data.groupList ?? [];
  regionListMap.value = keyBy(regionList.value, 'id') as any;
  hospitalListMap.value = keyBy(hospitalList.value, 'id') as any;
  groupListMap.value = keyBy(groupList.value, 'id') as any;
};
const getText = (data: {
  ids: number[];
  types: (0 | 1 | 2 | 3)[];
  date?: string[];
  orProjectId?: number;
  diseaseTypes: DiseaseType[];
}) => {
  const { ids, types, date, diseaseTypes, orProjectId } = data;
  const [r, h, g] = ids;
  const res: string[] = [];
  orProjectId && res.push(projectListMap.value[orProjectId]?.projectName);
  r && res.push(regionListMap.value[r]?.regionName);
  h && res.push(hospitalListMap.value[h]?.hospitalName);
  g && res.push(groupListMap.value[g]?.groupName);
  if (diseaseTypes?.length) {
    const diseaseTypeOptionsMap = keyBy(diseaseTypeOptions, 'value');
    diseaseTypes.forEach(v => {
      res.push(diseaseTypeOptionsMap[v].name);
    });
  }
  if (types?.length) {
    const patientTypeOptionsMap = keyBy(patientTypeOptions, 'value');
    types.forEach(v => {
      res.push(patientTypeOptionsMap[v].name);
    });
  }
  if (date) {
    if (date[0] === date[1]) {
      res.push(dayjs(date[0]).format('YYYY-MM-DD'));
    } else {
      res.push(
        dayjs(date[0]).format('YYYY-MM-DD') +
          '~' +
          dayjs(date[1]).format('YYYY-MM-DD')
      );
    }
  }
  return res.filter(v => !!v).join('、');
};

onMounted(async () => {
  queryOptions();
  queryProjectOptions();
});

defineExpose({
  getText,
});
</script>

<style scoped lang="less">
.advance-search {
  .sure-btn {
    width: 76px;
    background: #2e6be6;
    border-radius: 2px;
    border-color: #2e6be6;
  }
  .cancel-btn {
    width: 76px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
}
.item {
  min-height: 32px;
  margin-bottom: 16px;
  > span {
    display: inline-block;
    color: #203549;
    width: 142px;
    text-align: right;
    padding-right: 16px;
  }
  &:last-child {
    margin-bottom: 0;
  }
}
.patientTyhpes {
  display: inline-block;
}
</style>
