<template>
  <div class="list-container">
    <div
      v-if="config.title"
      class="h-44 pt-12 font-bold pb-12 text-[14px] font-500 flex items-center"
    >
      <img :src="config.icon" />
      <span
        class="text-[#101B25] ml-6 mr-6"
        :style="{ color: config.titleColor }"
      >
        {{ config.title }}
      </span>
      <span class="text-[#2E6BE6]">{{ '(' + data.count + ')' }}</span>
      <span
        class="flex-1 border-b-[1px] border-[#8193A3] border-dashed m-4"
      ></span>
      <el-icon class="cursor-pointer">
        <i-ep-caret-right v-if="!config.visible" @click="() => toggle(true)" />
        <i-ep-caret-bottom v-if="config.visible" @click="() => toggle(false)" />
      </el-icon>
    </div>
    <div v-show="config.visible" class="patient-list">
      <Loadmore
        v-if="data.data.length"
        ref="loadmoreRef"
        :key="renderKey"
        :height="config.height - 36"
        @load="loadHandler"
        @scroll="scrollHandler"
      >
        <Card
          v-for="(item, index) in data.data"
          :id="getItemId(item.patientId)"
          :key="index"
          :type="type"
          :data="item"
          :list-index="listIndex"
          :active="item.patientId === global.userId"
          @click="(e: MouseEvent) => clickHandler(index, item, e)"
        />
      </Loadmore>
      <div
        v-else
        class="h-80 text-28 text-[#DCDEE0] flex items-center justify-center"
      >
        暂无消息
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Loadmore from '../LoadMore/index.vue';
import Card from '../Card/index.vue';
import store from '@/store';
import { ICard, IType } from '@/store/module/useUserList';
import { patientAttention } from '@/api/userList';
import { throttle } from 'lodash-es';

interface IProps {
  config: {
    title: string;
    titleColor?: string;
    icon: string;
    height: number;
    visible: boolean;
  };
  data: {
    count: number;
    data: any[];
  };
  type: IType;
  listIndex: number;
  toTabHandler: (item: ICard) => void;
}
const global = store.useGlobal();

const renderKey = ref(1);
const scrollTop = ref(0);
const scrollDiff = ref(0);
const loadmoreRef = shallowRef();
const props = defineProps<IProps>();
const emits = defineEmits(['toggle', 'click', 'load']);
const getItemId = (patientId: any) => {
  return 'list_' + props.type + '_' + props.listIndex + '_' + patientId;
};
const loadHandler = () => {
  emits('load');
};
const toggle = (val: boolean) => {
  emits('toggle', val);
};

const checkPatientInData = () => {
  return props.data.data.find(v => v.patientId === global.userId);
};
const setScrollDiff = () => {
  const curEle = getCurCardEle(global.userId!);
  const { offsetTop = 0 } = curEle ?? {};
  const diffTop = offsetTop - scrollTop.value;
  scrollDiff.value = diffTop;
};
const setScrollDelta = throttle(() => {
  if (!checkPatientInData()) return;
  setScrollDiff();
}, 16);
const scrollHandler = e => {
  scrollTop.value = e.scrollTop;
  setScrollDelta();
};
const getCurCardEle = (patientId: number) => {
  const curId = getItemId(patientId);
  return document.getElementById(curId);
};
const checkCardView = (patientId: number, { top, bottom, normal }: any) => {
  const curCardEle = getCurCardEle(patientId);
  const { offsetTop = 0, offsetHeight = 0 } = curCardEle ?? {};
  const diffTop = offsetTop - scrollTop.value;
  if (diffTop < 0) {
    top?.(scrollTop.value + diffTop);
  } else {
    const diffBottom = offsetTop - scrollTop.value;
    const containerHeight = props.config.height - 36;
    const diff = containerHeight - (diffBottom + offsetHeight);
    if (diff < 0) {
      bottom?.(scrollTop.value - diff);
    } else {
      normal?.(offsetTop, offsetHeight);
    }
  }
};
const scrollCardToView = (top: number) => {
  loadmoreRef.value?.setScrollTop(top);
};
const setCardInView = (item: ICard) => {
  checkCardView(item.patientId, {
    top: scrollCardToView,
    bottom: scrollCardToView,
  });
  setScrollDiff();
};
const checkPatientNeedInView = () => {
  if (props.data.data.length > 8) return;
  const curPatientId = global.userId;
  if (!checkPatientInData()) return;
  const index = props.data.data.findIndex(v => v.patientId === curPatientId);
  if (index === -1) return;
  checkCardView(curPatientId!, {
    top: scrollCardToView,
    bottom: scrollCardToView,
    normal: (offsetTop: number) => {
      if (scrollDiff.value < 0 || scrollDiff.value > props.config.height - 36) {
        return;
      }
      const diff = offsetTop - scrollDiff.value;
      loadmoreRef.value?.setScrollTop(diff);
    },
  });
};
const clickHandler = (index: number, item: ICard, e: MouseEvent) => {
  const target = e.target as HTMLDivElement;
  if (!item.patientId) {
    ElMessage.warning('患者 ID 不存在！');
    return;
  }
  // 关注，取消关注
  if (target.classList.contains('follow-mark')) {
    const params = {
      patientId: item.patientId,
      status: !item.isMarkPatient,
    };
    patientAttention(params).then(() => {
      item.isMarkPatient = !item.isMarkPatient;
      ElMessage.success(item.isMarkPatient ? '关注成功!' : '取消关注成功!');
    });
  } else {
    global.setUserId(item.patientId);
    if (global.currentRole === 3) {
      let curStatus: any =
        props.type === 0 ? item.manageStatus : (item.manageStatus ?? 2);
      global.manageStatus = curStatus;
    }
    global.diseaseType = item.serviceDiseaseType;
    setCardInView(item);
    //跳转随访 or 复查
    props.toTabHandler(item);
  }
};
watch(
  () => props.data.data,
  () => {
    renderKey.value += 1;
    setTimeout(() => {
      scrollTop.value = 0;
      checkPatientNeedInView();
    }, 100);
  }
);
defineOptions({
  name: 'List',
});
</script>

<style scoped lang="less">
.list-container {
  .patient-list {
    width: calc(100% + 8px);
  }
  :deep(.el-scrollbar) {
    height: calc(100% - 36px);
  }
}
</style>
