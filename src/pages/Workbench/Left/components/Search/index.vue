<template>
  <div class="flex pt-16 pl-16 pr-16 relative">
    <el-input
      v-model="userList.keywords"
      class="flex-1"
      clearable
      size="default"
      maxlength="30"
      placeholder="患者姓名/标签/手机号/聊天记录"
      :prefix-icon="Search"
      @keyup.enter="enterHandler"
    />
    <el-tooltip class="box-item" effect="dark" placement="bottom-start">
      <template #content>
        <div class="font-bold">
          列表自动刷新已{{ globalStore.disableAutoRefresh ? '关闭' : '开启' }}
        </div>
        <div v-if="globalStore.disableAutoRefresh">
          列表不会自动刷新，以便处理长列表事务
        </div>
      </template>
      <div
        class="icon w-32 h-32 flex ml-8 cursor-pointer"
        @click="toggleRefreshStatus"
      >
        <img v-if="!globalStore.disableAutoRefresh" :src="refresh_active" />
        <img v-else :src="refresh_disable" />
      </div>
    </el-tooltip>
    <el-popover
      v-model:visible="visible"
      placement="right-start"
      :show-arrow="false"
      :width="600"
      :offset="2"
      trigger="click"
      popper-style="padding:0"
    >
      <template #reference>
        <div
          class="w-32 h-32 flex-shrink-0 cursor-pointer border-[1px] border-solid border-[#DCDEE0] ml-8 flex justify-center items-center rounded-[2px]"
          :style="{ borderColor: visible ? '#2e6be6' : '' }"
        >
          <img :src="advanceSearch" />
        </div>
      </template>
      <AdvancedSearch
        ref="seachRef"
        @close="visible = false"
        @change="onAdvancedChange"
      />
    </el-popover>
  </div>
  <span
    v-if="advancedText"
    class="flex h-24 items-center p-16 pt-4 pb-0 text-[14px]"
  >
    <span class="flex border-b-[1px] border-[#2E6BE6]">
      <Text custom-style="color: #2E6BE6;max-width:332px;height:20px">
        {{ advancedText }}
      </Text>
      <i-ep-close
        class="cursor-pointer text-[#ccc]"
        @click="deleteAdvancedSearch"
      />
    </span>
  </span>
  <SearchResult v-if="showResult" @load="loadHandler" />
</template>

<script setup lang="ts">
import { debounce } from 'lodash-es';
import Text from '@/components/Text/index.vue';
import { Search } from '@element-plus/icons-vue';
import AdvancedSearch from '../AdvancedSearch/index.vue';
import SearchResult from '../SearchResult/index.vue';
import advanceSearch from '@/assets/imgs/userList/search.png';
import refresh_active from '@/assets/imgs/userList/refresh_active.png';
import refresh_disable from '@/assets/imgs/userList/refresh_disable.png';
import store from '@/store';
import { IState } from '@/store/module/useUserList';
const visible = ref(false);
defineOptions({
  name: 'Search',
});
const userList = store.useUserList();
const globalStore = store.useGlobal();

const seachRef = shallowRef();
const deleteAdvancedSearch = () => {
  userList.resetAdvancedData();
};
const onAdvancedChange = (data: any) => {
  userList.advancedData = { ...data };
};
const advancedText = computed(() => {
  if (seachRef.value) {
    const data = userList.advancedData;
    const { patientTypes, transformDate, diseaseTypes, orProjectId } = data;
    const ids = [data.regionId, data.hospitalId, data.groupId];
    return seachRef.value.getText({
      ids,
      types: patientTypes,
      date: transformDate,
      diseaseTypes,
      orProjectId,
    });
  }
  return '';
});

const toggleRefreshStatus = () => {
  globalStore.disableAutoRefresh = !globalStore.disableAutoRefresh;
};
const loadHandler = (type: keyof IState['searchResult']) => {
  userList.loadSearchMore(type);
};
const enterHandler = () => {
  if (userList.keywords) {
    userList.resetSearchResult();
    searchHandler();
  }
};
const searchHandler = debounce(() => {
  userList.startSearch();
}, 200);

const showResult = computed(() => {
  const keys = Object.keys(userList.searchResult);
  return keys.some(
    key =>
      userList.searchResult[key as keyof typeof userList.searchResult].data
        .length > 0
  );
});
watch(
  () => userList.keywords,
  newVal => {
    if (newVal) {
      // searchHandler();
    } else {
      userList.resetSearchResult();
    }
  }
);
</script>

<style scoped lang="less">
// todo
</style>
