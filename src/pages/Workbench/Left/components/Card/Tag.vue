<template>
  <div class="overflow-hidden h-30 whitespace-nowrap pt-8">
    <div v-if="data.length > 1 && !showAll" class="text-[#7A8599]">
      {{ data.length }}条标签匹配
    </div>
    <div v-else>
      <span
        v-for="item in data"
        :key="item.tagId"
        class="inline-block h-22 item"
        :style="{
          color: color,
          background: bgColor,
        }"
        v-html="getHilightStr(item.tagName, highlightKey)"
      ></span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getHilightStr } from '../../utils';
interface IProps {
  color?: string;
  bgColor?: string;
  data: {
    tagName: string;
    tagId?: number;
  }[];
  showAll?: boolean;
  highlightKey?: string;
}
withDefaults(defineProps<IProps>(), {
  color: () => '#2FB324',
  bgColor: () => 'rgba(45,166,65,0.1)',
  data: () => [],
  highlightKey: () => '',
});
defineOptions({
  tagName: 'Tag',
});
</script>

<style scoped lang="less">
.item {
  max-width: 132px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 22px;
  font-size: 12px;
  background: rgba(45, 166, 65, 0.1);
  border-radius: 2px;
  color: #2fb324;
  line-height: 22px;
  margin-right: 4px;
  padding: 0 6px;
}
</style>
