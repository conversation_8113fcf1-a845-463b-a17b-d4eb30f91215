<template>
  <div class="record-wrap flex h-26 text-[14px] text-[#7A8599] pt-6">
    <span
      class="flex-1 leading-[20px] w-200 whitespace-nowrap text-ellipsis overflow-hidden"
      v-html="getHilightStr(text, highlightKey)"
    ></span>
    <span v-if="showTimes" class="shrink-0 ml-12">
      {{ formatDate(time, { showFutureTime: false }) }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { getHilightStr } from '../../utils';
import { formatDate } from '@/utils';

interface IProps {
  showTimes?: boolean;
  text?: string;
  time?: string | number;
  highlightKey?: string;
}
withDefaults(defineProps<IProps>(), {
  showTimes: true,
  text: '',
  time: '',
  highlightKey: '',
});
defineOptions({
  name: 'ChatRecord',
});
</script>

<style scoped lang="less">
.record-wrap {
  :deep(em) {
    font-style: normal;
  }
}
</style>
