<template>
  <div class="card relative" :class="active ? 'active-card' : ''">
    <Avator :name="getAvatorName(data.patientName)" />
    <PatientType
      v-if="[2, 3].includes(data.patientType)"
      :type="data.patientType"
    />
    <slot>
      <div class="flex-1 ml-16 overflow-hidden">
        <BasicInfo :data="data">
          <template #extra>
            <span
              v-if="type === 0"
              class="inline-block text-white text-[12px] h-16 leading-[16px] rounded-sm pl-4 pr-4"
              :style="{
                background:
                  statusConfig[data.manageStatus as keyof typeof statusConfig]
                    ?.color,
              }"
              >{{
                statusConfig[data.manageStatus as keyof typeof statusConfig]
                  ?.title
              }}</span
            >
          </template>
        </BasicInfo>
        <ChatRecord
          v-if="type === 0 && data.patientChat"
          :text="getRenderText(data)"
          :time="data.patientChat?.chatTime"
        />
        <Tag
          v-if="data.tagList?.length"
          :show-all="true"
          :data="data.tagList"
        />
      </div>
      <div v-if="type === 0 && unreadNum" class="badge">{{ unreadNum }}</div>
      <div v-if="type === 0" class="follow follow-mark">
        <img v-if="data.isMarkPatient" class="follow-mark" :src="follow" />
        <img v-else class="follow-mark" :src="unfollow" />
      </div>
      <!-- <div v-if="active" class="active"></div> -->
      <div
        class="risk"
        :style="{
          borderTopColor: riskColor[data.riskLevel as keyof typeof riskColor],
          borderRightColor: riskColor[data.riskLevel as keyof typeof riskColor],
        }"
      ></div>
      <img class="risk-img" :src="risk" />
    </slot>
  </div>
</template>

<script setup lang="ts">
import Avator from './Avator.vue';
import PatientType from './PatientType.vue';
import BasicInfo from './BasicInfo.vue';
import ChatRecord from './ChatRecord.vue';
import Tag from './Tag.vue';
import follow from '@/assets/imgs/userList/follow.png';
import unfollow from '@/assets/imgs/userList/unfollow.png';
import risk from '@/assets/imgs/userList/risk.png';
import { getChatText, getAvatorName } from '../../utils';
import { IBaseCardProps } from './DoctorCard.vue';
interface IProps extends IBaseCardProps {}
defineProps<IProps>();
const riskColor = ref({
  0: '#7A8599',
  1: '#2FB324',
  2: '#E37221',
  3: '#E63746',
});
const statusConfig = ref({
  1: {
    color: '#2E6BE6',
    title: '待管理',
  },
  2: {
    color: '#2FB324',
    title: '管理中',
  },
  3: {
    color: '#7A8599',
    title: '结束管理',
  },
  4: {
    color: '#7A8599',
    title: '管理中断',
  },
});
defineOptions({
  name: 'Card',
});

const getRenderText = (data: IProps['data']) => getChatText(data.patientChat);
</script>

<style scoped lang="less">
@import './commonCard.less';

.risk {
  position: absolute;
  right: 0;
  top: 0;
  width: 30px;
  height: 30px;
  border: 15px solid #e37221;
  border-radius: 4px;
  border-left-color: transparent;
  border-bottom-color: transparent;
}
.risk-img {
  position: absolute;
  right: 3px;
  top: 3px;
  width: 12px;
  height: 12px;
  z-index: 99;
}
</style>
