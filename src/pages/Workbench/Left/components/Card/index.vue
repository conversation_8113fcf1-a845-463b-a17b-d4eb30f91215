<template>
  <DoctorCard v-if="role === 1" v-bind="attrs" :unread-num="currentUnreadNum" />
  <HealthManagerCard
    v-if="role === 2"
    v-bind="attrs"
    :unread-num="currentUnreadNum"
  />
  <SportRehabilitationCard
    v-if="role === 3"
    v-bind="attrs"
    :unread-num="currentUnreadNum"
  />
</template>

<script setup lang="ts">
import DoctorCard from './DoctorCard.vue';
import HealthManagerCard from './HealthManagerCard.vue';
import SportRehabilitationCard from './SportRehabilitationCard.vue';
import store from '@/store';

const imStore = store.useIM();
const globalStore = store.useGlobal();
const attrs = useAttrs() as typeof DoctorCard.props;

const role = computed(() => globalStore.currentRole);
const currentUnreadNum = computed(() => {
  let res = 0;
  const teamBaseInfos = attrs.data.teamBaseInfos ?? [];
  if (teamBaseInfos?.length) {
    let curTeamList = teamBaseInfos;
    if (role.value !== 1) {
      curTeamList = curTeamList.filter(v => v.teamType !== 1);
    }
    curTeamList.forEach(v => {
      res += imStore.teamUnreadMap[v?.teamId] ?? 0;
    });
  } else {
    res = imStore.teamUnreadMap[attrs.data.teamId] ?? 0;
  }
  return res > 99 ? '99+' : res;
});
defineOptions({
  name: 'Card',
  inheritAttrs: false,
});
</script>

<style scoped lang="less"></style>
