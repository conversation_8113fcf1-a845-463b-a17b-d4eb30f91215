<template>
  <img
    :src="getTagPath"
    alt=""
    class="w-36 h-17 die-img absolute top-0 left-0"
  />
</template>
<script lang="ts" setup>
import dieImg from '@/assets/imgs/chat/die-img.png';
import refuseToVisitImg from '@/assets/imgs/chat/refuse-to-visit.png';
import lossToFollowUpImg from '@/assets/imgs/chat/loss-to-follow-up.png';

export interface IImgTagProps {
  status: 1 | 2 | 3;
}
const props = defineProps<IImgTagProps>();

const pathInfo = ref({
  1: dieImg,
  2: refuseToVisitImg,
  3: lossToFollowUpImg,
});
const getTagPath = computed(() => {
  return pathInfo.value[props.status];
});
</script>
