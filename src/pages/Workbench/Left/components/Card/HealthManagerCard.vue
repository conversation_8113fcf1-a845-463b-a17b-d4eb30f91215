<template>
  <div
    class="card relative"
    :class="{ 'active-card': active, 'die-card': true }"
  >
    <ImgTag :status="status" />
    <Avator :name="getAvatorName(data.patientName)" />
    <PatientType
      v-if="[2, 3].includes(data.patientType)"
      :type="data.patientType"
    />
    <slot>
      <div class="flex-1 ml-16 overflow-hidden">
        <BasicInfo :data="data" />
        <ChatRecord
          v-if="data.patientChat || type === 0"
          :text="getRenderText(data)"
          :time="data.patientChat?.chatTime"
        />
        <Tag
          v-if="data.tagList?.length"
          :show-all="true"
          :data="data.tagList"
        />
      </div>
      <div v-if="type === 1 && unreadNum" class="badge">{{ unreadNum }}</div>
      <div class="follow follow-mark">
        <img v-if="data.isMarkPatient" class="follow-mark" :src="follow" />
        <img v-else class="follow-mark" :src="unfollow" />
      </div>
      <!-- <div v-if="active" class="active"></div> -->
    </slot>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import Avator from './Avator.vue';
import PatientType from './PatientType.vue';
import BasicInfo from './BasicInfo.vue';
import ChatRecord from './ChatRecord.vue';
import Tag from './Tag.vue';
import follow from '@/assets/imgs/userList/follow.png';
import unfollow from '@/assets/imgs/userList/unfollow.png';
import { getChatText, getAvatorName } from '../../utils';
import { IBaseCardProps } from './DoctorCard.vue';
import ImgTag from './ImgTag.vue';
interface IProps extends IBaseCardProps {}
const props = defineProps<IProps>();
defineOptions({
  name: 'Card',
});

const getRenderText = (data: IProps['data']) => {
  if (props.type === 0) {
    // 复查跟踪
    const typeText = '复查';
    const date = data.patientReview?.reviewDate;
    let text = data.isUpload ? '【已上传】' : '【未上传】';
    if (data.remainValidDays! < 0) {
      text += '已过期';
    } else if (data.remainValidDays === 0) {
      text += `不足1天${typeText}失效`;
    } else {
      text +=
        data.remainValidDays! <= 7
          ? `剩余${data.remainValidDays}天${typeText}失效`
          : `${typeText}日期：${dayjs(date).format('YYYY-MM-DD')}`;
    }
    let color = '#7A8599';
    if (props.listIndex === 0) {
      color = data.remainValidDays! <= 7 ? '#E63746' : '#E37221';
    }
    return `<span style="color:${color}">${text}</span>`;
  }
  if (props.type === 1) {
    // 症状随访
    const typeText = '随访';
    const date = data.patientFollowUp?.followUpDate;
    let text = '';
    if (data.remainValidDays! < 0) {
      text += '已过期';
    } else if (data.remainValidDays === 0) {
      text += `不足1天${typeText}失效`;
    } else {
      text +=
        data.remainValidDays! <= 7
          ? `剩余${data.remainValidDays}天${typeText}失效`
          : `${typeText}日期：${dayjs(date).format('YYYY-MM-DD')}`;
    }
    let color = '#7A8599';
    if (props.listIndex === 0) {
      color = data.remainValidDays! <= 7 ? '#E63746' : '#E37221';
    }
    return `<span style="color:${color}">${text}</span>`;
  }
  return getChatText(data.patientChat);
};

const status = ref<1 | 2 | 3>(1); // 1--死亡  2--失访  3--拒访
</script>

<style scoped lang="less">
@import './commonCard.less';
</style>
