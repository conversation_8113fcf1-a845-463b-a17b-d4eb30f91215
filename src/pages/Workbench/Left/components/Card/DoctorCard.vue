<template>
  <div
    class="card relative"
    :class="{ 'active-card': active, 'die-card': true }"
  >
    <ImgTag :status="getTagStatus" />
    <Avator :name="getAvatorName(data.patientName)" />
    <PatientType
      v-if="[2, 3].includes(data.patientType)"
      :type="data.patientType"
    />
    <slot>
      <div class="flex-1 ml-16 overflow-hidden">
        <BasicInfo :data="data" />
        <ChatRecord
          v-if="data.patientChat || props.type === 2"
          :text="getRenderText(data)"
          :time="data.patientChat?.chatTime"
        />
        <Tag
          v-if="(type === 0 || type === 1) && data.expireDays"
          :data="[{ tagName: `已过期${data.expireDays}天` }]"
          color="#E63746"
          bg-color="#FFE6E7"
        />
        <Tag
          v-if="
            (type === 0 || type === 1) &&
            !data.expireDays &&
            !data.isConfirmEnrollment
          "
          :data="[{ tagName: '未入组' }]"
          color="#3A4762"
          bg-color="#E8EAED"
        />
        <Tag
          v-if="
            (type === 0 || type === 1) &&
            !data.expireDays &&
            data.isConfirmEnrollment &&
            data.tagList?.length
          "
          :show-all="true"
          :data="data.tagList"
        />
      </div>
      <div v-if="unreadNum" class="badge">{{ unreadNum }}</div>
      <div class="follow follow-mark">
        <img v-if="data.isMarkPatient" class="follow-mark" :src="follow" />
        <img v-else class="follow-mark" :src="unfollow" />
      </div>
      <!-- <div v-if="active" class="active"></div> -->
    </slot>
  </div>
</template>

<script setup lang="ts">
import Avator from './Avator.vue';
import PatientType from './PatientType.vue';
import BasicInfo from './BasicInfo.vue';
import ChatRecord from './ChatRecord.vue';
import Tag from './Tag.vue';
import follow from '@/assets/imgs/userList/follow.png';
import unfollow from '@/assets/imgs/userList/unfollow.png';
import { ICard } from '@/store/module/useUserList';
import { getChatText, getAvatorName } from '../../utils';
import dayjs from 'dayjs';
import ImgTag from './ImgTag.vue';
export interface IBaseCardProps {
  active?: boolean;
  data: ICard;
  type: 0 | 1 | 2;
  listIndex: number;
  unreadNum: number | string;
  role: 1 | 2 | 3 | 4;
}
const props = defineProps<IBaseCardProps>();
defineOptions({
  name: 'Card',
});

const getRenderText = (data: IBaseCardProps['data']) => {
  if (props.type === 2 && props.role === 1) {
    // 复查解读
    const typeText = '复查';
    const date = data.patientReview?.reviewDate;
    let text = data.isUpload ? '【已上传】' : '【未上传】';
    if (data.remainValidDays! < 0) {
      text += '已过期';
    } else if (data.remainValidDays === 0) {
      text += `不足1天${typeText}失效`;
    } else {
      text +=
        data.remainValidDays! <= 7
          ? `剩余${data.remainValidDays}天${typeText}失效`
          : `${typeText}日期：${dayjs(date).format('YYYY-MM-DD')}`;
    }
    let color = '#7A8599';
    if (props.listIndex === 0) {
      color = data.remainValidDays! <= 7 ? '#E63746' : '#E37221';
    }
    return `<span style="color:${color}">${text}</span>`;
  }
  if (props.type === 2) {
    // 症状随访
    const typeText = '随访';
    const date = data.patientFollowUp?.followUpDate;
    let text = '';
    if (data.remainValidDays! < 0) {
      text += '已过期';
    } else if (data.remainValidDays === 0) {
      text += `不足1天${typeText}失效`;
    } else {
      text +=
        data.remainValidDays! <= 7
          ? `剩余${data.remainValidDays}天${typeText}失效`
          : `${typeText}日期：${dayjs(date).format('YYYY-MM-DD')}`;
    }
    let color = '#7A8599';
    if (props.listIndex === 0) {
      color = data.remainValidDays! <= 7 ? '#E63746' : '#E37221';
    }
    return `<span style="color:${color}">${text}</span>`;
  }
  return getChatText(data.patientChat);
};

const getTagStatus = computed(() => {
  return Number(props.data.died);
});
</script>

<style scoped lang="less">
@import './commonCard.less';
</style>
