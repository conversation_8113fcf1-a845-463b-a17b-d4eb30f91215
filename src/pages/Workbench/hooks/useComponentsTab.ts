import { Component } from 'vue';
import bus from '@/lib/bus';
import store from '@/store';
import { IPatientTabAddItem } from '@/store/module/useTabs';
import HandleExceptions from '@/pages/Workbench/HandleExceptions/index.vue';
import AddHospitalized from '@/features/PatientRecord/AddHospitalized/index.vue';
import AddOutpatientRecord from '@/features/PatientRecord/AddOutpatientRecord/index.vue';
import Reexamination from '@/features/PatientRecord/Reexamination/index.vue';
import LifestyleAssessment from '@/pages/Workbench/Main/Home/FollowUp/components/LifestyleAssessment/index.vue';
import SymptomFollowUp from '@/pages/Workbench/Main/Home/FollowUp/components/SymptomFollowUp/index.vue';
import ReportDetails from '@/pages/Workbench/Main/Management/StructuredReport/ReportDetails/index.vue';

const BusinessComponents: Record<string, Component> = {
  // 住院
  0: AddHospitalized,
  // 入组
  3: AddHospitalized,
  // 门诊
  1: AddOutpatientRecord,
  // 复查
  2: Reexamination,
  // 批量异常处理
  4: HandleExceptions,
  // 生活方式评估
  5: LifestyleAssessment,
  // 症状随访
  6: SymptomFollowUp,
  // 阶段性总结报告详情
  7: ReportDetails,
};
export function useComponentsTab() {
  const tabs = store.useTabs();
  const tabAction = store.useComponentsTabAction();

  /** 外部打开指定tab */
  const handleTabAction = () => {
    const { componentType, name, ...rest } = tabAction;

    if (!name) return;
    const tabItem = {
      ...rest,
      name,
      component: markRaw(BusinessComponents[componentType]),
    } as unknown as IPatientTabAddItem;
    tabs.addTab(tabItem);
  };

  onMounted(() => {
    bus.on('open-component-tab', () => {
      handleTabAction();
    });
  });

  onBeforeMount(() => {
    bus.off('open-component-tab');
  });
}
