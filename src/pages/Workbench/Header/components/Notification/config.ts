import { keyBy } from 'lodash-es';
import { messageType } from '@/constant/message';
import chat from '@/assets/imgs/message/chat.png';
import groupInImg from '@/assets/imgs/message/group-in.png';
import groupOutImg from '@/assets/imgs/message/group-out.png';
import bpFail from '@/assets/imgs/message/bp_fail.png';
import bpSuccess from '@/assets/imgs/message/bp_success.png';
import ocrFail from '@/assets/imgs/message/ocr_fail.png';
import ocrSuccess from '@/assets/imgs/message/ocr_success.png';
import renew from '@/assets/imgs/message/renew.png';
import transcription from '@/assets/imgs/message/transcription.png';
import upload from '@/assets/imgs/message/upload.png';
import diagnose from '@/assets/imgs/message/diagnose.png';
import treatment from '@/assets/imgs/message/treatment.png';
import bloodRisk from '@/assets/imgs/message/blood-risk-icon.png';
import bloodPrssureRisk from '@/assets/imgs/message/bloodprssure-risk-icon.png';
import heartRisk from '@/assets/imgs/message/heart-risk-icon.png';
import weightRisk from '@/assets/imgs/message/weight-risk-icon.png';
import uploadImageFileIcon from '@/assets/imgs/message/upload-image-file.png';

import store from '@/store';
import bus from '@/lib/bus';
import dayjs from 'dayjs';
import { IApiMessageCenterListResponse } from '@/interface/type';
import {
  IAction,
  IRecordSourceType,
} from '@/store/module/useComponentsTabAction';

type ITabParams = IAction & { mode?: string };
type IData = IApiMessageCenterListResponse;
const imStore = store.useIM();
const globalStore = store.useGlobal();
const tabStore = store.useTabs();
const userListStore = store.useUserList();
const tabAction = store.useComponentsTabAction();
const indicatorInfo = store.useIndicatorInfo();

const toTabs = (params: ITabParams) => {
  const { componentType, name, key, data, mode } = params;
  tabAction.setAction({
    componentType,
    name,
    key,
    mode: !mode ? 'new' : mode,
    data,
  });
  bus.emit('open-component-tab');
};
const setUserId = (id: number) => {
  globalStore.setUserId(id);
  if (globalStore.currentRole === 2) {
    userListStore.activeTab = 1;
  } else {
    userListStore.activeTab = 0;
  }
  userListStore.keepUser = true;
};
const getDateTitle = (time: string | number, name: string) => {
  if (!time) return name;
  return dayjs(time).format('YYYY-MM-DD') + ' ' + name;
};
const setFindMsg = ({ idServer, time, teamId }: any) => {
  imStore.findMsgData = {
    idServer: idServer,
    time: time,
  };
  imStore.findTeamId = teamId;
  imStore.findMsgFocus = true;
};
const emitClearMessage = (data: any) => {
  bus.emit('clear-message', data);
};
const getMsgType = (teamId: string) => {
  const curItem = imStore.patientTeamList.find(v => v.teamNumber === teamId);
  const teamType = curItem?.teamType;
  if (teamType === 1) {
    return messageType.EXPERT_CHAT_RECORD;
  } else if (teamType === 4) {
    return messageType.TEAM_CHAT_RECORD;
  }
  return messageType.PATIENT_CHAT_RECORD;
};
// 消息跳转
const chatHandler = (data: IData) => {
  const findMsgData = {
    idServer: data.secondSourceId,
    time: data.latestTime,
    teamId: data.sourceId,
  };
  if (globalStore.userId === data.patientId) {
    imStore.curTeamId = data.sourceId!;
    imStore.findMsgFocus = true;
    imStore.findMsg(findMsgData as any);
    emitClearMessage({
      sourceId: imStore.curTeamId,
      msgType: getMsgType(imStore.curTeamId),
    });
    imStore.resetSessionUnread();
  } else {
    setUserId(data.patientId!);
    setFindMsg(findMsgData);
  }
};
const tabActionHandler = ({
  data,
  type,
  name,
  actionType,
}: {
  data: IData;
  type: IRecordSourceType;
  name: string;
  actionType?: 'add' | 'view' | 'edit';
}) => {
  toTabs({
    componentType: type,
    name: getDateTitle(data.sourceTime!, name),
    mainTabCode: 2,
    data: {
      id: data.sourceId!,
      recordActionType: {
        actionType: actionType ?? 'view',
        sourceType: type,
      },
    },
  });
};
// 症状随访跳转
const followUpHandler = (data: IData) => {
  setUserId(data.patientId!);
  toTabs({
    componentType: 6,
    name: getDateTitle(data.sourceTime!, '症状随访'),
    mainTabCode: 2,
    data: {
      followUpDate: data.sourceTime,
      followUpId: data.sourceId,
    },
  });
};
// 入组跳转
const enrollmentHandler = (data: IData, ignoreSourceId = true) => {
  setUserId(data.patientId!);
  const newData: any = { ...data };
  let actionType = 'view';
  if (ignoreSourceId || newData.sourceId === '-1') {
    newData.sourceId = null;
    actionType = 'add';
  }
  tabActionHandler({
    data: newData,
    type: 3,
    actionType: actionType as any,
    name: '入组',
  });
};
// 复查跳转
const reviewHandler = (data: IData) => {
  setUserId(data.patientId!);
  tabActionHandler({ data, type: 2, name: '复查' });
};
// 门诊跳转
const outPatientHandler = (data: IData) => {
  setUserId(data.patientId!);
  tabActionHandler({ data, type: 1, name: '门诊' });
};
// 住院跳转
const hostpitalizedHandler = (data: IData) => {
  setUserId(data.patientId!);
  tabActionHandler({ data, type: 0, name: '住院' });
};
// 患者信息主页跳转
const homeHandler = (data: IData, setCommonSign = true, fixedId?: string) => {
  setUserId(data.patientId!);
  tabStore.clearCache();
  const group = 'patient_info';
  const curTab = tabStore.getCurrentPatientTabs(group);
  if (curTab) {
    const curId = curTab?.[1]?.id ?? '';
    if (curId) {
      if (setCommonSign) {
        indicatorInfo.bloodPressureMonitoring = {
          patientId: data.patientId!,
          indexTermId: Number(fixedId ?? data.sourceId) || 0,
        };
      }
      tabStore.mainActiveTab = 2;
      tabStore.patientActiveTabMap[group] = curTab?.[1]?.id ?? '';
    }
  }
};
// 患者工作室变更
const groupChangeHandler = (data: IData) => {
  if (data.msgType === 'PATIENT_GROUP_OUT') {
    bus.emit('get-patient-Group', data);
  } else {
    setUserId(data.patientId!);
    tabStore.mainActiveTab = 2;
  }
};

// 图片档案上传
const handlerUploadImageFile = (data: IData) => {
  setUserId(data.patientId!);
  tabStore.mainActiveTab = 5;
};

export const CARD_LIST = [
  // 患者转入工作室
  {
    type: messageType.PATIENT_GROUP_IN,
    icon: groupInImg,
    handler: (data: IData) => groupChangeHandler(data),
  },
  // 患者转出工作室
  {
    type: messageType.PATIENT_GROUP_OUT,
    icon: groupOutImg,
    handler: (data: IData) => groupChangeHandler(data),
  },
  {
    type: messageType.PATIENT_CHAT_RECORD,
    icon: chat,
    handler: chatHandler,
    formatter: (text: string) => {
      return text;
    },
  },
  {
    type: messageType.EXPERT_CHAT_RECORD,
    icon: chat,
    handler: chatHandler,
  },
  {
    type: messageType.TEAM_CHAT_RECORD,
    icon: chat,
    handler: chatHandler,
  },
  {
    type: messageType.PATIENT_ENROLLMENT,
    icon: treatment,
    handler: enrollmentHandler,
    // 信息区定位到“患者信息”并新开“入组信息查看”页面
  },
  {
    type: messageType.SCIENTIFIC_RESEARCH_PATIENT_ENROLLMENT,
    icon: treatment,
    handler: enrollmentHandler,
  },
  {
    type: messageType.PATIENT_RENEW,
    icon: renew,
    handler: (data: IData) => homeHandler(data, false),
  },
  //血压风险处理
  {
    type: messageType.BLOOD_PRESSURE_RISK,
    icon: bloodPrssureRisk,
    handler: (data: IData) => {
      // 信息区定位到“患者信息”—“主页”，并打开对应的异常指标页，显示异常指标数据，如页面已打开，则刷新页面
      setUserId(data.patientId!);
      toTabs({
        componentType: 4,
        name: '处理血压异常',
        mode: 'new',
        mainTabCode: 2,
        data: {
          sourceId: data.sourceId,
          sourceType: 2,
          riskType: 1,
        },
      });
    },
  },
  {
    type: messageType.HEART_RISK,
    icon: heartRisk,
    handler: (data: IData) => {
      // 信息区定位到“患者信息”—“主页”，并打开对应的异常指标页，显示异常指标数据，如页面已打开，则刷新页面
      setUserId(data.patientId!);
      toTabs({
        componentType: 4,
        name: '处理心率异常',
        mode: 'new',
        mainTabCode: 2,
        data: {
          sourceId: data.sourceId,
          sourceType: 2,
          riskType: 2,
        },
      });
    },
  },
  {
    type: messageType.BLOOD_SUGAR_RISK,
    icon: bloodRisk,
    handler: (data: IData) => {
      // 信息区定位到“患者信息”—“主页”，并打开对应的异常指标页，显示异常指标数据，如页面已打开，则刷新页面
      setUserId(data.patientId!);
      toTabs({
        componentType: 4,
        name: '处理血糖异常',
        mode: 'new',
        mainTabCode: 2,
        data: {
          sourceId: data.sourceId,
          sourceType: 2,
          riskType: 3,
        },
      });
    },
  },
  {
    type: messageType.WEIGHT_RISK,
    icon: weightRisk,
    handler: (data: IData) => {
      // 信息区定位到“患者信息”—“主页”，并打开对应的异常指标页，显示异常指标数据，如页面已打开，则刷新页面
      setUserId(data.patientId!);
      toTabs({
        componentType: 4,
        name: '处理体重异常',
        mode: 'new',
        mainTabCode: 2,
        data: {
          sourceId: data.sourceId,
          sourceType: 2,
          riskType: 4,
        },
      });
    },
  },
  {
    type: messageType.PATIENT_ABNORMAL_SYMPTOMS,
    icon: diagnose,
    handler: followUpHandler,
    //信息区定位到“患者信息”，并新开对应的症状随访详情页，若页面已打开则刷新页面信息
  },
  {
    type: messageType.INCLUDED_DATA_UPLOADED,
    icon: upload,
    handler: (data: IData) => enrollmentHandler(data, false),
    // 息区定位到“患者信息”，并新开对应的入组信息页，若页面已打开则刷新页面信息
  },
  {
    type: messageType.REVIEW_REPORT_UPLOADED,
    icon: upload,
    handler: reviewHandler,
    // 息区定位到“患者信息”，并新开对应的复查信息页，若页面已打开则刷新页面信息
  },
  {
    type: messageType.INCLUDED_DATA_TRANSCRIPTION,
    icon: transcription,
    handler: (data: IData) => enrollmentHandler(data, false),
    // 1. 信息区定位到“患者信息”，并新开对应的入组信息页，若页面已打开则刷新页面信息；
  },
  {
    type: messageType.OUTPATIENT_RECORD_TRANSCRIPTION,
    icon: transcription,
    handler: outPatientHandler,
    //1. 信息区定位到“患者信息”，并新开对应的门诊信息页，若页面已打开则刷新页面信息；
  },
  {
    type: messageType.HOSPITALIZATION_RECORD_TRANSCRIPTION,
    icon: transcription,
    handler: hostpitalizedHandler,
    //1. 信息区定位到“患者信息”，并新开对应的住院信息页，若页面已打开则刷新页面信息；
  },
  {
    type: messageType.PATIENT_ADMISSION_UPDATE,
    icon: transcription,
    handler: hostpitalizedHandler,
    //1. 信息区定位到“患者信息”，并新开对应的住院信息页，若页面已打开则刷新页面信息；
  },
  {
    type: messageType.REVIEW_REPORT_TRANSCRIPTION,
    icon: transcription,
    handler: reviewHandler,
    // 1. 信息区定位到“患者信息”，并新开对应的复查信息页，若页面已打开则刷新页面信息；
  },
  {
    type: messageType.AMBULATORY_BLOOD_PRESSURE_SUCCESS,
    icon: bpSuccess,
    handler: (data: IData) => homeHandler(data, true, '-2'),
    // 1. 信息区定位到“患者信息”，并在“报告原文”中打开“动态血压监测”
  },
  {
    type: messageType.AMBULATORY_BLOOD_PRESSURE_FAILURE,
    icon: bpFail,
    handler: (data: IData) => homeHandler(data, true, '-2'),
    // 1. 信息区定位到“患者信息”，并在“报告原文”中打开“动态血压监测”
  },
  {
    type: messageType.PATIENT_JOIN_SPORT_MANAGE,
    icon: treatment,
    handler: (data: IData) => {
      globalStore.setUserId(data.patientId);
      tabStore.mainActiveTab = 1;
      tabStore.mainTabs[0].key = Math.random() + '';
      userListStore.keepUser = true;
    },
  },
  {
    type: messageType.OCR_IDENTIFY_SUCCESS,
    icon: ocrSuccess,
    handler: () => {},
  },
  {
    type: messageType.OCR_IDENTIFY_FAILURE,
    icon: ocrFail,
    handler: () => {},
  },
  {
    type: messageType.IMAGE_ARCHIVE_UPLOADED,
    icon: uploadImageFileIcon,
    handler: (data: IData) => handlerUploadImageFile(data),
  },
];

export const CardMap = keyBy(CARD_LIST, 'type');
