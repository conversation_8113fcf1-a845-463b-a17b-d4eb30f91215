<template>
  <div class="message-container">
    <TransitionGroup tag="div" name="message">
      <div
        v-for="item in data"
        :key="item.id"
        class="message-item"
        @mouseover="mouseoverHandler"
        @mouseout="mouseoutHandler"
      >
        <Card
          :data="item.data"
          :config="CardMap[item.data.msgType]"
          @click-card="() => clickHanlder(item.id)"
        />
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import Card from '../MessageCard/index.vue';
import store from '@/store';
import { CardMap } from './config';
import bus from '@/lib/bus';
import { getUuid } from '@/utils';

interface IProps {
  resetNum: () => void;
}
interface IItem {
  showTime: number;
  [key: string]: any;
}

defineOptions({
  name: 'Notification',
});
const props = defineProps<IProps>();
const messageStore = store.useMessage();
const VISIBLE_TIEM = 5 * 1000;
const data = ref<IItem[]>([]);
const isHover = ref(false);

const clickHanlder = (id: string) => {
  const index = data.value.findIndex(v => v.id === id);
  data.value.splice(index, 1);
  transitionData();
  // props?.resetNum();
};
const mouseoverHandler = () => {
  isHover.value = true;
};
const mouseoutHandler = () => {
  isHover.value = false;
};
const transitionData = () => {
  const needLen = 5 - data.value.length;
  if (!needLen) return;
  const newData = messageStore.data
    .slice(0, needLen)
    .map(v => ({
      showTime: Date.now(),
      id: getUuid(),
      data: v,
    }))
    .filter(v => CardMap[v.data.msgType]);
  if (!newData.length) return;
  data.value.push(...newData);
  messageStore.data = messageStore.data.slice(needLen);
};
const checkData = () => {
  if (!isHover.value) {
    data.value = data.value.filter(v => {
      return Date.now() - v.showTime < VISIBLE_TIEM;
    });
    transitionData();
  }
};
const startCheckData = () => {
  setTimeout(() => {
    checkData();
    startCheckData();
  }, 1000);
};

onMounted(() => {
  transitionData();
  startCheckData();
  messageStore.init();
  bus.on('notification-message', () => {
    props?.resetNum();
    transitionData();
  });
  bus.on('notification-message-clear', () => {
    data.value = [];
    messageStore.data = [];
  });
});
</script>

<style scoped lang="less">
.message-container {
  position: fixed;
  width: 288px;
  right: 16px;
  top: 76px;
  z-index: 3001;
}
.message-item {
  width: 288px;
  height: 70px;
  background: #000000;
  box-shadow: 0px 4px 12px 0px rgba(8, 38, 99, 0.2);
  border-radius: 4px;
  opacity: 0.8;
  margin-bottom: 8px;
  :deep(.card) {
    color: #fff;
    .title {
      color: #fff;
    }
    .detail {
      padding-left: 22px;
    }
  }
}
.message-move,
.message-enter-active,
.message-leave-active {
  transition: all 0.3s ease;
}
.message-enter-from,
.message-leave-to {
  opacity: 0;
  transform: translateX(288px);
}

.message-leave-active {
  position: absolute;
}
</style>
