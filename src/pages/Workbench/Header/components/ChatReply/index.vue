<template>
  <el-popover
    ref="refChatReplyPopover"
    popper-class="chat-reply-popover"
    :width="500"
    trigger="click"
    @show="getConversationList"
    @hide="handleResetPageNumber"
  >
    <template #reference>
      <el-badge :value="aiStore.chatCount" :max="99">
        <img
          src="@/assets/imgs/chat/chat-img.png"
          class="chat-img"
          alt="ai-img"
        />
      </el-badge>
    </template>
    <el-scrollbar v-loading="loading" max-height="calc(100vh - 80px)">
      <div v-infinite-scroll="loadHandler" :infinite-scroll-immediate="false">
        <template v-if="chatList?.length">
          <chartItem
            v-for="item in chatList"
            :key="item.patient?.id"
            :item="item"
            @handle-respond="handleRespond"
            @handle-ignore-all="handleIgnoreAll"
          />
        </template>
        <el-empty v-else description="暂无数据" />
      </div>
    </el-scrollbar>
  </el-popover>
</template>

<script setup lang="ts">
import bus from '@/lib/bus';
import chartItem from './chartItem.vue';
import { getRecommendConversation } from '@/api/recommendConversation';
import store from '@/store';
import {
  IApiRecommendConversationQueryPageContents,
  IApiRecommendConversationQueryPageContentsRecommends,
} from '@/interface/type';

defineOptions({ name: 'ChatReply' });

const globalStore = store.useGlobal();
const aiStore = store.useAIConversation();
const userListStore = store.useUserList();
const imStore = store.useIM();
const refChatReplyPopover = shallowRef();
const chatList = ref<IApiRecommendConversationQueryPageContents[]>([]);
const total = ref(0);
const pageNumber = ref(1);
const loading = ref(false);

const loadHandler = () => {
  if ((chatList.value?.length || 0) >= total.value) return;
  pageNumber.value += 1;
  getConversationList();
};

const getConversationList = async () => {
  loading.value = true;
  try {
    const res = await getRecommendConversation({
      pageNumber: pageNumber.value,
      pageSize: 5,
    });
    const { contents, total: t, totalMsg } = res;
    total.value = t ?? 0;
    aiStore.chatCount = totalMsg || undefined;
    if (!contents?.length) return (chatList.value = []);
    if (!chatList.value) chatList.value = [];
    if (pageNumber.value === 1) {
      chatList.value = contents;
    } else {
      chatList.value.push(...contents);
    }
  } finally {
    loading.value = false;
  }
};

/** 重置pageNumber */
const handleResetPageNumber = () => {
  pageNumber.value = 1;
};

/** 切换患者 */
const handleChangePatient = (id?: number) => {
  if (!id) return ElMessage.warning('患者 ID 不存在！');
  globalStore.setUserId(id);
  userListStore.activeTab = 0;
  userListStore.keepUser = true;
  refChatReplyPopover.value?.hide();
};

/** 处理单调数据 */
const handleRespond = async (obj: {
  recommend: IApiRecommendConversationQueryPageContentsRecommends;
  confirmType: 'IGNORED' | 'CONFIRMED' | 'REPLIED';
}) => {
  const { recommend, confirmType } = obj;
  const { recommendId, patientId, outputDialogue, conversationNo } = recommend;
  if (!patientId || !recommendId || !outputDialogue) return;

  // 切换患者
  if (confirmType === 'CONFIRMED') handleChangePatient(patientId);

  await aiStore.handleAiRespond({ recommendId, confirmType });

  switch (confirmType) {
    case 'CONFIRMED':
      aiStore.aiChatMsg = outputDialogue;
      break;
    case 'REPLIED':
      imStore.sendTextMsg({
        to: conversationNo,
        text: outputDialogue,
      });
      break;
  }

  if (confirmType !== 'CONFIRMED') {
    ElMessage.success('操作成功！');
    handleResetPageNumber();
    getConversationList();
  }
};

/** 忽略指定患者所有信息 */
const handleIgnoreAll = async (patientId: number) => {
  const res = await aiStore.removeConversationMsg(patientId);
  if (res) {
    ElMessage.success('操作成功！');
    handleResetPageNumber();
    getConversationList();
  }
};

onMounted(() => {
  aiStore.getRecommendConversationTotal();

  bus.on('ai-notification-message-refresh', () => {
    aiStore.getRecommendConversationTotal();
  });
});

onUnmounted(() => {
  bus.off('ai-notification-message-refresh');
});
</script>
<style scoped lang="less">
.chat-img {
  width: 32px;
  height: 32px;
  cursor: pointer;
}
</style>
<style>
.chat-reply-popover {
  padding: 16px 0 0 !important;
}
</style>
