<template>
  <div class="card" @click.stop="clickHandler">
    <div class="flex items-center h-20">
      <img class="w-16 h-16 mr-4" :src="config?.icon" />
      <span class="title">{{ data.msgTitle }}</span>
      <span class="text-[12px]">
        {{ formatDate(data.latestTime, { showFutureTime: false }) }}
      </span>
    </div>
    <div class="detail leading-[20px] pt-6">
      <!-- <Text :custom-text="getContent(data.msgContent)" /> -->
      <span
        class="flex-1 leading-[20px] whitespace-nowrap text-ellipsis overflow-hidden"
      >
        {{ getContent(data.msgSimpleContent) }}
      </span>
      <!-- <span class="text-[#2E6BE6] shrink-0 pl-2 cursor-pointer"
        >进入聊天 &gt;</span
      > -->
    </div>
  </div>
</template>

<script setup lang="ts">
// import Text from '@/components/Text/index.vue';
import { messageType } from '@/constant/message';
import { readMessage } from '@/api/message';
import { formatDate } from '@/utils';
import { IApiMessageCenterListResponse } from '@/interface/type';
import useGlobal from '@/store/module/useGlobal';
interface IProps {
  data: IApiMessageCenterListResponse;
  config: any;
}
const globalStore = useGlobal();
const emits = defineEmits(['click-card']);
const props = defineProps<IProps>();
defineOptions({
  name: 'MessageCard',
});
const chatTypes = [
  messageType.PATIENT_CHAT_RECORD,
  messageType.TEAM_CHAT_RECORD,
  messageType.EXPERT_CHAT_RECORD,
];

const clickHandler = () => {
  const { data, config } = props;
  if (!data.patientId) {
    ElMessage.warning('患者 ID 不存在！');
    return;
  }
  if (config.handler) {
    config.handler(data);
  }
  emits('click-card', { data, config });
  globalStore.setManageStatus(data.patientId);
  if (!chatTypes.includes(data.msgType as messageType)) {
    readMessage({ msgId: data.msgId! });
  }
};
const getContent = (val: string = '') => {
  let res = val;
  if (props.config?.formatter) {
    res = props.config.formatter(val);
  }
  return res;
};
</script>

<style scoped lang="less">
.card {
  padding: 12px;
  color: #7a8599;
  font-size: 14px;
  .title {
    flex: 1;
    font-weight: bold;
    color: #3a4762;
    padding: 0 2px;
    line-height: 20px;
  }
  .detail {
    display: flex;
  }
}
</style>
