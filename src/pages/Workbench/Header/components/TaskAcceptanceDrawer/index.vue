<template>
  <div>
    <Drawer
      v-model:visible="visible"
      title="任务事项"
      modal
      @chat-visible="chatVisible"
      @update:visible="chatVisible"
    >
      <div class="p-16 main">
        <Header @query-data-list="queryDataList" />
        <ul v-infinite-scroll="load" class="list-box mt-16">
          <li
            v-for="item in taskList"
            :key="item.taskId"
            class="item h-127 mb-8 px-16"
          >
            <ListItem :form="item" />
          </li>
        </ul>
      </div>
    </Drawer>
  </div>
</template>
<script setup lang="ts">
import { taskSearchType, ITaskList, ITaskSearch } from './type';
import Header from './components/Header.vue';
import ListItem from './components/ListItem.vue';
import bus from '@/lib/bus';
import { taskListApi } from '@/api/task';
const props = defineProps({
  taskAcceptanceVisible: {
    type: Boolean,
    default: false,
  },
});
import Drawer from '@/components/Drawer/index.vue';
const visible = ref(true);

const emits = defineEmits<{
  (e: 'update:taskAcceptanceVisible', value: boolean): void;
}>();
const chatVisible = () => {
  emits('update:taskAcceptanceVisible', false);
};

const queryInfo = ref<ITaskSearch>({});
// 头部搜索条件、完成历史
const queryDataList = (form: taskSearchType, flag: boolean) => {
  const { completionTime, taskType } = form;
  const info: ITaskSearch = {
    taskMethod: taskType === -1 ? null : taskType,
    taskStatus: form.taskStatus === -1 ? null : form.taskStatus,
  };

  // 完成历史
  if (flag) {
    info.completeStartTime = completionTime ? completionTime[0] : null;
    info.completeEndTime = completionTime ? completionTime[1] : null;
  }
  queryInfo.value = info;
  resetRequestData();
};

// 数据列表
const pageNumber = ref<number>(1);
const totalNumber = ref<number>(0);
// 加载到底部分页加载数据
const load = () => {
  const totle = Math.ceil(totalNumber.value / 10);
  if (pageNumber.value < totle) {
    pageNumber.value++;
    getTaskList();
  }
};

const taskList = ref<ITaskList[]>([]);
// 获取任务列表
const getTaskList = async () => {
  const info: ITaskSearch = {
    pageNumber: pageNumber.value,
    pageSize: 10,
    ...queryInfo.value,
  };
  taskListApi(info).then((res: any) => {
    taskList.value = [...taskList.value, ...(res.data.contents ?? [])];
    totalNumber.value = res.data.total;
  });
};

bus.on('updata-task-refresh', () => {
  resetRequestData();
});

// 重置请求数据
const resetRequestData = () => {
  pageNumber.value = 1;
  taskList.value = [];
  totalNumber.value = 0;
  getTaskList();
};

watch(
  () => props.taskAcceptanceVisible,
  newVal => {
    visible.value = newVal;
    pageNumber.value = 1;
  },
  { immediate: true }
);
</script>
<style lang="less" scoped>
.list-box {
  height: calc(100vh - 200px);
  overflow-y: auto;
  .item {
    background: #f7f8fa;
    border-radius: 4px;
  }
}
</style>
