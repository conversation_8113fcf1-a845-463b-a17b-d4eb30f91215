<template>
  <div class="box">
    <div class="base-msg pt-16 flex items-center">
      <div class="base-left flex items-center">
        <div class="avatar w-48 h-48 flex items-center justify-center">
          {{ item.patientInfo?.patientName.slice(0, 1) }}
        </div>
      </div>
      <div class="base-right ml-12">
        <div class="top">
          <span>{{ item.patientInfo?.patientName }}</span>
          <el-divider direction="vertical" />
          <span>{{ item.patientInfo?.patientSex === 1 ? '男' : '女' }}</span>
          <el-divider direction="vertical" border-style="dashed" />
          <span>{{ item.patientInfo?.patientAge }}岁</span>
        </div>
        <div class="bottom mt-8 flex items-center justify-between">
          <div class="bottom-left">
            {{
              item.sourceTime
                ? dayjs(item.sourceTime).format('YYYY-MM-DD')
                : '--'
            }}
            <span class="ml-8">{{ getSourceType(item.taskType) }}</span>
          </div>
          <div class="bottom-right">
            {{ formatTime(item.createTime) }}
            {{ item.generatedName || '--' }}发起
          </div>
        </div>
      </div>
    </div>
    <div class="btns pt-8 flex flex items-center justify-between mt-12">
      <div class="btns-left flex items-center">
        <div
          class="task-status w-50 h-24 center"
          :class="{
            'btns-primary': item.taskStatus === 0,
            'btns-success':
              item.taskStatus === 1 ||
              item.taskStatus === 2 ||
              item.taskStatus === 3 ||
              item.taskStatus === 5,
            'btns-warning': item.taskStatus === 6,
          }"
        >
          {{ getTaskStatus(item.taskStatus) }}
        </div>
        <span class="ml-8">{{ getTips(item) }}</span>
      </div>
      <div class="handle-btns flex items-center">
        <div
          v-if="item.taskMethod === 0 && item.taskStatus === 0"
          class="cancel-task center w-80 h-32 mr-8 cursor-pointer"
          @click="handleCancelTask(item)"
        >
          撤销任务
        </div>
        <div
          v-if="item.taskStatus !== 5"
          class="query-details center w-80 h-32 cursor-pointer"
          @click="handleDetailsTask(item)"
        >
          查看详情
        </div>
        <div
          v-if="item.taskStatus === 5"
          class="query-details center w-80 h-32 cursor-pointer"
          @click="handleDetailsTask(item)"
        >
          前往审核
        </div>
      </div>
    </div>
    <div class="task-type w-72 h-32 flex items-center justify-center">
      {{ item.taskMethod === 0 ? '转录任务' : '批量OCR' }}
    </div>
  </div>
</template>
<script setup lang="ts">
import { getTaskStatus, getSourceType } from '../config';
import { useComponentsTabAction } from '@/store/module/useComponentsTabAction';
const tabAction = useComponentsTabAction();
import useGlobal from '@/store/module/useGlobal';
const globalStore = useGlobal();
import bus from '@/lib/bus';
import { quashTaskApi } from '@/api/task';
import { formatTime } from '@/utils/index';
import dayjs from 'dayjs';
import { getTips } from '../config';
import useInternDrawer from '@/store/module/useInternDrawer';
import { getKeysByValue } from '@/features/OcrAuditResult/hooks';
const { setVisible, setBaseInfo } = useInternDrawer();
const props = defineProps({
  form: {
    type: Object,
    default: () => {},
  },
});
const item = ref(props.form);

// 撤销任务
const handleCancelTask = (item: any) => {
  ElMessageBox.confirm(
    '<div class="ml-36">是否确认撤回转录任务?</div><div class="ml-36">转录任务撤回后，实习生端将不可继续进行转录</div>',
    '提示',
    {
      dangerouslyUseHTMLString: true,
    }
  )
    .then(() => {
      quashTaskApi({ taskId: item.taskId }).then(res => {
        const { code } = res;
        if (code === 'E000000') {
          ElMessage({
            type: 'success',
            message: '操作成功!',
          });
          bus.emit('updata-task-refresh');
        }
      });
    })
    .catch(() => {});
};

// 查看详情
const handleDetailsTask = (item: any) => {
  globalStore.setUserId(item.patientInfo.patientId);

  const time = item.sourceTime
    ? dayjs(item.sourceTime).format('YYYY-MM-DD')
    : '--';
  const taskType = getSourceType(item.taskType);
  const title = `${time} ${taskType}`;

  if (globalStore.currentRole === 4) {
    const {
      patientInfo,
      taskId,
      sourceId,
      taskType,
      sourceTime,
      caseId,
      taskSection,
    } = item;
    setBaseInfo({
      patientId: patientInfo.patientId,
      taskId: sourceId,
      sourceId,
      sourceType: taskType,
      sourceTime,
      modules: getKeysByValue(taskType, 'number'),
      caseId,
      submitSourceId: taskId,
      internSubmitBatchId: sourceId,
      internModules: JSON.parse(taskSection),
    });
    setVisible(true);
  } else {
    tabAction.setAction({
      componentType: item.taskType,
      name: title,
      data: {
        id: item.sourceId,
        taskInfo: {
          taskMethod: item.taskMethod,
          taskId: item.taskId,
        },
        recordActionType: {
          actionType: 'add',
          sourceType: item.taskType,
        },
      },
    });
    bus.emit('open-component-tab');
  }
};
</script>
<style lang="less" scoped>
.box {
  flex: 1;
  position: relative;
}
.base-msg {
  .base-left {
    .avatar {
      background: #ffffff;
      border: 2px solid #efefef;
      border-radius: 50%;
      font-weight: bold;
      font-size: 26px;
      color: #2e6be6;
    }
  }
  .base-right {
    flex: 1;
    .top {
      span {
        font-size: 14px;
        color: #3a4762;
      }
    }
    .bottom {
      .bottom-left {
        font-size: 14px;
        color: #3a4762;
        font-weight: bold;
      }
      .bottom-right {
        font-size: 14px;
        color: #7a8599;
      }
    }
  }
}
.task-type {
  position: absolute;
  right: -16px;
  top: 0;
  background: #e6eeff;
  border-radius: 0px 4px 0px 6px;
  font-size: 14px;
  color: #2e6be6;
  font-weight: bold;
}
.btns {
  border-top: 1px solid #e1e5ed;
  .task-status {
    font-size: 14px;
  }
  .btns-primary {
    color: #2e6be6;
    background: #e6eeff;
  }
  .btns-success {
    color: #2fb324;
    background: #e2f5e1;
  }
  .btns-warning {
    color: #e63746;
    background: #ffe6e7;
  }
  .btns-left {
    font-size: 14px;
    color: #7a8599;
  }
  .center {
    display: flex;
    justify-content: center;
    border-radius: 2px;
    align-items: center;
  }
  .handle-btns {
    .cancel-task {
      background: #ffffff;
      border: 1px solid #e37221;
      font-size: 14px;
      color: #e37221;
    }
    .query-details {
      background: #2e6be6;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>
