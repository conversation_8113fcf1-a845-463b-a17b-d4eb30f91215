import { taskType } from './type';
import { formatTime } from '@/utils/index';
// 任务类型
export const TASK_TYPE: taskType[] = [
  {
    label: '全部',
    value: -1,
  },
  {
    label: '转录任务',
    value: 0,
  },
  {
    label: '批量OCR',
    value: 1,
  },
];

// 任务状态
export const TASK_STATUS: taskType[] = [
  {
    label: '全部',
    value: -1,
    type: [0, 1, 4],
  },
  {
    label: '待接单',
    value: 0,
    type: [0],
  },
  {
    label: '已接单',
    value: 1,
    type: [0],
  },
  {
    label: '处理中',
    value: 3,
    type: [0, 1, 4],
  },
  {
    label: '已提交',
    value: 5,
    type: [0, 1, 4],
  },
  {
    label: '已驳回',
    value: 6,
    type: [0, 1],
  },
];
export function getTaskStatus(status: number) {
  const lable =
    status === 2
      ? '已验收'
      : TASK_STATUS.find((item: taskType) => item.value === status)?.label ||
        '';
  return lable;
}

// 获取来源类型
const SOURCE_TYPE: taskType[] = [
  {
    label: '住院',
    value: 0,
  },
  {
    label: '门诊',
    value: 1,
  },
  {
    label: '复查',
    value: 2,
  },
  {
    label: '入组',
    value: 3,
  },
];
export function getSourceType(type: number) {
  return SOURCE_TYPE.find((item: taskType) => item.value === type)?.label || '';
}

// 获取tips展示
export const getTips = (item: {
  taskStatus?: number;
  internName?: string;
  statusTime?: number;
  taskMethod?: number;
  imageCount?: number;
  generatedName?: string;
}) => {
  const {
    taskStatus,
    internName,
    statusTime,
    taskMethod,
    imageCount,
    generatedName,
  } = item;
  let str = '';
  const time = formatTime(statusTime as number);
  // 已接单
  if (taskStatus === 1) {
    str = `${internName} ${time} 接单`;
  }
  // 已完成
  if (taskStatus === 2 && taskMethod === 0) {
    str = `${generatedName} ${time} 验收通过`;
  }
  // 处理中
  if (taskStatus === 3) {
    // 转录任务
    if (taskMethod === 0) {
      str = `${internName} ${time} 处理，等待提交`;
    }
    // 批量OCR
    if (taskMethod === 1) {
      str = imageCount ? `（${imageCount}张图片）` : '';
    }
  }
  // 已提交
  if (taskStatus === 5) {
    const text = `${taskMethod === 1 ? generatedName : internName} ${time} 提交`;
    // 转录任务
    if (taskMethod === 0) {
      str = text;
    }
    // 批量OCR
    if (taskMethod === 1) {
      const showImgTips = imageCount ? `（${imageCount}张图片）` : '';
      str = `${showImgTips}${text}`;
    }
  }
  // 已驳回
  if (taskStatus === 6 && taskMethod === 0) {
    str = `${generatedName} ${time} 驳回，${internName}转录中。`;
  }
  return str;
};
