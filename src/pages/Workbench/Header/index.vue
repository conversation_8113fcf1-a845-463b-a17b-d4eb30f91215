<template>
  <div
    class="h-60 flex bg-[#203549] text-white items-center pl-16 shadow flex-shrink-0 justify-between"
    @click="clickHeaderHandler"
  >
    <div class="flex">
      <img class="w-36 h-36 rounded-full" :src="logo" alt="" />
      <div class="text-[20px] ml-12 font-semibold leading-9">
        {{ getRoleName }}工作台
      </div>
    </div>
    <div class="right flex pr-16 items-center">
      <!-- 登录呼叫中心 -->
      <div v-if="globalStore.currentRole !== 4">
        <CallCenter />
      </div>

      <!-- 任务验收 -->
      <div class="ml-32">
        <el-badge :value="taskNumber" :max="99" :show-zero="false">
          <img
            class="w-20 h-20 cursor"
            :src="taskAcceptance"
            alt=""
            @click="showTaskcenter()"
          />
        </el-badge>
      </div>

      <!-- AI推荐聊天回复 -->
      <div v-if="globalStore.currentRole === 1" class="ml-32">
        <ChatReply />
      </div>

      <!-- 切换角色 -->
      <div v-permission="{ exclude: true, val: RoleEnum[4] }">
        <img class="w-16 h-16 ml-32 cursor" :src="switchRoles" alt="" />
      </div>

      <div
        v-permission="{ exclude: true, val: RoleEnum[4] }"
        @click="showReminderHandler"
      >
        <img class="w-20 h-20 ml-32 cursor" :src="openReminder" alt="" />
      </div>

      <!-- 消息提醒 -->
      <div
        v-permission="{ exclude: true, val: RoleEnum[4] }"
        class="msg-warn"
        @click="showMessageCenterHandler"
      >
        <el-badge v-if="msgNumber" :value="msgNumber" :max="99" class="item">
          <img class="w-16 h-16 cursor ms-trigger" :src="notification" alt="" />
        </el-badge>
        <img
          v-else
          class="w-16 h-16 cursor ms-trigger"
          :src="notification"
          alt=""
        />
      </div>

      <!-- 用户信息 -->
      <div
        class="user-msg flex items-center cursor pr-32 ml-32 operation-popup"
      >
        <div class="flex" @click="isShowOprate = !isShowOprate">
          <img
            class="w-20 h-20 rounded-full operation-popup"
            :src="userAvatar"
            alt=""
          />
          <div class="userName ml-8 flex items-center operation-popup">
            {{ userName }}
            <div class="triangle-down ml-8 mt-4"></div>
          </div>
        </div>
        <div
          v-if="isShowOprate"
          class="user-operate flex items-center flex-col justify-between"
        >
          <template v-for="(item, index) in operateList" :key="index">
            <div
              v-if="getRecordType(item.id)"
              class="flex items-center cursor item-operate mb-12"
              @click.stop="operate(item.id)"
            >
              <img :src="item.img" alt="" class="w-16 h-16" />
              <span :style="{ color: item.color }" class="title">
                {{ item.title }}
              </span>
            </div>
          </template>
          <div v-if="isShowBangCode" class="bind-code-box">
            <div v-if="isBand" class="w-160 untie" @click.stop="unitWechat">
              解绑微信
            </div>
            <div v-else class="w-160 h-160">
              <img :src="imgPath" alt="" class="w-160 h-160" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改密码 -->
    <el-dialog
      v-model="dialogVisible"
      width="400px"
      class="edit-password"
      @close="closeDialog"
    >
      <div class="dialog-mian">
        <div class="header flex items-center">修改密码</div>
        <div class="mian flex flex-col justify-between">
          <div class="flex items-center justify-between mb-12">
            <div class="mian-title">旧密码</div>
            <el-input
              v-model.number="form.oldPassword"
              placeholder="请输入旧密码"
              clearable
              oninput="value = value.replace(/\s+/g,'')"
              minlength="8"
            />
          </div>
          <div class="flex items-center justify-between mb-12">
            <div class="mian-title">新密码</div>
            <el-input
              v-model.number="form.newPassword"
              placeholder="请输入新密码"
              clearable
              minlength="8"
              oninput="value = value.replace(/\s+/g,'')"
            />
          </div>
          <div class="flex items-center justify-between">
            <div class="mian-title">确认新密码</div>
            <el-input
              v-model.number="form.sureNewPassword"
              placeholder="请再次输入新密码"
              clearable
              minlength="8"
              oninput="value = value.replace(/\s+/g,'')"
            />
          </div>
        </div>
        <div class="footer flex items-center justify-end">
          <div
            class="cancel flex items-center justify-center"
            @click="closeDialog"
          >
            取消
          </div>
          <div
            class="sure flex items-center justify-center"
            @click="savePassword"
          >
            确定
          </div>
        </div>
      </div>
    </el-dialog>
    <MessageCenter
      v-if="globalStore.currentRole !== 4"
      :visible="messageCenterVisible"
      :reset-num="resetNum"
      @close="messageCenterVisible = false"
    />
    <Notification v-if="globalStore.currentRole !== 4" :reset-num="resetNum" />
  </div>

  <Dialog v-model:visible="patientVisibles" :width="842" title="患者转出">
    <div class="patient-evacuation">
      <div class="evacuation-msg flex">
        <div class="msg-title">申请变更时间：</div>
        <div class="msg-content">{{ applyChangeTime }}</div>
      </div>
      <div class="evacuation-msg flex mt-12">
        <div class="msg-title">变更原因：</div>
        <div class="msg-content">{{ applyChangeReason }}</div>
      </div>
      <div class="evacuation-msg mt-12">
        <div class="msg-title">变更详情：</div>
        <div class="mt-8">
          <el-table
            :data="tableData"
            style="width: 100%"
            class="evacuation-table"
          >
            <el-table-column prop="patientName" label="患者姓名" width="150" />
            <el-table-column prop="sourceName" label="转出工作室" />
            <el-table-column prop="targetName" label="转入工作室" />
          </el-table>
        </div>
      </div>
    </div>
  </Dialog>
  <!--  提醒事项-->
  <ReminderDrawer v-model:reminderVisible="reminderVisible" />

  <!-- 任务验收 -->
  <TaskAcceptanceDrawer v-model:taskAcceptanceVisible="taskAcceptanceVisible" />
</template>
<script setup lang="ts">
import logo from '@/assets/imgs/logo.png';
import userAvatar from '@/assets/imgs/indexHeader/user-avatar.png';
import notification from '@/assets/imgs/indexHeader/notification.png';
import switchRoles from '@/assets/imgs/indexHeader/switch-roles.png';
import taskAcceptance from '@/assets/imgs/indexHeader/task-acceptance.png';
import openReminder from '@/assets/imgs/indexHeader/reminder-icon.png';
import changePassword from '@/assets/imgs/indexHeader/change-password.png';
import recordInformation from '@/assets/imgs/indexHeader/record-information.png';
import codeImg from '@/assets/imgs/indexHeader/code.png';
import loginOut from '@/assets/imgs/indexHeader/login-out.png';
import Dialog from '@/components/Dialog/index.vue';
import { readMessage } from '@/api/message';
import router from '@/router';
import CallCenter from '@/components/CallCenter/index.vue';
import ReminderDrawer from '@/components/ReminderDrawer/index.vue';
import TaskAcceptanceDrawer from './components/TaskAcceptanceDrawer/index.vue';
import {
  editPassword,
  queryCheckBindApi,
  unbindWechatApi,
  getQrCodeApi,
  getPatientGroupChangeApi,
} from '@/api/login';
import { getMessageNum } from '@/api/message';
import MessageCenter from './components/MessageCenter/index.vue';
import Notification from './components/Notification/index.vue';
import ChatReply from './components/ChatReply/index.vue';
import bus from '@/lib/bus';
import { debounce } from 'lodash-es';
import RoleEnum from '@/constant/role';
import useGlobal from '@/store/module/useGlobal';
import useDoubt from '@/store/module/useDoubt';
import useUserStore from '@/store/module/useUserStore';
import { waitAuditNumApi } from '@/api/task';

bus.on('close-operation-popup', () => {
  isShowOprate.value = false;
  isShowBangCode.value = false;
});

const userStore = useUserStore();
const userName = computed(() => userStore.userName);
// 患者转出
const patientVisibles = ref(false);
const tableData = ref([]);
const applyChangeTime = ref('');
const applyChangeReason = ref('');
// 获取患者工作室变更信息
bus.on('get-patient-Group', (val: any) => {
  patientVisibles.value = true;
  getPatientGroupChangeApi({
    businessId: val.sourceId,
  }).then((res: any) => {
    let {
      patientName,
      sourceGroupName,
      targetGroupName,
      applyTime,
      changeReason,
      sourceHospitalName,
      targetHospitalName,
    } = res.data;
    let arr: any = [];
    let hospitalSourceName = sourceHospitalName
      ? '(' + sourceHospitalName + ')'
      : '';
    let sourceName = sourceGroupName + hospitalSourceName;
    let hospitalTargetName = targetHospitalName
      ? '(' + targetHospitalName + ')'
      : '';
    let targetName = targetGroupName + hospitalTargetName;
    arr.push({
      patientName,
      sourceName,
      targetName,
    });
    applyChangeTime.value = timestampToDateString(applyTime);
    applyChangeReason.value = changeReason;
    tableData.value = arr;
  });
});

const timestampToDateString = (timestamp: any) => {
  // 创建一个Date对象
  const date = new Date(timestamp);

  // 获取年、月、日、时、分、秒
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以要加1，并使用padStart填充0
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  // 拼接字符串
  const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return formattedDate;
};

const globalStore = useGlobal();
const doubtStore = useDoubt();
// 获取登录角色名
const getRoleName = computed(() => {
  let roleName = '';
  const userRoles = userStore.userRoles;
  const currentRole = userRoles?.[0];
  // 医生：ASSISTANT, 健康管理师：CUSTOMER_SERVER, 运动康复师：REHAB, 实习生：INTERN,
  if (currentRole === 'CUSTOMER_SERVER') {
    roleName = '健康管理师';
  } else if (currentRole === 'REHAB') {
    roleName = '运动康复师';
  } else if (currentRole === 'INTERN') {
    roleName = '哈瑞特医学实习生';
  } else {
    roleName = '医生';
  }
  return roleName;
});

// 是否显示备案信息和绑定微信二维码
const getRecordType = computed(() => {
  return function (id: number) {
    const userRoles = userStore.userRoles;
    const currentRole = userRoles?.[0];
    if (id === 2 || id === 4) {
      if (currentRole === 'ASSISTANT') {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  };
});

// 消息条数
const msgNumber = ref<number>(0);

// 绑定二维码
const isShowBangCode = ref<boolean>(false);
const isBand = ref(false);
const imgPath = ref('');
// 查询是否绑定微信
const getBandStatus = async () => {
  await queryCheckBindApi().then((res: any) => {
    if (res.code === 'E000000') {
      if (!res.data.bind) {
        getQrCodeApi().then((resule: any) => {
          if (resule.code === 'E000000') {
            imgPath.value = resule.data.qrCode;
          }
        });
      }
      isBand.value = res.data.bind;
    }
  });
};
// 解绑微信
const unitWechat = () => {
  unbindWechatApi().then((res: any) => {
    if (res.code === 'E000000') {
      ElMessage({
        message: '解绑微信成功！',
        type: 'success',
      });
    }
  });
};

// 是否显示操作列表
const isShowOprate = ref<boolean>(false);
// 操作列表
const operateList = reactive([
  {
    title: '修改密码',
    color: '#3A4762',
    img: changePassword,
    id: 1,
  },
  {
    title: '备案信息',
    color: '#3A4762',
    img: recordInformation,
    id: 2,
  },
  {
    title: '绑定微信',
    color: '#3A4762',
    img: codeImg,
    id: 4,
  },
  {
    title: '退出登录',
    color: '#E63746',
    img: loginOut,
    id: 3,
  },
]);

const messageCenterVisible = ref(false);

const reminderVisible = ref(false);

const emit = defineEmits(['information']);
// 操作事件
const operate = (id: number) => {
  // 修改密码
  if (id === 1) {
    dialogVisible.value = true;
    isShowOprate.value = false;
  }

  // 备案信息
  if (id === 2) {
    isShowOprate.value = false;
    emit('information');
    isShowOprate.value = false;
  }

  // 退出登录
  if (id === 3) {
    ElMessageBox.confirm('是否要退出当前系统?', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        isShowOprate.value = false;
        await userStore.logout();
      })
      .catch(() => {
        isShowOprate.value = false;
      });
  }

  // 绑定二维码
  if (id === 4) {
    isShowBangCode.value = !isShowBangCode.value;
    getBandStatus();
  }

  if (id === 100) {
    //todo download
  }
};

// 修改密码弹窗
const dialogVisible = ref(false);
// 修改密码数据展示
let form = reactive({
  oldPassword: '',
  newPassword: '',
  sureNewPassword: '',
});
// 修改密码关闭弹窗回调
const closeDialog = () => {
  isShowOprate.value = false;
  dialogVisible.value = false;
  form.oldPassword = '';
  form.newPassword = '';
  form.sureNewPassword = '';
};
// 确定修改密码
const savePassword = () => {
  const { oldPassword, newPassword, sureNewPassword } = form;
  if (!oldPassword || !newPassword || !sureNewPassword) {
    warnMsg('请填写完整！', '');
  } else if (oldPassword === newPassword) {
    warnMsg('新密码和旧密码不能一样！', '');
  } else if (!validatePassword(newPassword)) {
    warnMsg(
      '密码格式错误，正确应为：8-15位数的大小写英文、数字及特殊符号任意三种！',
      ''
    );
  } else if (newPassword !== sureNewPassword) {
    warnMsg('两次新密码不一致！', '');
  } else {
    editOldPassword();
  }
};
// 修改密码
const editOldPassword = () => {
  editPassword({
    oldPassword: form.oldPassword,
    password: form.newPassword,
  }).then((res: any) => {
    if (res.code === 'E000000') {
      warnMsg('修改密码成功,即将退出重新登录！', 'success');
      closeDialog();
      localStorage.clear();
      sessionStorage.clear();
      setTimeout(() => {
        router.push('/login');
      }, 1000);
    } else {
      warnMsg(res.message, '');
    }
  });
};

// 正则验证密码输入规则
const validatePassword = (password: string) => {
  const regex =
    /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{8,15}$/;
  return regex.test(password);
};

// 提醒消息
const warnMsg = (msg: string, style: string) => {
  const obj = {
    showClose: true,
    message: msg || '请检查是否完成！',
    type: style || 'error',
  };
  const newObj = obj as any;
  ElMessage(newObj);
};
const resetNum = debounce(() => {
  getMessageNumHandler();
}, 100);
const getMessageNumHandler = async () => {
  const res = await getMessageNum();
  msgNumber.value = res ?? 0;
};
const closeDoubtDrawer = () => {
  globalStore.isDoubting = false;
  doubtStore.reset();
};
const showMessageCenterHandler = () => {
  messageCenterVisible.value = !messageCenterVisible.value;
  closeDoubtDrawer();
};

const showTaskcenter = () => {
  taskAcceptanceVisible.value = true;
  closeDoubtDrawer();
};
const showReminderHandler = () => {
  reminderVisible.value = !reminderVisible.value;
  closeDoubtDrawer();
};

const clickHeaderHandler = () => {
  bus.emit('notification-message-clear');
};

// 任务验收
const taskAcceptanceVisible = ref<boolean>(false);
const isUpdataTaskNumber = ref(true);
const taskNumber = ref<number>(0);
const getWaitAuditNum = () => {
  waitAuditNumApi().then(res => {
    taskNumber.value = (res.data as number) || 0;
    isUpdataTaskNumber.value = true;
  });
};
bus.on('batch-ocr-success-refresh', () => {
  isUpdataTaskNumber.value = false;
  getWaitAuditNum();
});
const TIME = 1000 * 60 * 3;
setInterval(() => {
  if (isUpdataTaskNumber.value) getWaitAuditNum();
}, TIME);

onMounted(() => {
  if (globalStore.currentRole !== 4) {
    getMessageNumHandler();
  } else {
    /*
      const newOperateList = operateList.filter(v => [1, 3].includes(v.id));
      newOperateList.push({
        title: '操作手册',
        color: '#3A4762',
        img: 'changePassword',
        id: 100,
      });
      operateList = newOperateList;
    */
  }
  bus.on('close-operation-popup', () => {
    isShowOprate.value = false;
    isShowBangCode.value = false;
  });

  bus.on('clear-message', (data: any) => {
    const { sourceId, msgType } = data ?? {};
    if (!sourceId || !msgType) {
      ElMessage.error('群聊编号和消息类型不能为空！');
    }
    readMessage({ sourceId, messageType: msgType });
  });

  getWaitAuditNum();
});
onUnmounted(() => {
  bus.off('clear-message');
  bus.off('close-operation-popup');
});
</script>

<script lang="ts">
export default {
  name: 'WorkBenchHeader',
};
</script>

<style scoped lang="less">
:deep(.el-badge__content) {
  background: #e63746;
  font-size: 12px;
  border: none;
}
.msg-warn {
  margin-left: 32px;
  :deep(.el-badge__content) {
    top: -5px;
    right: 8px;
  }
}
.cursor {
  cursor: pointer;
}
.user-msg {
  position: relative;
  .user-operate {
    position: absolute;
    width: 120px;
    background: #ffffff;
    border-radius: 2px;
    top: 24px;
    right: 20px;
    padding: 12px;
    z-index: 3010;
    padding-bottom: 0;
    box-sizing: border-box;
    .bind-code-box {
      background: #ffffff;
      box-shadow: 0px 4px 12px 0px rgba(8, 38, 99, 0.2);
      border-radius: 4px;
      position: absolute;
      top: 43px;
      right: 150px;
      z-index: 99;
      color: #606266;
      .untie {
        padding: 12px;
      }
    }
    .item-operate {
      width: 100%;
    }
    .title {
      font-size: 14px;
      margin-left: 8px;
    }
  }
  .userName {
    font-size: 14px;
    color: #ffffff;
  }
  .triangle-down {
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #fff;
  }
}
:deep(.edit-password) {
  background: #ffffff;
  box-shadow: 0 2px 24px 0 rgba(200, 201, 204, 0.5);
  border-radius: 4px;
  .el-dialog__header,
  .el-dialog__body {
    padding: 0;
  }
  .el-dialog__headerbtn {
    top: -4px;
    right: -6px;
  }
  .dialog-mian {
    .header {
      height: 46px;
      border-bottom: 1px solid #e9e8eb;
      padding-left: 24px;
      box-sizing: border-box;
      font-size: 16px;
      font-weight: 600;
      color: #15233f;
    }
    .mian {
      padding: 24px 36px;
      box-sizing: border-box;
      .mian-title {
        min-width: 70px;
        font-size: 14px;
        color: #15233f;
        text-align: right;
        white-space: nowrap;
        margin-right: 16px;
      }
      .el-input {
        height: 32px;
        background: #ffffff;
        border-radius: 2px;
      }
    }
    .footer {
      border-top: 1px solid #e9e8eb;
      padding: 16px 0;
      .cancel {
        width: 74px;
        height: 32px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #dcdee0;
        font-size: 14px;
        color: #323233;
        box-sizing: border-box;
        cursor: pointer;
      }
      .sure {
        width: 74px;
        height: 32px;
        background: #2e6be6;
        border-radius: 2px;
        font-size: 14px;
        color: #ffffff;
        margin-left: 8px;
        margin-right: 24px;
        cursor: pointer;
      }
    }
  }
}
.patient-evacuation {
  font-size: 14px;
  padding: 24px;
  .msg-title {
    color: #708293;
    width: 100px;
    margin-right: 8px;
  }
  .msg-content {
    color: #3a4762;
  }
  :deep(.evacuation-table) {
    .el-table__header {
      thead {
        .el-table__cell {
          background: #f7f8fa;
          box-shadow: 0px 1px 0px 0px #ebedf0;
          font-weight: bold;
          font-size: 14px;
          color: #15233f;
        }
      }
    }
  }
}
</style>
