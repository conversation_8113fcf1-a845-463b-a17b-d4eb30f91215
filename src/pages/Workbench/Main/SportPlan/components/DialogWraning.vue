<template>
  <Dialog v-model:visible="tipsDialogVisibles" title="" class="tips-dialog">
    <div class="tips">
      <img src="@/assets/imgs/overview/icon-del.png" alt="" class="untieImg" />
      {{ title }}
    </div>
    <div class="desc">
      {{ desc }}
    </div>
    <template #footer>
      <div class="btn-box">
        <div class="cancel-btn" @click="choseDialog">取消</div>
        <div class="sure-btn" @click="emit('sureBtn')">确定</div>
      </div>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import Dialog from '@/components/Dialog/index.vue';
interface IProps {
  tipsDialogVisible?: boolean | false;
  title?: string;
  desc?: string;
}
const props = defineProps<IProps>();
const tipsDialogVisibles = ref(props.tipsDialogVisible);

const emit = defineEmits(['update:tipsDialogVisible', 'sureBtn']);
const choseDialog = () => {
  emit('update:tipsDialogVisible', false);
};
watch(
  () => tipsDialogVisibles.value,
  () => {
    if (tipsDialogVisibles.value === false) {
      choseDialog();
    }
  }
);
</script>
<style lang="less">
.tips-dialog {
  .tips {
    font-size: 16px;
    font-weight: bold;
    color: #111111;
    display: flex;
    align-items: center;
    padding-top: 40px;
    padding-left: 24px;

    .untieImg {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
  .desc {
    padding: 12px 25px 0 50px;
    color: #7a8599;
    white-space: pre-line;
  }
  .btn-box {
    border: none;
    padding-top: 0;
  }
}
</style>
