<template>
  <div>
    <el-radio-group v-model="radioValues" :class="{ readonly: readonly }">
      <el-radio
        v-for="ite in radioList"
        :key="ite"
        :value="ite.value"
        :label="ite.value"
        :disabled="readonly"
        @change="handleCheckedCitiesChange"
      >
        <div class="flex items-center">
          <img class="w-14 h-18 mr-6" :src="ite.icon" alt="" />
          {{ ite.value }}
        </div>
      </el-radio>
    </el-radio-group>
  </div>
</template>

<script setup lang="ts">
interface IProps {
  radioList: any;
  radioValue?: any;
  readonly?: boolean;
}
const props = defineProps<IProps>();
const emit = defineEmits(['update:radioValue', 'changeValue']);
const radioValues = ref(props.radioValue);
const handleCheckedCitiesChange = val => {
  emit('update:radioValue', radioValues.value);
  emit('changeValue', radioValues.value);
};
onMounted(() => {});
watch(
  () => props.radioValue,
  () => {
    radioValues.value = props.radioValue;
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<script lang="ts">
export default {
  name: 'RadioGroup',
};
</script>
<style scoped lang="less">
:deep(.el-radio-group) {
  .el-radio {
    height: 20px;
  }
  .el-radio__input {
    width: 14px;
    height: 14px;
    font-size: 14px;
    color: #323233;
  }
  .el-radio__label {
    padding-left: 8px;
  }

  //.is-checked;
  .el-radio__input.is-checked {
    .el-radio__inner {
      background-color: #fff;
      &::after {
        transform: translate(-50%, -50%) scale(2);
        background-color: #0a73e4;
      }
    }
  }
}
:deep(.readonly) {
  .el-radio {
    display: none !important;
  }
  .el-radio.is-checked {
    display: flex !important;
  }
  .el-radio__input.is-checked {
    width: 8px !important;
    height: 8px !important;
    border-radius: 50%;
    .el-radio__inner {
      width: 8px !important;
      height: 8px !important;
      background: #2e6be6 !important;
      border-radius: 50% !important;
      &::after {
        display: none !important;
      }
    }
  }
}
</style>
