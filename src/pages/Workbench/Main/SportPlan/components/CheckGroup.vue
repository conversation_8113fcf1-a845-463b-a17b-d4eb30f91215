<template>
  <div>
    <el-checkbox-group v-model="checkValues" :class="{ readonly: readonly }">
      <el-checkbox
        v-for="ite in checkList"
        :key="ite"
        :value="ite"
        :label="ite"
        :disabled="readonly"
        @change="handleCheckedCitiesChange"
        >{{ ite }}</el-checkbox
      >
    </el-checkbox-group>
  </div>
</template>

<script setup lang="ts">
interface IProps {
  checkList: any;
  checkValue?: any;
  readonly?: boolean;
}
const props = defineProps<IProps>();
const emit = defineEmits(['update:checkValue', 'changeValue']);
const checkValues = ref(props.checkValue);
const handleCheckedCitiesChange = (val, e) => {
  emit('update:checkValue', checkValues.value);
  emit('changeValue', checkValues.value);
};
onMounted(() => {});
watch(
  () => props.checkValue,
  () => {
    checkValues.value = props.checkValue;
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<script lang="ts">
export default {
  name: 'CheckGroup',
};
</script>
<style scoped lang="less">
:deep(.el-checkbox-group) {
  .el-checkbox {
    height: 20px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
  }
  .el-checkbox__input {
    width: 16px;
    height: 16px;
    border-radius: 2px;
  }
  .el-checkbox__inner {
    width: 16px;
    height: 16px;
    border-radius: 2px;
  }
  .el-checkbox__label {
    padding-left: 8px;
    color: #3a4762;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner::after {
    left: 5px;
    border-width: 2px;
  }
}
:deep(.readonly) {
  .el-checkbox {
    display: none !important;
  }
  .el-checkbox.is-checked {
    display: flex !important;
  }
  .el-checkbox__input.is-checked {
    width: 8px !important;
    height: 8px !important;
    border-radius: 50%;
    .el-checkbox__inner {
      width: 8px !important;
      height: 8px !important;
      background: #2e6be6 !important;
      border-radius: 50% !important;
      &::after {
        display: none !important;
      }
    }
  }
}
</style>
