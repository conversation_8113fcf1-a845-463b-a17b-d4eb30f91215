<template>
  <CardWrapper title="变更历史" class="change-history-wrapper">
    <div v-for="(item, i) in historyList" :key="i" class="record-item">
      <div class="date flex items-center justify-between">
        <div>
          <span class="mr-60">
            评估时间：{{
              item.evaluationTime
                ? dayjs(item.evaluationTime).format('YYYY-MM-DD')
                : '--'
            }}
          </span>
          <span>评估人：{{ item.rehabName || '--' }}</span>
        </div>
      </div>
      <div class="result-info">
        <div class="flex-1 flex mb-7">
          评估结论：
          <span :class="{ two: item.conclusion }">
            {{ item.conclusion || '--' }}
          </span>
        </div>
        <div class="flex-1 flex mb-7">
          评估建议：
          <div class="flex-1">
            <span :class="{ two: item.suggestion }">
              {{ item.suggestion || '--' }}
            </span>
            <span v-if="item.suggestion">
              {{ resultDesc(item) }}
            </span>
          </div>
        </div>
        <div v-if="item.rehabName === '系统评估'" class="flex-1 flex text mb-7">
          变更内容：
          <div class="flex-1">{{ item.changes }}</div>
        </div>
        <div v-else class="flex-1 flex text mb-7">
          变更内容：
          <div
            v-if="
              (item.changes && JSON.parse(item.changes)?.length > 0) ||
              item.notes
            "
            class="flex-1"
          >
            <span v-for="(ite, idx) in JSON.parse(item.changes)" :key="idx">
              {{ idx + 1 }}.{{ ite.name }}：{{ ite.changeText }}
            </span>
            <span v-if="item.notes">
              {{ JSON.parse(item.changes).length + 1 }}.备注：{{ item.notes }}
            </span>
          </div>
          <div v-else>--</div>
        </div>
      </div>
    </div>
    <div v-if="historyList.length == 0" class="nodata">暂无数据</div>
  </CardWrapper>
</template>

<script setup lang="ts">
import CardWrapper from '@/components/CardWrapper/index.vue';
import dayjs from 'dayjs';
interface IProps {
  historyList?: any;
  type: number; //1运动状态 2运动风险
}
const props = defineProps<IProps>();
const resultDesc = computed(() => {
  return function (val) {
    let textArr = [];
    if (props.type == 1) {
      if (
        val.absoluteContraindication &&
        JSON.parse(val.relativeContraindication).length
      ) {
        textArr.push(
          `${JSON.parse(val.absoluteContraindication).length}项绝对禁忌症`
        );
      }
      if (
        val.relativeContraindication &&
        JSON.parse(val.relativeContraindication).length
      ) {
        textArr.push(
          `${JSON.parse(val.relativeContraindication).length}项相对禁忌症`
        );
      }
    }
    if (props.type == 2) {
      if (val.lowRisk && JSON.parse(val.lowRisk).length) {
        textArr.push(`${JSON.parse(val.lowRisk).length}项低危`);
      }
      if (val.moderateRisk && JSON.parse(val.moderateRisk).length) {
        textArr.push(`${JSON.parse(val.moderateRisk).length}项中危`);
      }
      if (val.highRisk && JSON.parse(val.highRisk).length) {
        textArr.push(`${JSON.parse(val.highRisk).length}项高危`);
      }
      return textArr.length > 0 ? `(患者存在${textArr.join('；')})` : '';
    }
    return textArr.length > 0 ? `(患者存在${textArr.join('；')})` : '';
  };
});
onMounted(() => {});
</script>
<script lang="ts">
export default {
  name: 'ChangeHistory',
};
</script>
<style scoped lang="less"></style>
<style lang="less">
.nodata {
  color: #cfcfcf;
}
.change-history-wrapper {
  .record-item {
    width: 100%;
    background: #f7f8fa;
    border-radius: 4px;
    margin-bottom: 12px;
    .date {
      height: 44px;
      border-bottom: 1px solid #e9e8eb;
      margin: 0 16px;
      font-size: 14px;
      color: #7a8599;
    }
    .result-info {
      font-size: 14px;
      color: #7a8599;
      padding: 16px 16px 8px 16px;
      line-height: 20px;
      .two {
        color: #e37221;
        font-weight: bold;
      }
      .one {
        color: #e63746;
        font-weight: bold;
      }
      .text span {
        color: #101b25;
        display: block;
        margin-bottom: 7px;
        &:last-of-type {
          margin: 0;
        }
      }
    }
  }
}
</style>
