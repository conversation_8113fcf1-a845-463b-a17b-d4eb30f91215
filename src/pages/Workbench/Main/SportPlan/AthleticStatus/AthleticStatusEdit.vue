<template>
  <div class="edit-box" :class="{ read: readOnly }">
    <div class="mb-6">
      <div class="line"></div>
      <div class="label-til">
        绝对禁忌症<span>满足任意一项即不建议运动</span>
      </div>
      <div
        v-if="
          readOnly && athleticStatusFrom.absoluteContraindication?.length === 0
        "
      >
        --
      </div>
      <CheckGroup
        v-else
        v-model:check-value="athleticStatusFrom.absoluteContraindication"
        :check-list="checkList"
        :readonly="readOnly"
      />
    </div>
    <div class="mb-6">
      <div class="line"></div>
      <div class="label-til">
        相对禁忌症<span>满足任意一项即不建议运动</span>
      </div>
      <div
        v-if="
          readOnly && athleticStatusFrom.relativeContraindication?.length === 0
        "
      >
        --
      </div>
      <CheckGroup
        v-else
        v-model:check-value="athleticStatusFrom.relativeContraindication"
        :check-list="checkList2"
        :readonly="readOnly"
      />
    </div>
    <div class="pb-16">
      <div class="line"></div>
      <div class="label-til">备注</div>
      <div>
        <span v-if="readOnly">{{ athleticStatusFrom.notes || '--' }}</span>
        <el-input
          v-else
          v-model="athleticStatusFrom.notes"
          type="textarea"
          placeholder="请输入其他信息"
          maxlength="300"
          show-word-limit
        />
      </div>
    </div>
  </div>
  <div v-if="!readOnly" class="affix">
    <div class="evaluation-recommendations p-16 flex items-center">
      <div class="flex-1">
        <div class="til flex">
          <div>
            评估建议：<span class="one">{{ resultJson.suggestion }}</span>
          </div>
          <!--          <div class="ml-25">评估结论：<span class="two">中危</span></div>-->
        </div>
        <div class="result">{{ resultJson.desc }}</div>
      </div>
      <div class="btn">
        <el-button
          :type="resultJson.status == 2 ? 'primary' : ''"
          @click="conclusionClick('加入运动管理', resultJson.status == 2)"
        >
          {{
            editInfo && globalData.manageStatus != 3
              ? '仅更新内容'
              : '可以参与管理'
          }}
        </el-button>
        <el-button
          :type="resultJson.status == 1 ? 'primary' : ''"
          @click="conclusionClick('结束管理', resultJson.status == 1)"
        >
          {{
            editInfo && globalData.manageStatus != 3
              ? '结束管理'
              : '暂不参与管理'
          }}
        </el-button>
      </div>
    </div>
  </div>
  <DialogWraning
    v-if="tipsDialogVisible"
    v-model:tipsDialogVisible="tipsDialogVisible"
    :title="diolagTipJson.title"
    :desc="diolagTipJson.tip"
    @sure-btn="submit"
  />
</template>

<script setup lang="ts">
import CheckGroup from '../components/CheckGroup.vue';
import store from '@/store';
import DialogWraning from '@/pages/Workbench/Main/SportPlan/components/DialogWraning.vue';
import { cloneDeep } from 'lodash-es';
import { getRehabManageEditMotion } from '@/api/exerciseRehabilitation';
import globalBus from '@/lib/bus';

interface IProps {
  readOnly?: boolean | false;
  editInfo?: object;
}
const props = defineProps<IProps>();
const tipsDialogVisible = ref(false);
const globalData = store.useGlobal();
const athleticStatusFrom = reactive({
  absoluteContraindication: [],
  relativeContraindication: [],
  notes: '',
  conclusion: '',
});
const cloneFrom = ref(cloneDeep(athleticStatusFrom));
const checkList = ref([
  '近期静息心电图变化提示有显著缺血、急性心肌梗死或其他急性心脏事件',
  '未控制的心律失常',
  '不稳定型心绞痛',
  '症状严重的主动脉狭窄或其他瓣膜病',
  '心力衰竭失代偿',
  '急性肺栓塞或肺梗死',
  '急性心肌炎或心包炎',
  '急性非心源性疾病，影响运动的完成或运动可使其加重（例如：感染、甲状腺功能亢进）',
  '急性血栓性静脉炎',
  '残疾，妨碍安全和准确测试',
]);
const checkList2 = ref([
  '电解质异常',
  '快速性心律失常或缓慢性心律失常',
  '高度房室传导阻滞',
  '房颤且心室率未得到控制',
  '梗阻性肥厚型心肌病，静息最大左室流出道压差大于25mmHg',
  '已知的主动脉夹层',
  '严重的静息时高血压（收缩压>180mmHg和舒张压>90mmHg）',
]);
const emit = defineEmits(['changeTabIndex', 'update:readOnly']);

const conclusionClick = (conclusion, flag) => {
  if (!flag && !athleticStatusFrom.notes) {
    ElMessage.warning('当前结论与建议不一致，请填写备注！');
    return false;
  }
  tipsDialogVisible.value = true;
  athleticStatusFrom.conclusion = conclusion;
};
const submit = () => {
  if (!globalData.userId) {
    ElMessage.warning('当前没有选中患者!');
    return false;
  }
  let changes = resultModel();
  let params = {
    absoluteContraindication: JSON.stringify(
      athleticStatusFrom.absoluteContraindication
    ),
    relativeContraindication: JSON.stringify(
      athleticStatusFrom.relativeContraindication
    ),
    conclusion: athleticStatusFrom.conclusion,
    notes: athleticStatusFrom.notes,
    suggestion: resultJson.value.suggestion,
    changes: changes ? JSON.stringify(changes) : '',
    patientId: globalData.userId,
  };
  getRehabManageEditMotion(params)
    .then(() => {
      ElMessage.success('保存成功!');
      tipsDialogVisible.value = false;
      globalBus.emit('refresh-patient-list');
      if (!props.editInfo && athleticStatusFrom.conclusion == '加入运动管理') {
        // 更新状态为管理中
        globalData.manageStatus = 2;
        //   显示风险评估
        emit('changeTabIndex', 2);
      } else {
        // 更新状态为结束管理
        globalData.manageStatus = 3;
        emit('update:readOnly', true);
      }
    })
    .catch(err => {
      tipsDialogVisible.value = false;
      ElMessage.error(`保存失败:${err.msg}`);
    });
};
const resultModel = () => {
  let changeArr = [];
  let absoluteContraindicationChange = calculateTheDifference(
    cloneFrom.value.absoluteContraindication,
    athleticStatusFrom.absoluteContraindication
  );
  let relativeContraindicationChange = calculateTheDifference(
    cloneFrom.value.relativeContraindication,
    athleticStatusFrom.relativeContraindication
  );
  if (absoluteContraindicationChange) {
    changeArr.push({
      name: '绝对禁忌症',
      changeText: absoluteContraindicationChange,
    });
  }
  if (relativeContraindicationChange) {
    changeArr.push({
      name: '相对禁忌症',
      changeText: relativeContraindicationChange,
    });
  }
  // changeArr.push({ name: '备注', changeText: athleticStatusFrom.notes });

  return changeArr;
};
const calculateTheDifference = (oldArr, newArr) => {
  let decreaseArr = oldArr.filter(re => !newArr.includes(re));
  let addArr = newArr.filter(re => !oldArr.includes(re));
  let decreaseText = '';
  let addText = '';
  for (let i = 0; i < decreaseArr.length; i++) {
    decreaseText += '取消' + decreaseArr[i] + '勾选；';
  }
  for (let i = 0; i < addArr.length; i++) {
    addText += '勾选' + addArr[i] + '；';
  }
  return decreaseText + addText;
};
const resultJson = computed(() => {
  const status =
    athleticStatusFrom.absoluteContraindication.length +
      athleticStatusFrom.relativeContraindication.length >
    0
      ? 1
      : 2;

  return {
    status,
    suggestion: status == 1 ? '不建议加入运动管理' : '建议加入运动管理',
    desc: `患者存在${athleticStatusFrom.absoluteContraindication.length}项绝对禁忌症；
          ${athleticStatusFrom.relativeContraindication.length}项相对禁忌症`,
  };
});
const diolagTipJson = computed(() => {
  let title = '';
  let tip = '';
  if (athleticStatusFrom.conclusion == '加入运动管理') {
    title = '是否继续进入风险评估，并将患者加入运动管理？';
    let arr = resultModel().map(v => {
      let text = v.name + '：' + v.changeText;
      return text;
    });
    tip = arr.join('\n');
  }
  if (athleticStatusFrom.conclusion == '结束管理') {
    title = `确定该患者${
      props.editInfo && globalData.manageStatus != 3 ? '结束' : '不加入'
    }运动管理吗？`;
    tip = `选择${
      props.editInfo && globalData.manageStatus != 3
        ? '结束管理'
        : '暂不参与管理'
    }，即表示不再对患者进行运动管理，患者管理状态为“结束管理”；当患者身体适合管理时，您可以手动变更患者管理状态`;
  }
  if (props.editInfo && athleticStatusFrom.conclusion == '加入运动管理') {
    let arr = resultModel().map(v => {
      let text = v.name + '：' + v.changeText;
      return text;
    });
    title =
      arr && arr.length > 0
        ? '是否继续更新评估内容？'
        : '本次无勾选项变更，是否继续更新评估内容？';
    tip = arr.join('\n');
  }
  return {
    title,
    tip,
  };
});
const init = () => {
  if (props.editInfo) {
    athleticStatusFrom.absoluteContraindication = props.editInfo
      .absoluteContraindication
      ? JSON.parse(props.editInfo.absoluteContraindication)
      : [];
    athleticStatusFrom.relativeContraindication = props.editInfo
      .relativeContraindication
      ? JSON.parse(props.editInfo.relativeContraindication)
      : [];
    athleticStatusFrom.notes = props.editInfo.notes;
    athleticStatusFrom.conclusion = props.editInfo.conclusion;
  }
  cloneFrom.value = cloneDeep(athleticStatusFrom);
};
onMounted(() => {
  init();
});
watch(
  () => props.editInfo,
  () => {
    init();
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<script lang="ts">
export default {
  name: 'AthleticStatusEdit',
};
</script>
<style scoped lang="less">
.line {
  border-bottom: 1px solid #e9e8eb;
}
.edit-box {
  padding: 0 16px;
  color: #3a4762;
  .label-til {
    padding: 16px 0 12px 0;
    color: #101b25;
    font-weight: bold;
    > span {
      color: #e63746;
      font-weight: normal;
      margin-left: 8px;
    }
  }
  .el-checkbox-group {
    .el-checkbox {
      height: 20px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
    }
    .el-checkbox__input {
      width: 16px;
      height: 16px;
      border-radius: 2px;
    }
    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      border-radius: 2px;
    }
    .el-checkbox__label {
      padding-left: 8px;
      color: #3a4762;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner::after {
      left: 5px;
      border-width: 2px;
    }
  }
  .el-textarea__inner {
    width: 100%;
    min-height: 64px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #b8becc;
    box-shadow: none;
    color: #3a4762;
    &::placeholder {
      color: #c8c9cc;
    }
  }
}
.evaluation-recommendations {
  width: 100%;
  min-height: 78px;
  background: #ffffff;
  box-shadow: 0px -1px 2px 0px rgba(186, 200, 212, 0.5);
  border-radius: 0 0 6px 6px;
  box-sizing: border-box;
  .til {
    color: #101b25;

    span.one {
      color: #e63746;
    }
    span.two {
      color: #e37221;
    }
  }
  .result {
    color: #7a8599;
  }
}
.edit-box.read {
  .line {
    display: none;
  }
}
</style>
<style lang="less">
.tips-dialog {
  .tips {
    font-size: 16px;
    font-weight: bold;
    color: #111111;
    display: flex;
    align-items: center;
    padding-top: 40px;
    padding-left: 24px;

    .untieImg {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
  .desc {
    padding: 12px 25px 0 50px;
    color: #7a8599;
  }
  .btn-box {
    border: none;
    padding-top: 0;
  }
}
</style>
