<template>
  <RiskAssessmentDetail
    v-if="readOnly"
    v-model:readOnly="readOnly"
    :history-list="historyList"
  />
  <RiskAssessmentEdit
    v-else
    v-model:readOnly="readOnly"
    :edit-info="editInfo"
    @change-tab-index="changeTabIndex"
  />
</template>

<script setup lang="ts">
import store from '@/store';
import RiskAssessmentEdit from './RiskAssessmentEdit.vue';
import RiskAssessmentDetail from './RiskAssessmentDetail.vue';
import { getRehabManageRiskHistory } from '@/api/exerciseRehabilitation';

const readOnly = ref(false);
const title = ref('请从以下不确定条件中判断患者是否适合运动管理：');
const globalData = store.useGlobal();
const historyList = ref([]);
const editInfo = ref();
const emit = defineEmits(['changeTabIndex']);

const changeTabIndex = index => {
  emit('changeTabIndex', index);
};
const getHistory = () => {
  readOnly.value = !!globalData.manageStatus && globalData.manageStatus != 1;
  if (!globalData.userId || !readOnly.value) {
    historyList.value = [];
    editInfo.value = null;
    return false;
  }
  getRehabManageRiskHistory({ patientId: globalData.userId || 100300 })
    .then(res => {
      res.reverse();
      historyList.value = res;
      editInfo.value = res[0];
      readOnly.value = res?.length > 0;
    })
    .catch(() => {
      historyList.value = [];
      editInfo.value = null;
    });
};
onMounted(() => {
  title.value = `请从以下不确定条件中判断患者【${
    globalData.userInfo.name || '--'
  }】是否适合运动管理`;
  getHistory();
});
watch(
  () => globalData.manageStatus,
  () => {
    getHistory();
  },
  {
    immediate: true,
  }
);
watch(
  () => globalData.userId,
  () => {
    getHistory();
  },
  {
    immediate: true,
  }
);
watch(
  () => readOnly.value,
  val => {
    if (val) {
      getHistory();
    }
  },
  {
    immediate: true,
  }
);
</script>
<script lang="ts">
export default {
  name: 'RiskAssessment',
};
</script>
<style scoped lang="less"></style>
<style lang="less"></style>
