<template>
  <CardWrapper
    :title="titles"
    class="risk-assessment-wrapper"
    :class="{ edit: !readOnly }"
  >
    <div class="edit-box" :class="{ read: readOnly }">
      <div>
        <div class="header">
          <span>风险项</span>
          <span>风险等级</span>
        </div>
        <div
          v-for="item in riskConfig"
          :key="item.label"
          class="column"
          :class="{ readonly: readOnly }"
        >
          <div class="label">{{ item.label }}</div>
          <div class="value">
            <el-radio-group v-model="athleticStatusFrom.risks[item.key]">
              <el-radio
                v-for="(v, i) in item.options"
                :key="v.value"
                :value="v.value"
                :label="v.value"
                :disabled="v.value === -1"
                @click="() => radioClickHandler(item.key, v.value)"
              >
                <span :class="riskLevelMap[i].class">
                  （{{ riskLevelMap[i].label }}）
                </span>
                <span class="value_label">{{ v.label }}</span>
              </el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div class="mb-6">
        <div class="line"></div>
        <div class="label-til">评估结论</div>
        <div v-if="readOnly && !athleticStatusFrom.conclusion">--</div>
        <RadioGroup
          v-else
          v-model:radioValue="athleticStatusFrom.conclusion"
          :radio-list="resultList"
          :readonly="readOnly"
        />
      </div>
      <div class="pb-16">
        <div class="label-til">备注</div>
        <div>
          <span v-if="readOnly">{{ athleticStatusFrom.notes || '--' }}</span>
          <el-input
            v-else
            v-model="athleticStatusFrom.notes"
            type="textarea"
            placeholder="请输入其他信息"
            maxlength="300"
            show-word-limit
          />
        </div>
      </div>
    </div>
    <div v-if="!readOnly" class="affix">
      <div class="evaluation-recommendations p-16 flex items-center">
        <div class="flex-1">
          <div class="til flex">
            <div>
              评估建议：
              <span :class="statusClassMap[resultJson.level]">
                {{ resultJson.suggestion || '未知' }}
              </span>
            </div>
            <div class="ml-25">
              评估结论：
              <span :class="statusClassMap[athleticStatusFrom.conclusion]">
                {{ athleticStatusFrom.conclusion || '--' }}
              </span>
            </div>
          </div>
          <div class="result">{{ resultJson.desc }}</div>
        </div>
        <div class="btn">
          <el-button
            @click="
              editInfo
                ? emit('update:readOnly', true)
                : emit('changeTabIndex', 1)
            "
          >
            {{ editInfo ? '取消' : '上一步' }}
          </el-button>
          <el-button type="primary" @click="conclusionClick">
            {{ editInfo ? '确定' : '完成风险评估' }}
          </el-button>
        </div>
      </div>
    </div>
    <DialogWraning
      v-if="tipsDialogVisible"
      v-model:tipsDialogVisible="tipsDialogVisible"
      :title="diolagTipJson.title"
      :desc="diolagTipJson.tip"
      @sure-btn="submit"
    />
  </CardWrapper>
</template>

<script setup lang="ts">
import iconSport1 from '@/assets/imgs/sportPlan/icon-sport-level1.png';
import iconSport2 from '@/assets/imgs/sportPlan/icon-sport-level2.png';
import iconSport3 from '@/assets/imgs/sportPlan/icon-sport-level3.png';
import CardWrapper from '@/components/CardWrapper/index.vue';
import store from '@/store';
import RadioGroup from '@/pages/Workbench/Main/SportPlan/components/RadioGroup.vue';
import DialogWraning from '@/pages/Workbench/Main/SportPlan/components/DialogWraning.vue';
import { cloneDeep } from 'lodash-es';
import { getRehabManageEditRisk } from '@/api/exerciseRehabilitation';
import globalBus from '@/lib/bus';
import { riskConfig, riskLevelMap } from './config';
const title = ref('运动风险评估');
const tipsDialogVisible = ref(false);
const globalData = store.useGlobal();

interface IProps {
  readOnly?: boolean | false;
  editInfo?: object;
}
const props = defineProps<IProps>();
const emit = defineEmits(['changeTabIndex', 'update:readOnly']);
const athleticStatusFrom = reactive({
  risks: {
    arhythmia: '', // 心律失常
    revascularization: '', // 再血管化后并发症
    mentalBlock: '', // 心理障碍
    lvef: '', // 左心室射血分数
    oxygenLimit: '', // 峰值摄氧量 [ml/（min •kg）］
    oxygenLimitPercent: '', // 峰值摄氧量百分 预计值（%pred）
    at: '', // AT [ml/（min •kg）
    cardiacTroponinConcentration: '', // 心肌肌钙蛋白浓度
    sportChange: '', // 运动或恢复期症状及心电图改变
    sportEffect: '', // 运动测试中血流动力学反应
    isMyocardialIschemia: '', // 是否有心肌缺血症状
    pci: '', // PCI
  },
  conclusion: '',
  notes: '',
});
const cloneFrom = ref(cloneDeep(athleticStatusFrom));
const resultList = ref([
  { value: '低危', icon: iconSport1 },
  { value: '中危', icon: iconSport2 },
  { value: '高危', icon: iconSport3 },
]);
const statusClassMap = { 低危: 'three', 中危: 'two', 高危: 'one' };

const radioClickHandler = (key: string, itemVal: number) => {
  const val = athleticStatusFrom.risks[key];
  if (val === itemVal) {
    setTimeout(() => {
      athleticStatusFrom.risks[key] = '';
    });
  }
};
const conclusionClick = () => {
  if (
    athleticStatusFrom.conclusion != resultJson.value.level &&
    !athleticStatusFrom.notes
  ) {
    ElMessage.warning('当前结论与建议不一致，请填写备注！');
    return false;
  }
  tipsDialogVisible.value = true;
};

const submit = () => {
  if (!globalData.userId) {
    ElMessage.warning('当前没有选中患者!');
    return false;
  }
  const changes = resultModel();
  const params = {
    estimateContent: JSON.stringify(athleticStatusFrom.risks),
    conclusion: athleticStatusFrom.conclusion,
    notes: athleticStatusFrom.notes,
    suggestion: resultJson.value.suggestion,
    changes: changes ? JSON.stringify(changes) : '',
    patientId: globalData.userId,
  };
  getRehabManageEditRisk(params)
    .then(() => {
      ElMessage.success('保存成功!');
      tipsDialogVisible.value = false;
      emit('update:readOnly', true);
      globalBus.emit('refresh-patient-list');
    })
    .catch(err => {
      tipsDialogVisible.value = false;
      ElMessage.error(`保存失败:${err.msg}`);
    });
};
const titles = computed(() => {
  return props.readOnly ? '评估详情' : title.value;
});
const resultModel = () => {
  let changeArr: any[] = [];
  const riskChanges = calculateTheDifference(
    cloneFrom.value.risks,
    athleticStatusFrom.risks
  );
  Object.keys(riskChanges).forEach((v: any) => {
    if (riskChanges[v]) {
      changeArr.push({
        name: riskLevelMap[v - 1].label,
        changeText: riskChanges[v],
      });
    }
  });
  return changeArr;
};
const getText = (key, val) => {
  return riskConfig.find(v => v.key === key)?.options.find(v => v.value === val)
    ?.label;
};
const calculateTheDifference = (oldObj, newObj) => {
  const keys = Object.keys(newObj);
  const result = { 1: '', 2: '', 3: '' };
  keys.forEach(key => {
    const oldVal = oldObj[key];
    const newVal = newObj[key];
    if (oldVal !== newVal) {
      if (oldVal && oldVal !== -1) {
        result[oldVal] += '取消' + getText(key, oldVal) + '勾选；';
      } else if (newVal && newVal !== -1) {
        result[newVal] += '勾选' + getText(key, newVal) + '；';
      }
    }
  });
  return result;
};

const getLevelLength = (values, n) => {
  return values.filter(v => v === n).length;
};
const resultJson = computed(() => {
  const risks = athleticStatusFrom.risks;
  const values = Object.values(risks) as any as number[];
  const maxValue = Math.max(...values);
  const getLen = n => getLevelLength(values, n);
  const level = riskLevelMap[maxValue - 1]?.label ?? '';
  return {
    level,
    suggestion: level ? `运动${level}患者` : '',
    desc: `患者存在${getLen(1)}项低危；${getLen(2)}项中危，${getLen(3)}项高危`,
  };
});
const diolagTipJson = computed(() => {
  let title = '确定完成风险评估吗？';
  let tip = athleticStatusFrom.conclusion
    ? `风险评定结论：${athleticStatusFrom.conclusion}`
    : '';
  // let arr = resultModel().map(v => {
  //   let text = v.name + '：' + v.changeText;
  //   return text;
  // });
  // tip = tip + '\n' + arr.join('\n');
  return {
    title,
    tip,
  };
});
const init = () => {
  if (props.editInfo) {
    const { estimateContent = '{}', notes, conclusion } = props.editInfo as any;
    athleticStatusFrom.risks = JSON.parse(estimateContent ?? '{}');
    athleticStatusFrom.notes = notes;
    athleticStatusFrom.conclusion = conclusion;
  }
  cloneFrom.value = cloneDeep(athleticStatusFrom);
};
onMounted(() => {
  init();
});
watch(
  () => props.editInfo,
  () => {
    init();
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<script lang="ts">
export default {
  name: 'RiskAssessment',
};
</script>
<style scoped lang="less">
.risk-assessment-wrapper {
  .line {
    border-bottom: 1px solid #e9e8eb;
  }
  :deep(.edit-box) {
    padding: 0 16px;
    .label-til {
      padding: 16px 0 12px 0;
      color: #101b25;
      font-weight: bold;
      > span {
        color: #e63746;
        font-weight: normal;
        margin-left: 8px;
      }
    }
    .el-checkbox-group {
      .el-checkbox {
        height: 20px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
      }
      .el-checkbox__input {
        width: 16px;
        height: 16px;
        border-radius: 2px;
      }
      .el-checkbox__inner {
        width: 16px;
        height: 16px;
        border-radius: 2px;
      }
      .el-checkbox__label {
        padding-left: 8px;
        color: #3a4762;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner::after {
        left: 5px;
        border-width: 2px;
      }
    }
    .el-textarea__inner {
      width: 100%;
      min-height: 64px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #b8becc;
      box-shadow: none;
      color: #3a4762;
      &::placeholder {
        color: #c8c9cc;
      }
    }
  }

  .evaluation-recommendations {
    width: 100%;
    min-height: 78px;
    background: #ffffff;
    box-shadow: 0px -1px 2px 0px rgba(186, 200, 212, 0.5);
    border-radius: 0 0 6px 6px;
    box-sizing: border-box;
    .til {
      color: #101b25;

      span.one {
        color: #e63746;
      }
      span.two {
        color: #e37221;
      }
      span.three {
        color: #2fb324;
      }
    }
    .result {
      color: #7a8599;
    }
  }
  .edit-box.read {
    .line {
      display: none;
    }
  }
  .header {
    width: 100%;
    height: 52px;
    background: #f7f8fa;
    box-shadow: 0px 1px 0px 0px #ebedf0;
    > span {
      display: inline-block;
      font-weight: bold;
      font-size: 14px;
      color: #15233f;
      height: 52px;
      line-height: 52px;
      &:first-child {
        padding-left: 12px;
        width: 180px;
      }
    }
  }
  .column {
    min-height: 52px;
    box-shadow: 0px 1px 0px 0px #ebedf0;
    display: flex;
    padding: 4px 0;
    .label {
      display: flex;
      flex-shrink: 0;
      width: 200px;
      font-weight: bold;
      font-size: 14px;
      color: #3a4762;
      align-items: center;
      padding: 0 24px 0 12px;
    }
    .value {
      display: flex;
      :deep(.el-radio) {
        width: 100%;
      }
      :deep(.el-radio) {
        min-height: 32px;
        height: unset;
      }
      :deep(.el-radio__label) {
        display: flex;
        align-items: center;
      }
    }
    .high {
      color: #e63746;
    }
    .middle {
      color: #e37221;
    }
    .low {
      color: #2fb324;
    }
    .value_label {
      display: inline-block;
      white-space: wrap;
      min-width: 400px;
      width: calc(100vw - 1300px);
      line-height: 24px;
    }
    &.readonly {
      :deep(.el-radio) {
        display: none !important;
        &.is-checked {
          display: flex !important;
        }
        .el-radio__input.is-checked {
          width: 8px !important;
          height: 8px !important;
          border-radius: 50%;
          .el-radio__original {
            display: none !important;
          }
          .el-radio__inner {
            width: 8px !important;
            height: 8px !important;
            background: #2e6be6 !important;
            border-radius: 50% !important;
            &::after {
              display: none !important;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.risk-assessment-wrapper {
  > div {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
  }
}
.tips-dialog {
  .tips {
    font-size: 16px;
    font-weight: bold;
    color: #111111;
    display: flex;
    align-items: center;
    padding-top: 40px;
    padding-left: 24px;

    .untieImg {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
  .desc {
    padding: 12px 25px 0 50px;
    color: #7a8599;
  }
  .btn-box {
    border: none;
    padding-top: 0;
  }
}
</style>
