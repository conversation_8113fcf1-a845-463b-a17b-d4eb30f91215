import { ZRColor } from 'echarts/types/dist/shared';
import { LineSeriesOption } from 'echarts/charts';

export const lineEChartCommonOption = {
  tooltip: {
    trigger: 'axis',
  },
  grid: {
    top: 28,
    left: 34,
    right: 34,
    bottom: '40%',
  },
  yAxis: {
    axisLabel: {
      // formatter: function (value) {
      //   return value || '';
      // },
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
      },
    },
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisLabel: {
      color: '#708293',
      margin: 18,
    },
    axisLine: { show: false },
    axisTick: { show: false },
  },
  series: [
    {
      type: 'line',
      connectNulls: true,
      lineStyle: {
        color: '#0A73E4',
      },
    },
  ],
};

// 睡眠质量
export const lineMarkAreaSleepQuality = {
  label: {
    fontFamily: 'PingFang SC',
    fontWeight: 'bolder',
    fontStyle: 'italic',
    position: 'inside',
    opacity: '0.2',
    fontSize: 18,
  },
  itemStyle: { opacity: '0.06' },
  emphasis: { disabled: true },
  // 0-5分：睡眠质量很好
  // 6-10分：睡眠质量还行
  // 11-15分：睡眠质量一般
  // 16-21分：睡眠质量很差
  data: [
    [
      {
        name: '很好',
        yAxis: 0,
        itemStyle: {
          color: '#2DA641',
        },
        label: {
          color: '#2DA641',
        },
      },
      { yAxis: 5 },
    ],
    [
      {
        name: '较好',
        yAxis: 5.001,
        itemStyle: {
          color: '#0A73E4',
        },
        label: {
          color: '#0A73E4',
        },
      },
      { yAxis: 10 },
    ],
    [
      {
        name: '较差',
        yAxis: 10.001,
        itemStyle: {
          color: '#FF9546',
        },
        label: {
          color: '#FF9546',
        },
      },
      { yAxis: 15 },
    ],
    [
      {
        name: '很差',
        yAxis: 15.001,
        itemStyle: {
          color: '#DC0101',
        },
        label: {
          color: '#DC0101',
        },
      },
      {},
    ],
  ],
};

// 抑郁症
export const lineMarkAreaDepressedQuality = {
  label: {
    fontFamily: 'PingFang SC',
    fontWeight: 'bolder',
    fontStyle: 'italic',
    position: 'inside',
    opacity: '0.2',
    fontSize: 18,
  },
  itemStyle: { opacity: '0.06' },
  emphasis: { disabled: true },
  // 0-4分：没有抑郁症
  // 5-9分：可能有轻微抑郁
  // 10-14分：可能有中度抑郁
  // 15-19分：可能有中重度抑郁
  // 20-27分：可能有重度抑郁
  data: [
    [
      {
        name: '没有抑郁症',
        yAxis: 0,
        itemStyle: {
          color: '#2DA641',
        },
        label: {
          color: '#2DA641',
        },
      },
      { yAxis: 4 },
    ],
    [
      {
        name: '可能有轻微抑郁',
        yAxis: 4.001,
        itemStyle: {
          color: '#0A73E4',
        },
        label: {
          color: '#0A73E4',
        },
      },
      { yAxis: 9 },
    ],
    [
      {
        name: '可能有中度抑郁',
        yAxis: 9.001,
        itemStyle: {
          color: '#FF9546',
        },
        label: {
          color: '#FF9546',
        },
      },
      { yAxis: 14 },
    ],
    [
      {
        name: '可能有中重度抑郁',
        yAxis: 14.001,
        itemStyle: {
          color: '#DC0101',
        },
        label: {
          color: '#DC0101',
        },
      },
      { yAxis: 19 },
    ],
    [
      {
        name: '可能有重度抑郁',
        yAxis: 19.001,
        itemStyle: {
          color: '#DC0101',
          opacity: '0.16',
        },
        label: {
          color: '#DC0101',
        },
      },
      {},
    ],
  ],
};
//焦虑
export const lineMarkAreaAnxietyQuality = {
  label: {
    fontFamily: 'PingFang SC',
    fontWeight: 'bolder',
    fontStyle: 'italic',
    position: 'inside',
    opacity: '0.2',
    fontSize: 18,
  },
  itemStyle: { opacity: '0.06' },
  emphasis: { disabled: true },
  // 0-4分：没有抑郁症
  // 5-9分：可能有轻微抑郁
  // 10-14分：可能有中度抑郁
  // 15-19分：可能有中重度抑郁
  // 20-27分：可能有重度抑郁
  data: [
    [
      {
        name: '没有焦虑',
        yAxis: 0,
        itemStyle: {
          color: '#2DA641',
        },
        label: {
          color: '#2DA641',
        },
      },
      { yAxis: 4 },
    ],
    [
      {
        name: '可能有轻微焦虑',
        yAxis: 4.001,
        itemStyle: {
          color: '#0A73E4',
        },
        label: {
          color: '#0A73E4',
        },
      },
      { yAxis: 9 },
    ],
    [
      {
        name: '可能有中度焦虑',
        yAxis: 9.001,
        itemStyle: {
          color: '#FF9546',
        },
        label: {
          color: '#FF9546',
        },
      },
      { yAxis: 13 },
    ],
    [
      {
        name: '可能有中重度焦虑',
        yAxis: 13.001,
        itemStyle: {
          color: '#DC0101',
        },
        label: {
          color: '#DC0101',
        },
      },
      { yAxis: 18 },
    ],
    [
      {
        name: '可能有严重焦虑',
        yAxis: 18.001,
        itemStyle: {
          color: '#DC0101',
          opacity: '0.16',
        },
        label: {
          color: '#DC0101',
        },
      },
      {},
    ],
  ],
};

export function getStandardLineOptions({
  xAxisConfig = {},
  yAxisConfig = {},
  seriesConfig = [],
  extra = {},
}: any) {
  const { xAxis, yAxis, ...restLineOptions } = lineEChartCommonOption;
  return {
    ...restLineOptions,
    xAxis: {
      ...xAxis,
      ...xAxisConfig,
    },
    yAxis: {
      ...yAxis,
      ...yAxisConfig,
    },
    series: generateLineSeries(seriesConfig),
    ...extra,
  };
}
interface ILineSeriesOption extends LineSeriesOption {
  markLineData: {
    yAxis: string | number;
    name?: string | number;
    color?: ZRColor;
    labelPosition?: string;
  }[];
}
export const generateLineSeries = (series: Array<ILineSeriesOption>) => {
  if (!series?.length) return [];
  return series.map(item => {
    const {
      color = '#0A73E4',
      name,
      data = [],
      markArea,
      markLine,
      markLineData,
      ...otherObj
    } = item;
    return {
      name,
      data,
      type: 'line',
      connectNulls: true,
      lineStyle: { color },
      itemStyle: { color },
      markLine:
        markLine ??
        (markLineData && {
          emphasis: { disabled: true },
          symbol: 'none',
          data: markLineData?.map(mark => ({
            name: mark.name ?? name,
            yAxis: mark.yAxis,
            symbol: 'none',
            lineStyle: { color: mark.color ?? color },
            label: {
              padding: 12,
              fontSize: 12,
              color: mark.color ?? color,
              position: mark.labelPosition ?? 'end',
              formatter: params => params.name,
            },
          })),
        }),
      markArea,
      ...otherObj,
    };
  });
};
