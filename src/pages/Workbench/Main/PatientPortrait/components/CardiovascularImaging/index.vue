<!-- 心血管画像 -->
<template>
  <div>
    <PatientPortraitWrapper title="心血管画像">
      <template #reference>
        <div class="content-box">
          <div class="img-box">
            <img :src="heart" alt="" />
          </div>
          <div v-if="surgeryData.length" class="surgery-box">
            <el-scrollbar height="116px">
              <div
                v-for="(item, index) in surgeryData"
                :key="index"
                class="surgery-item"
              >
                <div class="label">
                  {{ dayjs(item.surgeryTime).format('YYYY-MM-DD') }}
                </div>
                <div>
                  <div
                    v-for="(segment, segIndex) in item.segmentData"
                    :key="segIndex"
                  >
                    <span style="margin-right: 12px">
                      {{ segment.location }}.{{ segment.segmentName
                      }}{{ segment.alias }}
                    </span>
                    <span>狭窄度：{{ segment.degree }} ；</span>
                    <span>支架个数：{{ segment.support }}</span>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
          <div v-if="!surgeryData.length" class="surgery-box">暂无手术数据</div>
        </div>
      </template>
    </PatientPortraitWrapper>
  </div>
</template>

<script setup lang="ts">
import { getPciHeartData } from '@/api/indicatorsReport';
import heart from '@/assets/imgs/patientImaging/heart.webp';
import useGlobal from '@/store/module/useGlobal';
import useTabs from '@/store/module/useTabs';
import PatientPortraitWrapper from '../PatientPortraitWrapper.vue';

import dayjs from 'dayjs';

const globalData = useGlobal();

const tabStore = useTabs();
const surgeryData = ref<any[]>([]);
const getData = () => {
  if (!globalData.userId) return;
  getPciHeartData({ patientId: globalData.userId as number }).then(data => {
    surgeryData.value = data ?? [];
  });
};

watch(
  () => globalData.userId,
  () => {
    getData();
  }
);

watch(
  () => tabStore.mainActiveTab,
  () => {
    if (tabStore.mainActiveTab === 3) {
      getData();
    }
  }
);
onMounted(() => {
  getData();
});
</script>

<style scoped lang="less">
.content-box {
  box-sizing: border-box;
  padding: 0 30px;
  margin-top: 28px;
  max-height: 116px;
  display: flex;
  .img-box {
    width: 114px;
  }
  .surgery-box {
    flex: 1;
    margin-left: 40px;
    .surgery-item {
      display: flex;
      span {
        font-size: 14px;
        color: #101b25;
      }
      .label {
        font-weight: bold;
        margin-right: 12px;
      }
    }
  }
}
</style>
