<template>
  <div class="summary">
    <div class="summary-title">{{ title }}</div>
    <div v-if="content?.length" class="summary-content">
      <div
        v-for="(item, index) of content"
        :key="index"
        class="summary-content-item"
      >
        <div>{{ item.time }}</div>
        <div>{{ item.desc }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: '--',
  },
  content: {
    type: Array<{ time: string; desc: string }>,
    default: () => [],
  },
});
</script>

<style scoped lang="less">
.summary {
  &-title {
    color: #3a4762;
    font-size: 16px;
    font-weight: 600;
  }

  &-content {
    color: #101b25;
    margin-top: 8px;
    font-size: 14px;
    padding: 6px 12px;
    background: #f7f8fa;
    border-radius: 2px;

    &-item {
      padding: 6px 0;
      line-height: 20px;
    }
  }
}
</style>
