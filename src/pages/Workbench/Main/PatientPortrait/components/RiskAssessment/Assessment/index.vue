<template>
  <div v-for="(item, index) of list" :key="index" v-loading="loading">
    <Summary :title="RISK_TYPE_MAP[item.subType]" :content="item.content" />
    <BaseChart
      type="line"
      style="height: 300px"
      :data-complete="!!item.chartsOption"
      :options="item.chartsOption"
    />
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import BaseChart from '@/components/BaseChart';
import Summary from './Summary.vue';
import { IAssessment, RISK_TYPE_MAP } from '../hooks';
import { getEchartsLineConfig, formatDescription } from '../hooks/utils';
defineOptions({ name: 'Assessment' });

const props = defineProps({
  data: {
    type: Object as PropType<IAssessment>,
    default: () => ({ type: 1, list: [] }),
  },
  loading: Boolean,
});

const list = computed(() => {
  const {
    data: { list, type },
  } = props;
  return list?.map(item => {
    const { data, content, subType } = item;
    return {
      subType,
      content: formatDescription(content, type),
      chartsOption: getEchartsLineConfig({
        data,
        type,
        subType,
      }),
    };
  });
});
</script>
<style lang="less" scoped>
.chart {
  height: 300px;
}
</style>
<style lang="less">
.assessment-prognosis-tooltip {
  font-size: 14px;

  .common-size {
    width: 260px;
    padding: 16px 24px;
    box-sizing: border-box;
  }

  .danger {
    color: #dc0101;
  }

  .sub-text {
    color: #8193a3;
  }

  .hidden {
    display: none !important;
  }

  .header {
    display: flex;
    background: #f7f8fa;
    div:nth-child(2) {
      border-left: 1px dashed #e0e0e0;
    }
  }

  .title-text {
    font-weight: 500;
    color: #111111;
    display: flex;
    align-items: center;

    &-point {
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #101b25;
      border-radius: 100%;
      margin-right: 6px;
    }

    &-count {
      color: #203549;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    max-height: 452px;
    overflow-y: auto;

    &-item {
      display: flex;
      color: #203549;

      &.content-item-only {
        .danger {
          color: inherit;
        }
        .content-item-diagnose {
          span {
            &.danger {
              color: #0a73e4;
              background: #ebf6ff;
            }
          }
        }
      }

      &-sub {
        padding-bottom: 0;
        &:nth-child(2) {
          color: #8193a3;
          border-left: 1px dashed #e0e0e0;
          .title-text {
            color: #8193a3;
          }
        }
      }

      &-img {
        padding-left: 5px;
      }
      &-danger,
      &-vertical,
      &-diagnose {
        display: flex;
        flex-wrap: wrap;
        span {
          display: inline-block;
          text-wrap: initial;
          word-break: break-all;
        }
      }
      &-vertical {
        flex-direction: column;
        & > div {
          padding-left: 12px;
          display: flex;
          flex-direction: column;
          span {
            flex: 1;
          }
        }
      }
      &-diagnose {
        span {
          margin: 4px 6px 4px 0;
          padding: 2px 6px;
          background: #ebf6ff;
          border-radius: 2px;
          font-size: 12px;
          color: #0a73e4;
          &.danger {
            color: #dc0101;
            background: #ffe7e7;
          }
          &.normal {
            color: #8193a3;
            background: #f0f0f0;
          }
        }
      }

      &:last-child {
        .content-item-sub {
          padding-bottom: 24px;
        }
      }
    }
  }
}
</style>
