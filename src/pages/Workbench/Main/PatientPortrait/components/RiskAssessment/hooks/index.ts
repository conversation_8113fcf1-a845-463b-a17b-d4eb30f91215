import {
  getAciRiskData,
  getAscvdRiskData,
  getBledRiskData,
  getChaRiskData,
  getDeathRiskData,
  getGraRiskData,
  getRiskLevel,
  getSynRiskData,
  IPatientId,
} from '@/api/riskAssessment';
import { reverse } from 'lodash-es';
import useGlobal from '@/store/module/useGlobal';

export const RISK_LEVEL = {
  3: 'danger',
  2: 'warning',
  1: 'success',
  0: 'info',
};

/** 风险子类 */
export const RISK_TYPE_MAP = {
  gra: '急性ACS风险',
  cha: '非瓣膜性房颤脑卒中风险',
  syn: '复杂ACS风险',
  aci: 'ACS出血风险',
  bled: '房颤抗凝出血风险',
  ascvd: 'ASCVD总体风险',
  death: '猝死风险',
};
export type IAssessmentSubType = keyof typeof RISK_TYPE_MAP;

interface IRiskReq {
  /** 视图 */
  view?: any[];
  /** 风险数据列表 */
  riskDataList?: any[];
}

/** 风险类型 1:预后 2:出血 3:ASCVD 4:猝死 */
export type IAssessmentType = 1 | 2 | 3 | 4;

/** 风险评估 */
export interface IAssessment {
  type: IAssessmentType;
  list: {
    data: any[];
    content: any[];
    subType: IAssessmentSubType;
  }[];
}

const riskReqMap: Record<
  IAssessmentType,
  {
    requests: (params: IPatientId) => Promise<IRiskReq>;
    subType: IAssessmentSubType;
  }[]
> = {
  1: [
    {
      requests: getGraRiskData,
      subType: 'gra',
    },
    {
      requests: getChaRiskData,
      subType: 'cha',
    },
    {
      requests: getSynRiskData,
      subType: 'syn',
    },
  ],
  2: [
    {
      requests: getAciRiskData,
      subType: 'aci',
    },
    {
      requests: getBledRiskData,
      subType: 'bled',
    },
  ],
  3: [
    {
      requests: getAscvdRiskData,
      subType: 'ascvd',
    },
  ],
  4: [
    {
      requests: getDeathRiskData,
      subType: 'death',
    },
  ],
};

export function useRiskAssess() {
  const loading = ref(false);
  const globalData = useGlobal();

  const riskTab = ref<
    { type: IAssessmentType; name: string; state: number; stateKey: string }[]
  >([
    { type: 1, name: '预后风险', state: 0, stateKey: 'grace' },
    { type: 2, name: '出血风险', state: 0, stateKey: 'bleed' },
    { type: 3, name: 'ASCVD总体风险', state: 0, stateKey: 'ascvd' },
    { type: 4, name: '猝死风险', state: 0, stateKey: 'death' },
  ]);

  const risk = ref<IAssessmentType>(riskTab.value[0].type);

  const assessmentData = ref<IAssessment>({
    type: 1,
    list: [
      {
        subType: 'gra',
        data: [],
        content: [],
      },
      {
        subType: 'cha',
        data: [],
        content: [],
      },
      {
        subType: 'syn',
        data: [],
        content: [],
      },
    ],
  });

  async function getData(type: IAssessmentType, patientId: number) {
    try {
      loading.value = true;
      const res = await Promise.all(
        riskReqMap[type]
          .map(({ requests }) => requests)
          .map(req => req({ patientId }))
      );
      let list: IAssessment['list'] = [];
      const subItems = riskReqMap[type].map(({ subType }) => subType);
      list = subItems.map((sub, index) => {
        const { view = [], riskDataList = [] } = res[index] || {};
        return {
          subType: sub,
          content: riskDataList,
          data: [3, 4].includes(type) ? reverse(view) : view,
        };
      });

      assessmentData.value = { type, list };
    } finally {
      loading.value = false;
    }
  }

  async function getRiskLevelData(patientId: number) {
    const riskLevel = (await getRiskLevel({ patientId })) || {};
    riskTab.value = riskTab.value.map(tab => {
      return {
        ...tab,
        state: riskLevel[tab.stateKey] || 0,
      };
    });
  }

  watch([risk, () => globalData.userId], ([type, patientId]) => {
    if (patientId) {
      getData(type, patientId);
    }
  });

  watch(
    () => globalData.userId,
    patientId => {
      if (patientId) {
        getData(risk.value, patientId);
        getRiskLevelData(patientId);
      }
    },
    { immediate: true }
  );

  return {
    risk,
    loading,
    riskTab,
    assessmentData,
  };
}
