<!-- 评估量表 -->
<template>
  <div class="content-box">
    <PatientPortraitWrapper title="生活质量评估">
      <template #reference>
        <div class="chart-wrapper">
          <BaseChart
            type="line"
            :options="psqiChartsOptions"
            :data-complete="!loading"
          />
        </div>
        <div>
          <div class="check-wrapper">
            <div
              class="check-item"
              v-for="item in checkLineTypeList"
              :key="item.label"
            >
              <div class="head">
                <img :src="item.url" alt="" />
                <div>{{ item.label }}</div>
              </div>
              <el-checkbox
                v-model="item.checked"
                label=""
                :true-value="true"
                :false-value="false"
                :disabled="item.disabled"
              />
            </div>
          </div>
        </div>
      </template>
    </PatientPortraitWrapper>
  </div>
</template>

<script setup lang="ts">
import PatientPortraitWrapper from '../PatientPortraitWrapper.vue';
import BaseChart from '@/components/BaseChart/BaseChart.vue';
import bpImg from '@/assets/imgs/patientImaging/bp.png';
import ghImg from '@/assets/imgs/patientImaging/gh.png';
import htImg from '@/assets/imgs/patientImaging/ht.png';
import mhImg from '@/assets/imgs/patientImaging/mh.png';
import pfImg from '@/assets/imgs/patientImaging/pf.png';
import reImg from '@/assets/imgs/patientImaging/re.png';
import rpImg from '@/assets/imgs/patientImaging/rp.png';
import sfImg from '@/assets/imgs/patientImaging/sf.png';
import vtImg from '@/assets/imgs/patientImaging/vt.png';

import { getStandardLineOptions } from '../../utils';

import { getQuestionnaireLifeQualityScore } from '@/api/questionnaire';

import { LineTypeItem } from './type';

import useGlobal from '@/store/module/useGlobal';

let psqiChartsOptions = ref<any>({});

const loading = ref(false);

const globalData = useGlobal();

const checkLineTypeList = reactive<LineTypeItem[]>([
  {
    label: '生理机能(PF)',
    url: pfImg,
    checked: true,
    disabled: false,
    color: '#EC6671',
    keys: 'physicalFunctioning',
    data: [],
  },
  {
    label: '生理职能(RP)',
    url: rpImg,
    checked: true,
    disabled: false,
    color: '#3E77E8',
    keys: 'rolePhysical',
    data: [],
  },
  {
    label: '躯体疼痛(BP)',
    url: bpImg,
    checked: true,
    disabled: false,
    color: '#E27F31',
    keys: 'bodilyPain',
    data: [],
  },
  {
    label: '情感职能(RE)',
    url: reImg,
    checked: false,
    disabled: false,
    color: '#54C14B',
    keys: 'roleEmotional',
    data: [],
  },
  {
    label: '精神健康(MH)',
    url: mhImg,
    checked: false,
    disabled: false,
    color: '#26A4AE',
    keys: 'mentalHealth',
    data: [],
  },
  {
    label: '健康变化(HT)',
    url: htImg,
    checked: false,
    disabled: false,
    color: '#9A59DA',
    keys: 'reportedHealthTransition',
    data: [],
  },
  {
    label: '一般健康状况(GH)',
    url: ghImg,
    checked: false,
    disabled: false,
    color: '#E58DE4',
    keys: 'generalHealth',
    data: [],
  },
  {
    label: '社会功能(SF)',
    url: sfImg,
    checked: false,
    disabled: false,
    color: '#A4D46E',
    keys: 'socialFunctioning',
    data: [],
  },
  {
    label: '精力(VT)',
    url: vtImg,
    checked: false,
    disabled: false,
    color: '#9A59DA',
    keys: 'vitality',
    data: [],
  },
]);

let xAxisData = reactive<string[]>([]);

const initPsqiData = seriesConfig => {
  psqiChartsOptions.value = getStandardLineOptions({
    xAxisConfig: {
      data: xAxisData,
    },
    seriesConfig: seriesConfig,
    extra: {
      dataZoom: [
        {
          start: 0,
          end: 100,
          fillerColor: 'rgba(24,72,238,0.1)',
          dataBackground: {
            areaStyle: {
              color: '#1848EE',
            },
          },
          textStyle: {
            color: 'rgba(255, 255, 255, 0)',
          },
        },
      ],
    },
  });
  loading.value = false;
};

const dealCheckLineStatus = () => {
  const res = checkLineTypeList.filter(item => item.checked);
  const noCheckList = checkLineTypeList.filter(item => !item.checked);
  if (res.length >= 3) {
    noCheckList.forEach(item => {
      item.disabled = true;
    });
  } else {
    checkLineTypeList.forEach(item => {
      item.disabled = false;
    });
  }
  const seriesData = res.map(item => {
    return {
      data: item.data,
      name: item.label,
      symbol: item.data.length > 1 ? 'none' : 'emptyCircle',
      color: item.color,
      lineStyle: {
        color: item.color,
      },
    };
  });
  initPsqiData(seriesData);
};

const getData = () => {
  if (!globalData.userId) return;
  loading.value = true;
  getQuestionnaireLifeQualityScore({
    patientId: globalData.userId as number,
  })
    .then(data => {
      checkLineTypeList.forEach(item => {
        if (item.keys === 'physicalFunctioning') {
          item.data = data.map(
            dataItem => dataItem.physicalFunctioning as unknown as number
          );
        }
        if (item.keys === 'rolePhysical') {
          item.data = data.map(
            dataItem => dataItem.rolePhysical as unknown as number
          );
        }
        if (item.keys === 'bodilyPain') {
          item.data = data.map(
            dataItem => dataItem.bodilyPain as unknown as number
          );
        }
        if (item.keys === 'generalHealth') {
          item.data = data.map(
            dataItem => dataItem.generalHealth as unknown as number
          );
        }
        if (item.keys === 'vitality') {
          item.data = data.map(
            dataItem => dataItem.vitality as unknown as number
          );
        }
        if (item.keys === 'socialFunctioning') {
          item.data = data.map(
            dataItem => dataItem.socialFunctioning as unknown as number
          );
        }
        if (item.keys === 'roleEmotional') {
          item.data = data.map(
            dataItem => dataItem.roleEmotional as unknown as number
          );
        }
        if (item.keys === 'mentalHealth') {
          item.data = data.map(
            dataItem => dataItem.mentalHealth as unknown as number
          );
        }
        if (item.keys === 'reportedHealthTransition') {
          item.data = data.map(
            dataItem => dataItem.reportedHealthTransition as unknown as number
          );
        }
      });
      xAxisData = data.map(dataItem => dataItem.startTime as string);
      dealCheckLineStatus();
    })
    .catch(() => {
      loading.value = false;
    });
};

watch(
  () => checkLineTypeList,
  () => {
    dealCheckLineStatus();
  },
  { deep: true }
);

watch(
  () => globalData.userId,
  () => {
    getData();
  }
);
onMounted(() => {
  getData();
});
</script>

<style scoped lang="less">
.chart-wrapper {
  height: 300px;
  padding-bottom: 30px;
}
.check-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  .check-item {
    display: flex;
    align-items: center;
    .head {
      display: flex;
      align-items: center;
      min-width: 160px;
      font-weight: 400;
      font-size: 14px;
      color: #3a4762;
    }
    img {
      width: 32px;
      margin-right: 8px;
    }
  }
}
</style>
