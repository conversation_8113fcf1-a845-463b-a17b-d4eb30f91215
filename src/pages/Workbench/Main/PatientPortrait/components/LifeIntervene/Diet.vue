<template>
  <div>
    <div class="diet-title">合理膳食</div>
    <div class="diet-content flex">
      <div class="pr-sm">
        <div
          v-for="(item, index) of category"
          :key="index"
          :title="item.value"
          class="diet-content-category"
        >
          <span class="line-clamp-2">{{ item.name }}</span>
        </div>
      </div>
      <div class="diet-content-table">
        <el-table :data="category" border>
          <el-table-column v-for="item of showList" :key="item.id" width="150">
            <template #default="scope">
              <div class="checked-box flex-c">
                <template v-for="sub of item.dietList">
                  <img
                    v-if="sub === scope.row.value"
                    :key="sub"
                    :src="followUpImg"
                    alt="flag"
                  />
                </template>
              </div>
            </template>
          </el-table-column>
          <template #append>
            <div class="table-append">
              <div v-for="item of showList" :key="item.id">
                {{ item.date }}
              </div>
            </div>
          </template>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { useEventListener } from '@vueuse/core';
import { throttle, uniqueId } from 'lodash-es';
import followUpImg from '@/assets/icons/checkedGreen.png';
import store from '@/store';
import { getFollowDiet } from '@/api/followup';
import { IApiFollowStatisticsDietFollowList } from '@/interface/type';

defineOptions({ name: 'Diet' });

interface IListItem extends IApiFollowStatisticsDietFollowList {
  id: string;
}

const props = defineProps({
  patientId: {
    type: Number,
    default: 0,
  },
});

const tabs = store.useTabs();

// 干预类别 ：蔬菜，：水果，：肥肉、油炸食品、动物内脏等，：较甜，：较咸
const category = [
  { name: '较咸', value: 'preferSalt' },
  { name: '较甜', value: 'preferSweet' },
  { name: '肥肉/油炸食品/动物内脏', value: 'preferFatty' },
  { name: '水果', value: 'preferFruit' },
  { name: '蔬菜', value: 'preferVegetable' },
];
// 最小显示数据
const minListLen = 11;
// table 多数据优化
const tableTdWidth = 150;

const dataList = ref();
const targetDom = ref<HTMLElement | null>(null);
const showList = ref<IListItem[]>(getDefaultData());
const onScroll = throttle(handleScroll, 100);

async function initData(patientId: number) {
  const { followList = [] } = (await getFollowDiet({ patientId })) || {};
  const len = followList?.length || 0;
  if (len < minListLen) followList.push(...getDefaultData(len));

  dataList.value = followList.map(item => {
    const { date, dietList = [], id } = item as IListItem;
    return {
      dietList,
      id: id ?? uniqueId(),
      date: date ? dayjs(date).format('YYYY-MM-DD') : '--',
    };
  });
}

/** 获取默认显示数据
 * currentLent： 当前真实数据长度
 */
function getDefaultData(currentLent = 0) {
  const len = !currentLent ? minListLen : minListLen - currentLent;
  return Array(len)
    .fill('')
    .map(() => ({
      dietList: [],
      id: uniqueId(),
      date: undefined,
    }));
}

/** 根据数据量计算当前可视区域数据 */
function handleScroll() {
  const el = targetDom.value as
    | (HTMLElement & {
        wrapEl: HTMLDivElement;
        innerEl: HTMLDivElement;
      })
    | null;
  const list = dataList.value;
  if (!el || !list?.length) return;
  const scrollLeft = targetDom.value?.scrollLeft || 0;
  if (!el?.wrapEl) {
    const wrapEl = document.createElement('div');
    const innerEl = document.createElement('div');
    wrapEl.appendChild(innerEl);
    innerEl.appendChild(el.children[0]);
    el.insertBefore(wrapEl, el.firstChild);
    el.wrapEl = wrapEl;
    el.innerEl = innerEl;
    el.wrapEl.style.overflow = 'hidden';
  }
  if (el?.wrapEl) {
    el.wrapEl.style.width = `${list.length * tableTdWidth}px`;
    const showNum = Math.ceil(el.clientWidth / tableTdWidth); // 可展示个数
    const startIndex = Math.floor(scrollLeft / tableTdWidth); // 起点位置
    const endIndex = startIndex + showNum; // 结束位置
    showList.value = list.slice(startIndex, endIndex);
    el.innerEl.style.transform = `translateX(${startIndex * tableTdWidth}px)`;
  }
}

let isDragging = false,
  startX: number | undefined,
  scrollLeft: number | undefined,
  cleanupDomListener: undefined | (() => void),
  cleanupWindowListener: undefined | (() => void),
  cleanupMousedownListener: undefined | (() => void),
  cleanupMouseleaveListener: undefined | (() => void),
  cleanupMouseupListener: undefined | (() => void),
  cleanupMousemoveListener: undefined | (() => void);
function initEventListener() {
  nextTick(() => {
    targetDom.value = document.querySelector(
      '.diet-content-table .el-scrollbar__wrap'
    );
    if (!targetDom.value) return;
    cleanupDomListener = useEventListener(
      targetDom.value,
      'scroll',
      handleScroll
    );
    cleanupMousedownListener = useEventListener(
      targetDom.value,
      'mousedown',
      (e: MouseEvent) => {
        isDragging = true;
        startX = e.pageX - targetDom.value!.offsetLeft;
        scrollLeft = targetDom.value!.scrollLeft;
      }
    );
    cleanupMouseleaveListener = useEventListener(
      targetDom.value,
      'mouseleave',
      () => {
        isDragging = false;
      }
    );
    cleanupMouseupListener = useEventListener(
      targetDom.value,
      'mouseup',
      () => {
        isDragging = false;
      }
    );
    cleanupMousemoveListener = useEventListener(
      targetDom.value,
      'mousemove',
      (e: MouseEvent) => {
        if (!isDragging) return;
        const x = e.pageX - targetDom.value!.offsetLeft;
        const walk = (x - (startX || 0)) * 2; // 控制滚动速度
        targetDom.value!.scrollLeft = (scrollLeft || 0) - walk;
      }
    );
    cleanupWindowListener = useEventListener(window, 'resize', onScroll);
  });
}
function removeIEventListener() {
  if (targetDom.value) {
    if (cleanupDomListener) cleanupDomListener();
    if (cleanupWindowListener) cleanupWindowListener();
    if (cleanupMousedownListener) cleanupMousedownListener();
    if (cleanupMouseleaveListener) cleanupMouseleaveListener();
    if (cleanupMouseupListener) cleanupMouseupListener();
    if (cleanupMousemoveListener) cleanupMousemoveListener();
  }
}

onMounted(initEventListener);
onBeforeUnmount(removeIEventListener);

watch(
  () => tabs.mainActiveTab,
  code => {
    // 如果当前显示未非患者画像则移除事件兼听
    if (code !== 3) {
      removeIEventListener();
    } else {
      initEventListener();
    }
  }
);

watch(
  () => props.patientId,
  async patientId => {
    if (!patientId) return;
    await initData(patientId);
    handleScroll();
  },
  { immediate: true }
);
</script>
<style scoped lang="less">
.diet-title {
  padding-bottom: 16px;
  font-weight: 600;
  font-size: 14px;
  color: #15233f;
}
.diet-content {
  font-size: 14px;
  &-category {
    height: 39px;
    width: 66px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  :deep(.diet-content-table) {
    cursor: grab;
    overflow: auto;
    border-top: 1px dashed #d7dcea;
    .el-table__cell {
      padding: 0;
      border-bottom: 1px dashed #d7dcea;
      border-right: 1px dashed #d7dcea;
      &:last-child {
        border-right: none;
      }
      .cell {
        padding: 0;
      }
    }
    .el-table--border {
      .el-table__inner-wrapper {
        &::after,
        &::before {
          height: 0;
        }
      }
      &::after,
      &::before {
        width: 0;
      }
    }
    .el-table__header-wrapper {
      display: none;
    }
    .el-table__append-wrapper {
      overflow: initial;
      position: relative;
      z-index: 1;
      margin-top: -1px;
      border-top: 1px solid #d7dcea;
    }
    .el-table__border-left-patch {
      width: 0;
    }
  }
  .checked-box {
    flex-wrap: wrap;
    overflow: auto;
    height: 38px;
    width: 150px;
    & > img {
      width: 24px;
      height: 24px;
      padding: 4px;
    }
  }
  .table-append {
    display: flex;
    & > div {
      width: 150px;
      padding: 16px 0;
      text-align: center;
      color: #708293;
    }
  }
}
</style>
