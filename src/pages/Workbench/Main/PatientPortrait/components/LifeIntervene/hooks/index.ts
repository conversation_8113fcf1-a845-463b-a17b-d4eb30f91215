/** 干预类型 11:饮食 1:吸烟 2:饮酒 3:BMI 4:运动 */
export type IType = 11 | 1 | 2 | 3 | 4;

/** 风险评估 */
export interface IIntervene {
  type: IType;
  name: string;
}
export const riskTab: IIntervene[] = [
  { type: 11, name: '饮食' },
  { type: 1, name: '吸烟' },
  { type: 2, name: '饮酒' },
  { type: 3, name: 'BMI' },
  { type: 4, name: '运动' },
];

export function useIntervene() {
  const loading = ref(false);

  const risk = ref(riskTab[0].type);

  return {
    risk,
    loading,
  };
}
