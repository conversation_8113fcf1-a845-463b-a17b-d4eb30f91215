import { getLineEchartsOptions } from '@/components/BaseChart/options/line';
import { LineECOption } from '@/components/BaseChart';
import { IType, riskTab } from './index';
import { IApiFollowStatisticsNumberQuestionFollowList } from '@/interface/type';
import { formatTimeTemplate } from '@/utils';

// 开启 dataZoom 数据量
const dataZoomLimit = 50;

const unitMap = {
  1: '根/天',
  2: '两/周（按白酒计算）',
  3: '',
  4: '分钟/周',
};

/** 获取chart option */
export const getEchartsLineConfig = (
  data: IApiFollowStatisticsNumberQuestionFollowList[] = [],
  type: IType
): LineECOption => {
  if (!data?.length) return {};
  data = data.filter(item => item.number !== null);
  const xAxisData = data?.map(item =>
    formatTimeTemplate(item.date, 'YYYY-MM-DD')
  );

  return getLineEchartsOptions({
    title: {
      subtext: unitMap[type] ? `单位：${unitMap[type]}` : undefined,
    },
    grid: { top: 50 },
    legend: { enable: false },
    dataZoom: { enable: xAxisData.length >= dataZoomLimit },
    xAxisData,
    seriesConfig: [
      {
        name: riskTab.find(r => r.type === type)?.name || '',
        color: '#2E6BE6',
        markLine: {
          symbol: 'none', // 终点图形的样式，这里使用圆形
          data: [
            {
              type: 'average',
              name: 'Avg',
            },
          ],
        },
        data: data?.map(({ number }) => number || 0),
      },
    ],
  });
};
