<template>
  <CardWrapper title="生活方式干预">
    <el-tabs v-model="risk" v-loading="loading" type="card" class="custom-tabs">
      <el-tab-pane
        v-for="item in riskTab"
        :key="item.type"
        :name="item.type"
        :label="item.name"
      />
      <component
        :is="currentComponent"
        :type="risk"
        :patient-id="globalData.userId"
      />
    </el-tabs>
  </CardWrapper>
</template>

<script setup lang="ts">
import CardWrapper from '@/components/CardWrapper/index.vue';
import Diet from './Diet.vue';
import ChartView from './ChartView.vue';
import { useIntervene, riskTab } from './hooks';
import useGlobal from '@/store/module/useGlobal';
defineOptions({ name: 'LifeIntervenes' });

const globalData = useGlobal();

const { loading, risk } = useIntervene();

const currentComponent = computed(() => {
  if (risk.value === 11) return markRaw(Diet);
  return markRaw(ChartView);
});
</script>
<style lang="less" scoped>
.custom-tabs {
  :deep(& > div.el-tabs__header) {
    .el-tabs__item {
      height: 39px;
      line-height: 39px;
      background: #f7f8fa;
      width: 156px;
      border-bottom: 1px solid #e5e7eb;
      &.is-active,
      &:hover {
        color: #323233;
      }
      &.is-active {
        background: #fff;
        border-bottom: none;
      }
    }
  }
}

.risk-badge {
  :deep(sup.el-badge__content.is-dot) {
    top: 0;
    right: 5px;
    width: 10px;
    height: 10px;
  }
}

.chart {
  height: 300px;
}
</style>
