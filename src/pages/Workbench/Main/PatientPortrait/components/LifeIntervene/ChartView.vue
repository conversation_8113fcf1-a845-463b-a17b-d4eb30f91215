<template>
  <div class="container">
    <div class="title">{{ title }}</div>
    <BaseChart
      style="height: 300px"
      type="line"
      :data-complete="!loading"
      :options="chartsOption"
    />
  </div>
</template>
<script setup lang="ts">
import { PropType } from 'vue';
import { IType, riskTab } from './hooks';
import BaseChart from '@/components/BaseChart';
import { getEchartsLineConfig } from './hooks/utils';
import { getFollowNumQuestion } from '@/api/followup';
const props = defineProps({
  type: {
    type: Number as PropType<IType>,
    default: 1,
  },
  patientId: {
    type: Number,
    default: 0,
  },
});

const loading = ref(false);

const title = computed(
  () => riskTab.find(r => r.type === props.type)?.name + '定性折线图'
);

const chartsOption = ref<any>([]);

async function getData(type: IType, patientId: number) {
  try {
    loading.value = true;
    const res = await getFollowNumQuestion({ type, patientId });
    chartsOption.value = getEchartsLineConfig(res?.followList || [], type);
  } finally {
    loading.value = false;
  }
}

watch(
  [() => props.type, () => props.patientId],
  ([type, patientId]) => {
    if (!patientId) return;
    getData(type, patientId);
  },
  { immediate: true }
);
</script>

<style scoped lang="less">
.title {
  font-weight: 600;
  font-size: 14px;
  color: #15233f;
}
</style>
