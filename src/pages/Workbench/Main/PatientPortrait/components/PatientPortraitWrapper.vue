<template>
  <div class="wrapper">
    <div class="label">
      <div class="label-icon"></div>
      {{ title }}
    </div>
    <div class="content-wrapper">
      <slot name="reference"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string;
}

defineProps<Props>();

// todo
</script>
<script lang="ts">
export default {
  name: 'PatientPortraitWrapper',
};
</script>
<style scoped lang="less">
// todo
.wrapper {
  box-sizing: border-box;
  padding: 16px 0;
  background-color: #fff;
  border-radius: 6px;
  margin-bottom: 8px;
  .content-wrapper {
    box-sizing: border-box;
    padding: 0 16px;
  }
  .label {
    font-weight: bold;
    font-size: 16px;
    color: #3a4762;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    .label-icon {
      width: 6px;
      height: 16px;
      background: #2e6be6;
      border-radius: 2px;
      margin-right: 10px;
    }
  }
}
</style>
