<!-- 评估量表 -->
<template>
  <div class="content-box">
    <PatientPortraitWrapper title="评估量表">
      <template #reference>
        <div class="content-box">
          <el-tabs
            v-model="activeName"
            type="card"
            class="demo-tabs"
            @tab-change="tabChange"
          >
            <el-tab-pane
              label="PSQI匹兹堡睡眠质量指数"
              name="睡眠质量调查问卷"
            />
            <el-tab-pane label="PHQ-9抑郁症筛查量表" name="抑郁评估调查问卷" />
            <el-tab-pane label="GAD-7焦虑筛查量表" name="焦虑评估调查问卷" />
          </el-tabs>
          <div class="chart-wrapper">
            <BaseChart
              type="line"
              :options="psqiChartsOptions"
              :data-complete="!loading"
            />
          </div>
        </div>
      </template>
    </PatientPortraitWrapper>
  </div>
</template>

<script setup lang="ts">
import PatientPortraitWrapper from '../PatientPortraitWrapper.vue';
import BaseChart from '@/components/BaseChart/BaseChart.vue';
import {
  lineMarkAreaSleepQuality,
  lineMarkAreaDepressedQuality,
  lineMarkAreaAnxietyQuality,
} from '../../utils';

import { getLineEchartsOptions } from '@/components/BaseChart/options/line';
import { getQuestionnaireScore } from '@/api/questionnaire';

import useGlobal from '@/store/module/useGlobal';

const globalData = useGlobal();

import type { TabPaneName } from 'element-plus';

const activeName = ref('睡眠质量调查问卷');

const tabChange = (name: TabPaneName) => {
  if (name === '睡眠质量调查问卷') {
    initChart(lineMarkAreaSleepQuality);
  }
  if (name === '抑郁评估调查问卷') {
    initChart(lineMarkAreaDepressedQuality);
  }
  if (name === '焦虑评估调查问卷') {
    initChart(lineMarkAreaAnxietyQuality);
  }
};

let psqiChartsOptions = reactive<any>({});

const loading = ref(false);

const initChart = markArea => {
  if (!globalData.userId) return;
  loading.value = true;
  getQuestionnaireScore({
    patientId: globalData.userId,
    questionnaireName: activeName.value,
  })
    .then(data => {
      let xAxisData: string[] = data.map(item => item.date as string);
      let yAxisData: number[] = data.map(item => item.score as number);
      psqiChartsOptions = getLineEchartsOptions({
        xAxisData: xAxisData,
        grid: {
          top: 28,
          left: 34,
          right: 34,
          bottom: 32,
        },
        legend: {},
        seriesConfig: [
          {
            data: yAxisData,
            markArea: markArea,
          },
        ],
      });
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
};

watch(
  () => globalData.userId,
  () => {
    if (activeName.value === '睡眠质量调查问卷') {
      initChart(lineMarkAreaSleepQuality);
    }
    if (activeName.value === '抑郁评估调查问卷') {
      initChart(lineMarkAreaDepressedQuality);
    }
    if (activeName.value === '焦虑评估调查问卷') {
      initChart(lineMarkAreaAnxietyQuality);
    }
  }
);
onMounted(() => {
  initChart(lineMarkAreaSleepQuality);
});
</script>

<style scoped lang="less">
// todo
.content-box {
  :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
    background-color: #f7f8fa;
    border-color: #dcdee0;
    padding: 10px 16px;
    font-size: 14px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #323233;
    line-height: 20px;
  }
  :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
    background-color: #ffffff;
    border-bottom-color: #fff;
  }
}
.chart-wrapper {
  height: 300px;
}
</style>
