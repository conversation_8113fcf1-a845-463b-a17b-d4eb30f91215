<template>
  <el-tooltip
    :disabled="!showTip && !showTooltip"
    placement="top"
    raw-content
    popper-class="surgery-tip-box"
  >
    <div
      class="text-ellipsis overflow-hidden"
      :style="customStyle"
      @mouseenter="onmouseenter"
    >
      <slot></slot>
    </div>
    <template #content>
      <div class="content max-w-700 max-h-500 overflow-y-auto">
        <slot></slot>
      </div>
    </template>
  </el-tooltip>
</template>

<script setup lang="ts">
const showTooltip = ref(false);
interface IProps {
  customStyle?: string;
  showTip?: boolean;
}
defineProps<IProps>();

// const slots = useSlots();
// const children = slots.default?.()?.[0].children;
const onmouseenter = (e: MouseEvent) => {
  const el = e.target as HTMLElement;
  let elTip = el.getElementsByClassName('tip-content')[0] as HTMLElement;
  if (
    (elTip && elTip.offsetWidth < elTip.scrollWidth) ||
    el.offsetWidth < el.scrollWidth
  ) {
    showTooltip.value = true;
  } else {
    showTooltip.value = false;
  }
};
</script>

<script lang="ts">
export default {
  name: 'HrtText',
};
</script>

<style lang="less">
.surgery-tip-box.el-popper {
  padding: 16px 16px 8px 16px;

  .content {
    &::-webkit-scrollbar {
      width: 8px;
    }
    &::-webkit-scrollbar-thumb {
      width: 8px;
      background: #bebebe;
      border-radius: 5px;
    }
  }
}
</style>
