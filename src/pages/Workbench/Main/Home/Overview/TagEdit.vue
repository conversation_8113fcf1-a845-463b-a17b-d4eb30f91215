<template>
  <div ref="TagContent" class="tags-content">
    <div class="tag-box">
      <h4 class="til">系统标签</h4>
      <div v-if="systemTags?.length === 0">无</div>
      <HrtCheckboxGroup v-model="tagListOr" :max="20">
        <HrtCheckbox
          v-for="(item, i) in systemTags"
          :key="i + item.tagName"
          :label="item"
          :value="item"
          @change="changeTag($event, item, null)"
        >
          {{ item.tagName }}
        </HrtCheckbox>
      </HrtCheckboxGroup>
    </div>
    <div class="tag-box">
      <h4 class="til">历史标签</h4>
      <div v-if="historyTags?.length === 0">无</div>
      <HrtCheckboxGroup v-model="tagListOr" :max="20">
        <HrtCheckbox
          v-for="(item, i) in historyTags"
          :key="i + item.tagName"
          :label="item"
          :value="item"
          @change="changeTag($event, item, null)"
        >
          {{ item.tagName }}
        </HrtCheckbox>
      </HrtCheckboxGroup>
    </div>
    <div class="tag-box">
      <h4 class="til">查找标签</h4>
      <div class="search mb-8">
        <HrtInput
          v-model="searchKey"
          maxlength="10"
          size="middle"
          placeholder="请输入标签名（最长10个汉字）"
          :prefix-icon="Search"
          @input="(e: any) => getTags(e, 1)"
        />
        <HrtButton link @click="changeTag(true, { tagName: searchKey }, 1)">
          存为自定义
        </HrtButton>
      </div>
      <div v-if="allTags?.length === 0">无</div>
      <HrtCheckboxGroup v-model="tagListOr">
        <HrtCheckbox
          v-for="(item, i) in allTags"
          :key="i + item.tagName"
          :label="item"
          :value="item"
          @change="changeTag($event, item, null)"
        >
          {{ item.tagName }}
        </HrtCheckbox>
      </HrtCheckboxGroup>
    </div>
    <div v-show="false">展开更多{{ bottom }}</div>
  </div>

  <HrtDialog v-model="lostToFollowUpVisible" title="标记失访" @close="cancel">
    <QualityRisk v-model:situation-textarea="situationTextarea" />
    <template #footer>
      <div class="btn-box">
        <HrtButton class="cancel-btn" @click="cancel">取消</HrtButton>
        <HrtButton type="primary" class="sure-btn" @click="confirm">
          继续完成
        </HrtButton>
      </div>
    </template>
  </HrtDialog>
</template>
<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue';
import {
  getPatientTagSystem,
  getPatientTagHistory,
  addPatientTag,
  deletePatientTag,
  getPatientSystemTag,
} from '@/api/overview';
import bus from './hooks/bus';
import globalBus from '@/lib/bus';
import { useScroll } from '@vueuse/core';
import { throttle } from 'lodash-es';
import useGlobal from '@/store/module/useGlobal';
import QualityRisk from '@/components/QualityRisk/index.vue';

const TagContent = ref<HTMLElement | null>(null);

const { arrivedState } = useScroll(TagContent);

const bottom = computed(() => arrivedState.bottom);
watch(bottom, newVal => {
  if (newVal && totals.value > allTag.value.length) {
    getTags(searchKey.value, ++pageNumber.value);
  }
});

interface IHistoryTags {
  tagName: string;
}
interface IProps {
  tagList?: IHistoryTags[];
}

const props = defineProps<IProps>();
const searchKey = ref('');
const allTag = ref<IHistoryTags[]>([]);
const historyTag = ref<IHistoryTags[]>([]);
const allTags = ref<IHistoryTags[]>([]);
const historyTags = ref<IHistoryTags[]>([]);
const systemTag = ref<IHistoryTags[]>([]);
const systemTags = ref<IHistoryTags[]>([]);
const global = useGlobal();
const pageNumber = ref<number>(1);
const totals = ref<number>(10);
const tagListOr = ref(props.tagList);
const lostToFollowUpVisible = ref<boolean>(false);
const situationTextarea = ref<string>('');
const lostTagName = ref<string>('');
const confirm = () => {
  if (situationTextarea.value.length < 5) {
    ElMessage.warning('风险说明不能少于5个字!');
    return false;
  }
  addTag(global.userId, lostTagName.value);
};
const cancel = () => {
  lostToFollowUpVisible.value = false;
  tagListOr.value = tagListOr.value?.filter(
    item => item.tagName !== lostTagName.value
  );
};

const getTags = throttle(async (keyword = '', page = 1) => {
  const dataAllTag: any = await getPatientTagSystem({
    pageNumber: page,
    pageSize: 10,
    keyword,
  });
  console.log('[ dataAllTag ] >', dataAllTag.data);
  pageNumber.value = page;
  allTag.value =
    page == 1 ? dataAllTag.data : [...allTag.value, ...dataAllTag.data];
  totals.value = dataAllTag.totals;
  initTagList();
}, 800);
const getHistoryTag = async () => {
  const data: any = await getPatientTagHistory();
  historyTag.value = data.tagList || [];
  initTagList();
};

const changeTag = (
  e: any,
  item: { tagName: any; patientTagId?: any; tagType?: any },
  type: any
) => {
  if (type && !item.tagName) {
    ElMessage.warning('查找标签输入框不能为空!');
    return false;
  }
  if (e && (tagListOr.value as IHistoryTags[]).length === 20) {
    ElMessage.warning('患者标签超过限制,可清除无用标签后重新添加!');
    return false;
  }
  if (e) {
    //   新增
    // if (item.tagName === '失访' && item.tagType === 1) {
    //   lostToFollowUpVisible.value = true;
    //   lostTagName.value = item.tagName;
    //   situationTextarea.value = '';
    // } else {
    // }
    addTag(global.userId, item.tagName, item.tagType);
  } else {
    //   删除
    deletePatientTag({ patientTagId: item.patientTagId })
      .then(() => {
        ElMessage.success('删除成功!');
        // //修改标签成功后， 刷新患者标签信息、网页左侧列表
        bus.emit('refresh-patient-Info-tag');
        globalBus.emit('refresh-patient-list');
      })
      .catch(() => {
        ElMessage.error('删除失败!');
      });
  }
};
const addTag = (patientId: any, tagName: string, tagType: number = 2) => {
  addPatientTag({ patientId, tagName, tagType })
    .then(() => {
      ElMessage.success('保存成功!');
      lostToFollowUpVisible.value = false;
      // 更新历史标签
      getHistoryTag();
      // //修改标签成功后， 刷新患者标签信息、网页左侧列表
      bus.emit('refresh-patient-Info-tag');
      globalBus.emit('refresh-patient-list');
    })
    .catch(() => {
      ElMessage.error('保存失败!');
    });
};

const initTagList = () => {
  allTags.value = allTag.value.map(v => {
    const info =
      (tagListOr.value as IHistoryTags[]).find(
        (el: { tagName: any }) => el.tagName === v.tagName
      ) || v;
    return info;
  });
  historyTags.value = historyTag.value.map(v => {
    const info =
      (tagListOr.value as IHistoryTags[]).find(
        (el: { tagName: string }) => el.tagName === v.tagName
      ) || v;
    return info;
  });
  systemTags.value = systemTag.value.map(v => {
    const info =
      (tagListOr.value as IHistoryTags[]).find(
        (el: { tagName: string }) => el.tagName === v.tagName
      ) || v;
    return info;
  });
};

// 获取患者系统标签
const getPatientSystemTagList = async () => {
  const params = {
    pageNumber: 1,
    pageSize: 10,
  };
  const data: any = await getPatientSystemTag(params);
  systemTag.value = data.data || [];
  initTagList();
};

onMounted(async () => {
  getHistoryTag();
  getTags();
  getPatientSystemTagList();
});
watch(
  () => props.tagList,
  () => {
    tagListOr.value = props.tagList;
    initTagList();
  }
);
</script>
<style lang="less">
.tags-content {
  height: 308px;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-thumb {
    width: 8px;
    background: #bebebe;
    border-radius: 5px;
  }
}
.tag-box {
  width: 464px;
  margin: 0 auto;
  .til {
    font-size: 14px;
    font-weight: bold;
    color: #101b25;
    padding: 16px 0 8px 0;
  }
  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .el-checkbox {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    margin-right: 0;
    width: 218px;
    height: 36px;
    border-radius: 2px;
    padding: 0 12px;
    margin-bottom: 8px;
    .el-checkbox__label {
      padding: 0;
      font-size: 14px;
      color: #3a4762;
      line-height: 20px;
    }
    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      background: #fff;
      border-radius: 2px;
      border: 1px solid #dcdee0;
      &::after {
        border-width: 2px;
      }
    }
  }
  .is-checked {
    background: #e2f5e1;
    .el-checkbox__label {
      color: #2fb324;
    }
    .el-checkbox__inner {
      background: #2fb324;
      border: 1px solid #2fb324;
      &::after {
        //width: 11px;
        //height: 8px;
        //background: #ffffff;
        //border: 2px solid transparent;
        border-width: 2px;
      }
    }
  }
  .search {
    .el-input--large {
      width: 360px;
      height: 32px;

      .el-input__wrapper {
        border-radius: 2px;
        padding: 0 12px;
        .el-input__inner {
          height: 100%;
          color: #3a4762;
          &::placeholder {
            color: #bac8d4;
          }
        }
      }
    }
    .el-button {
      font-size: 14px;
      color: #2e6be6;
    }
  }
}
</style>
