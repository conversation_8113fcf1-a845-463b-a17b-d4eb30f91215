<template>
  <CardWrapper title="概况" class="overview-box mt-2xs">
    <div
      class="manage-duration flex"
      :class="[2, 3].includes(patientData.currentState) ? 'scientific' : ''"
    >
      <img
        v-show="
          [1].includes(patientData.currentState) && patientData.excessTime
        "
        class="w-30 h-18 mr-9"
        src="@/assets/imgs/overview/icon-manage-duration.png"
        alt=""
      />
      <div v-if="patientData.currentState === 2" class="tag">科研干预</div>
      <div v-if="patientData.currentState === 3" class="tag">科研对照</div>
      <div>
        已服务：
        <span>{{ patientData.manageTime || '--' }}</span>
        天，
      </div>
      <div>
        剩余时长：
        <span>{{ patientData.excessTime || '--' }}</span>
        天
      </div>
      <div class="ml-10">
        {{
          patientData.scientificName ? `(${patientData.scientificName})` : ''
        }}
      </div>
    </div>
    <InfoShow :patient-data="patientData" />
  </CardWrapper>
</template>

<script setup lang="ts">
import InfoShow from './InfoShow.vue';
import CardWrapper from '@/components/CardWrapper/index.vue';
import patientInfo from './hooks/patientInfo';

const { patientData } = patientInfo();
</script>
<style scoped lang="less">
.overview-box {
  .manage-duration {
    width: 100%;
    height: 32px;
    background: #f6f8fb;
    border-radius: 4px;
    align-items: center;
    font-size: 14px;
    color: #de901e;
    padding: 0 12px;
    margin-bottom: 10px;
  }
  .scientific {
    color: #3bb4cc;
  }
}
.tag {
  margin-right: 8px;
  width: 54px;
  height: 18px;
  line-height: 18px;
  font-style: italic;
  background: #3bb4cc;
  color: #fff;
  font-size: 10px;
  text-align: center;
  border-radius: 2px;
}
</style>
