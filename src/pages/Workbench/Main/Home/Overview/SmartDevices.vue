<template>
  <div class="add-equipment">
    <div class="type equipment-info">
      <div class="label">智能设备类型</div>
      <div class="value">
        <el-radio-group
          v-model="equipmentInfos.type"
          @change="equipmentTypeChange"
        >
          <el-radio
            v-for="(v, i) in typeList"
            :key="i"
            :label="v.value"
            disabled
          >
            {{ v.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
    <div class="type equipment-info">
      <div class="label">设备型号</div>
      <div class="value">
        <el-radio-group v-model="equipmentInfos.model">
          <el-radio
            v-for="(v, i) in unitTypeListCurr"
            :key="i"
            :label="v.value"
          >
            {{ v.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
    <div class="equipment-info">
      <div class="label">
        设备编号（{{
          equipmentInfos.type == 3 ||
          (equipmentInfos.type == 1 && equipmentInfos.model == 5)
            ? 'IMEI码'
            : equipmentInfos.type == 4
              ? 'MAC码'
              : 'SN码'
        }}）
      </div>
      <div class="value">
        <el-input
          v-model.trim="equipmentInfos.equipmentSN"
          placeholder="请输入内容"
          maxlength="20"
          clearable
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface IProps {
  equipmentInfo?: any;
  typeList?: any[];
  unitTypeList?: any[];
}
const props = withDefaults(defineProps<IProps>(), {
  equipmentInfo: () => {
    return {
      type: '1', //设备类型(血压计=1，手表=3)，默认为血压计
      model: '', //设备型号（爱奥乐=1，脉搏波=2，手表=3）
      equipmentSN: '', //设备编号
      operation: '1', // 设备是更换还是新增，默认新增
    };
  },
  typeList: () => [
    //智能设备类型
    {
      value: 1,
      label: '血压计',
    },
    {
      value: 3,
      label: '智能手表',
    },
    {
      value: 4,
      label: '体重秤',
    },
  ],
  unitTypeList: () => [
    //设备型号
    {
      value: 1,
      label: '爱奥乐',
      type: 1, //此type用于区分血压计/手表，与智能设备类型选项value对应，方便根据当前选择的设备类型过滤设备型号的选项数据
    },
    {
      value: 2,
      label: '脉搏波',
      type: 1,
    },
    {
      value: 3,
      label: 'ZK204',
      type: 3,
    },
    {
      value: 4,
      label: 'CF516BLE',
      type: 4,
    },
    {
      value: 5,
      label: '掌护',
      type: 1,
    },
  ],
});

const equipmentInfos = ref(props.equipmentInfo);
const typeList = ref(props.typeList);
const unitTypeList = ref(props.unitTypeList);

const equipmentTypeChange = () => {
  equipmentInfos.value.model = '';
  equipmentInfos.value.equipmentSN = '';
};

watch(
  () => props.equipmentInfo,
  newVal => {
    if (newVal.type == 4) equipmentInfos.value.model = newVal.type;
    if (String(newVal.equipmentSN).startsWith('86978406'))
      equipmentInfos.value.model = 5;
  },
  { immediate: true, deep: true }
);

const unitTypeListCurr = computed(() => {
  let arr = unitTypeList.value.filter(
    re => re.type == equipmentInfos.value.type
  );
  return arr;
});
const onClose = () => {
  return {
    ...equipmentInfos.value,
  };
};
defineExpose({ onClose });
</script>
<style lang="less">
.add-equipment {
  .equipment-info {
    display: flex;
    align-items: center;
    padding: 12px 0;

    .label {
      font-size: 14px;
      color: #111111;
      margin-right: 16px;
      width: 170px;
      text-align: right;
    }

    :deep(.value) {
      color: #101b25;

      .el-radio {
        .el-radio__inner {
          width: 16px;
          height: 16px;
          border: 1px solid #7a8599;
          background: #ffffff;
        }

        .el-radio__input.is-checked .el-radio__inner {
          border: 1px solid #0a73e4;

          &::after {
            width: 8px;
            height: 8px;
            background: #0a73e4;
          }
        }
      }

      .el-radio.is-checked .el-radio__label {
        color: #203549;
      }

      .el-input {
        .el-input__inner {
          height: 32px;
          width: 240px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #c4cbda;
          color: #203549;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
