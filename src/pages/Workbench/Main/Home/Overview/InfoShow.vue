<template>
  <div class="info-show">
    <div class="info-doc">
      <div class="label">工作室：</div>
      <div class="value">
        <span class="separate">{{ patientData.hospitalName || '--' }}</span>
        <span class="separate">{{ patientData.groupName || '--' }}</span>
      </div>
    </div>
    <div class="info-doc">
      <div class="label">基本信息：</div>
      <div class="value line">
        <div class="line-content">
          <span class="separate">{{ patientData.patientName || '--' }}</span>
          <span class="separate">
            {{
              patientData.gender == 1
                ? '男'
                : patientData.gender == 2
                  ? '女'
                  : '--'
            }}
          </span>
          <span class="separate">{{ patientData.age || '--' }}岁</span>
        </div>
        <el-button link class="link" @click="visible = true">
          详情
          <el-icon class="icon-add"><i-ep-arrow-right /></el-icon>
        </el-button>
      </div>
    </div>
    <div class="info-doc">
      <div class="label">风险等级：</div>
      <div class="value risk-level">
        <span
          v-for="(item, i) in patientData.riskGrade"
          :key="i"
          :class="[`flag${item.level}`]"
          class="flag"
        >
          <el-tooltip class="box-item" effect="dark" placement="bottom">
            {{ item.name }}
            <template #content>
              <div style="white-space: pre-line">
                {{ item.name }}:
                <br />
                {{ item.toolTipTil }}
              </div>
            </template>
          </el-tooltip>
        </span>
        <div v-if="patientData.chfLevel" class="w-108 h-32 heart-failure mr-8">
          <div
            :class="{
              low: patientData.chfLevel === 1,
              middle: patientData.chfLevel === 2,
              high: patientData.chfLevel === 3,
            }"
            class="level-flag w-8 h-8 mr-6"
          ></div>
          心衰
          <span
            :style="{
              color:
                patientData.chfLevel === 1
                  ? '#2fb324'
                  : patientData.chfLevel === 2
                    ? '#e37221'
                    : '#e63746',
            }"
            class="ml-4"
          >
            ({{
              heartFailureGradeTypeList.filter(
                item => item.id === patientData.chfLevel
              )[0].title
            }})
          </span>
        </div>
        <span v-if="patientData.nyhaLevel" class="grade-box">
          NYHA分级：
          <span class="grade">
            {{ NYHALevel[patientData.nyhaLevel - 1] }}级
          </span>
        </span>
        <span v-if="patientData.lvefLevel" class="grade-box">
          LVEF分级：
          <span class="grade">{{ patientData.lvefLevel }}%</span>
        </span>
        <span v-if="patientData.vvp" class="grade-box">易损期</span>
        <span
          v-if="
            !patientData.riskGrade ||
            (patientData.riskGrade?.length === 0 &&
              !patientData.chfLevel &&
              !patientData.nyhaLevel &&
              !patientData.lvefLevel &&
              !patientData.vvp)
          "
        >
          --
        </span>
        <!-- <div class="adjust cursor-pointer flex items-center">
          <img
            src="@/assets/imgs/userList/adjust-img.png"
            alt=""
            class="w-14 h-14 mr-2"
          />
          调整
        </div> -->
      </div>
    </div>
    <div class="info-doc">
      <div class="label">危险因素：</div>
      <div class="value">
        <SurgeryToolTip>
          <div class="tip-content text-ellipsis overflow-hidden">
            {{ patientData.highRisk || '--' }}
          </div>
        </SurgeryToolTip>
      </div>
    </div>
    <div class="info-doc">
      <div class="label">依从性：</div>
      <div class="value">
        <span>随访完成度{{ patientData.followUp }}%，</span>
        <span>复查完成度{{ patientData.review }}%，</span>
        <span>血压心率检测率{{ patientData.blood }}%</span>
      </div>
    </div>
    <div class="info-doc">
      <div class="label">临床诊断：</div>
      <div class="value">
        <SurgeryToolTip>
          <div class="tip-content text-ellipsis overflow-hidden">
            {{ patientData.clinicalDiagnosis || '--' }}
          </div>
        </SurgeryToolTip>
      </div>
    </div>
    <div class="info-doc">
      <div class="label">既往史：</div>
      <div class="value">
        <SurgeryToolTip>
          <div class="tip-content text-ellipsis overflow-hidden">
            {{ patientData.previousHistory || '--' }}
          </div>
        </SurgeryToolTip>
      </div>
    </div>
    <div class="info-doc">
      <div class="label">手术信息：</div>
      <div class="value line">
        <SurgeryToolTip :show-tip="patientData.surveyList?.length > 1">
          <div
            v-for="item in patientData.surveyList"
            :key="item"
            class="surgery flex mb-8"
          >
            <img
              class="w-16 h-20 mr-8"
              src="@/assets/imgs/overview/icon-event.png"
              alt=""
            />

            <span
              class="content tip-content flex-1 text-ellipsis overflow-hidden"
            >
              {{ item.conclusion }}
            </span>
            <span class="date ml-16">
              {{
                item.surgeryTime
                  ? dayjs(item.surgeryTime).format('YYYY-MM-DD')
                  : ''
              }}
            </span>
          </div>
        </SurgeryToolTip>
        <span
          v-if="!patientData.surveyList || patientData.surveyList?.length === 0"
        >
          --
        </span>
      </div>
    </div>
    <div class="info-doc">
      <div class="label">临床事件：</div>
      <div class="value line">
        <div class="line-content">
          <div v-if="patientData.eventList && patientData.eventList[0]">
            最新：
            <span>
              {{
                dayjs(patientData.eventList[0].clinicalTime).format(
                  'YYYY-MM-DD'
                )
              }}
            </span>
            &nbsp;
            <span>
              {{ clinicalTypes[patientData.eventList[0].clinicalType] }}
            </span>
          </div>
          <div v-else>--</div>
          <el-button
            link
            class="link btn-add"
            @click="showClinicalEventEdit = true"
          >
            <el-icon class="icon-add"><i-ep-circle-plus /></el-icon>
          </el-button>
        </div>

        <el-button link class="link" @click="showClinicalEventsDrawer = true">
          全部临床事件
          <el-icon class="icon-add"><i-ep-arrow-right /></el-icon>
        </el-button>
      </div>
    </div>
    <div class="info-doc">
      <div class="label">标签：</div>
      <div class="value">
        <div class="tag-content">
          <span
            v-if="!patientData.tagList || patientData.tagList?.length === 0"
          >
            --
          </span>
          <span v-for="(item, i) in patientData.tagList" :key="i" class="tag">
            <el-tooltip class="box-item" effect="dark" placement="bottom">
              {{ item.tagName }}
              <template #content>
                <div style="text-align: center">
                  {{ item.userName }}
                  <br />
                  {{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm') }}
                </div>
              </template>
            </el-tooltip>
            <el-icon class="close-icon" @click="deleteTag(item)">
              <i-ep-circle-close-filled />
            </el-icon>
          </span>
          <el-button link class="link" @click="showTagEditHandle">
            <img
              class="w-11 h-13 ml-12"
              src="@/assets/imgs/overview/icon-edit.png"
              alt=""
            />
          </el-button>
        </div>
      </div>
    </div>
    <div class="info-doc">
      <div class="label">备注：</div>
      <div class="value line">
        <div style="max-width: calc(100% - 30px)">
          <SurgeryToolTip>
            <div class="tip-content text-ellipsis overflow-hidden">
              {{ patientData.remarks || '--' }}
            </div>
          </SurgeryToolTip>
        </div>
        <div class="link" @click="showRemarkEdit = true">
          <img
            class="w-11 h-13 ml-12"
            src="@/assets/imgs/overview/icon-edit.png"
            alt=""
          />
        </div>
      </div>
    </div>
  </div>
  <!--  基本信息编辑-->
  <Drawer v-model:visible="visible" title="基本信息">
    <InfoEdit :patient-data="patientData" />
  </Drawer>
  <!--  临床事件记录列表-->
  <Drawer v-model:visible="showClinicalEventsDrawer" title="临床事件记录">
    <ClinicalEvents
      :event-list="patientData.eventList"
      :clinical-types="clinicalTypes"
    />
  </Drawer>
  <!--  start-备注编辑-->
  <Dialog
    v-model:visible="showRemarkEdit"
    title=""
    :width="600"
    class="remark-dialog"
  >
    <div class="remark-content">
      <div class="label">备注</div>
      <el-input
        v-model="remarks"
        maxlength="2000"
        class="remark-input"
        :rows="7"
        show-word-limit
        placeholder="请输入"
        type="textarea"
      />
    </div>
    <template #footer>
      <div class="btn-box no-line">
        <el-button @click="showRemarkEdit = false">取消</el-button>
        <el-button type="primary" @click="updatePatientRemarks">确定</el-button>
      </div>
    </template>
  </Dialog>
  <!--  end-备注编辑-->
  <Dialog
    v-model:visible="showTagEdit"
    title="患者标签"
    :width="600"
    :height="360"
    class="tag"
  >
    <TagEdit v-model:tag-list="patientData.tagList" />
    <!--    <template #footer>-->
    <!--      <div class="btn-box no-line">-->
    <!--        <el-button @click="showTagEdit = false">取消</el-button>-->
    <!--        <el-button type="primary" @click="showTagEdit = false">-->
    <!--          确定-->
    <!--        </el-button>-->
    <!--      </div>-->
    <!--    </template>-->
  </Dialog>
  <!--  临床事件编辑-->
  <Dialog v-model:visible="showClinicalEventEdit" :width="600" title="临床事件">
    <AddClinicalEvent
      ref="refAddClinicalEvent"
      v-model:visible="showClinicalEventEdit"
      :user-id="global.userId"
      :curr-choose-event="curChooseEvent"
    />
    <template #footer>
      <div class="btn-box">
        <div class="cancel-btn" @click="showClinicalEventEdit = false">
          取消
        </div>
        <div class="sure-btn" @click="sureBtnVisible">确定</div>
      </div>
    </template>
  </Dialog>
  <!-- 风险等级调整弹窗 -->
  <Dialog
    v-model:visible="showAdjustDialogVisible"
    :width="600"
    title="风险等级调整"
  >
    <div class="adjust-dialog flex flex-col items-center">
      <div class="item-adjust flex mb-18">
        <div class="item-title w-80">心衰等级：</div>
        <div class="item-content">
          <div class="flex items-center">
            <div
              v-for="item in heartFailureGradeTypeList"
              :key="item.id"
              class="all-box flex items-center cursor-pointer mr-24"
              @click="changeHeartFailureGrade(item.id)"
            >
              <div
                v-if="heartFailureGrade === item.id"
                class="change-box-checked mr-8 flex items-center justify-center"
              >
                <div class="interior-check"></div>
              </div>
              <div v-else class="change-box mr-8"></div>
              <span class="change-title">{{ item.title }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="item-adjust flex">
        <div class="item-title">
          <span class="star">*</span>
          调整原因：
        </div>
        <div class="item-content">
          <el-input
            v-model="heartFailureGradeReason"
            style="width: 240px"
            :rows="2"
            type="textarea"
            placeholder="Please input"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="btn-box">
        <div class="cancel-btn" @click="showAdjustDialogVisible = false">
          取消
        </div>
        <div class="sure-btn" @click="sureAdjustBtnVisible">确定</div>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
// import Text from '@/components/Text/index.vue';
import SurgeryToolTip from './SurgeryToolTip.vue';
import Dialog from '@/components/Dialog/index.vue';
import Drawer from '@/components/Drawer/index.vue';
import AddClinicalEvent from '@/components/AddClinicalEvent/index.vue';
import InfoEdit from './InfoEdit.vue';
import TagEdit from './TagEdit.vue';
import ClinicalEvents from './ClinicalEvents/index.vue';
import dayjs from 'dayjs';
import { deletePatientTag, updatePatientInfoRemarks } from '@/api/overview';
import bus from './hooks/bus';
import globalBus from '@/lib/bus';
import { saveEvent } from '@/api/event';
import useGlobal from '@/store/module/useGlobal';
interface IProps {
  patientData?: object;
}
const props = defineProps<IProps>();

// 风险等级调整
let showAdjustDialogVisible = ref(false);
let sureAdjustBtnVisible = () => {};
// 心衰等级
let heartFailureGradeTypeList = ref([
  { title: '低危', id: 1 },
  { title: '中危', id: 2 },
  { title: '高危', id: 3 },
]);
let heartFailureGrade = ref(1);
let changeHeartFailureGrade = (val: number) => {
  heartFailureGrade.value = val;
};
let heartFailureGradeReason = ref('');
let NYHALevel = ['I', 'II', 'III', 'IV'];

const visible = ref(false);
const showRemarkEdit = ref(false);
const showTagEdit = ref(false);
const showClinicalEventsDrawer = ref(false);
const showClinicalEventEdit = ref(false);
const refAddClinicalEvent = ref();
const remarks = ref(props.patientData.remarks);
const clinicalTypes = ref({
  1: 'MACE事件',
  2: '药物不良反应',
  4: '死亡事件',
  5: '其他',
  6: '无临床事件',
});
const curChooseEvent = ref({});

const global = useGlobal();
const updatePatientRemarks = () => {
  updatePatientInfoRemarks({ patientId: global.userId, remarks: remarks.value })
    .then(() => {
      ElMessage.success('保存成功!');
      showRemarkEdit.value = false;
      //修改备注成功后， 刷新患者信息
      bus.emit('refresh-patient-infos');
    })
    .catch(() => {
      ElMessage.error('保存失败!');
    });
};
const sureBtnVisible = () => {
  // refAddClinicalEvent.value.onClose();
  let params = refAddClinicalEvent.value.onClose().params;
  if (params) {
    ElMessageBox.confirm('确定提交内容并保存?', '确定', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      draggable: true,
    })
      .then(() => {
        params.clinicalTime = dayjs(params.clinicalTime).format('YYYY-MM-DD');
        saveEvent(params).then(() => {
          showClinicalEventEdit.value = false;
          //保存事件成功后， 刷新患者临床事件信息
          bus.emit('refresh-patient-Info-event');
          globalBus.emit('updete-todo-list');
          curChooseEvent.value = {};
          ElMessage({
            type: 'success',
            message: '保存成功！',
          });
        });
      })
      .catch(() => {});
  }
};
const showTagEditHandle = () => {
  if (props.patientData.tagList.length == 20) {
    ElMessageBox.alert('可清除无用标签后重新添加!', '患者标签超过限制', {
      confirmButtonText: '好的',
      type: 'warning',
      draggable: true,
    });
    return false;
  }
  showTagEdit.value = true;
};
const deleteTag = (item: { patientTagId: any }) => {
  deletePatientTag({ patientTagId: item.patientTagId })
    .then(() => {
      ElMessage.success('删除成功!');
      // //修改标签成功后， 刷新患者标签信息、网页左侧列表
      bus.emit('refresh-patient-Info-tag');
      globalBus.emit('refresh-patient-list');
    })
    .catch(() => {
      ElMessage.error('删除失败!');
    });
};
const openClinicalEventDialog = () => {
  showClinicalEventEdit.value = true;
  curChooseEvent.value = { clinicalType: 6 };
};
onMounted(() => {
  globalBus.on('open-clinical-event', openClinicalEventDialog);
});
onUnmounted(() => {
  globalBus.off('open-clinical-event', openClinicalEventDialog);
});
watch(
  () => props.patientData,
  () => {
    remarks.value = props.patientData.remarks;
  }
);
</script>

<style scoped lang="less">
.info-show {
  > div {
    display: flex;
    align-items: center;
    min-height: 32px;
    //max-height: 32px;
    //padding-top: 12px;
    line-height: 32px;
    .label {
      width: 94px;
      font-size: 14px;
      color: #8193a3;
      align-self: flex-start;
    }
    .value.line {
      max-height: 32px;
      display: flex;
      flex-wrap: nowrap;
      //justify-content: space-between;
      .line-content {
        flex: 1;
        display: flex;
        //align-items: center;
        white-space: nowrap;
        overflow: hidden;
      }

      .link {
        font-size: 14px;
        color: #2e6be6;
        display: flex;
        align-items: center;
        &:hover {
          opacity: 0.75;
          cursor: pointer;
        }
        &:active {
          opacity: 1;
        }
      }
      .el-button {
      }
    }
    .risk-level {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }
    .value {
      flex: 1;
      font-size: 14px;
      //max-height: 32px;
      color: #203549;
      white-space: nowrap;
      overflow: hidden;
      .separate {
        &:after {
          display: inline-block;
          content: '';
          border-right: 1px solid #bac8d4;
          height: 12px;
          width: 1px;
          margin: 0 12px;
        }
        &:last-of-type:after {
          display: none;
        }
      }
      .grade-box {
        font-size: 14px;
        color: #7a8599;
        background: #f7f8fa;
        border-radius: 2px;
        padding: 0 12px;
        margin-right: 8px;
        display: inline-block;
        box-sizing: border-box;
      }
      .adjust {
        font-size: 14px;
        color: #2e6be6;
      }
      .flag {
        margin-right: 8px;
        background: #f7f8fa;
        border-radius: 2px;
        padding: 0 12px;
        display: inline-block;
        box-sizing: border-box;
        &:before {
          display: inline-block;
          content: '';
          width: 8px;
          height: 8px;
          background: #1da272;
          border-radius: 50%;
          margin-right: 4px;
        }

        .grade {
          color: #3a4762;
        }
      }
      .flag2:before {
        background: #ed6a0c;
      }
      .flag3:before {
        background: #dc0101;
      }
      .flag4:before,
      .flag5:before {
        background: #dc0101;
      }
      .surgery {
        width: 100%;
        height: 32px;
        background: #f7f8fa;
        display: flex;
        align-items: center;
        padding: 0 12px;
        .content {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
        }
        .date {
          font-size: 14px;
          color: #708293;
        }
      }
      .tag-content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        .tag {
          display: inline-block;
          min-width: 32px;
          height: 22px;
          line-height: 22px;
          background: rgba(45, 166, 65, 0.1);
          border-radius: 2px;
          font-size: 12px;
          color: #2da641;
          padding: 0 6px;
          margin: 4px 12px 4px 0;
          position: relative;
          .close-icon {
            font-size: 14px;
            color: #e63746;
            position: absolute;
            top: -4px;
            right: -4px;
            display: none;
            cursor: pointer;
          }
          &:hover .close-icon {
            display: inline-block;
          }
        }
      }
    }
    .btn-add {
      margin-left: 10px;
      .icon-add {
        color: #2e6be6;
        align-self: center;
        font-size: 16px;
      }
    }
  }
}
.remark-dialog {
  .remark-content {
    padding: 0 24px;
    .label {
      font-size: 14px;
      font-weight: bold;
      color: #111111;
      margin: 24px 0 8px 0;
    }
  }
}
.btn-box {
  padding: 16px 24px 24px 24px;
  .el-button {
    width: 74px;
    height: 32px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #dcdee0;
    margin-left: 8px;
    font-size: 14px;
    color: #323233;
    &:last-of-type {
      background: #2e6be6;
      border: 1px solid #2e6be6;
      color: #ffffff;
    }
  }
}
.adjust-dialog {
  padding: 16px;
  .item-adjust {
    .item-title {
      font-size: 14px;
      color: #3a4762;
      text-align: right;
      .star {
        color: #e63746;
      }
    }
    .item-content {
      .all-box {
        .change-box {
          width: 16px;
          height: 16px;
          background: #ffffff;
          border-radius: 8px;
          border: 1px solid #dcdee0;
        }
        .change-box-checked {
          width: 16px;
          height: 16px;
          background: #ffffff;
          border-radius: 8px;
          border: 1px solid #2e6be6;
          .interior-check {
            width: 8px;
            height: 8px;
            background: #0a73e4;
            border-radius: 8px;
          }
        }
        .change-title {
          font-size: 14px;
          color: #3a4762;
        }
      }
    }
  }
}
.btn-box.no-line {
  border: none;
}
.heart-failure {
  background: #f7f8fa;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  .level-flag {
    border-radius: 50%;
  }
  .low {
    background: #2fb324;
  }
  .middle {
    background: #e37221;
  }
  .high {
    background: #e63746;
  }
}
</style>
