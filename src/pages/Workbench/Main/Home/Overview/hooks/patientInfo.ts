import { isArray } from 'lodash-es';
import {
  getPatientInfoBase,
  getPatientRisk,
  getPatientFactor,
  getPatientClinical,
  getPatientSurgery,
  getPatientTag,
} from '@/api/overview';
import { getEventList } from '@/api/event';
import bus from './bus';
import globalBus from '@/lib/bus';
import useGlobal from '@/store/module/useGlobal';

// const THROTTLE_TIME = 1000 * 2;

const patientInfo = () => {
  const global = useGlobal();
  const patientData = ref({});

  const handleSet = async (patientId: number) => {
    const data: any = await getPatientInfoBase({ patientId });
    // patientData.value = data || {};
    patientData.value = { ...patientData.value, ...data };
    global.setUserInfo(data);
  };
  const patientRisk = async (patientId: number) => {
    const data: any = await getPatientRisk({ patientId });

    // riskGrade.value = data || {};
    const prognosticRiskType = {
      1: ['急性ACS风险', 'GRACE评分'],
      2: ['复杂ACS风险', 'SYNTAX II评分'],
      3: ['非瓣膜性房颤脑卒中风险', 'CHA2DS2-VASc评分'],
    };
    const bleedingRiskType = {
      1: ['ACS出血风险', 'ACUITY评分'],
      2: ['房颤抗凝血风险', 'HAS-Bled评分'],
    };
    //riskLevel: '超高危', '高危', '中危', '低危'

    if (data) {
      const riskLevelType = {
        低危: 1,
        中危: 2,
        高危: 3,
        超高危: 4,
        极高危: 5,
      };
      const arr = [];
      let newArr = [];
      for (const dataKey in data) {
        let el = data[dataKey];

        let info = {};
        if (!el || (isArray(el) && el.length == 0)) {
          continue;
        }
        if (dataKey === 'prognosticRisk') {
          const elsText = el.map(v => {
            return `${prognosticRiskType[v.type][0]}${v.riskLevel}，${
              prognosticRiskType[v.type][1]
            }${v.score}`;
          });
          el = el && el.length > 0 ? el[0] : {};
          info = {
            name: '预后风险',
            riskLevel: el.riskLevel || '',
            level: riskLevelType[el.riskLevel] || 0,
            score: `${el.score}`,
            toolTipTil: elsText.join('；\n'),
          };
        }
        if (dataKey === 'bleedingRisk') {
          const elsText = el.map(v => {
            return `${bleedingRiskType[v.type][0]}${v.riskLevel}，${
              bleedingRiskType[v.type][1]
            }${v.score}`;
          });
          el = el && el.length > 0 ? el[0] : {};
          info = {
            name: '出血风险',
            riskLevel: el.riskLevel || '',
            level: riskLevelType[el.riskLevel] || 0,
            score: `${el.score}`,
            toolTipTil: elsText.join('；\n'),
          };
        }
        if (dataKey === 'ascvdRisk') {
          el = el || {};
          info = {
            name: 'ASCVD风险',
            riskLevel: el?.riskLevel || '',
            level: riskLevelType[el.riskLevel] || 0,
            toolTipTil: `ASCVD风险${el?.riskLevel}，${el.riskNumber}次ASCVD事件`,
          };
        }
        if (dataKey === 'deathRisk') {
          el = el || {};
          const clinicalDiagnosis = JSON.parse(el.clinicalDiagnosis);
          const ecg = JSON.parse(el.ecg);
          info = {
            name: '猝死风险' || '',
            riskLevel: el?.riskLevel || '',
            level: riskLevelType[el.riskLevel] || 0,
            toolTipTil: `猝死风险${el?.riskLevel}，
            ${clinicalDiagnosis.join('，')}，${ecg.join('，')}`,
          };
        }
        arr.push(info);
      }
      newArr = arr.filter(item => JSON.stringify(item) != '{}');
      patientData.value = {
        ...patientData.value,
        riskGrade: newArr,
        chfLevel: data.chfLevel,
        nyhaLevel: data.nyhaLevel,
        lvefLevel: data.lvefLevel,
        vvp: data.vvp,
      };
    }
  };
  const patientFactor = async (patientId: number) => {
    const data: any = await getPatientFactor({ patientId });
    patientData.value = {
      ...patientData.value,
      highRisk: data?.highRisk,
      ...data?.compliance,
    };
  };

  const patientClinical = async (patientId: number) => {
    const data: any = await getPatientClinical({ patientId });
    patientData.value = {
      ...patientData.value,
      clinicalDiagnosis: data?.clinicalDiagnosis.join('、'),
      previousHistory: data?.previousHistory.join('、'),
    };
  };
  const patientSurgery = async (patientId: number) => {
    const data: any = await getPatientSurgery({ patientId });
    patientData.value = {
      ...patientData.value,
      ...data,
    };
  };

  const patientTag = async (patientId: number) => {
    const data: any = await getPatientTag({ patientId });
    patientData.value = {
      ...patientData.value,
      ...data,
    };
  };
  const eventList = async (patientId: number) => {
    const data: any = await getEventList({ patientId });
    patientData.value = {
      ...patientData.value,
      eventList: data,
    };
  };

  onMounted(() => {
    bus.on('refresh-patient-infos', () => {
      handleSet(global.userId);
    });
    bus.on('refresh-patient-Info-tag', () => {
      patientTag(global.userId);
    });
    bus.on('refresh-patient-Info-event', () => {
      eventList(global.userId);
    });
    globalBus.on('refresh-patient-info-event', () => {
      eventList(global.userId!);
    });
    handleSet(global.userId);
    patientRisk(global.userId);
    patientFactor(global.userId);
    patientClinical(global.userId);
    patientSurgery(global.userId);
    patientTag(global.userId);
    eventList(global.userId);
  });
  watch(
    () => global.userId,
    () => {
      handleSet(global.userId);
      patientRisk(global.userId);
      patientFactor(global.userId);
      patientClinical(global.userId);
      patientSurgery(global.userId);
      patientTag(global.userId);
      eventList(global.userId);
    }
  );

  return {
    patientData,
    handleSet,
  };
};
export default patientInfo;
