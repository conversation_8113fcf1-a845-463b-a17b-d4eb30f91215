<template>
  <div class="item">
    <div class="label">请选择地区</div>
    <el-cascader
      v-model="regionValue"
      :disabled="isDisabled"
      :options="areaData.cityData"
      :placeholder="isDisabled ? '无' : '请选择'"
      :props="{ expandTrigger: 'hover' }"
      :title="regionValue.join('/')"
      class="input"
    />
  </div>
  <div class="item">
    <div class="titleBox">请输入详细地址</div>
    <el-input
      v-model="detailsAddress"
      placeholder="请输入内容"
      maxlength="100"
    />
  </div>
</template>

<script setup lang="ts">
import areaData from '@/config/cascader-city.json';

interface IProps {
  regionData?: any[];
  details?: string;
}
const props = defineProps<IProps>();

const regionValue = ref(props.regionData);
const isDisabled = ref(false);
const detailsAddress = ref(props.details);
// const emits = defineEmits<{
//   (e: 'update:regionData', value: any[]): void;
//   (e: 'update:details', value: string): void;
// }>();

const onClose = () => {
  // emits('update:regionData', regionValue.value);
  // emits('update:details', detailsAddress.value);
  return {
    regionData: regionValue.value,
    details: detailsAddress.value,
  };
};
defineExpose({ onClose });
</script>
<style scoped lang="less">
.info-edit {
  padding: 0 16px;
  .base-info {
    .til {
      font-size: 14px;
      font-weight: bold;
      color: #101b25;
      padding: 16px 0 8px 0;
    }
    .info-box {
      display: flex;
      flex-wrap: wrap;
      border: 1px solid #d2dce6;
      border-right: none;
      border-bottom: none;

      .info-item {
        width: 50%;
        height: 44px;
        line-height: 44px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #d2dce6;
        .label {
          width: 120px;
          height: 100%;
          background: #f7f8fa;
          border-right: 1px solid #d2dce6;
          text-indent: 12px;
          font-size: 14px;
          color: #7a8599;
          white-space: nowrap;
        }
        .value {
          flex: 1;
          height: 100%;
          border-right: 1px solid #d2dce6;
          text-indent: 12px;
          font-size: 14px;
          color: #3a4762;
          white-space: nowrap;
        }
      }
      .info-item.line {
        width: 100%;
        .value {
        }
      }
    }
  }
  .equipment-type {
    width: 100%;
    height: 44px;
    align-items: center;
    background: #f7f8fa;
    font-size: 14px;
    color: #3a4762;
    font-weight: bold;
    padding: 0 12px;
    border: 1px solid #d2dce6;
    border-top: none;
    border-bottom: none;
    .el-button {
      height: 20px;
      padding: 0;
      margin: 0;
      margin-left: 16px;
      &:first-of-type {
        color: #e63746;
      }
      &:last-of-type {
        color: #2e6be6;
      }
    }
    .type {
      > span {
        font-weight: normal;
      }
    }
    &:nth-of-type(2) {
      border-top: 1px solid #d2dce6;
    }
  }
  .equipment-type.no-bind {
    .type > span {
      color: #7a8599;
    }
  }
}
.item {
  .titleBox {
    font-size: 14px;
    color: #101b25;
    margin-right: 16px;
    width: 170px;
    text-align: right;
  }
}
</style>
<style lang="less">
.address-box {
  .el-cascader,
  .el-icon-arrow-down,
  .el-icon-arrow-up {
    line-height: 32px;
  }
  .el-input {
    height: 32px;
    width: 240px;
    .el-input__inner {
      height: 32px;
      width: 240px;
    }
  }
  .item {
    display: flex;
    align-items: center;
    padding: 12px;
    .label {
      font-size: 14px;
      color: #101b25;
      margin-right: 16px;
      width: 170px;
      text-align: right;
    }
  }
}
</style>
