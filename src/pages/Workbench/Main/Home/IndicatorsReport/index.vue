<template>
  <CardWrapper title="报告指标" sub-title="仅展示固定指标和已添加的项目指标">
    <template #tools>
      <LinkBtn>
        <img class="img-icon" :src="documentIcon" alt="icon" />
        <span @click="applyInspVisible = true">检查申请</span>
      </LinkBtn>
    </template>

    <el-tabs v-model="activeTab" type="card" class="custom-tabs">
      <el-tab-pane
        v-for="item in indicTab"
        :key="item.id"
        :name="item.id"
        :lazy="true"
      >
        <template #label>
          {{ item.name }}
          <el-badge
            v-if="item.id === 'analysis' && indicatorInfo.hasAnalysisRisk"
            is-dot
          />
        </template>
        <component :is="item.component" />
      </el-tab-pane>
    </el-tabs>
  </CardWrapper>
  <ApplyInspection v-model:visible="applyInspVisible" />
</template>

<script setup lang="ts">
import { type Component } from 'vue';
import documentIcon from '@/assets/icons/document.svg';
import CardWrapper from '@/components/CardWrapper/index.vue';
import LinkBtn from '@/pages/Workbench/Main/components/LinkBtn.vue';
import Analysis from './Analysis/index.vue';
import Raw from './Raw/index.vue';
import ApplyInspection from './ApplyInspection.vue';
import store from '@/store';
import bus from '@/lib/bus';
defineOptions({ name: 'IndicatorsReport' });

const indicatorInfo = store.useIndicatorInfo();
// 检查申请
const applyInspVisible = ref(false);
const activeTab = ref(
  indicatorInfo.bloodPressureMonitoring ? 'raw' : 'analysis'
);
const indicTab: { id: string; name: string; component: Component }[] = [
  { id: 'analysis', name: '指标分析', component: markRaw(Analysis) },
  { id: 'raw', name: '报告原文', component: markRaw(Raw) },
];
watch(activeTab, val => {
  if (val === 'analysis') bus.emit('update-analysis-indicator');
});
</script>
<style scoped lang="less">
.img-icon {
  width: 16px;
  height: 16px;
  margin-right: 2px;
}
.custom-tabs {
  :deep(& > div.el-tabs__header) {
    .el-tabs__item {
      height: 39px;
      line-height: 39px;
      background: #f7f8fa;
      width: 110px;
      border-bottom: 1px solid #e5e7eb;
      &.is-active,
      &:hover {
        color: #323233;
      }
      &.is-active {
        background: #fff;
        border-bottom: none;
      }
    }
  }
}
</style>
