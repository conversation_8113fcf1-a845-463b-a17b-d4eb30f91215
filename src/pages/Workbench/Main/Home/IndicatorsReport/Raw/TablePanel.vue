<template>
  <BaseTable
    ref="baseTableRef"
    :columns="columns"
    :request-api="getReportTableList"
    :init-param="tableReqParam"
    :request-auto="false"
    max-height="500"
  />
</template>

<script setup lang="tsx">
import BaseTable from '@/components/BaseTable';
import ImgPreview from '@/components/ImgPreview/index.vue';
import { ColumnProps } from '@/components/BaseTable/type';
import { getReportTableList } from '@/api/indicatorsReport';
import {
  IApiPatientReportListData,
  IApiPatientReportListParams,
} from '@/interface/type';
import { PropType } from 'vue';
import bus from '@/lib/bus';
import { formatTimeTemplate } from '@/utils';
import useComponentsTabAction from '@/store/module/useComponentsTabAction';
import { getTitleTime } from '@/api/disease';
import { isNil } from 'lodash-es';
import { idsTransform } from '@/components/DiseaseSelector/util';
import { FormModeValues } from '@/constant';

export type ITableReqParamProps = Partial<IApiPatientReportListParams>;
type ActionType = FormModeValues | 'delete';
export interface IHandleActionProps {
  actionType: ActionType;
  item: IApiPatientReportListData;
}
const props = defineProps({
  tableReqParam: {
    type: Object as PropType<ITableReqParamProps>,
    default: () => ({}),
  },
});
const emits = defineEmits<{
  (e: 'handleAction', info: IHandleActionProps): void;
}>();

const baseTableRef = shallowRef();

function renderAttachmentVNode(list: string[] | undefined, key: string) {
  if (!list?.length) return <span>--</span>;
  return (
    <div class="flex flex-wrap">
      {list?.map(item => (
        <ImgPreview
          class="mr-2xs my-2xs cursor-pointer"
          showStatus={false}
          url={item}
          width={50}
          type={key}
          fixed={true}
        />
      ))}
    </div>
  );
}

// 组装结论数据
function handleAssembleExamineLise(list?: any[]) {
  if (!list?.length) return [];
  const { showNameList } = idsTransform(
    list.map(conclusion => conclusion.conclusionId),
    list.reduce((pre, cur) => {
      const { conclusionId, name, remark, pid } = cur;
      return {
        ...pre,
        [conclusionId]: {
          ...cur,
          pId: pid,
          diseaseId: conclusionId,
          diseaseName: `${name}${remark ? '-' + remark : ''}`,
        },
      };
    }, {})
  );
  return showNameList;
}

const sourceTypeMap: Record<string, string> = {
  0: '住院',
  1: '门诊',
  2: '复查',
  3: '手动添加',
  4: '自动监测',
};

const bloodPressureStaMap: Record<string, string> = {
  0: '未开始',
  1: '进行中',
  2: '已结束',
};

// 表格配置项
const columns = computed<ColumnProps<IApiPatientReportListData>[]>(() => {
  const indexTermId = props.tableReqParam?.indexTermId;
  const list: ColumnProps<IApiPatientReportListData>[] = [
    {
      type: 'index',
      prop: 'id',
      label: '序号',
      width: 80,
      fixed: 'left',
      align: 'left',
    },
    {
      prop: 'sourceType',
      label: '来源',
      minWidth: 100,
      fixed: 'left',
      align: 'left',
      render: scope => {
        return sourceTypeMap[String(scope.row.sourceType)] || '--';
      },
    },
    {
      prop: 'checkTime',
      format: 'date',
      label: '检查日期',
      minWidth: 120,
      hidden: indexTermId === 85,
    },
    {
      prop: 'checkTime',
      format: 'date',
      label: '试验日期',
      minWidth: 120,
      hidden: indexTermId !== 85,
    },
    {
      prop: 'bloodPressureStatus',
      label: '状态',
      minWidth: 120,
      hidden: indexTermId !== -2,
      render: scope => {
        return bloodPressureStaMap[String(scope.row.bloodPressureStatus)];
      },
    },
    {
      prop: 'reportName',
      label: '报告名称',
      minWidth: 120,
      hidden: indexTermId !== -1,
    },
    {
      prop: 'accessory',
      label: '结论',
      align: 'left',
      showOverflowTooltip: false,
      minWidth: 220,
      hidden: ![60, 61, 67].includes(indexTermId!),
      render: scope => {
        return (
          handleAssembleExamineLise(scope.row.conclusions)?.reduce(
            (pre, cur) => {
              pre += pre ? `；${cur.text}` : cur.text;
              return pre;
            },
            ''
          ) || '--'
        );
      },
    },
    {
      prop: 'accessory',
      label: '附件',
      align: 'left',
      showOverflowTooltip: false,
      minWidth: 220,
      hidden: indexTermId === -2 || indexTermId === 85,
      render: scope => {
        const { accessory, sourceId = '1' } = scope.row;
        return renderAttachmentVNode(accessory, sourceId + '');
      },
    },
    {
      prop: 'userName',
      label: '添加人',
      minWidth: 100,
    },
    {
      prop: 'modifyTime',
      format: 'time',
      label: '添加时间',
      minWidth: 120,
    },
  ];

  const operation: ColumnProps<IApiPatientReportListData> = {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    minWidth: 180,
    render: scope => {
      return (
        <>
          {indexTermId === -2 ? (
            <el-button
              type="primary"
              link
              onClick={() => handleAction('view', scope.row)}
            >
              查看
            </el-button>
          ) : (
            <>
              <el-button
                type="primary"
                link
                onClick={() => handleAction('edit', scope.row)}
              >
                编辑
              </el-button>
              {indexTermId !== 85 && (
                <el-button
                  type="primary"
                  link
                  onClick={() => handleAction('view', scope.row)}
                >
                  查看
                </el-button>
              )}

              {scope.row.sourceType !== 3 && (
                <el-button type="primary" link onClick={() => toTab(scope.row)}>
                  {sourceTypeMap[String(scope.row.sourceType)]}详情
                </el-button>
              )}
              {scope.row.sourceType === 3 && (
                <el-button
                  type="danger"
                  link
                  onClick={() => handleAction('delete', scope.row)}
                >
                  删除
                </el-button>
              )}
            </>
          )}
        </>
      );
    },
  };

  list.push(operation);
  return list.filter(
    item => item.hidden === false || item.hidden === undefined
  );
});

const tabAction = useComponentsTabAction();

const toTab = async (item: IApiPatientReportListData) => {
  const { sourceId, sourceType } = item;
  if (!sourceId || isNil(sourceType)) return;
  const { titleTime } =
    (await getTitleTime({ sourceId, sourceType: sourceType! })) || {};
  tabAction.setAction({
    componentType: sourceType!,
    name: `${(titleTime && formatTimeTemplate(titleTime, 'YYYY-MM-DD')) || ''} ${sourceTypeMap[String(sourceType)]}`,
    data: {
      id: sourceId,
      recordActionType: {
        actionType: 'view',
        sourceType: sourceType!,
      },
    },
  });
  bus.emit('open-component-tab');
};

const handleAction = (
  actionType: ActionType,
  item: IApiPatientReportListData
) => emits('handleAction', { actionType, item });

defineExpose({ baseTableRef });
</script>
