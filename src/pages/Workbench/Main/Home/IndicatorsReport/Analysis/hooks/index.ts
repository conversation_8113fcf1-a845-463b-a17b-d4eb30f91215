import dayjs from 'dayjs';
import { IApiIndexFromQueryDataList } from '@/interface/type';
import { ActionType } from '../hooks/useUpdateIndicator';
import TablePanel from '../IndicatorPanel/TablePanel.vue';
import LineChart from '../IndicatorPanel/LineChart.vue';
import AverageChart from '../IndicatorPanel/AverageChart.vue';
import AnomalyChart from '../IndicatorPanel/AnomalyChart.vue';
import bus from '@/lib/bus';
import { formatTimeTemplate } from '@/utils';

export interface IHandleActionProps {
  option: {
    actionType: ActionType;
    item: IApiIndexFromQueryDataList;
  };
  templateName: string;
}

// 阈值设置弹框宽度
export const thresholdSetWithMap = {
  heartRate: 620,
  bloodLipid: 620,
  bloodPressure: 760,
  bloodSugar: 760,
};

// 时间筛选
export function useTimeSelect() {
  const times = [
    { type: '1', name: '一周内' },
    { type: '2', name: '15天内' },
    { type: '3', name: '30天内' },
    { type: '4', name: '60天内' },
    { type: '5', name: '近一年' },
    { type: '6', name: '自定义时间范围' },
  ];
  const timeType = ref('5');
  // 自定义时间
  const customRange = ref<[number, number] | undefined>();
  const displayCustomRange = computed(() => {
    const [start, end] = customRange.value || [];
    if (!start || !end) return '';
    return (
      formatTimeTemplate(start, 'YYYY-MM-DD') +
      ' 至 ' +
      formatTimeTemplate(end, 'YYYY-MM-DD')
    );
  });

  /** 日历面板 - 自定义时间范围选择（选择间隔为1年内） */
  const startDate = ref<Date | null>(null);
  function disabledDate(time: Date) {
    const yearsTime = 365 * 24 * 60 * 60 * 1000;
    const currentTimes = new Date().getTime();
    const times = time.getTime();
    if (startDate.value !== null) {
      return (
        times < startDate.value.getTime() - yearsTime ||
        times >= currentTimes ||
        times > startDate.value.getTime() + yearsTime
      );
    }
    return times >= currentTimes;
  }
  const handleCalendarChange = (dates: [Date, Date]) => {
    const hasSelectDate = dates !== null && dates.length > 0;
    startDate.value = hasSelectDate ? dates[0] : null;
  };
  const handleDateChange = (dates: [number, number]) => {
    customRange.value = dates;
    startDate.value = null;
    timeType.value = '6';
  };

  const reqParamTime = computed(() => getTimeRangeByType(timeType.value));

  function getTimeRangeByType(type: string) {
    if (type === '6') {
      if (customRange.value?.length) {
        return {
          start: customRange.value[0],
          end: customRange.value[1],
        };
      }
      return null;
    }

    const currentDate = dayjs(); // 获取当前日期

    const ranges: Record<string, number> = {
      '1': 7,
      '2': 15,
      '3': 30,
      '4': 60,
      '5': 1,
    };

    const rangeDays = ranges[type];

    if (rangeDays) {
      const startDate =
        type === '5'
          ? currentDate.subtract(rangeDays, 'year')
          : currentDate.subtract(rangeDays, 'day');
      return {
        start: dayjs(startDate).valueOf(),
        end: dayjs(currentDate).valueOf(),
      };
    }

    return null;
  }

  return {
    handleDateChange,
    handleCalendarChange,
    times,
    reqParamTime,
    customRange,
    displayCustomRange,
    disabledDate,
    timeType,
  };
}

export function useContent() {
  // 阈值设置弹框
  const showThresholdDialog = ref(false);

  // 重新获取表格数据
  const getTableList = () => {
    bus.emit('refresh-indicators-analysis-table');
  };

  // 指标内容组件Tab
  const indicTab = reactive([
    { id: 'tablePanel', name: '表格', component: markRaw(TablePanel) },
    { id: 'lineChart', name: '折线图', component: markRaw(LineChart) },
    { id: 'averageChart', name: '平均分布', component: markRaw(AverageChart) },
    { id: 'anomalyChart', name: '异常分布', component: markRaw(AnomalyChart) },
  ]);
  const indicActiveTab = ref(indicTab[0].id);

  return {
    indicTab,
    indicActiveTab,
    getTableList,
    showThresholdDialog,
  };
}
