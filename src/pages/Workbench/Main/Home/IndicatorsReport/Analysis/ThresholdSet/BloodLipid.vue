<template>
  <div class="threshold">
    <template v-for="item in configData" :key="item.level">
      <div class="title">
        <span class="text-danger mr-2xs">{{ item.warningInfo.title }}</span>
        {{ item.warningInfo.tips }}
      </div>
      <div class="flex items-center pt-3xs pb-md text-sm">
        <div class="w-80">{{ item.label }}</div>
        <div class="flex-c">
          <div>LDL-c（mmol/L）</div>
          <InputNumber
            v-model="item.threshold.value"
            type="number"
            :min="0"
            :max="999"
            :precision="2"
            :placeholder="`默认：${item.threshold.defaultVal}`"
            @blur="e => onInputBlur(e, item)"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { isNumber } from 'lodash-es';
import InputNumber from '@/components/InputNumber/index.vue';
import { IHeartRateOrBloodLipid } from '@/pages/Workbench/Main/Home/IndicatorsReport/Analysis/Content/hooks/useThresholdSet';
interface IProps {
  configData: IHeartRateOrBloodLipid[];
}
const props = withDefaults(defineProps<IProps>(), {
  configData: () => [],
});

const emits = defineEmits<{
  (e: 'deal-error'): void;
}>();

function onInputBlur(event: FocusEvent, currentItem: IHeartRateOrBloodLipid) {
  const val = +(event.target as HTMLInputElement).value;
  const { level } = currentItem;
  const firstLevelItem = props.configData.find(item => item.level === 0);
  const secondLevelItem = props.configData.find(item => item.level === 1);

  let errMsg = '';
  // 当前值
  let tempVal: number | undefined = val;

  switch (level) {
    case 0:
      {
        const secondLevelVal = secondLevelItem?.threshold.value;
        if (isNumber(secondLevelVal) && val >= secondLevelVal) {
          tempVal = secondLevelVal - 0.01;
          errMsg = `当前值应小于${secondLevelItem?.label},当前最大值为：`;
        }
      }
      break;
    case 1:
      {
        const firstLevelVal = firstLevelItem?.threshold.value;
        if (isNumber(firstLevelVal) && val <= firstLevelVal) {
          tempVal = firstLevelVal + 0.01;
          errMsg = `当前值应小于${firstLevelItem?.label},当前最大值为：`;
        }
      }
      break;
  }

  if (tempVal < 0 || tempVal > 999) tempVal = undefined;

  if (errMsg) {
    let msg = errMsg + tempVal;
    if (!tempVal) msg = '当前值应为小于999的数且满足阈值等级范围限制';
    currentItem.threshold.value = tempVal;
    emits('deal-error');
    ElMessage.error(msg);
  }
}
</script>
<style scoped lang="less">
.threshold {
  padding: 24px 100px 0;

  .title {
    color: #708293;
  }

  div.flex-bc:last-child {
    padding-bottom: 4px;
  }
}
</style>
