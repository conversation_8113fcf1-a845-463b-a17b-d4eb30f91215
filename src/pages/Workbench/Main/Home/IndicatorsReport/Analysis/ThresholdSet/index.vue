<template>
  <HrtDialog v-model="attrs.visible" @update:visible="handleClose">
    <component
      :is="ComponentMap[type]"
      :config-data="configData"
      @deal-error="dealError"
    />
    <template #footer>
      <div class="p-sm pr-5xl border-t">
        <span style="color: #708293" class="mr-2xs text-xs">
          以上阈值仅用于当前患者
        </span>
        <el-button @click="resetThreshold(type)">重置</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          :disabled="submitDisabled"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </div>
    </template>
  </HrtDialog>
</template>

<script setup lang="ts">
import { promiseTimeout } from '@vueuse/core';
import { HrtDialog } from '@hrt/components';
import BloodPressure from './BloodPressure.vue';
import HeartRate from './HeartRate.vue';
import BloodLipid from './BloodLipid.vue';
import BloodSugar from './BloodSugar.vue';
import useThresholdSet, {
  IThresholdSubmitParams,
} from '../hooks/useThresholdSet';
import { type Component } from 'vue';

defineOptions({
  name: 'ThresholdSet',
});
interface IProps {
  updateCommonParams: IThresholdSubmitParams;
}
const props = withDefaults(defineProps<IProps>(), {
  type: 'bloodPressure',
  updateCommonParams: undefined,
});

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'handle-update'): void;
}>();

const submitDisabled = ref(false);

const dealError = async () => {
  submitDisabled.value = true;
  await promiseTimeout(1000);
  submitDisabled.value = false;
};

const type = computed(() => props.updateCommonParams?.type || '');

const attrs = useAttrs() as {
  title: string;
  visible: boolean;
  [key: string]: unknown;
};

const ComponentMap: Record<string, Component> = {
  bloodPressure: BloodPressure,
  heartRate: HeartRate,
  bloodLipid: BloodLipid,
  bloodSugar: BloodSugar,
};

const handleClose = () => {
  emits('update:visible', false);
};

const {
  loading,
  setThresholdSubmit,
  resetThreshold,
  configDataMap,
  getThresholdDetail,
} = useThresholdSet();

const configData = computed(() => {
  return configDataMap[type.value];
});

function handleSubmit() {
  if (!props.updateCommonParams) return;
  setThresholdSubmit(props.updateCommonParams, () => {
    emits('handle-update');
    handleClose();
  });
}
watch(
  () => attrs.visible,
  visible => {
    const { patientId, templateName } = props.updateCommonParams;
    if (visible && patientId && templateName) {
      getThresholdDetail(patientId, templateName, type.value);
    }
  }
);
</script>
