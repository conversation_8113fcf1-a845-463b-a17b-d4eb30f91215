<template>
  <div class="threshold">
    <template v-for="item in configData" :key="item.level">
      <div class="title">
        <span class="text-danger mr-2xs">{{ item.warningInfo.title }}</span>
        {{ item.warningInfo.tips }}
      </div>
      <div class="flex items-center pt-3xs pb-md text-sm">
        <div class="w-80">{{ item.label }}</div>
        <div class="flex-c">
          <div>心率（次/分）</div>
          <InputNumber
            v-model="item.threshold.value"
            type="number"
            step-strictly
            :min="1"
            :max="999"
            :placeholder="`默认：${item.threshold.defaultVal}`"
            :disabled="item.threshold.disabled"
            @blur="e => onInputBlur(e, item)"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import InputNumber from '@/components/InputNumber/index.vue';
import { IHeartRateOrBloodLipid } from '@/pages/Workbench/Main/Home/IndicatorsReport/Analysis/Content/hooks/useThresholdSet';
interface IProps {
  configData: IHeartRateOrBloodLipid[];
}
const props = withDefaults(defineProps<IProps>(), {
  configData: () => [],
});

const emits = defineEmits<{
  (e: 'deal-error'): void;
}>();

function onInputBlur(event: FocusEvent, currentItem: IHeartRateOrBloodLipid) {
  const val = +(event.target as HTMLInputElement).value;
  if (!val) return;
  const { level } = currentItem;
  const prevLevelItem = props.configData.find(item => item.level === level - 1);
  const nextLevelItem = props.configData.find(item => item.level === level + 1);
  let errMsg = '';
  // 当前值
  let tempVal: number | undefined = val;

  const nextLevelRateVal = nextLevelItem?.threshold.value;
  const prevLevelRateVal = prevLevelItem?.threshold.value;

  if ([0, 1].includes(level) && nextLevelRateVal && val >= nextLevelRateVal) {
    tempVal = nextLevelRateVal - 1;
    errMsg = `当前值应小于${nextLevelItem.label},当前最大值为：`;
  } else if (
    [1, 2].includes(level) &&
    prevLevelRateVal &&
    val <= prevLevelRateVal
  ) {
    tempVal = prevLevelRateVal + 1;
    errMsg = `当前值应大于${prevLevelItem.label},当前最小值为：`;
  }

  if (tempVal < 1 || tempVal > 999) tempVal = undefined;

  if (errMsg) {
    let msg = errMsg + tempVal;
    if (!tempVal) msg = '当前值应为小于等于999的正整数且满足阈值等级范围限制';
    currentItem.threshold.value = tempVal;
    emits('deal-error');
    ElMessage.error(msg);
  }
}
</script>
<style scoped lang="less">
.threshold {
  padding: 24px 100px 0;

  .title {
    color: #708293;
  }

  div.flex-bc:last-child {
    padding-bottom: 4px;
  }
}
</style>
