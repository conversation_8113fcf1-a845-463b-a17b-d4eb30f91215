<template>
  <div class="dialog-content pt-sm">
    <div
      v-for="item in configList"
      :key="item.key"
      class="flex-c items-start pb-xs"
    >
      <div class="dialog-content-title">{{ item.name }}</div>
      <div
        v-if="item.key && item.type !== 'BMI' && item.type !== 'Balance'"
        class="flex-1"
      >
        <template v-if="item.type === 'date'">
          <el-date-picker
            v-model="data[item.key]"
            type="datetime"
            placeholder="请选择"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="x"
            :disabled-date="(time: Date) => time.getTime() > Date.now()"
          />
          <div class="dialog-content-tips">指标测量时间，精确到时间点。</div>
        </template>
        <template v-if="item.type === 'select'">
          <el-select
            v-model="data[item.key]"
            placeholder="请选择"
            style="width: 220px"
          >
            <el-option
              v-for="op in item.options"
              :key="op.value"
              :label="op.label"
              :value="op.value"
            />
          </el-select>
        </template>
        <template v-if="item.type === 'input-number'">
          <input-number v-model="data[item.key]" />
          {{ item.unit }}
        </template>
      </div>
      <div v-if="item.type === 'BMI' && item.BMI" class="flex-1">
        <div class="bmi">{{ bmi }}</div>
        <div class="dialog-content-tips">自动计算：体重（Kg）÷ 身高（m）²</div>
      </div>
      <div v-if="item.type === 'Balance'" class="flex-1">
        <div class="bmi">{{ balanceStr }}</div>
        <div class="dialog-content-tips">自动计算：饮水量-尿量</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import InputNumber from '@/components/InputNumber/index.vue';
import { IAddIndicator } from './hooks/useUpdateIndicator';
import { getBMI } from '@/utils';
import { isNil } from 'lodash-es';
interface IndicatorProps {
  configList: IAddIndicator[];
  itemData?: object;
}
const props = withDefaults(defineProps<IndicatorProps>(), {
  configList: () => [],
  itemData: () => ({}),
});

const data = ref<object>({});

const bmi = computed(() => {
  const BMIConfig = props.configList.find(
    item => item.type === 'BMI' && item.BMI
  );
  const { heightKey, weightKey } = BMIConfig?.BMI || {};
  const height = heightKey && data.value[heightKey];
  const weight = weightKey && data.value[weightKey];
  if (!height || !weight) return '--';

  return getBMI(height, weight);
});

const balanceStr = computed(() => {
  const balanceConfig = props.configList.find(
    item => item.type === 'Balance' && item.balance
  );
  const { waterVolumeKey, urineOutputKey } = balanceConfig?.balance || {};
  const waterVolume = waterVolumeKey && data.value[waterVolumeKey];
  const urineOutput = urineOutputKey && data.value[urineOutputKey];
  if (!waterVolume || !urineOutput) return '--';
  if (waterVolume - urineOutput > 0) {
    return waterVolume - urineOutput + ' (正平衡)';
  } else if (waterVolume - urineOutput < 0) {
    return waterVolume - urineOutput + ' (负平衡)';
  } else {
    return '0';
  }
});

watch(bmi, v => {
  const BMIConfig = props.configList.find(
    item => item.type === 'BMI' && item.BMI
  );
  if (BMIConfig?.key) {
    data.value[BMIConfig?.key] = v;
  }
});

watch(
  () => props.itemData,
  (itemData: any) => {
    const currentData = { ...itemData };
    props.configList.forEach(config => {
      const defaultVal = config.defaultVal;
      if (!isNil(defaultVal) && isNil(currentData[config.key])) {
        currentData[config.key] = defaultVal;
      }
    });
    data.value = currentData;
  },
  { immediate: true }
);

function clear() {
  data.value = {};
}

defineExpose({
  data,
  clear,
});
</script>
<style scoped lang="less">
.dialog {
  &-content {
    color: #8193a3;
    font-size: var(--text-size-sm);
    &-title {
      width: 30%;
      color: #3a4762;
      font-weight: 500;
      line-height: 32px;
      text-align: right;
      box-sizing: border-box;
      padding-right: var(--spacing-sm);
    }
    &-tips {
      margin-top: var(--spacing-2xs);
      color: #7a8599;
    }

    .bmi {
      line-height: 32px;
    }
  }
}
</style>
