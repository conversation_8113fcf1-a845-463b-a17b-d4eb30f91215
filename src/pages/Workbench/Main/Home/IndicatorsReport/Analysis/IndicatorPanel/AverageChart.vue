<template>
  <div class="chart">
    <div class="chart-switch">
      <el-radio-group v-model="switchType">
        <el-radio-button :value="1">日平均</el-radio-button>
        <el-radio-button :value="2">周平均</el-radio-button>
        <el-radio-button :value="3">月平均</el-radio-button>
      </el-radio-group>
    </div>

    <div v-if="riskDesc" class="py-sm">
      <RiskDescription :info="riskDesc" />
    </div>

    <div class="chart-content">
      <BaseChart
        type="line"
        :options="averageOptions"
        :data-complete="!loading"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseChart from '@/components/BaseChart';
import RiskDescription from '../RiskDescription.vue';
import { PropType } from 'vue';
import { useChartData } from '../hooks/useChartData';
const props = defineProps({
  indicActiveTab: {
    type: String,
    default: '',
  },
  reqParam: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
});

const {
  averageOptions,
  getAverageLineChartData,
  switchType,
  riskDesc,
  loading,
} = useChartData();

watch(
  [switchType, () => props.indicActiveTab],
  data => {
    if (props.indicActiveTab === 'averageChart') {
      const { patientId, templateName, checkType } = props.reqParam || {};
      getAverageLineChartData({
        patientId,
        templateName,
        checkType,
        timeType: data[0],
      });
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style scoped lang="less">
.chart {
  &-switch {
    :deep(.el-radio-button__original-radio + .el-radio-button__inner) {
      width: 72px;
      box-sizing: border-box;
    }
    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      border-color: #0a73e4;
      background: #ebf6ff;
      color: #0a73e4;
    }
  }
  &-content {
    height: 360px;
  }
}
</style>
