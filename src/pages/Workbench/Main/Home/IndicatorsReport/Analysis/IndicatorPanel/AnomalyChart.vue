<template>
  <div class="chart">
    <div class="chart-switch">
      <el-radio-group v-model="anomalySwitchType">
        <el-radio-button :value="1">周</el-radio-button>
        <el-radio-button :value="2">月</el-radio-button>
      </el-radio-group>
    </div>
    <div v-if="riskDesc" class="py-sm">
      <RiskDescription :info="riskDesc" />
    </div>
    <div class="chart-content">
      <BaseChart
        type="bar"
        :options="anomalyOptions"
        :data-complete="!loading"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseChart from '@/components/BaseChart';
import RiskDescription from '../RiskDescription.vue';
import { PropType } from 'vue';
import { useChartData } from '../hooks/useChartData';
const props = defineProps({
  indicActiveTab: {
    type: String,
    default: '',
  },
  reqParam: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
});

const {
  anomalyOptions,
  getAnomalyChartData,
  anomalySwitchType,
  riskDesc,
  loading,
} = useChartData();

watch(
  [anomalySwitchType, () => props.indicActiveTab],
  data => {
    if (props.indicActiveTab === 'anomalyChart') {
      const { patientId, templateName, checkType } = props.reqParam || {};
      getAnomalyChartData({
        patientId,
        templateName,
        checkType,
        timeType: data[0],
      });
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style scoped lang="less">
.chart {
  &-switch {
    :deep(.el-radio-button__original-radio + .el-radio-button__inner) {
      width: 72px;
      box-sizing: border-box;
    }
    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      border-color: #0a73e4;
      background: #ebf6ff;
      color: #0a73e4;
    }
  }
  &-content {
    height: 360px;
  }
}
</style>
