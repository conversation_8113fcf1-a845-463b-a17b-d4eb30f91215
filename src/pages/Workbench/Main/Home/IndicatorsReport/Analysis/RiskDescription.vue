<template>
  <div class="risk-description p-xs text-sm">
    <div class="survey leading-lg">
      患者近{{ info.frequency }}共测量{{ info.title
      }}{{ isNumber(info.num) ? info.num : '--' }}次，报告异常
      <span :class="{ 'text-danger': <PERSON><PERSON><PERSON>(info.errorNum) }">
        {{ isNumber(info.errorNum) ? info.errorNum : '--' }}
      </span>
      次
    </div>
    <div v-for="(item, index) of info.itemList" :key="index" class="leading-lg">
      {{ item.name }}最高
      <span :class="{ 'text-danger': item.maxAbnormal }">{{ item.max }}</span>
      ，最低
      <span :class="{ 'text-danger': item.minAbnormal }">{{ item.min }}</span>
      <template v-if="info.showAvg">
        ，平均
        <span>{{ item.avg }}</span>
        <span v-if="item.ttR">{{ `，TTR为${item.ttR}` }}</span>
      </template>
      ；
    </div>
  </div>
</template>

<script setup lang="ts">
import { RiskDescProps } from './hooks/useAbnormalContent';
import { isNumber } from 'lodash-es';
interface IProps {
  info: RiskDescProps;
}
withDefaults(defineProps<IProps>(), {
  info: () => ({
    title: '',
    frequency: '',
    num: 0,
    errorNum: 0,
    itemList: [],
  }),
});
</script>
<style scoped>
.risk-description {
  color: #3a4762;
  border-radius: 4px;
  background: #ecf4fc;
}
</style>
