<template>
  <div class="indicator w-full px-xs flex justify-between text-sm">
    <div class="indicator-title py-xs flex-shrink-0">指标项：</div>
    <EllipsisContainer :line-height="32">
      <div class="indicator-content py-6 flex-1 flex px-3xs flex-wrap">
        <div
          v-for="(item, index) of list"
          :key="item?.id"
          class="px-12 py-6 text-nowrap whitespace-nowrap cursor-pointer"
          :class="[
            activeItem?.id === item?.id ? 'text-primary font-medium' : '',
            item.isGray && index > 4 ? 'forbidden-indicator' : '',
          ]"
          @click="handleClick(item, index)"
        >
          {{ item?.name }}
          <el-badge :is-dot="item?.isError" />
        </div>
      </div>
    </EllipsisContainer>
  </div>
</template>

<script setup lang="ts">
import EllipsisContainer from '@/components/EllipsisContainer/index.vue';

export interface IIndicator {
  id: string | number;
  name: string;
  isError?: boolean;
  isGray?: boolean;
}
interface IndicatorProps {
  list: IIndicator[];
  activeItem?: IIndicator;
}
withDefaults(defineProps<IndicatorProps>(), {
  list: () => [],
  activeItem: undefined,
});
const emit = defineEmits<{ (e: 'handle-click', item: IIndicator): void }>();
const handleClick = (item: IIndicator, index: number) => {
  if (item.isGray && index > 4) return;
  emit('handle-click', item);
};
</script>
<style scoped lang="less">
.indicator {
  background: #f7f8fa;
  border-radius: 4px;
  &-title {
    color: #203549;
  }
  &-content {
    color: #8193a3;
  }
}
.forbidden-indicator {
  color: #d4d9d6;
  cursor: not-allowed;
}
</style>
