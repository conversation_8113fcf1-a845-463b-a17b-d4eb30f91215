<template>
  <Dialog
    :visible="visible"
    :title="exhibitionInfo.title"
    :width="exhibitionInfo.width"
    @update:visible="handleClose"
  >
    <div class="inspection">
      <template v-if="step === 1">
        <div class="text-base text-sub-text pb-2xs">
          请选择您要申请的检查项目
        </div>
        <el-radio-group :model-value="1" class="ml-4">
          <el-radio :label="1">动态血压监测</el-radio>
        </el-radio-group>
      </template>
      <template v-else>
        <div class="content-item">
          <div class="content-item-title">选择时间</div>
          <div class="flex-1">
            <el-date-picker
              v-model="data.remindTime"
              type="date"
              placeholder="请选择"
              value-format="x"
              format="YYYY-MM-DD"
              :disabled-date="(time: Date) => time.getTime() < Date.now()"
            />
            <div class="text-sub-text pt-2xs">开始日期当日6:00开始首次测量</div>
          </div>
        </div>
        <div class="content-item">
          <div class="content-item-title">选择时间</div>
          <div class="flex-1">
            <el-radio-group v-model="data.monitorType">
              <el-radio :label="1">24h</el-radio>
              <el-radio :label="2">48h</el-radio>
              <el-radio :label="3">72h</el-radio>
            </el-radio-group>
            <div class="text-sub-text">动态血压监测总时长</div>
          </div>
        </div>
        <div class="content-item">
          <div class="content-item-title">监测频率</div>
          <div class="flex-1 leading-[32px]">分钟/次</div>
        </div>
        <div class="content-frequency mb-sm">
          <div class="content-item">
            <div class="content-item-title">白昼</div>
            <div class="flex-1">
              <el-radio-group v-model="data.earlyFrequency">
                <el-radio :label="15">15</el-radio>
                <el-radio :label="20">20</el-radio>
                <el-radio :label="25">25</el-radio>
                <el-radio :label="30">30</el-radio>
              </el-radio-group>
              <div class="text-sub-text">6:00-22:00，建议15-30分钟测量1次</div>
            </div>
          </div>
          <div class="content-item">
            <div class="content-item-title">夜晚</div>
            <div class="flex-1">
              <el-radio-group v-model="data.lateFrequency">
                <el-radio :label="30">30</el-radio>
              </el-radio-group>
              <div class="text-sub-text">22:00-次日6:00，建议30分钟测量1次</div>
            </div>
          </div>
        </div>
        <div class="text-sub-text flex">
          <div class="shrink-0">注意：</div>
          <div>
            <p>
              1.若动态血压测量任务中途未达柝率超30％，则系统会自动终止本次动态血压测量任务；
            </p>
            <p>2.动态血压监测任务下发后请及时告知用户。</p>
          </div>
        </div>
      </template>
    </div>
    <template #footer>
      <div class="button-group flex-c pb-2xl">
        <el-button :loading="loading" type="primary" @click="handleAction">
          {{ exhibitionInfo.btnText }}
        </el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import Dialog from '@/components/Dialog/index.vue';
import { addWatchMonitor } from '@/api/indicatorsReport';
import bus from '@/lib/bus';
import store from '@/store';

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

const globalData = store.useGlobal();
const userStore = store.useUserStore();

const data = ref({
  remindTime: '',
  monitorType: 1,
  earlyFrequency: 15,
  lateFrequency: 30,
});

const step = ref(1);

const loading = ref(false);

const exhibitionInfo = computed(() => {
  return step.value === 1
    ? { btnText: '下一步', title: '检查申请', width: 400 }
    : { btnText: '确认', title: '发起动态血压监测', width: 600 };
});

const handleClose = () => {
  emits('update:visible', false);
  step.value = 1;
  data.value.remindTime = '';
};

const save = async () => {
  const { userId, currentRole } = globalData;
  loading.value = true;
  try {
    await addWatchMonitor({
      patientId: userId!,
      uid: userStore.accountId!,
      roleType: currentRole,
      ...data.value,
    });
    ElMessage.success('申请成功');
  } finally {
    loading.value = false;
  }
  handleClose();
  bus.emit('refresh-indicators-original-table');
};

const handleAction = () => {
  if (step.value === 1) {
    step.value = 2;
  } else {
    if (!data.value.remindTime) return ElMessage.error('请选择检测开始时间');
    save();
  }
};
</script>
<style scoped lang="less">
.inspection {
  padding: 16px 80px 8px 102px;

  .content-item {
    display: flex;
    padding-bottom: 8px;

    &-title {
      color: var(--color-primary-text);
      font-weight: 500;
      padding-right: 16px;
      line-height: 32px;
    }
  }

  .content-frequency {
    padding: 16px 28px;
    background: #f7f8fa;
  }
}
.button-group {
  .el-button {
    width: 76px;
    &.el-button--primary {
      background-color: var(--color-primary);
      border-color: var(--color-primary);
    }
    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
