<template>
  <div class="procedure-box flex items-center justify-center pt-16 pb-16">
    <div
      v-for="(item, index) in props.procedureList"
      :key="item.id"
      class="item ml-14 mr-14 cursor-pointer"
      @click="jump(item.id)"
    >
      <span :class="{ itemActive: item.id === active }">
        {{ item.title }}
      </span>
      <span>{{ item.des }}</span>
      <span v-if="index < procedureList.length - 1" class="separator">
        ......
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface IProps {
  patientData?: object;
  procedureList?: any;
  defalutActive?: string;
}
const props = defineProps<IProps>();
// 锚点定位
const active = ref(props.defalutActive);

const jump = (id: string) => {
  active.value = id; // 设置选中的锚点为当前点击的
  const scrollItem = document.getElementById(id) as HTMLElement | null;
  scrollItem?.scrollIntoView({ behavior: 'smooth', block: 'start' });
};
</script>

<style scoped lang="less">
.procedure-box {
  background: #ffffff;
  box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
  z-index: 1000;
  .item {
    font-size: 14px;
    color: #7a8599;
    position: relative;
    .separator {
      top: -4px;
      right: -24px;
      position: absolute;
    }
  }
  .itemActive {
    font-size: 14px;
    font-weight: 600;
    color: #2e6be6;
  }
}
</style>
