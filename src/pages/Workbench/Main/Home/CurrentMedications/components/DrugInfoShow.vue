<template>
  <div class="drug-info">
    <div class="current-info flex justify-between px-12 pb-8">
      <span>
        确认时间：{{
          drugInfo.createTime
            ? dayjs(drugInfo.createTime).format('YYYY-MM-DD HH:mm')
            : '--'
        }}
      </span>
      <span>确认人：{{ drugInfo.userName || '--' }}</span>
      <span>
        开始用药日期：{{
          drugInfo.medicationTime
            ? dayjs(drugInfo.medicationTime).format('YYYY-MM-DD')
            : '--'
        }}
      </span>
    </div>
    <AListOfMedications
      :show-stop-status="props.drugInfo.isStop"
      :table-data="props.tableData"
    />
  </div>
</template>

<script setup lang="ts">
import AListOfMedications from './AListOfMedications.vue';
import dayjs from 'dayjs';

interface IDrugInfo {
  createTime?: any;
  userName?: string;
  medicationTime?: string | number;
  isStop?: boolean;
}
interface IProps {
  showStopStatus?: boolean;
  drugInfo: IDrugInfo;
  tableData?: Array<any>;
}
const props = defineProps<IProps>();
</script>
<style scoped lang="less">
.drug-info {
  .current-info {
    border-bottom: 3px solid #7a8599;
    position: relative;
    font-size: 14px;
    color: #101b25;
    &:after {
      width: 100%;
      content: '';
      display: inline-block;
      border-bottom: 1px solid #7a8599;
      position: absolute;
      bottom: -7px;
      left: 0;
    }
  }
  .more-btn {
    .link {
      font-size: 14px;
      color: #2e6be6;
      display: flex;
      align-items: center;
      &:hover {
        color: #2e6be6;
        opacity: 0.75;
        cursor: pointer;
      }
      &:active {
        color: #2e6be6 !important;
        opacity: 1;
      }
      .icon-arrow {
        transform: rotate(90deg);
      }
    }
  }
  :deep(.drug-list) {
    margin-top: 8px;
    .el-table__inner-wrapper::before {
      background: none;
    }
    .el-table__header-wrapper {
      margin-bottom: 6px;
    }
    .head-class {
      background: #f7f8fa;

      .el-table__cell {
        background-color: #f7f8fa !important;
        padding: 6px 0;
        .cell {
          font-size: 14px;
          font-weight: normal !important;
          color: #708293;
          min-width: 60px;
          height: 20px;
        }
      }
    }
    .el-table__body-wrapper {
      .el-table__body {
        .el-table__cell {
          font-size: 14px;
          border: none;
          padding: 6px 0;
          .cell {
            color: #203549;
            line-height: 20px;
          }
        }
      }
    }
    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: transparent;
    }
    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border-bottom: none;
    }
  }
  .stop-status {
    position: relative;
    :deep(.el-table__cell) .cell {
      color: #b8becc !important;
    }
    .link {
      color: #b8becc !important;
    }
    .status-stop {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      .text {
        border-radius: 4px;
        border: 3px solid #15233f;
        font-weight: bold;
        font-size: 32px;
        color: #3a4762;
        line-height: 38px;
        height: 46px;
        text-align: center;
        margin: 80px 0 0 235px;
        transform: rotate(-30deg);
      }
      z-index: 999;
    }
  }
}
</style>
