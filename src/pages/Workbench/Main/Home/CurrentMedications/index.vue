<template>
  <CardWrapper title="当前用药" class="overview-box">
    <template #tools>
      <HrtButton link class="link" @click="showHistoryDrugDrawer = true">
        历史用药
        <el-icon class="icon-add"><i-ep-arrow-right /></el-icon>
      </HrtButton>
    </template>
    <div v-loading="loading">
      <div v-if="!drugInfo.drugInfoId" class="no-data">暂无用药信息</div>
      <DrugInfoShow
        v-else
        :show-stop-status="drugInfo.isStop"
        :drug-info="drugInfo"
        :table-data="tableData"
      />
      <div class="h-60 m flex px-12 operation-btn">
        <HrtButton
          v-show="!drugInfo.isStop && drugInfo.drugInfoId"
          link
          class="link color-red"
          @click="stopTakingTheMedication"
        >
          停止用药
        </HrtButton>
        <div class="h-60 flex-1 flex items-center justify-end">
          <HrtButton
            v-show="!drugInfo.isStop && drugInfo.drugInfoId"
            class="one"
            @click="continueToTakeMedications"
          >
            继续当前用药
          </HrtButton>
          <HrtButton class="two" @click="chooseDrugVisible = true">
            调药
          </HrtButton>
        </div>
      </div>
    </div>
    <EditDrug
      v-if="chooseDrugVisible"
      v-model:choose-drug-visible="chooseDrugVisible"
      :params-data="tableData"
      @confirm-drug="handleAdjustDrug"
      @cancel-edit="chooseDrugVisible = false"
    />
    <!--  历史用药记录列表-->
    <HrtDrawer v-model="showHistoryDrugDrawer" title="历史用药" size="middle">
      <HistoryDrug ref="RefHistoryDrug" />
    </HrtDrawer>
  </CardWrapper>
</template>

<script setup lang="ts">
import EditDrug from '@/components/DrugInfo/components/EditDrug.vue';
import CardWrapper from '@/components/CardWrapper/index.vue';
import DrugInfoShow from './components/DrugInfoShow.vue';
import HistoryDrug from './components/HistoryDrug.vue';
import { debounce } from 'lodash-es';
import {
  getDrugPatientAdjust,
  getDrugPatientCurrent,
  getDrugPatientStop,
} from '@/api/drug';
import useIM from '@/store/module/useIM';
import useGlobal from '@/store/module/useGlobal';
const chooseDrugVisible = ref(false);
const showHistoryDrugDrawer = ref(false);

interface DrugInfo {
  drugInfoId?: number;
  medicationTime?: string;
  isStop?: boolean;
  userName?: string;
  createTime?: string | null;
}
interface IDrugAmount {
  contentUnit: string;
  ingredients: string;
}
interface IDrugSpec extends IDrugAmount {
  packageNum: string;
  packageUnit: string;
  unit: string;
}
interface ITableData {
  commonName?: string;
  drugAmount: IDrugAmount;
  drugMode: string;
  drugName: string;
  drugSpec: IDrugSpec;
  drugUsage: string;
  medicineTime: number;
}

const tableData = ref<ITableData[]>([]);
const drugInfo = ref<DrugInfo>({});
const drugInfoAll = ref({});
const loading = ref(false);
const global = useGlobal();
const imStore = useIM();
const RefHistoryDrug = ref();

const getDrugPatientCurrents = patientId => {
  tableData.value = [];
  drugInfo.value = {};
  drugInfoAll.value = {};
  loading.value = true;
  getDrugPatientCurrent({ patientId })
    .then(res => {
      loading.value = false;
      drugInfoAll.value = JSON.parse(JSON.stringify(res));
      drugInfo.value = {
        drugInfoId: res.drugInfoId,
        medicationTime: res.medicationTime || res.createTime,
        isStop: res.isStop,
        userName: res.userName,
        createTime:
          res.isStop && res.stopTime ? res.stopTime || null : res.createTime,
      };
      tableData.value = res.drugList.map(v => {
        delete v.drugAmount['custom'];
        return v;
      });
    })
    .catch(() => {
      tableData.value = [];
      drugInfo.value = {};
      drugInfoAll.value = {};
      loading.value = false;
    });
};
const getDrugPatientStops = (drugInfoId: number) => {
  getDrugPatientStop({ drugInfoId })
    .then(() => {
      ElMessage.success('停止用药成功!');
      getDrugPatientCurrents(global.userId);

      if (showHistoryDrugDrawer.value) {
        RefHistoryDrug.value.getDrugPatientHistorys(1);
      }
    })
    .catch(() => {
      ElMessage.error('停止用药失败!');
    });
};
const handleAdjustDrug = debounce((obj, type) => {
  obj.patientId = global.userId;
  // console.log(JSON.stringify(obj));
  getDrugPatientAdjust(obj)
    .then(res => {
      ElMessage.success(`${type === 2 ? '继续' : '调整'}用药成功!`);
      getDrugPatientCurrents(global.userId);
      chooseDrugVisible.value = false;
      if (obj.adjustDrugTrack) {
        // 跟踪用药需要发送药物调整消息卡片
        imStore.sendPatientCustomMsg({
          content: {
            name: '药物调整',
            id: res.drugInfoId,
            type: 7,
          },
        });
      }
      if (showHistoryDrugDrawer.value) {
        RefHistoryDrug.value.getDrugPatientHistorys(1);
      }
    })
    .catch(() => {
      ElMessage.error(`${type === 2 ? '继续' : '调整'}用药失败!`);
    });
}, 600);
const stopTakingTheMedication = () => {
  ElMessageBox.confirm(
    '停止用药后用药方案中的内容将清空；如要再次开启用药方案，需通过”调药“功能完成。',
    '是否确认患者已停止用药？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      draggable: true,
    }
  )
    .then(() => {
      getDrugPatientStops(Number(drugInfo.value.drugInfoId));
    })
    .catch(() => {});
};
const continueToTakeMedications = () => {
  ElMessageBox.confirm(
    '继续用药表示患者无需调整用药方案；' +
      '每次确认都将生成一条处理记录，请勿频繁操作；' +
      '如有药物调整情况，请使用“调药”功能。',
    '是否确认患者继续当前用药？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      let params = {
        drugDetailList: drugInfoAll.value.drugList,
        adjustDrugTrack: false,
        adjustReason: ['其他'],
        adjustReasonOther: '继续上次用药',
        drugOperation: '',
        type: 3,
        continueDrug: true,
        medicationTime: new Date().getTime(),
      };
      handleAdjustDrug(params, 2);
    })
    .catch(() => {});
};

onMounted(() => {
  getDrugPatientCurrents(global.userId);
});
watch(
  () => global.userId,
  () => {
    getDrugPatientCurrents(global.userId);
  }
);
</script>
<style scoped lang="less">
.overview-box {
  .manage-duration {
    width: 100%;
    height: 32px;
    background: #f6f8fb;
    border-radius: 4px;
    align-items: center;
    font-size: 14px;
    color: #de901e;
    padding: 0 12px;
    margin-bottom: 10px;
  }
}
.no-data {
  font-size: 20px;
  color: #b8becc;
  line-height: 28px;
  text-align: center;
  margin: 24px 0 40px 0;
}
.link {
  font-size: 14px;
  color: #2e6be6;
  display: flex;
  align-items: center;
  &:hover {
    color: #2e6be6;
    opacity: 0.75;
    cursor: pointer;
  }
  &:active {
    color: #2e6be6 !important;
    opacity: 1;
  }
}
.operation-btn {
  border-top: 1px solid #e9e8eb;
  .color-red {
    font-size: 14px;
    color: #e63746;
    &:hover {
      color: #e63746;
      opacity: 0.75;
      cursor: pointer;
    }
    &:active {
      color: #e63746 !important;
      opacity: 1;
    }
  }
  .one {
    width: 108px;
    height: 28px;
    font-size: 14px;
    color: #2e6be6;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #2e6be6;
    box-sizing: border-box;
  }
  .two {
    width: 60px;
    height: 28px;
    font-size: 14px;
    color: #ffffff;
    background: #2e6be6;
    border-radius: 2px;
    border: 1px solid #2e6be6;
    box-sizing: border-box;
  }
  .el-button {
    &:hover {
      opacity: 0.75;
      cursor: pointer;
    }
    &:active {
      opacity: 1;
    }
  }
}
</style>
