<template>
  <div>
    <HrtDialog
      v-model="props.visible"
      title="新增个性化随访"
      width="600"
      class="followup-dialog"
      @close="close"
    >
      <div class="pb-16 h-180">
        <div class="item">
          <span class="label">随访日期</span>
          <el-date-picker
            v-model="followupTime"
            type="date"
            placeholder="请选择时间"
            size="default"
            value-format="x"
            :disabled-date="disabledDateFn"
            :clearable="false"
          />
          <img :src="changeTime" alt="" class="w-13 h-13 change-time-icon" />
        </div>
        <div class="item">
          <div class="label">随访类型</div>
          <el-radio-group v-model="followupType" @change="typeChange">
            <el-radio :value="1">症状随访</el-radio>
            <el-radio :value="2">生活方式随访</el-radio>
          </el-radio-group>
        </div>
        <div class="item">
          <div class="label">随访表单</div>
          <el-select
            v-model="followupValue"
            placeholder="请选择"
            multiple
            style="width: 360px"
          >
            <el-option
              v-for="item in options"
              :key="item.questionnaireId"
              :label="item.questionnaireName"
              :value="item.questionnaireId"
            />
          </el-select>
        </div>
      </div>
      <div class="btns">
        <div class="label">重点跟踪</div>
        <div class="item">
          <HrtSwitch v-model="keyFollow" />
          <span class="ml-8">
            重点跟踪的随访会自动生成随访提醒和跟进两个待办
          </span>
        </div>
        <div class="flex items-center justify-end">
          <div
            class="cancel w-76 h-32 flex items-center justify-center cursor-pointer mr-8"
            @click="cancel"
          >
            取消
          </div>
          <div
            class="sure w-76 h-32 flex items-center justify-center cursor-pointer"
            @click="confirm"
          >
            确认
          </div>
        </div>
      </div>
    </HrtDialog>
  </div>
</template>

<script setup lang="ts">
import changeTime from '@/assets/imgs/callCenter/change-time.png';
import { addFollow } from '@/api/followup';
import useMeta from '@/store/module/useMeta';
import useGlobal from '@/store/module/useGlobal';
import bus from '@/lib/bus';
interface IProps {
  visible: boolean;
}

const globalStore = useGlobal();
const metaData = useMeta();
const emit = defineEmits(['close']);
const props = withDefaults(defineProps<IProps>(), {
  visible: false,
});

const followupTime = ref('');
const followupType = ref(1);
const followupValue = ref([]);
const keyFollow = ref(false);

const options = computed(() => {
  return metaData.followUpOptions[followupType.value - 1];
});
const typeChange = () => {
  followupValue.value = [];
};
const disabledDateFn = (time: { getTime: () => number }) => {
  if (time.getTime() < Date.now() - 8.64e7) {
    return true;
  } else {
    return false;
  }
};

const close = () => {
  emit('close');
  followupTime.value = '';
  followupType.value = 1;
  followupValue.value = [];
  keyFollow.value = false;
};
const getQuestionList = (list: number[]) => {
  return options.value.filter(v => list.includes(v.questionnaireId));
};
const confirm = async () => {
  if (!followupTime.value) {
    ElMessage.error('请选择随访日期');
    return;
  }
  if (!followupValue.value.length) {
    ElMessage.error('请选择随访表单');
    return;
  }
  const params = {
    patientId: globalStore.userId,
    date: followupTime.value,
    type: followupType.value,
    followQuestionnaireList: getQuestionList(followupValue.value),
    keyFollow: keyFollow.value,
  };
  await addFollow(params);
  ElMessage.success('操作成功！');
  bus.emit('refresh-followup-list');
  close();
};
const cancel = () => {
  close();
};

defineOptions({
  name: 'AddFollowUpModal',
});
</script>

<style scoped lang="less">
:deep(.followup-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #e9e8eb;
    margin-right: 0;
    .el-dialog__title {
      font-size: 16px;
      font-weight: bold;
      color: #101b25;
    }
  }
  .el-dialog__headerbtn {
    top: 0;
  }
  .el-dialog__body {
    padding: 16px 24px;
    .item {
      position: relative;
      display: inline-flex;
      height: 32px;
      align-items: center;
      margin-bottom: 16px;
      .label {
        font-size: 14px;
        font-weight: 700;
        color: #3a4762;
        text-align: right;
        white-space: nowrap;
        margin-right: 16px;
      }
      .change-time-icon {
        position: absolute;
        top: 9px;
        right: 10px;
      }
      .el-input__prefix {
        display: none;
      }
    }
    .btns {
      border-top: 1px solid #e9e8eb;
      padding-top: 16px;
      .sure {
        background: #2e6be6;
        border-radius: 2px;
        font-size: 14px;
        color: #ffffff;
      }
      .cancel {
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #dcdee0;
        font-size: 14px;
        color: #323233;
      }
      .label {
        font-size: 14px;
        font-weight: 700;
        color: #3a4762;
      }
    }
  }
}
</style>
