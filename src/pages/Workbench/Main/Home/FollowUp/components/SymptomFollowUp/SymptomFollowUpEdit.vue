<template>
  <div v-loading="loading" class="p-16">
    <el-form
      ref="form"
      label-width="80px"
      label-position="top"
      class="form-box"
    >
      <el-form-item
        v-for="(question, _index) in questionsList"
        :key="_index"
        :label="_index + 1 + '.' + question.questionContent"
        :class="question.pquestionId ? 'subsQuestion' : ''"
        class="p-12"
      >
        <template v-if="question.questionType == 1">
          <el-radio-group v-model="question.answerId">
            <el-radio
              v-for="(answer, index) in question.chooseContent"
              :key="index"
              :label="answer.answerId"
              :value="answer.answerId"
              @change="dealRadioValue(answer, question, _index)"
            >
              {{ answer.answerContent }}
            </el-radio>
          </el-radio-group>
          <div
            v-if="
              question.linkAnswerInfo?.length &&
              question.answerId == question.linkAnswerInfo[0]?.answerId
            "
            class="children"
          >
            <div v-for="(ite, i) in question.linkAnswerInfo" :key="i">
              <div>
                <div class="tile">
                  {{ _index + 1 }}.{{ i + 1 }}{{ ite.linkContent }}
                </div>
                <el-date-picker
                  v-if="ite.type === '2'"
                  v-model="ite.content"
                  type="date"
                  placeholder="选择日期"
                />
                <el-input
                  v-if="ite.type === '1'"
                  v-model="ite.content"
                  type="textarea"
                  placeholder="请输入内容"
                  maxlength="20"
                  show-word-limit
                />
                <template v-if="ite.type === '4'">
                  <el-checkbox-group v-model="ite.content">
                    <el-checkbox
                      v-for="item in ite.chooseContent"
                      :key="item.answerId"
                      :label="item"
                      :value="item"
                      >{{ item.answerContent }}</el-checkbox
                    >
                  </el-checkbox-group>
                  <el-input
                    v-if="
                      ite.content &&
                      ite.content?.find(i => i.answerContent === '其他')
                    "
                    v-model="ite.specificSituation"
                    type="textarea"
                    placeholder="请输入其他信息"
                    maxlength="50"
                    show-word-limit
                  />
                </template>
              </div>
            </div>
          </div>
        </template>
        <template v-if="question.questionType == 2">
          <el-checkbox-group v-model="question.checkBox">
            <template
              v-for="item in question.chooseContent"
              :key="item.answerId"
            >
              <el-checkbox :label="item.answerId" :value="item.answerId">
                {{ item.answerContent }}
              </el-checkbox>
              <el-input
                v-if="
                  item.answerRemark === '1' &&
                  question.checkBox?.includes(item.answerId)
                "
                v-model="question.specificSituation"
                type="textarea"
                placeholder="请输入其他信息"
                maxlength="50"
                show-word-limit
              />
            </template>
          </el-checkbox-group>
        </template>
        <template v-if="question.questionType == 3">
          <el-input
            v-model="question.textarea"
            type="textarea"
            placeholder="请输入内容"
            maxlength="50"
            show-word-limit
          />
        </template>
      </el-form-item>
    </el-form>
    <div class="btn">
      <el-button type="primary" @click="onSubmit">提交</el-button>
      <el-button @click="cancelFillQues">取消</el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { cloneDeep, debounce, isArray } from 'lodash-es';
import dayjs from 'dayjs';
import store from '@/store';
import bus from '@/lib/bus';
import { saveFollowSymptomEdit } from '@/api/followup';
import { PROFESSIONAL_TEXT } from './index.vue';

interface IProps {
  currQuestionsInfo?: any;
  surveyData?: any;
}
const props = defineProps<IProps>();
const emits = defineEmits(['computedSymptomFollowUp']);

const loading = ref(false);
const questionsList = ref<any[]>([]);
const childrenArr = ref<any[]>([]);

const init = () => {
  const { surveyData, currQuestionsInfo } = props;
  questionsList.value = cloneDeep(surveyData);
  childrenArr.value = questionsList.value.filter(ite => ite.pquestionId);
  if (currQuestionsInfo.status !== 128) {
    questionsList.value = questionsList.value.filter(ite => !ite.pquestionId);
  } else {
    // 如果再次编辑问卷，需要过滤掉答案未填写的隐藏子问题
    // 当前只判断了 单选 多选
    questionsList.value = questionsList.value.filter(questions => {
      const { pquestionId, answerId, questionType } = questions;
      return (
        !pquestionId ||
        ![1, 2].includes(questionType) ||
        answerId ||
        answerId === 0
      );
    });
  }
  questionsList.value.forEach(item => {
    item.chooseContent = item.chooseContent ? item.chooseContent : [];
    item.chooseContent.sort(compare('answerId'));
    item.linkAnswerInfo = item.linkAnswerInfo ? item.linkAnswerInfo : [];
  });
};

const onSubmit = debounce(async () => {
  // 必填校验
  for (let i = 0; i < questionsList.value.length; i++) {
    const {
      questionType,
      answerId,
      questionContent,
      checkBox,
      chooseContent,
      linkAnswerInfo,
    } = questionsList.value[i];

    if (questionType === 1 && !answerId) {
      return ElMessage.error('请选择' + questionContent);
    }

    if (questionType === 2 && !checkBox.length) {
      return ElMessage.error(questionContent);
    }

    if (questionContent?.includes('近期有无再次入院情况')) {
      for (let v = 0; v < chooseContent.length; v++) {
        let ite = chooseContent[v];
        if (
          questionType === 1 &&
          linkAnswerInfo.length &&
          ite.answerContent === '有' &&
          ite.answerId === answerId
        ) {
          for (let j = 0; j < linkAnswerInfo.length; j++) {
            let val = linkAnswerInfo[j];
            const hasContent = isArray(val.content)
              ? val.content?.length
              : val.content;
            if (!hasContent) {
              return ElMessage.error('请完善' + val.linkContent);
            }
          }
        }
      }
    }
  }

  // 数据处理
  const arr: any[] = [];

  questionsList.value.forEach(item => {
    const { questionType, linkAnswerInfo } = item;

    // 单选
    if (questionType === 1) {
      if (!linkAnswerInfo.length) {
        arr.push({
          questionId: item.questionId,
          answerInfo: [
            {
              answerContent: '',
              answerId: item.answerId,
              answerPictureList: [],
              hospitalTime: '',
              inpatientDepartment: '',
              reason: '',
              specificSituation: '',
            },
          ],
        });
      } else {
        const newArr: any[] = [];
        linkAnswerInfo.forEach(ite => {
          const { type, content, linkContent, specificSituation } = ite;
          let reason = PROFESSIONAL_TEXT.inHospitalReason.includes(linkContent)
            ? content
            : '';
          if (isArray(reason)) {
            reason = reason?.map(reasonItem => {
              if (reasonItem.answerContent === '其他') {
                return { ...reasonItem, remark: specificSituation };
              }
              return reasonItem;
            });
          }

          newArr.push({
            answerContent: '',
            answerId: item.answerId,
            answerPictureList: [],
            hospitalTime:
              type === '2' && content
                ? dayjs(content).format('YYYY-MM-DD')
                : '',
            inpatientDepartment: PROFESSIONAL_TEXT.inpatientDepartment.includes(
              linkContent
            )
              ? content
              : '',
            reason,
            specificSituation:
              isArray(reason) && reason?.find(r => r.answerContent === '其他')
                ? specificSituation
                : '',
          });
        });
        arr.push({
          questionId: item.questionId,
          answerInfo: newArr,
        });
      }
    }

    // 多选
    if (questionType == 2) {
      const list: any[] = [];
      item.checkBox.forEach(ite => {
        item.chooseContent.forEach(it => {
          if (ite == it.answerId) {
            list.push({
              answerContent: '',
              answerId: ite,
              answerPictureList: [],
              hospitalTime: '',
              inpatientDepartment: '',
              reason: '',
              specificSituation:
                it.answerRemark == 1 ? item.specificSituation : '',
            });
          }
        });
      });
      arr.push({
        questionId: item.questionId,
        answerInfo: list,
      });
    }

    // 填空题
    if (questionType == 3) {
      if (item.textarea) {
        arr.push({
          questionId: item.questionId,
          answerInfo: [
            {
              answerContent: item.textarea,
              answerId: '',
              answerPictureList: [],
              hospitalTime: '',
              inpatientDepartment: '',
              reason: '',
              specificSituation: '',
            },
          ],
        });
      }
    }
  });

  // 组装未显示题目
  childrenArr.value?.forEach(({ questionId }) => {
    if (!arr?.find(item => item.questionId === questionId)) {
      arr.push({ questionId, answerInfo: [] });
    }
  });

  const params: any = {
    patientId: store.useGlobal().userId,
    followUpId: props.currQuestionsInfo.followUpId,
    questions: JSON.stringify(arr.sort(compare('questionId'))),
  };

  loading.value = true;
  try {
    await saveFollowSymptomEdit(params);
    ElMessage.success('提交成功!');
    emits('computedSymptomFollowUp', 1);
    bus.emit('refresh-followup-list');
  } finally {
    loading.value = false;
  }
}, 600);

// 排序
const compare = (property: any) => {
  return function (a, b) {
    return a[property] - b[property];
  };
};

// 取消
const cancelFillQues = () => {
  emits('computedSymptomFollowUp', 0);
};

// 单选框选择事件
const dealRadioValue = (answer: any, questions: any, index: number) => {
  questions.requestValue = {
    questionId: answer.questionId,
    answerId: questions.userAnswerId,
  };

  // 处理子问题
  if (questions.trueAnswer) {
    const trueAnswer = JSON.parse(questions.trueAnswer);
    trueAnswer.forEach(item => {
      // 获取子问题
      if (item.answerId == answer.answerId) {
        childrenArr.value.forEach(item => {
          item.chooseContent.sort(compare('answerId'));
          item.checkBox = [];
          const _index = questionsList.value.findIndex(
            i => i.questionId === item.questionId
          );
          if (_index !== -1) {
            return;
          } else {
            questionsList.value.splice(index + 1, 0, item);
          }
        });
      } else {
        // 处理点击父问题后获取到了子问题然后又点击其他选项清除已加载的子问题
        questionsList.value = questionsList.value.filter(ite => {
          return String(ite.pquestionId) !== questions.questionId;
        });
      }
    });
  }

  // 处理选项下的子选项
  if (questions.linkAnswerInfo?.length) {
    if (answer.answerId != questions.linkAnswerInfo[0].answerId) {
      questions.linkAnswerInfo.forEach(el => {
        el.content = el.type == 4 ? [] : '';
      });
    }
  }
};

watch(
  () => props.surveyData,
  () => {
    init();
  },
  { deep: true }
);

onMounted(() => {
  init();
});
</script>
<style lang="less" scoped>
:deep(.form-box) {
  .el-form-item {
    width: 100%;
    background: #f7f8fa;
    border-radius: 4px;
    margin-bottom: 8px;

    .el-form-item__label {
      margin-bottom: 8px;
      font-size: 14px;
      color: #101b25;
      font-weight: bold;
    }
    .el-form-item__content {
      line-height: 20px;
      display: inline-block;
      width: 100%;
    }
    .el-radio-group {
      .el-radio {
        height: 20px;
      }
      .el-radio__input {
        width: 14px;
        height: 14px;
        font-size: 14px;
        color: #323233;
      }
      .el-radio__label {
        padding-left: 8px;
      }

      //.is-checked;
      .el-radio__input.is-checked {
        .el-radio__inner {
          background-color: #fff;
          &::after {
            transform: translate(-50%, -50%) scale(2);
            background-color: #0a73e4;
          }
        }
      }
    }
    .el-checkbox-group {
      .el-checkbox {
        height: 20px;
      }
      .el-checkbox__input {
        width: 16px;
        height: 16px;
        border-radius: 2px;
      }
      .el-checkbox__inner {
        width: 16px;
        height: 16px;
        border-radius: 2px;
      }
      .el-checkbox__label {
        padding-left: 8px;
        color: #3a4762;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner::after {
        left: 5px;
        border-width: 2px;
      }
    }
    .children {
      .tile {
        font-weight: bold;
        font-size: 14px;
        color: #101b25;
        margin: 24px 0 8px 0;
      }
    }
    .el-input__inner,
    .el-textarea__inner {
      color: #3a4762;
    }
  }
}
</style>
