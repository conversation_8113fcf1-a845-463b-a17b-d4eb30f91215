<template>
  <TopInfo>
    <div class="transcribe-box flex items-center">
      <div class="flex-1 text-right date">
        {{ dayjs(data.followUpDate).format('YYYY-MM-DD') }}
        症状随访
      </div>
      <el-button
        v-if="followInfo.symptomStatus == 1"
        link
        class="link"
        @click="showHandleExceptionsDialogVisible = true"
      >
        <img
          class="w-11 h-13 ml-24 mr-6"
          src="@/assets/imgs/overview/icon-edit.png"
          alt=""
        />
        处理异常
      </el-button>
    </div>
  </TopInfo>
  <div class="num-box mt-58">
    <div class="text">
      共计1个量表，已填写{{ followInfo.status === 128 ? 1 : 0 }}个
    </div>
  </div>
  <div class="follows mb-2xs">
    <div class="header flex items-center">
      <div class="left flex items-center">
        <div class="module-one"></div>
        <div class="module-two ml-10">症状随访</div>
      </div>
      <div v-show="!currEditId" class="flex flex-1 pr-16">
        <div
          v-if="!followInfo.status || followInfo.status < 128"
          class="status ml-16 mr-16 flex-1"
        >
          未完成
        </div>
        <div v-else class="flex-1 text-right time">
          上次编辑人员/时间：{{ followInfo.editName || '--' }}
          {{
            followInfo.editTime
              ? dayjs(followInfo.editTime).format('YYYY-MM-DD HH:mm')
              : '--'
          }}
        </div>
        <el-button
          link
          class="link"
          @click="currEditId = followInfo.followUpId"
        >
          <img
            class="w-11 h-13 ml-24 mr-6"
            src="@/assets/imgs/overview/icon-edit.png"
            alt=""
          />
          编辑
        </el-button>
      </div>
    </div>
    <SymptomFollowUpShow
      v-if="!currEditId && followInfo.status === 128"
      :survey-data="survey"
      :conclusion="followInfo.conclusion"
      :doctor-opinion="followInfo.doctorOpinion"
    />
    <SymptomFollowUpEdit
      v-if="currEditId"
      :survey-data="survey"
      :curr-questions-info="followInfo"
      @computed-symptom-follow-up="computedSymptomFollowUp"
    />
  </div>
  <HandleExceptions
    v-if="showHandleExceptionsDialogVisible"
    v-model:showDialogVisible="showHandleExceptionsDialogVisible"
    v-model:followUpId="followInfo.followUpId"
    @change-deal="getSymptomFollowUp"
  />
</template>
<script lang="ts">
export const PROFESSIONAL_TEXT = {
  inpatientDepartment: ['住院科室', '入院科室'],
  inHospitalReason: ['入院原因', '原因'],
  hospitalTimeText: ['再次住院时间', '再次入院时间'],
};
</script>
<script setup lang="ts">
import SymptomFollowUpShow from './SymptomFollowUpShow.vue';
import SymptomFollowUpEdit from './SymptomFollowUpEdit.vue';
import HandleExceptions from './components/HandleExceptions.vue';
import TopInfo from '../TopInfo.vue';
import dayjs from 'dayjs';
import { getFollowSymptomDetail } from '@/api/followup';
import { isArray } from 'lodash-es';
import { IApiFollowSymptomDetail } from '@/interface/type';
defineOptions({
  inheritAttrs: false,
});
interface IProps {
  data?: any;
}
const props = defineProps<IProps>();

const followInfo = ref<IApiFollowSymptomDetail>({});
const currEditId = ref();
const showHandleExceptionsDialogVisible = ref(false);
const survey = ref<any[]>([]);

const getSymptomFollowUp = () => {
  getFollowSymptomDetail({ followUpId: props.data.followUpId }).then(res => {
    const { questionAnswer, status } = res;
    followInfo.value = res;
    survey.value =
      questionAnswer?.map(v => {
        const {
          answerInfo,
          questionContent,
          questionType,
          chooseContent,
          linkAnswerInfo,
        } = v;
        v['answerId'] = answerInfo ? answerInfo[0]?.answerId : null;
        v['title'] = questionContent;
        if (status == 128) {
          // 单选
          if (questionType == 1) {
            let answerText =
              chooseContent?.find(re => re.answerId == v['answerId'])
                ?.answerContent || '';

            if (answerText == '有' && answerInfo && answerInfo?.length > 1) {
              const {
                inpatientDepartment,
                hospitalTimeText,
                inHospitalReason,
              } = PROFESSIONAL_TEXT;

              linkAnswerInfo?.forEach(el => {
                const { linkContent = '' } = el;

                if (inpatientDepartment.includes(linkContent)) {
                  el['content'] =
                    answerInfo.find(re => re.inpatientDepartment)
                      ?.inpatientDepartment || '';
                  answerText += `；${linkContent}-${el['content']}`;
                }
                if (hospitalTimeText.includes(linkContent)) {
                  el['content'] =
                    answerInfo.find(re => re.hospitalTime)?.hospitalTime || '';
                  answerText += `；${linkContent}-${el['content']}`;
                }
                if (inHospitalReason.includes(linkContent)) {
                  let reason = answerInfo.find(re => re.reason)?.reason || '';
                  let contentStr = '';
                  try {
                    reason = JSON.parse(reason);
                  } catch {}

                  if (isArray(reason)) {
                    reason?.forEach(re => {
                      const remark = re.remark ? '-' + re.remark : '';
                      if (remark) el['specificSituation'] = re.remark;
                      delete re.remark;
                      contentStr += `${contentStr && '，'}${re.answerContent}${remark}`;
                    });
                  }
                  el['content'] = reason;
                  answerText += `；${linkContent}-${contentStr || reason}`;
                }
              });
            }

            v['answerText'] = answerText;
          }

          // 多选
          if (questionType == 2) {
            const checkArr: string[] = [];
            v['checkBox'] = answerInfo?.map(re => {
              v['specificSituation'] = v['specificSituation']
                ? v['specificSituation']
                : re.specificSituation;
              let text = chooseContent?.find(
                el => el.answerId == re.answerId
              )?.answerContent;
              text = re.specificSituation
                ? `${text}-${re.specificSituation}`
                : text;
              checkArr.push(text || '');
              return re.answerId;
            });
            v['answerText'] = checkArr.join('，');
          }

          // 文本
          if (questionType == 3) {
            v['answerText'] = v['textarea'] =
              (answerInfo && answerInfo[0]?.answerContent) || '';
          }
        }

        return v;
      }) || [];
  });
};
const computedSymptomFollowUp = type => {
  currEditId.value = null;
  if (type === 1) {
    getSymptomFollowUp();
  }
};
onMounted(() => {
  getSymptomFollowUp();
});
</script>

<style scoped lang="less">
.transcribe-box {
  .date {
    font-size: 14px;
    color: #3a4762;
  }
  .link {
    font-size: 14px;
    color: #2e6be6 !important;
    display: flex;
    align-items: center;
    &:hover {
      color: #2e6be6;
      opacity: 0.75;
      cursor: pointer;
    }
    &:active {
      color: #2e6be6 !important;
      opacity: 1;
    }
  }
}
.num-box {
  font-size: 14px;
  height: 52px;
  line-height: 52px;
  box-sizing: border-box;
  background: #fff;
  padding: 0 16px;
  border-radius: 6px 6px 0 0;
  .text {
    width: 100%;
    height: 100%;
    color: #7a8599;
    border-bottom: 1px solid #e9e8eb;
  }
}
.follows {
  width: 100%;
  background: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(186, 200, 212, 0.5);
  border-radius: 6px;
  padding: 16px 0;
  box-sizing: border-box;
  &:nth-of-type(3) {
    border-radius: 0 0 6px 6px;
  }
  .header {
    .status {
      font-size: 14px;
      color: #e63746;
    }
    .time {
      color: #7a8599;
      font-size: 14px;
    }
    .link {
      font-size: 14px;
      color: #2e6be6 !important;
      display: flex;
      align-items: center;
      &:hover {
        color: #2e6be6;
        opacity: 0.75;
        cursor: pointer;
      }
      &:active {
        color: #2e6be6 !important;
        opacity: 1;
      }
      .icon-add {
        color: var(--hrt-color-blue);
      }
    }
    .left {
      .module-one {
        width: 6px;
        height: 16px;
        background: #2e6be6;
        border-radius: 2px;
      }
      .module-two {
        font-size: 16px;
        font-weight: 700;
        color: #101b25;
      }
    }
  }
}
.time-zone {
  padding: 0 16px;
  box-sizing: border-box;
}
</style>
