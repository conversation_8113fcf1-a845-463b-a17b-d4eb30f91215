<template>
  <div class="p-16">
    <div class="result p-12 mb-16">
      <div class="result-text">
        {{ resultText || '--' }}
      </div>
    </div>
    <div class="answer-content">
      <div class="header flex items-center">
        <span class="flex-1 mr-24">题目</span>
        <span class="w-226">结果</span>
      </div>
      <AnswerShow :servey-json="serveyJson" />
    </div>
  </div>
</template>
<script setup lang="ts">
import AnswerShow from '../LifestyleAssessment/AnswerShow.vue';
interface IProps {
  surveyData?: any;
  conclusion: string;
  doctorOpinion: string;
}
const props = defineProps<IProps>();
const serveyJson = ref([]);
const init = () => {
  // 需要过滤掉答案未填写的隐藏子问题
  serveyJson.value = props.surveyData?.filter(questions => {
    const { pquestionId, answerId, questionType } = questions;
    return (
      !pquestionId ||
      ![1, 2].includes(questionType) ||
      answerId ||
      answerId === 0
    );
  });
};

const resultText = computed(() => {
  if (!props.conclusion && !props.doctorOpinion) {
    return '';
  } else {
    return `${props.conclusion},${props.doctorOpinion}`;
  }
});
onMounted(() => {
  init();
});

watch(
  () => props.surveyData.value,
  () => {
    init();
  },
  {
    deep: true,
  }
);
</script>

<style scoped lang="less">
.result {
  width: 100%;
  //background: #ecf4fc;
  background: rgba(230, 55, 70, 0.1);
  border-radius: 4px;
  font-size: 14px;
  color: #3a4762;
  line-height: 20px;
  p {
    font-weight: bold;
    font-size: 14px;
    color: #101b25;
  }
  .result-text {
    color: #101b25;
    span {
      font-weight: bold;
    }
  }
}
.answer-content {
  .header {
    width: 100%;
    height: 32px;
    background: #f7f8fa;
    padding: 0 12px;
    > span {
      font-weight: bold;
      font-size: 14px;
      color: #101b25;
    }
  }
  .question {
    font-size: 14px;
    color: #101b25;
    padding: 0 12px;
    border-bottom: 1px solid #f6f8fb;
    min-height: 44px;
    .answer {
      color: #2e6be6;
    }
  }
}
</style>
