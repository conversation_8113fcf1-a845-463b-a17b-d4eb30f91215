<template>
  <div v-loading="loading" class="p-16">
    <SurveyComponent :model="surveys" />
    <div class="btn-box">
      <div class="sure-btn" @click="saveSurveyResult">确定</div>
      <div class="cancel-btn m-0" @click="emit('computedAssessment', 0)">
        取消
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import 'survey-core/survey.i18n';
import 'survey-core/defaultV2.min.css';
import { Model } from 'survey-core';
import { saveFollowLifestyleEdit } from '@/api/followup';

import theme from './hook/assessments/survey_theme.json';
import { debounce } from 'lodash-es';
import bus from '@/lib/bus';

interface IProps {
  surveyData?: object;
  surveyAnswer?: object;
  calculator: any;
  onlyOneTimesData?: object;
  userQuestionnaireId: number;
  status: number;
}
const props = defineProps<IProps>();

const surveys = ref();
const loading = ref(false);
const emit = defineEmits(['computedAssessment']);
const init = () => {
  // 首次填写处理不再需要回答的问题
  if (props.status === 0 && props.onlyOneTimesData) {
    props.surveyData.pages[0].elements = dataKeyModel(
      props.onlyOneTimesData,
      0
    );
  }
  // 二次编辑处理需要回显的问题
  if (props.status == 1) {
    props.surveyData.pages[0].elements = dataKeyModel(props.surveyAnswer, 1);
  }
  surveys.value = new Model(props.surveyData);
  surveys.value.locale = 'zh-cn';
  surveys.value.applyTheme(theme);
};
const dataKeyModel = (data, type) => {
  let dataKey = Object.keys(data) || [];
  let finalData = [];
  for (let i = 0; i < props.surveyData.pages[0].elements.length; i++) {
    const el = props.surveyData.pages[0].elements[i];
    if (type === 0) {
      if (!dataKey.some(re => re == el.name)) {
        finalData.push(el);
      }
    }
    if (type === 1) {
      if (
        dataKey.some(re => re == el.name) ||
        el.visibleIf ||
        el.type === 'panel'
      ) {
        finalData.push(el);
      }
    }
  }
  return finalData;
};
const saveSurveyResult = () => {
  if (surveys.value.completeLastPage()) {
    surveys.value.isCompleted = false;
    surveyCalculator(surveys.value.data);
  }
};
const surveyCalculator = debounce(data => {
  loading.value = true;
  const result = props.calculator(data, 'doctor');

  const { conclusion, score, answerInfo } = result || {};
  // 再次提交特殊数据时，处理最终要提交的数据(剔除只保留一次的已有数据的字段)
  if (props.onlyOneTimesData && result.desc) {
    for (const key in result.desc) {
      if (Object.hasOwnProperty.call(result.desc, key)) {
        delete props.onlyOneTimesData[key];
      }
    }
    result.desc = { ...result.desc, ...props.onlyOneTimesData };
  }
  let params = {
    userQuestionnaireId: Number(props.userQuestionnaireId),
    conclusion: conclusion ? JSON.stringify(conclusion) : '',
    score: score ? JSON.stringify(score) : '',
    answerInfo: JSON.stringify(answerInfo),
    desc: result.desc ? JSON.stringify(result.desc) : '',
  };

  saveFollowLifestyleEdit(params)
    .then(() => {
      loading.value = false;
      emit('computedAssessment', 1);
      ElMessage.success('提交成功！');
      bus.emit('refresh-followup-list');
    })
    .catch(err => {
      loading.value = false;
      ElMessage.error(`提交失败：${err.msg}`);
    });
}, 600);
onMounted(() => {
  init();
});

// watch(
//   () => props.surveyAnswer,
//   () => {
//     console.log(props.surveyAnswer, '//surveyAnswer//');
//     init();
//   }
// );
watch(
  () => props.onlyOneTimesData,
  () => {
    init();
  },
  { deep: true, immediate: true }
);
</script>

<style scoped lang="less">
:deep(.sd-root-modern) {
  background: none;

  .sd-container-modern__title,
  .sd-footer {
    display: none !important;
  }
  .sd-body {
    padding: 0;
    .sd-element--complex.sd-element--with-frame > .sd-element__header {
      padding: 0;
    }
    .sd-row.sd-page__row:not(.sd-row--compact)
      ~ .sd-row.sd-page__row:not(.sd-row--compact) {
      margin-top: 8px;
    }
    .sd-question__header--location-top {
      padding-bottom: 8px;
    }
    .sd-title .sv-title-actions {
      width: 100%;
    }
    .sd-element--with-frame > .sd-element__erbox {
      width: 100%;
      margin: 0;
    }
    .sd-title.sd-element__title {
      color: #101b25;
      font-weight: bold;
      span {
        font-size: 14px;
      }
      .sd-element__num {
        margin-left: 0;
        width: 20px;
        padding-right: 0;
        font-size: 14px;
        color: #101b25;
      }
    }
    .sd-element--complex > .sd-element__header:after {
      background: none !important;
      width: 0;
      height: 0;
    }
    .sd-panel__content {
      padding: 0 0 0 20px;
      .sd-row {
        margin-top: 8px;
      }
    }
    .sd-question__content {
      padding-left: 20px;
      padding-top: 0;
      .sd-input {
        //width: 240px;
        min-width: 60px;
        max-width: 400px;
        height: 32px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #dcdee0;
        padding: 6px 8px;
        box-shadow: none;
        font-size: 14px;
        color: #3a4762;
        box-sizing: border-box;
      }
      .sd-selectbase {
        display: flex;
        flex-wrap: wrap;
        .sd-item {
          margin-right: 32px;
          //height: 20px;
          //line-height: 20px;
          padding: 4px 0;
          .sd-item__decorator {
            box-shadow: none;
          }
          .sd-selectbase__label {
            display: flex;
            align-items: center;
            .sd-item__control-label {
              color: #323233;
              font-size: 14px;
              line-height: 20px;
            }
          }
          .sd-radio__decorator {
            width: 14px;
            height: 14px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #b8becc;
          }
          .sd-checkbox__decorator {
            width: 16px;
            height: 16px;
            background: #ffffff;
            border-radius: 2px;
            border: 1px solid #dcdee0;
            //.sd-checkbox__svg {
            //  width: 16px;
            //  height: 16px;
            //  //background: #ffffff;
            //}
            //&:checked {
            //  .sd-checkbox__svg {
            //    background: red;
            //  }
            //}
          }
        }
        .sd-radio--checked .sd-radio__decorator {
          border: 1px solid #0a73e4;
          &:after {
            width: 10px;
            height: 10px;
            background: #0a73e4;
            border-radius: 8px;
          }
        }
        .sd-checkbox--checked .sd-checkbox__decorator {
          background: #0a73e4;
          border: 1px solid #0a73e4;
          //.sd-checkbox__svg use {
          //  width: 16px;
          //  height: 16px;
          //  //color: red;
          //  //background: #fff;
          //}
        }
        .sd-checkbox--checked
          .sd-checkbox__control:focus
          + .sd-checkbox__decorator
          .sd-checkbox__svg
          use {
          fill: rgba(255, 255, 255, 1);
        }
      }
      .sd-table-wrapper {
        width: 500px;
        min-width: 200px;
        max-width: 600px;
        margin: 0;

        .sd-table--no-header {
          padding: 0;
        }
        .sd-table {
          background: none;
          .sd-table__cell {
            padding: 0;
            .sd-dropdown {
              line-height: 32px;
              .sd-dropdown__value {
                font-size: 14px;
                line-height: 20px;
                height: 20px;
                .sd-dropdown__filter-string-input {
                  font-size: 14px;
                  line-height: 20px;
                  height: 20px;
                }
              }
              .sd-dropdown_clean-button {
                display: none;
              }
            }
            .sv-dropdown-popup {
              font-size: 14px;
              .sv-list {
                .sd-list__item {
                  color: red;
                }
                .sd-list__item--selected sv-list__item-body {
                  background: red;
                  color: red;
                }
              }
            }
          }
        }
        &::before,
        &::after {
          display: none;
        }
      }
      .sd-table--no-header {
        //padding: 0;
      }
    }
    .sd-matrixdynamic__content.sd-question__content {
    }
  }
  .sd-root-modern__wrapper {
    .sd-element--with-frame {
      background: #f7f8fa;
      border-radius: 4px;
      padding: 12px;
      box-shadow: none;
    }
  }
}
.btn-box {
  justify-content: start;
  .sure-btn {
    margin: 0 8px 0 0;
  }
  .cancel-btn {
  }
}
</style>
