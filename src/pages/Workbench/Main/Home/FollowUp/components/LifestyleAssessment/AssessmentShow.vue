<template>
  <div class="p-16">
    <AssessmentResult
      :conclusion="conclusion"
      :score="score"
      :is-latest="isLatest"
    />
    <div class="answer-content">
      <div class="header flex items-center">
        <span class="flex-1 mr-24">题目</span>
        <span class="w-226">结果</span>
      </div>
      <AnswerShow :servey-json="serveyJson" />
    </div>
  </div>
</template>
<script setup lang="ts">
import AnswerShow from './AnswerShow.vue';
import AssessmentResult from './components/AssessmentResult.vue';
interface IProps {
  surveyData?: any;
  surveyAnswer?: object;
  conclusion?: object;
  score?: object;
  isLatest?: boolean;
}
const props = defineProps<IProps>();

const serveyJson = ref([]);
const init = () => {
  serveyJson.value = props.surveyData;
  if (props.surveyAnswer) {
    const dataKey = Object.keys(props.surveyAnswer || {});
    serveyJson.value = elementsProcessing(dataKey, props.surveyData);
  }
};
const elementsProcessing = (dataKey, elements) => {
  const { surveyAnswer = {} } = props;

  elements = elements.filter(el =>
    dataKey.some(re => re === el.name || el.type == 'panel')
  );
  elements.forEach(el => {
    const defaultValue = surveyAnswer[el.name];
    el['defaultValue'] = defaultValue;
    const type = el.type?.toLowerCase();

    switch (type) {
      case 'text':
        el['answerText'] = defaultValue;
        break;
      case 'radiogroup':
        el['answerText'] =
          el.choices.find(re => re.value === defaultValue)?.text || '--';
        break;
      case 'checkbox':
        {
          let checkItems: string[] = [];
          for (let i = 0; i < el.choices.length; i++) {
            if (defaultValue.some(re => re == el.choices[i].value)) {
              checkItems.push(el.choices[i]?.text);
            }
          }
          el['answerText'] = checkItems.join('、');
        }
        break;
      case 'matrixdynamic':
        {
          if (!Array.isArray(defaultValue)) return;
          const values = Object.values(defaultValue[0]);
          el['answerText'] = values.join('');
        }
        break;
    }

    if (el.elements) {
      el = elementsProcessing(dataKey, el.elements);
    }
  });
  return elements;
};
onMounted(() => {
  init();
});

watch(
  () => props.surveyAnswer,
  () => {
    init();
  }
);
watch(
  () => props.surveyData,
  () => {
    init();
  },
  {
    deep: true,
  }
);
</script>

<style scoped lang="less">
.result {
  width: 100%;
  background: #ecf4fc;
  border-radius: 4px;
  font-size: 14px;
  color: #3a4762;
  line-height: 20px;
  p {
    font-weight: bold;
    font-size: 14px;
    color: #101b25;
  }
}
.answer-content {
  .header {
    width: 100%;
    height: 32px;
    background: #f7f8fa;
    padding: 0 12px;
    > span {
      font-weight: bold;
      font-size: 14px;
      color: #101b25;
    }
  }
  .question {
    font-size: 14px;
    color: #101b25;
    padding: 0 12px;
    border-bottom: 1px solid #f6f8fb;
    min-height: 44px;
    .answer {
      color: #2e6be6;
    }
  }
}
</style>
