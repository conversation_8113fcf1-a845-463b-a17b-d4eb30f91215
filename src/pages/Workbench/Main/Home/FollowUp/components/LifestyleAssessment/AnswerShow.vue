<template>
  <div
    v-for="(item, i) in testData"
    :key="item._key"
    class="question"
    :class="{ elements: item.elements }"
  >
    <div class="flex items-center">
      <span class="flex-1 mr-24">
        <span v-if="!isChilden">{{ i + 1 }}.</span>
        <!-- <span v-else>{{ index }}.</span>-->
        {{ item.title }}
      </span>
      <span class="w-226 answer">{{ item.answerText }}</span>
    </div>
    <AnswerShow
      :servey-json="item.elements"
      :is-childen="true"
      :index="i + 1"
    />
  </div>
</template>
<script setup lang="ts">
import { uniqueId } from 'lodash-es';

interface IProps {
  serveyJson?: any;
  isChilden?: boolean;
  index?: number;
}
const props = defineProps<IProps>();
const testData = computed(() => {
  return props.serveyJson?.map(item => ({ ...item, _key: uniqueId() }));
});
</script>
<style scoped lang="less">
.question {
  font-size: 14px;
  color: #101b25;
  line-height: 20px;
  padding: 12px;
  border-bottom: 1px solid #e9e8eb;
  .answer {
    color: #2e6be6;
  }
}
.question.elements {
  padding: 12px;
  border-bottom: 1px solid #e9e8eb;
  .question {
    padding: 12px 0 0 12px;
    border: none;
  }
}
</style>
