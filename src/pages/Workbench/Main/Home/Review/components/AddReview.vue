<template>
  <HrtDialog
    v-model="props.dialogVisible"
    title="新增个性化复查"
    top="50px"
    size="extraLarge"
    append-to-body
    class="add-review-dialog"
    @close="close(0)"
  >
    <div class="pt-sm px-lg">
      <div class="item flex">
        <div class="item-title">复查日期</div>
        <div class="item-content">
          <div class="time-box">
            <HrtDatePicker
              v-model="reviewTime"
              type="date"
              placeholder="请选择时间"
              size="default"
              value-format="x"
              :disabled-date="disabledDateFn"
              :clearable="false"
            />
            <div class="tip mt-8 mb-16">制定个性化复查日期</div>
          </div>
        </div>
      </div>
      <HrtCheckboxGroup v-model="selectedDisease">
        <div
          v-for="(item, index) in checkList"
          :key="index"
          class="item mb-8 flex"
        >
          <div class="item-title">{{ item.diseaseName }}</div>
          <div class="item-content">
            <template v-for="ite in item.children" :key="ite.diseaseId">
              <HrtCheckbox
                :value="ite.diseaseId"
                :label="
                  item.diseaseType === 11
                    ? ite.diseaseName +
                      '（自定义检查不支持填写数据，请尽量选择已有的检查项）'
                    : ite.diseaseName
                "
              />
              <HrtInput
                v-if="item.diseaseType === 11 && selectedDisease.includes(94)"
                v-model="ite.remark"
                :rows="2"
                type="textarea"
                resize="none"
                maxlength="50"
                show-word-limit
                placeholder="请输入内容"
              />
            </template>
          </div>
        </div>
      </HrtCheckboxGroup>
    </div>

    <template #footer>
      <div class="btns flex items-center justify-between">
        <div class="flex items-center key-tracking">
          <div class="label mr-8">重点跟踪</div>
          <HrtSwitch v-model="keyFollow" />
          <span class="ml-8">
            重点跟踪的复查会自动生成复查提醒和跟进两个待办
          </span>
        </div>
        <div class="flex items-center justify-end">
          <HrtButton class="w-76 h-32 mr-8" @click="cancel(1)">取消</HrtButton>
          <HrtButton
            class="w-76 h-32"
            :loading="loading"
            type="primary"
            @click="sure"
          >
            确认
          </HrtButton>
        </div>
      </div>
    </template>
  </HrtDialog>
</template>
<script setup lang="ts">
import { debounce, keyBy, cloneDeep } from 'lodash-es';
import bus from '@/lib/bus';
import { addReviewApi } from '@/api/review';
import { dialogTip } from '@/pages/Workbench/Right/components/PatientTodo/index';
import useGlobal from '@/store/module/useGlobal';
import { useExaminationList } from '@/hooks';

const props = defineProps({
  dialogVisible: {
    default: false,
    type: Boolean,
  },
  data: {
    default: () => {},
    type: Object,
  },
  independent: {
    default: true,
    type: Boolean,
  },
  confirmCallback: {
    default: () => void 0,
    type: Function,
  },
  title: {
    default: '新增个性化复查',
    type: String,
  },
  baseText: {
    default: '个性化复查',
    type: String,
  },
});
const emit = defineEmits(['close', 'change']);
const useGlobalInfo = useGlobal();
const { processedData } = useExaminationList();
const loading = ref(false);
const reviewTime = ref('');
const selectedDisease = ref<number[]>([]);
const disabledDateFn = (time: { getTime: () => number }) =>
  time.getTime() < Date.now() - 8.64e7;
const checkList = ref<any[]>([]);
watch([processedData, () => props.dialogVisible], ([data]) => {
  if (data) {
    checkList.value = data.map(({ children, ...rest }) => {
      return {
        ...rest,
        children: children?.map(sub => ({ ...sub, remark: '' })),
      };
    });
  }
});
const keyFollow = ref<boolean>(false);

watch(
  () => props.data,
  val => {
    if (val) setValues();
  }
);
const setValues = () => {
  const { date, value } = props.data;
  reviewTime.value = date;
  selectedDisease.value = value?.map((item: any) => item.checkId);
  const itemIdMap = keyBy(value, 'checkId');
  checkList.value.forEach(({ children }) => {
    children?.forEach(item => {
      const rRemark = itemIdMap[item.indexTermId]?.remark;
      if (rRemark) {
        item.remark = rRemark;
      }
    });
  });
};

// 新增自定义复查
const addReview = debounce(() => {
  const obj: any = {
    patientId: useGlobalInfo.userId!,
    date: new Date(reviewTime.value),
    reportList: getCheckItem(),
    keyFollow: keyFollow.value,
  };
  loading.value = true;
  if (!props.independent) {
    emit('change', obj);
    props
      .confirmCallback?.(obj)
      .then(() => {
        close(1);
      })
      .finally(() => {
        loading.value = false;
      });
    return;
  }
  addReviewApi(obj).then(res => {
    if (res.code === 'E000000') {
      ElMessage({
        message: '新增成功！',
        type: 'success',
        showClose: true,
      });
      loading.value = false;
      bus.emit('updata-review-list');
      close(1);
    }
  });
}, 200);

// 获取选择的检查项
const getCheckItem = () => {
  const arr: any[] = [];
  checkList.value.forEach(({ children }) => {
    children?.forEach(child => {
      const { diseaseId, diseaseName, reportSort, remark } = child;
      if (selectedDisease.value?.includes(diseaseId)) {
        const curObj: any = {
          indexTermId: diseaseId,
          name: diseaseName,
          reportSort,
        };
        if (remark) curObj.remark = remark;
        arr.push(curObj);
      }
    });
  });
  return arr;
};

const sure = () => {
  if (!reviewTime.value) return dialogTip('请选择复查日期!');
  if (!selectedDisease.value?.length) return dialogTip('请选择复查项！');
  if (selectedDisease.value.includes(94)) {
    const list = cloneDeep(checkList.value) || [];

    const children = list.flatMap(item => item.children || []);

    const remarkItem = children.find(item => item.diseaseId === 94);

    if (remarkItem && !remarkItem.remark) {
      return dialogTip('请填写自定义复查项！');
    }
  }

  addReview();
};
// 取消
const cancel = (val: number) => {
  emit('close', val);
  reviewTime.value = '';
  selectedDisease.value = [];
  keyFollow.value = false;
};
// 关闭
const close = (val: number) => cancel(val);
</script>
<style lang="less">
.add-review-dialog {
  position: relative;
  .item {
    font-size: 14px;

    .item-title {
      width: 70px;
      margin: 6px 16px 0 0;
      box-sizing: border-box;
      font-weight: 700;
      color: #3a4762;
      text-align: right;
      white-space: nowrap;
      line-height: normal;
    }
    .item-content {
      flex: 1;
      position: relative;
      .time-box {
        width: 240px;
        .tip {
          color: #7a8599;
        }
      }
      .checkbox-box {
        padding-top: 6px;
        .el-checkbox-group {
          display: flex;
          flex-wrap: wrap;
          flex-direction: column;
        }
      }
    }
  }
  .btns {
    border-top: 1px solid #e9e8eb;
    padding: 16px 24px;
  }
  .key-tracking {
    font-size: 14px;
    .label {
      font-weight: 700;
      color: #3a4762;
    }
  }
}
</style>
