<template>
  <div class="review mb-2xs">
    <div class="header flex items-center justify-between">
      <div class="left flex items-center">
        <div class="module-one"></div>
        <div class="module-two ml-10">复查/诊疗</div>
      </div>
      <div class="right mr-16 flex items-center">
        <div class="all-record cursor-pointer" @click="queryAllList">
          全部记录
          <el-icon size="12px">
            <i-ep-arrow-right color="#2E6BE6" />
          </el-icon>
        </div>
      </div>
    </div>
    <div class="toolbar">
      <div class="item" @click="addReview">
        <img :src="addReviewImg" />
        自定义复查
      </div>
      <div class="item" @click="addOutpatient">
        <img :src="addOutpatientImg" />
        新增门诊记录
      </div>
      <div class="item" @click="AddHospitailzedRecords">
        <img :src="addHospitalImg" />
        新增住院记录
      </div>
      <div class="right-item">
        <div class="plan" @click="setPlan">
          <img :src="planImg" />
          调整复查计划
        </div>
      </div>
    </div>
    <div class="legend">
      <div>
        <img :src="reviewIcon" />
        复查
      </div>
      <div>
        <img :src="outpatientIcon" />
        门诊
      </div>
      <div>
        <img :src="hospitalIcon" />
        住院
      </div>
    </div>
    <div class="mt-17 time-zone">
      <TimeZone
        custom-status
        :top-title="'复查'"
        :bottom-title="'诊疗'"
        :top-data="topData"
        :bottom-data="bottomData"
        :custom-disabled="customDsiableHandler"
        :custom-status-color="customStatusColor"
        :types="[0, 1, 2, 3]"
        card-width="90px"
        @click-item="clickItem"
      >
        <template #default="{ scope: { data } }">
          <span>
            <!-- {{ data.data.name }}{{ data.data.type === 3 ? '复查' : '' }} -->
            {{ data.data.name }}
          </span>
        </template>
        <template #status="{ scope: { data } }">
          <span>
            {{ getReviewStatus(data.data.status) }}
          </span>
          <!-- <span
            v-if="data.data.type === 3"
            class="h-24 w-24 flex items-center"
            @click.stop="() => showLinkHandler(data)"
          >
            <img class="w-12 h-12 ml-12" :src="link" />
          </span> -->
        </template>
      </TimeZone>
    </div>

    <!-- 新增个性化复查 -->
    <AddReview :dialog-visible="dialogVisible" @close="close" />

    <!-- 全部记录 -->
    <Drawer v-model:visible="visible" title="全部诊疗记录">
      <div class="all-box">
        <el-checkbox-group v-model="checkList" @change="change">
          <el-checkbox label="门诊" />
          <el-checkbox label="住院" />
          <el-checkbox label="复查" />
          <el-checkbox label="入组" />
        </el-checkbox-group>
        <ul class="list mt-18">
          <li
            v-for="(item, index) in dataList"
            :key="index"
            v-infinite-scroll="load"
            class="item mb-8"
          >
            <div class="item-top flex items-center justify-between">
              <div class="top-left flex items-center">
                <img
                  :src="item.type === 3 ? reviewImgs : treatmentImg"
                  alt=""
                  class="w-14 h-14 mr-9"
                />
                {{
                  item.type === 0
                    ? '住院'
                    : item.type === 1
                      ? '门诊'
                      : item.type === 2
                        ? '入组'
                        : item.name
                }}
              </div>
              <div class="top-right">
                {{ item.date ? getTime(item.date) : '--' }}
              </div>
            </div>
            <div
              class="item-bottom ml-24 flex items-center justify-between mt-6"
            >
              <span
                v-if="item.type === 3"
                :class="
                  item.status === 64
                    ? 'complete'
                    : item.status === 32
                      ? 'uncompleted'
                      : 'underway'
                "
              >
                {{ getReviewStatus(item.status) }}
              </span>
              <div v-else class="underway w-360">
                <Text>
                  {{
                    item.type === 0
                      ? '住院'
                      : item.type === 1
                        ? '门诊'
                        : '入组'
                  }}诊断：{{ item.treatInfo || '--' }}
                </Text>
              </div>
              <div
                class="details-box cursor-pointer"
                @click="queryDetails(item)"
              >
                查看详情
              </div>
            </div>
          </li>
        </ul>
      </div>
    </Drawer>
    <PlanStep
      :title="'调整复查计划'"
      :visible="planVisible"
      :types="[1]"
      @close="planVisible = false"
    />
  </div>
</template>
<script setup lang="ts">
import bus from '@/lib/bus';
import AddReview from './components/AddReview.vue';
import treatmentImg from '@/assets/imgs/review/treatment.png';
import Drawer from '@/components/Drawer/index.vue';
import TimeZone from '@/components/TimeZone/index.vue';
import reviewImgs from '@/assets/imgs/todo/review-reminder-img.png';
import { IApiPatientReportListData } from '@/interface/type';
import { queryReviewTreatList, queryReviewTreatPage } from '@/api/review';
import Text from '@/components/Text/index.vue';
import hospitalIcon from '@/assets/imgs/review/hospital.png';
import outpatientIcon from '@/assets/imgs/review/outpatient.png';
import reviewIcon from '@/assets/imgs/review/review.png';
import addReviewImg from '@/assets/imgs/review/addReview.png';
import addHospitalImg from '@/assets/imgs/review/addHospital.png';
import addOutpatientImg from '@/assets/imgs/review/addOutpatient.png';
import planImg from '@/assets/imgs/review/plan.png';
import {
  IComponentType,
  IRecordActionType,
  IRecordSourceType,
  useComponentsTabAction,
} from '@/store/module/useComponentsTabAction';
import useGlobal from '@/store/module/useGlobal';
import PlanStep from '@/components/PlanStep/index.vue';

defineOptions({ name: 'Review' });

const useGlobalInfo = useGlobal();
const tabAction = useComponentsTabAction();
const planVisible = ref(false);

const setPlan = () => {
  planVisible.value = true;
};
type TToTab = Pick<
  IApiPatientReportListData,
  'sourceId' | 'sourceType' | 'checkTime'
> & { actionType: IRecordActionType; name: string };
const toTab = (item: TToTab) => {
  const { sourceId, sourceType, checkTime, actionType } = item;
  const curSourceType = [0, 1, 2, 3].includes(sourceType!) ? sourceType! : -1;
  tabAction.setAction({
    componentType: curSourceType as IComponentType,
    name: `${checkTime || ''} ${item.name}`,
    data: {
      id: sourceId,
      recordActionType: {
        actionType,
        sourceType: curSourceType as IRecordSourceType,
      },
    },
  });
  bus.emit('open-component-tab');
};

const clickItem = (data: any) => {
  let title = data.data.type === 3 ? '复查' : '';
  let sourceType = 0;
  if (data.data.type === 3) {
    sourceType = 2;
  } else if (data.data.type === 2) {
    sourceType = 3;
  } else {
    sourceType = data.data.type;
  }
  let obj: TToTab = {
    sourceId: data.data.treatId,
    checkTime: getTime(data.data.date),
    actionType: !data.data.treatId ? 'add' : 'view',
    sourceType,
    name: data.data.name + title,
  };

  toTab(obj);
  isAdd.value = false;
};

// 查看详情
let queryDetails = (val: {
  type: number;
  treatId: any;
  date: number;
  name: any;
}) => {
  let sourceType = 0;
  if (val.type === 3) {
    sourceType = 2;
  } else if (val.type === 2) {
    sourceType = 3;
  } else {
    sourceType = val.type;
  }
  let obj: TToTab = {
    sourceId: val.treatId,
    checkTime: val.date ? getTime(val.date) : '--',
    actionType: !val.treatId ? 'add' : 'view',
    sourceType,
    name:
      val.type === 0
        ? '住院'
        : val.type === 1
          ? '门诊'
          : val.type === 2
            ? '入组'
            : val.name,
  };

  toTab(obj);
};

// 新增门诊/住院
let isAdd = ref<boolean>(false);
// 新增门诊记录
let addOutpatient = () => {
  let obj: TToTab = {
    sourceId: undefined,
    name: '新增门诊记录',
    actionType: 'add',
    sourceType: 1,
  };
  toTab(obj);
  isAdd.value = false;
};
// 新增住院记录
let AddHospitailzedRecords = () => {
  let obj: TToTab = {
    sourceId: undefined,
    name: '新增住院记录',
    actionType: 'add',
    sourceType: 0,
  };
  toTab(obj);
  isAdd.value = false;
};
// 新增个性化复查
let dialogVisible = ref<boolean>(false);
let addReview = () => {
  dialogVisible.value = true;
};

// 全部记录
interface dataListInfo {
  date: number;
  treatId: number;
  status: number;
  type: number;
  name: string;
  treatInfo: string;
}
let visible = ref(false);
let checkList = ref(['住院', '门诊', '复查', '入组']);
let dataList = ref<dataListInfo[]>([]);
// 多选
let change = () => {
  queryInfo.value.page = 1;
  let arr: number[] = [];

  checkList.value.forEach(item => {
    if (item === '住院') arr.push(0);
    if (item === '门诊') arr.push(1);
    if (item === '复查') arr.push(3);
    if (item === '入组') arr.push(2);
  });
  queryInfo.value.type = arr;
  dataList.value = [];

  getAllDataList();
};
let queryAllList = () => {
  if (!visible.value) {
    queryInfo.value.page = 1;
    dataList.value = [];
    totalPageNumber.value = 0;
    getAllDataList();
  }
  visible.value = true;
};
// 复查状态
const getReviewStatus = computed(() => {
  return function (id: number | string) {
    // 1未开始，32：未上传，64：已上传，128：已完成，-1：已失效
    let str = '';
    if (id === -1) {
      str = '已失效';
    }
    if (id === 1) {
      str = '未开始';
    }
    if (id === 32) {
      str = '未上传';
    }
    if (id === 64) {
      str = '已上传';
    }
    if (id === 128) {
      str = '已完成';
    }

    return str ? `(${str})` : '';
  };
});
const customStatusColor = (data: any) => {
  if (data?.status === 128) {
    return '#4EC244';
  } else if ([32, 64].includes(data?.status)) {
    return '#2E6BE6';
  } else if (data?.status === -1) {
    return '#E63746';
  } else {
    return '';
  }
};
const customDsiableHandler = (data: any) => {
  return [-1, 1].includes(data?.status);
};
let close = (val: number) => {
  dialogVisible.value = false;
  if (val === 1) getDataList();
};

onMounted(() => {
  getDataList();
  bus.on('updata-review-list', () => {
    queryInfo.value.patientId = useGlobalInfo.userId;
    queryInfo.value.page = 1;
    dataList.value = [];
    totalPageNumber.value = 0;
    getDataList();
    getAllDataList();
  });
});

// 获取复查诊疗时间轴数据
interface dataInfo {
  time: string;
  data: any;
}
let topData = ref<dataInfo[]>([]);
let bottomData = ref<dataInfo[]>([]);
let getDataList = async () => {
  await queryReviewTreatList({ patientId: useGlobalInfo.userId }).then(
    (res: any) => {
      let { endDate, startDate, treatList } = res.data;
      const _topData: any = [];
      const _bottomData: any = [];
      if (treatList && treatList.length) {
        _topData.push({ time: startDate, data: {} });
        _bottomData.push({ time: startDate, data: {} });

        treatList.forEach(
          (item: {
            treatId: any;
            type: number;
            name: string;
            date: number;
          }) => {
            if (item.type !== 3) {
              item.name =
                item.type === 2 ? '入组' : item.type === 1 ? '门诊' : '住院';
            }
            let obj = {
              time: item.date,
              data: item,
            };
            if (item.type === 3) {
              _topData.push(obj);
            } else {
              _bottomData.push(obj);
            }
          }
        );

        _topData.push({ time: endDate, data: {} });
        _bottomData.push({ time: endDate, data: {} });
        topData.value = _topData;
        bottomData.value = _bottomData;
      } else {
        topData.value = [];
        bottomData.value = [];
      }
    }
  );
};

let getTime = (timestamp: number) => {
  let date = new Date(timestamp);

  // 使用Date对象的方法来获取年月日
  let year = date.getFullYear(); // 获取年份
  let month = date.getMonth() + 1; // 获取月份，注意月份是从0开始的，所以需要+1
  let day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate(); // 获取日期
  let currMonth = month < 10 ? '0' + month : month;

  return `${year}-${currMonth}-${day}`;
};

watch(
  () => useGlobalInfo.userId,
  () => {
    queryInfo.value.patientId = useGlobalInfo.userId;
    queryInfo.value.page = 1;
    dataList.value = [];
    totalPageNumber.value = 0;
    getDataList();
    getAllDataList();
  },
  { deep: true }
);

let flag = ref(true);
// 加载到底部分页加载数据
let queryInfo = ref({
  pageSize: 10,
  page: 1,
  patientId: useGlobalInfo.userId,
  type: [0, 1, 2, 3],
});
let totalPageNumber = ref(0);
const load = () => {
  if (queryInfo.value.page < totalPageNumber.value) {
    if (flag.value) {
      queryInfo.value.page++;
      getAllDataList();
    }
  }
};
// 查询全部数据
let getAllDataList = () => {
  flag.value = false;
  queryReviewTreatPage(queryInfo.value).then((res: any) => {
    if (res.code === 'E000000' && res.data && res.data.treatList) {
      totalPageNumber.value = Math.ceil(res.data.totals / 10);
      dataList.value = [...dataList.value, ...res.data.treatList];
      flag.value = true;
    } else {
      dataList.value = [];
      totalPageNumber.value = 0;
    }
  });
};
</script>
<style scoped lang="less">
.review {
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
  border-radius: 6px;
  padding: 16px 0;
  box-sizing: border-box;
  .header {
    .left {
      .module-one {
        width: 6px;
        height: 16px;
        background: #2e6be6;
        border-radius: 2px;
      }
      .module-two {
        font-size: 16px;
        font-weight: 700;
        color: #101b25;
      }
      .module-three {
        position: relative;
        .add {
          box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
          position: absolute;
          left: 30px;
          top: 8px;
          z-index: 99;
          .item-three {
            padding: 6px 12px;
            box-sizing: border-box;
            background: #ffffff;
          }
        }
      }
    }
    .right {
      font-size: 14px;
      color: #2e6be6;
    }
  }
}
.time-zone {
  padding: 0 16px;
  box-sizing: border-box;
}
.all-box {
  padding: 16px;
  box-sizing: border-box;
  .list {
    .item {
      height: 70px;
      background: #f7f8fa;
      border-radius: 4px;
      padding: 12px;
      box-sizing: border-box;
      .item-top {
        .top-left {
          font-size: 14px;
          font-weight: bold;
          color: #3a4762;
        }
        .top-right {
          font-size: 14px;
          color: #7a8599;
        }
      }
      .item-bottom {
        font-size: 14px;

        .complete {
          color: #2fb324;
        }
        .underway {
          color: #7a8599;
        }
        .uncompleted {
          color: #e37221;
        }
        .details-box {
          color: #2e6be6;
        }
      }
    }
  }
}
.toolbar {
  margin-top: 24px;
  display: flex;
  padding: 0 16px;
  img {
    width: 14px;
    height: 14px;
    margin-right: 4px;
  }
  .item {
    display: flex;
    justify-content: center;
    min-width: 112px;
    height: 32px;
    font-size: 14px;
    background: #ffffff;
    border-radius: 2px;
    margin-right: 8px;
    border: 1px solid #dcdfe6;
    cursor: pointer;
    align-items: center;
  }
  .right-item {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: flex-end;
    .plan {
      cursor: pointer;
      font-size: 14px;
      display: flex;
      align-items: center;
      color: #2e6be6;
    }
  }
}
.legend {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px;
  margin-top: 16px;
  > div {
    display: flex;
    font-size: 14px;
    align-items: center;
    margin-left: 16px;
    > img {
      margin-right: 4px;
    }
  }
}
</style>
