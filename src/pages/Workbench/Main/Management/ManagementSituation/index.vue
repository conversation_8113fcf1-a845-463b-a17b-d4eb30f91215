<template>
  <div>
    <CardWrapper class="mt-2xs" title="管理情况">
      <!-- 管理效果 -->
      <div class="management-effect">
        <div class="management-title">管理效果</div>
        <div class="effect-box flex items-center flex-wrap">
          <div
            v-for="(item, index) in effectList"
            :key="index"
            class="item-effect mt-8"
          >
            {{ item.title }}
            <span class="item-num">{{ item.num }}</span>
          </div>
        </div>
      </div>
      <!-- 复查随访完成率 -->
      <div class="completion-box mt-16">
        <div class="management-title">复查随访完成率：</div>
        <div class="progress mt-24 flex items-end">
          <!-- 随访完成率 -->
          <Progress :data="fellowCompletion" />
          <!-- 症状随访完成率 -->
          <Progress :data="symptomFellowCompletion" />
          <!-- 生活方式随访完成率 -->
          <Progress :data="lifeTypeFellowCompletion" />
          <!-- 复查完成率 -->
          <Progress :data="reveiwCompletion" />
        </div>
      </div>
    </CardWrapper>
  </div>
</template>
<script setup lang="ts">
import CardWrapper from '@/components/CardWrapper/index.vue';
import Progress from './components/Progress.vue';
import useGlobal from '@/store/module/useGlobal';
let useGlobalInfo = useGlobal();
import { queryEffectNumApi, queryCompleteApi } from '@/api/managementSituation';

onMounted(() => {
  getEffectList();
  getCompletion();
});

watch(
  () => useGlobalInfo.userId,
  () => {
    getEffectList();
    getCompletion();
  },
  {
    deep: true,
  }
);

// 管理效果
let effectList = ref([
  {
    title: '血压达标率：',
    num: '--',
  },
  {
    title: '血糖达标率：',
    num: '--',
  },

  {
    title: '心率达标率：',
    num: '--',
  },
  {
    title: '血脂达标率：',
    num: '--',
  },
  {
    title: '临床事件次数：',
    num: '--',
  },
]);
let getEffectList = () => {
  queryEffectNumApi({ patientId: useGlobalInfo.userId }).then((res: any) => {
    if (res.data) {
      let {
        bloodPressure,
        bloodSugarRate,
        heartRate,
        bloodFatRate,
        clinicalNum,
      } = res.data;
      effectList.value = [
        {
          title: '血压达标率：',
          num: bloodPressure,
        },
        {
          title: '血糖达标率：',
          num: bloodSugarRate,
        },

        {
          title: '心率达标率：',
          num: heartRate,
        },
        {
          title: '血脂达标率：',
          num: bloodFatRate,
        },
        {
          title: '临床事件次数：',
          num: clinicalNum + '次',
        },
      ];
    }
  });
};

// 随访完成率
let fellowCompletion = ref({
  percentage: 0,
  title: '随访完成率',
  size: 100,
  key: 'fellowCompletion',
});

// 症状随访完成率
let symptomFellowCompletion = ref({
  percentage: 0,
  title: '症状随访完成率',
  size: 90,
  key: 'symptomFellowCompletion',
});

// 生活方式随访完成率
let lifeTypeFellowCompletion = ref({
  percentage: 0,
  title: '生活方式随访完成率',
  size: 90,
  key: 'lifeTypeFellowCompletion',
});

// 复查完成率
let reveiwCompletion = ref({
  percentage: 0,
  title: '复查完成率',
  size: 100,
  key: 'reveiwCompletion',
});

let getCompletion = () => {
  queryCompleteApi({ patientId: useGlobalInfo.userId }).then((res: any) => {
    if (res.data) {
      let { followUpRate, symptomRate, lifeStyleRate, reviewRate } = res.data;
      fellowCompletion.value.percentage = parseFloat(
        followUpRate.replace('%', '')
      );

      symptomFellowCompletion.value.percentage = parseFloat(
        symptomRate.replace('%', '')
      );

      lifeTypeFellowCompletion.value.percentage = parseFloat(
        lifeStyleRate.replace('%', '')
      );

      reveiwCompletion.value.percentage = parseFloat(
        reviewRate.replace('%', '')
      );
    }
  });
};
</script>
<style scoped lang="less">
.management-title {
  font-weight: bold;
  font-size: 14px;
  color: #101b25;
}
.management-effect {
  .effect-box {
    width: 100%;
    .item-effect {
      width: 32%;
      background: #f7f8fa;
      border-radius: 2px;
      padding: 6px 12px;
      margin-right: 2%;
    }
    .item-effect:nth-child(3n) {
      margin-right: 0;
    }
  }
}
.completion-box {
  padding: 12px;
  background: #f7f8fa;
  border-radius: 4px;
  .progress {
    padding: 0 24px;
  }
}
</style>
