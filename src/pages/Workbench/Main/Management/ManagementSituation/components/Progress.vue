<template>
  <div class="custom-progress flex">
    <div
      v-if="key === 'reveiwCompletion'"
      class="hr h-60 w-1 mt-28 mr-56"
    ></div>
    <div class="box">
      <el-progress
        :key="Math.random()"
        :class="key"
        type="circle"
        :percentage="percentage"
        :stroke-width="12"
        class="percent"
        size="20"
      >
        <template #default>
          <div class="percentage-value">{{ percentage }}<span>%</span></div>
        </template>
      </el-progress>
      <div class="title mt-12 mb-12">{{ title }}</div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});
let { title, key } = props.data;
let percentage = ref(0);
watch(
  () => props.data,
  newData => {
    percentage.value = newData.percentage;

    // init();
  },
  {
    deep: true,
  }
);

// onMounted(() => {
//   init();
// });

// let init = () => {
//   let circle = document.querySelector(
//     `.${key} .el-progress-circle`
//   ) as HTMLElement;

//   if (circle) {
//     circle.style.width = size + 'px';
//     circle.style.height = size + 'px';
//   }
// };
</script>
<style scoped lang="less">
.custom-progress {
  width: 25%;
  text-align: center;
  .hr {
    background: #bac8d4;
  }
}
.percentage-value {
  font-weight: 600;
  font-size: 28px;
  color: #205280;
  span {
    font-size: 14px;
  }
}
.title {
  font-size: 14px;
  color: #3a4762;
}
:deep(.percent) {
  svg path:nth-child(1) {
    stroke: #e4f2ff;
  }
  svg path:nth-child(2) {
    stroke: #178af5;
  }
}
</style>
