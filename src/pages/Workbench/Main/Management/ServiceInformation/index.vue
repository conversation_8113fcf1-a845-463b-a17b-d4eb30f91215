<template>
  <div class="service-information">
    <CardWrapper class="mt-2xs" title="服务信息">
      <div
        class="team-record flex justify-end items-center cursor-pointer"
        @click="teamRecordVisible = true"
      >
        全部管理团队记录
        <el-icon size="12px">
          <i-ep-arrow-right color="#2E6BE6" />
        </el-icon>
      </div>
      <div class="base-box">
        <div
          v-for="item in dataList"
          :key="item.id"
          class="item-base flex items-center mb-12"
        >
          <div class="title w-104 mr-30">{{ item.title }}</div>
          <div class="value flex items-center">
            {{ item.value }}
            <PhoneCall
              v-if="item.id === 4"
              :name="item.value"
              :tel="item.sellerPhone"
              style="margin-left: 10px"
            />
          </div>
        </div>
      </div>
      <div class="controls flex items-center justify-between">
        <div class="information">
          服务到期时间：
          <span>{{ serviceTime }}</span>
        </div>
        <div class="money flex items-center">
          <div class="renew mr-16 cursor-pointer" @click="renew">续费</div>
          <div
            class="premium cursor-pointer flex items-center"
            @click="premium"
          >
            退费
            <el-icon size="12px">
              <i-ep-arrow-right color="#E63746" />
            </el-icon>
          </div>
        </div>
      </div>
      <ReinfarctionClaim />
    </CardWrapper>

    <!-- 续费 -->
    <RenewalDialog
      :visible="renewalVisible"
      :user-id="useGlobalInfo.userId"
      :current-product="orderObj"
      :order-id="orderId"
      :vip-type="vipType"
      @get-order-info="getPayOrderInfo"
      @close-dialog="closeDialog"
    />

    <!-- 退费 -->
    <HrtDrawer v-model="visible" title="订单选择" size="middle">
      <div class="table-box">
        <div class="header flex items-center">
          <div class="number w-60">序号</div>
          <div class="number w-145">订单编号</div>
          <div class="number w-170">服务包/设备类型</div>
          <div class="number w-60">价格</div>
          <div class="number w-80 flex justify-center mr-8">状态</div>
          <div class="number w-60 flex justify-center">操作</div>
        </div>
        <ul class="list-box">
          <li
            v-for="(item, index) in orderList"
            :key="item.orderId"
            v-infinite-scroll="load"
            class="item mb-8 flex items-center"
          >
            <div class="item-data w-60">{{ index + 1 }}</div>
            <div class="item-data w-145">{{ item.orderNo }}</div>
            <div class="item-data w-170">
              {{ item.productName }}
              <span v-if="item.orderType == 'REPLACE'">（补差价）</span>
            </div>
            <div class="item-data w-60">{{ item.payPrice }}</div>
            <div class="w-80 item-data flex justify-center mr-8">
              {{ getRefundStatus(item.refundStatus, item.status) }}
            </div>
            <div class="w-60 flex justify-center">
              <div
                v-if="
                  item.status == 100 ||
                  item.status == -11 ||
                  item.status == -12 ||
                  item.status == -13
                "
                class="refund w-60 cursor-pointer"
                @click="toRefundPage(item)"
              >
                <span
                  v-if="
                    !estimateOrderTime(item.payTime) &&
                    item.orderType != 'REPLACE'
                  "
                >
                  {{
                    (item.status === 100 && item.refundStatus == 4) ||
                    (item.status === -11 && item.refundStatus == 1)
                      ? '退款详情'
                      : item.payType === 4
                        ? '退费申请'
                        : item.refundStatus == 5
                          ? '再次提交'
                          : '退款申请'
                  }}
                </span>
              </div>
              <div
                v-else-if="isShowOrder(item.status, item.createTime)"
                class="refund w-60 cursor-pointer"
                @click="cancelOrder(item)"
              >
                取消订单
              </div>
              <div v-else class="w-60"></div>
            </div>
          </li>
        </ul>
      </div>
    </HrtDrawer>

    <!-- 退款弹窗 -->
    <Refund
      :objective-visibles="objectiveVisibles"
      :refund-info="refundInfo"
      @close-dialog="closeRefundDialog"
    />

    <HrtDrawer v-model="teamRecordVisible" title="管理团队记录" size="middle">
      <ul
        v-if="teamRecordList.length"
        v-infinite-scroll="loadTeamRecordList"
        class="team-list p-16"
      >
        <li
          v-for="item in teamRecordList"
          :key="item.teamRecordId"
          class="team-item mb-8"
        >
          <TeamRecord :item="item" />
        </li>
        分页需调整
      </ul>
      <el-empty v-else description="暂无数据" />
    </HrtDrawer>
  </div>
</template>
<script setup lang="ts">
import CardWrapper from '@/components/CardWrapper/index.vue';
import PhoneCall from '@/components/PhoneCall/index.vue';
import RenewalDialog from './components/RenewalDialog.vue';
import ReinfarctionClaim from './components/ReinfarctionClaim.vue';
import Refund from './components/Refund.vue';
import useGlobal from '@/store/module/useGlobal';
const useGlobalInfo = useGlobal();
import TeamRecord from './components/TeamRecord.vue';
import {
  queryOrderApi,
  queryOrderRecordListApi,
  queryExpirationDateApi,
  queryOrderLastApi,
  queryCancelOrderApi,
} from '@/api/managementSituation';
import { timestampToDate, timestampToDateTime } from '../hooks';
import { checkRefundReinfarctionClaimStatus } from '@/api/recurrentMIClaim';
import useReport from '@/store/module/useReport';
const useReportInfo = useReport();
import { isNil } from 'lodash-es';
import { IManageTeamRecord } from './type.d';

const teamRecordVisible = ref<boolean>(false);
const teamRecordList = ref<IManageTeamRecord[]>([
  {
    teamRecordId: 1,
    startTime: timestampToDateTime(new Date().getTime()),
    endTime: timestampToDateTime(new Date().getTime()),
    doctor: '张三',
    healthManager: '张三',
    rehabilitation: '张三',
    type: '患者工作室变更',
    remark:
      '医生交接，批量数据处理法大大阿发达发大幅度萨芬放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大发顺丰放大',
  },
  {
    teamRecordId: 1,
    startTime: timestampToDateTime(new Date().getTime()),
    endTime: '',
    doctor: '李四',
    healthManager: '李四',
    rehabilitation: '李四',
    type: '患者工作室变更',
    remark: '医生交接，批量数据处理',
  },
  {
    teamRecordId: 1,
    startTime: timestampToDateTime(new Date().getTime()),
    endTime: '',
    doctor: '李四',
    healthManager: '李四',
    rehabilitation: '李四',
    type: '患者工作室变更',
    remark: '医生交接，批量数据处理',
  },
  {
    teamRecordId: 1,
    startTime: timestampToDateTime(new Date().getTime()),
    endTime: '',
    doctor: '李四',
    healthManager: '李四',
    rehabilitation: '李四',
    type: '患者工作室变更',
    remark: '医生交接，批量数据处理',
  },
  {
    teamRecordId: 1,
    startTime: timestampToDateTime(new Date().getTime()),
    endTime: '',
    doctor: '李四',
    healthManager: '李四',
    rehabilitation: '李四',
    type: '患者工作室变更',
    remark: '医生交接，批量数据处理',
  },
  {
    teamRecordId: 1,
    startTime: timestampToDateTime(new Date().getTime()),
    endTime: '',
    doctor: '李四',
    healthManager: '李四',
    rehabilitation: '李四',
    type: '患者工作室变更',
    remark: '医生交接，批量数据处理',
  },
  {
    teamRecordId: 1,
    startTime: timestampToDateTime(new Date().getTime()),
    endTime: '',
    doctor: '李四',
    healthManager: '李四',
    rehabilitation: '李四',
    type: '患者工作室变更',
    remark: '医生交接，批量数据处理',
  },
  {
    teamRecordId: 1,
    startTime: timestampToDateTime(new Date().getTime()),
    endTime: '',
    doctor: '李四',
    healthManager: '李四',
    rehabilitation: '李四',
    type: '患者工作室变更',
    remark: '医生交接，批量数据处理',
  },
  {
    teamRecordId: 1,
    startTime: timestampToDateTime(new Date().getTime()),
    endTime: '',
    doctor: '李四',
    healthManager: '李四',
    rehabilitation: '李四',
    type: '患者工作室变更',
    remark: '医生交接，批量数据处理',
  },
]);
const loadTeamRecordList = () => {
  console.log('滚动到底，加载数据');
};

const RefundStatusMap = {
  0: '暂存',
  1: '退款中',
  2: '已驳回',
  3: '已撤回',
  4: '已退款',
  5: '退款失败',
};

const StatusMap = {
  100: '支付成功',
  0: '待支付',
  1: '已取消',
  2: '已退款',
};

// 获取退款状态
const getRefundStatus = computed(() => {
  return (refundStatus: number, status: number) => {
    let str = '';

    if (!isNil(refundStatus)) {
      str = RefundStatusMap[refundStatus];
    } else {
      str = StatusMap[status];
    }

    return str || '--';
  };
});

const serviceTime = ref<string>('');
const baseInfo = ref<any>({});
onMounted(() => {
  if (useGlobalInfo.userId) getExpirationDate();
});

interface dataInfo {
  title: string;
  value: string;
  id: number;
  sellerPhone?: number | string;
}
const dataList = ref<dataInfo[]>([
  {
    title: '医生：',
    value: '--',
    id: 1,
  },
  {
    title: '健康管理师：',
    value: '--',
    id: 2,
  },
  {
    title: '运动康复师：',
    value: '--',
    id: 3,
  },
  {
    title: '健康/代理顾问：',
    value: '--',
    id: 4,
    sellerPhone: '--',
  },
  {
    title: '服务开始时间',
    value: '--',
    id: 5,
  },
]);
const getBaseInfo = (orderId: any) => {
  queryOrderApi({ orderId }).then((res: any) => {
    if (res.data) {
      const {
        assistantName,
        customerName,
        rehabName,
        sellerName,
        sellerPhone,
      } = res.data;
      dataList.value = [
        {
          title: '医生：',
          value: assistantName,
          id: 1,
        },
        {
          title: '健康管理师：',
          value: customerName,
          id: 2,
        },
        {
          title: '运动康复师：',
          value: rehabName,
          id: 3,
        },
        {
          title: '健康/代理顾问：',
          value: sellerName,
          id: 4,
          sellerPhone,
        },
        {
          title: '服务开始时间',
          value: timestampToDate(baseInfo.value.startDate),
          id: 5,
        },
      ];
    }
  });
};

// 续费
const renewalVisible = ref(false);
const orderId = ref('');
const vipType = ref(1);
const orderObj = ref<any>({});

//选择完支付相关信息
const getPayOrderInfo = () => {
  getExpirationDate();
};
//获取到期时间
const expirationDate = ref('');
const getExpirationDate = () => {
  queryExpirationDateApi({
    patientId: useGlobalInfo.userId,
  }).then((res: any) => {
    vipType.value = Number(res.data.vipType);
    expirationDate.value = res.data ? res.data.expirationDate : null;
  });
};
const closeDialog = () => {
  renewalVisible.value = false;
  queryInfo.value.pageNumber = 1;
  orderList.value = [];
  getAllOrder();
};
const renew = () => {
  nearOrderInfo();
  renewalVisible.value = true;
};

/*获取最近一条订单*/
const nearOrderInfo = async () => {
  await queryOrderLastApi({
    patientId: useGlobalInfo.userId,
  }).then((res: any) => {
    if (res.code === 'E000000' && res.data) {
      orderId.value = res.data.orderId;
      orderObj.value = { ...res.data, expirationDate: expirationDate.value };
    }
  });
};

// 退费
const visible = ref(false);
const queryInfo = ref({
  pageNumber: 1,
  pageSize: 10,
  patientId: useGlobalInfo.userId,
});
const totalNumber = ref(0);
const premium = async () => {
  const checkClaimStatusRes = await checkRefundReinfarctionClaimStatus({
    patientId: useGlobalInfo.userId,
  });
  if (!checkClaimStatusRes) return;
  queryInfo.value.pageNumber = 1;
  orderList.value = [];
  getAllOrder();
  visible.value = true;
};
const orderList = ref<any>([]);
// 获取所有订单
const getAllOrder = async () => {
  await queryOrderRecordListApi(queryInfo.value).then((res: any) => {
    orderList.value = [...orderList.value, ...res.data.contents];
    totalNumber.value = Math.ceil(res.data.total / 10);
  });
};
// 加载到底部分页加载数据
const load = () => {
  if (queryInfo.value.pageNumber < totalNumber.value) {
    queryInfo.value.pageNumber++;
    getAllOrder();
  }
};
// 退款
const objectiveVisibles = ref(false);
const closeRefundDialog = (val: any) => {
  if (val) {
    queryInfo.value.pageNumber = 1;
    orderList.value = [];
    getAllOrder();
  }
  objectiveVisibles.value = false;
};
const refundInfo = ref({});
//去退款
const toRefundPage = (item: {
  orderId?: any;
  payType?: any;
  remarks?: any;
  orderType?: any;
}) => {
  item.remarks =
    item.orderType == 'HARDWARE' ? JSON.parse(item.remarks) : item.remarks;
  refundInfo.value = item;
  objectiveVisibles.value = true;
};

// 取消订单
const cancelOrder = (item: { orderId: any; orderType: any }) => {
  const { orderId, orderType } = item;
  ElMessageBox.confirm('确定要取消订单吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      queryCancelOrderApi({
        orderId,
        orderType,
      }).then(res => {
        if (res.code === 'E000000') {
          ElMessage({
            message: '已取消订单!',
            type: 'success',
          });
          queryInfo.value.pageNumber = 1;
          orderList.value = [];
          getAllOrder();
        }
      });
    })
    .catch(() => {});
};
/* 取消订单时间状态控制*/
const isShowOrder = computed(() => {
  const curDate = Number(new Date());
  return (status: number, time: string | number | Date) => {
    const date = new Date(time);
    const newDate = date.setMinutes(date.getMinutes() + 5);
    if (status == 0 && curDate > newDate) {
      return true;
    } else {
      return false;
    }
  };
});

const estimateOrderTime = (payTime: number) => {
  if (payTime) {
    const currentTime = new Date().getTime();
    const day = (currentTime - payTime) / (24 * 60 * 60 * 1000);
    return day > 365;
  } else {
    return true;
  }
};

const reportInfoOrderId = ref<string | number>(0);
watch(
  () => useReportInfo.reportInfo,
  reportInfo => {
    const curOrderId = reportInfo.orderId;
    if (!curOrderId) return;
    if (!reportInfoOrderId.value) reportInfoOrderId.value = curOrderId;
    if (curOrderId !== reportInfoOrderId.value) return;

    baseInfo.value = reportInfo;
    getBaseInfo(reportInfo.orderId);
    // 服务到期时间
    serviceTime.value = reportInfo.endDate
      ? timestampToDate(reportInfo.endDate)
      : '--';
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<style scoped lang="less">
.service-information {
  position: relative;
  .team-record {
    position: absolute;
    right: 18px;
    top: 14px;
    font-size: 14px;
    color: #2e6be6;
  }
}
.base-box {
  .item-base {
    font-size: 14px;
    .title {
      color: #708293;
    }
    .value {
      color: #3a4762;
    }
  }
}
.controls {
  padding: 12px;
  background: #f7f8fa;
  border-radius: 4px;
  .information {
    font-size: 14px;
    color: #708293;
    span {
      color: #3a4762;
    }
  }
  .money {
    font-size: 14px;
    .renew {
      color: #2e6be6;
    }
    .premium {
      color: #e63746;
    }
  }
}
.table-box {
  padding: 16px;
  .header {
    padding: 6px 12px;
    background: #f7f8fa;
    .number {
      font-weight: bold;
      font-size: 14px;
      color: #101b25;
    }
  }
  .list-box {
    .item {
      padding: 12px;
      border-bottom: 1px solid #e9e8eb;
      .item-data {
        font-size: 14px;
        color: #101b25;
      }
    }
  }
  .refund {
    font-size: 14px;
    color: #e63746;
  }
}
</style>
<style lang="less">
.btmSure {
  display: flex;
  justify-content: flex-end;
  .cancelBtn {
    width: 80px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    font-weight: 400;
    color: #303133;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .sureBtn {
    width: 80px;
    height: 32px;
    background: #0a73e4;
    border-radius: 4px;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 30px;
    cursor: pointer;
  }
}
</style>
