<template>
  <Drawer
    :visible="visible"
    title="再梗理赔详情"
    width="600px"
    @update:visible="emit('update:visible', $event)"
  >
    <!-- 理赔信息 -->
    <div class="space-y-4 mb-12">
      <div class="flex flex-col space-y-6 px-16 py-12 *:flex *:items-center">
        <div class="my-8">
          <label class="w-110 text-gray-600">再次入院时间</label>
          <div class="ml-6 text-[#7A8599]">
            {{ formatDate(detailData?.beHospitalizedTime) }}
          </div>
        </div>
        <div class="my-8">
          <label class="w-110 text-gray-600">对接人</label>
          <div class="ml-6 text-[#7A8599]">
            {{ detailData?.contactName || '--' }}
          </div>
        </div>
        <div class="my-8">
          <label class="w-110 text-gray-600">理赔金额</label>
          <div class="ml-6 text-[#7A8599]">
            {{ detailData?.claimAmount || '--' }}（元）
          </div>
        </div>

        <div class="my-8 flex">
          <label class="w-110 text-gray-600">患者小结</label>
          <textarea
            :class="[
              'text-[#7A8599] ml-6 w-460 overflow-y-auto  outline-none max-h-520',
              detailData?.summary && detailData.summary.length > 200
                ? 'h-260'
                : 'h-36',
            ]"
            readonly
            v-text="detailData?.summary || '暂无小结'"
          ></textarea>
        </div>
        <!-- 附件资料 -->
        <div v-if="detailData?.files?.length" class="my-8">
          <h4 class="w-110 text-gray-600">再次入院资料</h4>
          <div class="w-full grid grid-cols-5 gap-5 ml-6 max-w-420">
            <div
              v-for="(file, index) in detailData.files"
              :key="index"
              class="w-fit flex items-center p-2 bg-gray-50 rounded border cursor-pointer hover:bg-gray-100"
            >
              <a class="block" :href="file.url" target="_blank">
                <img
                  v-if="
                    file.url?.endsWith('.jpg') ||
                    file.url?.endsWith('.png') ||
                    file.url?.endsWith('.jpeg')
                  "
                  :src="file.url"
                  alt=""
                  class="w-80 h-80 object-cover"
                />
                <el-icon v-else class="text-[#2e6be6] w-full h-full">
                  <Document style="width: 80%; height: 80%" />
                </el-icon>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="border-b mx-16"></div>
      <!-- 审批流程 -->
      <div class="py-16 px-16">
        <div class="text-sm el-steps-control" style="height: 200px">
          <el-steps direction="vertical" :active="getActiveStep()">
            <el-step
              v-for="(item, index) in detailData?.processData"
              :key="index"
              :status="
                item.result === 'AGREE'
                  ? 'success'
                  : item.result === 'REFUSE'
                    ? 'error'
                    : 'success'
              "
            >
              <template #title>
                ({{ item.showName }})
                <span v-if="!item.ccPerson || !item.ccPerson.length">
                  {{ item.approver }}
                </span>
                <span
                  v-for="(cc, ccIndex) in item.ccPerson"
                  :key="ccIndex"
                  class="after:content-['、'] before:mr-2 last:after:content-['']"
                >
                  {{ cc }}
                </span>
              </template>
              <template #description>
                <div class="w-436">
                  {{
                    item.result === 'AGREE'
                      ? '同意'
                      : item.result === 'REFUSE'
                        ? '驳回'
                        : '--'
                  }}
                  ({{ item.date }})
                  {{ item.remark }}
                </div>
              </template>
            </el-step>
          </el-steps>
        </div>
      </div>
    </div>
  </Drawer>
</template>

<script setup lang="ts">
import { Document } from '@element-plus/icons-vue';
import Drawer from '@/components/Drawer/index.vue';
import dayjs from 'dayjs';
import { getReinfarctionClaimDetail } from '@/api/recurrentMIClaim';
import type { IApiMiClaimDetail } from '@/interface/type';
import { useGlobal } from '@/store';
const useGlobalStore = useGlobal();
interface Props {
  visible: boolean;
  status?: string;
  claimInfo?: any;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'edit', data: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  status: 'pending',
  claimInfo: () => ({}),
});

const emit = defineEmits<Emits>();

const detailData = ref<IApiMiClaimDetail>();
const getData = async () => {
  detailData.value = await getReinfarctionClaimDetail({
    patientId: useGlobalStore.userId,
    claimId: props.claimInfo.claimId,
  });
};
watch(
  () => props.visible,
  val => {
    if (val) {
      getData();
    }
  }
);

// 格式化日期
const formatDate = (date: string | number | undefined) => {
  if (!date) return '--';
  return dayjs(date).format('YYYY-MM-DD');
};

// 获取当前步骤
const getActiveStep = () => {
  const record = detailData.value?.processData?.findLastIndex(
    it => it.result === 'AGREE'
  );

  return record ? record + 1 : 0;
};

defineOptions({
  name: 'ClaimDrawer',
});
</script>

<style scoped lang="less">
:deep(.el-steps-control) {
  .el-step {
    .el-step__main {
      padding-bottom: 16px;
      padding-left: 6px;
    }
    .el-step__title {
      font-size: 14px;
      padding-bottom: 8px;
      font-weight: 600;
      color: #b8becc;
      line-height: 1;
      &.is-process,
      &.is-success {
        color: #3a4762;
      }
      &.is-error {
        color: #f56c6c;
      }
    }
    .el-step__description {
      font-size: 14px;
      color: #b8becc;
      &.is-process,
      &.is-success {
        color: #7a8599;
      }
    }
    .el-step__icon {
      width: 12px;
      height: 12px;
      margin-left: 5px;
      border: 2px solid #b8becc;
      & * {
        display: none;
      }
    }
    .el-step__line {
      background: #e1e5ed;
    }
    .is-success,
    .is-process {
      .el-step__icon {
        border-color: #2e6be6;
      }
      .el-step__line {
        color: #2e6be6;
      }
    }
    .is-error {
      .el-step__icon {
        border-color: #f56c6c;
      }
    }
  }
}
</style>
