<template>
  <Dialog
    :visible="visible"
    width="800px"
    size="large"
    :draggable="true"
    title="再梗理赔"
    @update:visible="emit('update:visible', $event)"
  >
    <div
      v-loading="loading"
      class="flex flex-col items-center justify-center py-16"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        align="center"
        label-width="156px"
      >
        <!-- 请选择再次入院时间 -->
        <el-form-item label="请选择再次入院时间" prop="beHospitalizedTime">
          <DatePicker
            :value="form.beHospitalizedTime"
            :mode="mode"
            style="width: 240px"
            placeholder="请选择再次入院时间"
            @change="val => (form.beHospitalizedTime = val)"
          />
        </el-form-item>
        <!-- 请选择对接人 -->
        <el-form-item label="请选择对接人" prop="contactPerson">
          <Select
            :value="form.contactPerson"
            :mode="mode"
            style="width: 240px"
            :options="contactPersonOptionsMap"
            placeholder="请选择对接人"
            filterable
            @change="
              val => {
                if (typeof val === 'string' || typeof val === 'number') {
                  form.contactPerson = val;
                }
              }
            "
          />
        </el-form-item>
        <el-form-item label="理赔金额(元)" prop="claimAmount">
          <InputNumber
            :value="form.claimAmount"
            :mode="mode"
            :precision="2"
            :max="999999.99"
            style="width: 240px"
            placeholder="请输入理赔金额"
            unit="元"
            @change="val => (form.claimAmount = val)"
          />
        </el-form-item>
        <!-- 患者小结 textArea -->
        <el-form-item label="患者小结" prop="summary">
          <Textarea
            :value="form.summary"
            :mode="mode"
            style="width: 240px"
            placeholder="姓名，性别，**岁，于何时入组。患者何时因何原因首次入院购买服务包，何时因何原因再次入院，行*****手术，术后恢复情况如何"
            @change="val => (form.summary = val)"
          />
        </el-form-item>
        <!-- 再次入院资料上传 -->
        <el-form-item label="再次入院资料上传" prop="admissionFiles">
          <Upload
            :model-value="form.admissionFiles"
            :multiple="true"
            :drag="true"
            :file-types="['jpg', 'jpeg', 'png', 'pdf']"
            tip="支持jpg、png、pdf格式，单个文件不超过5M"
            :limit-size="5 * 1024 * 1024"
            :limit="30"
            upload-btn-text="上传资料"
            @update:model-value="val => (form.admissionFiles = val)"
          />
        </el-form-item>
      </el-form>
      <div class="flex justify-center">
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import Dialog from '@/components/Dialog/index.vue';
import InputNumber from '@/components/FormItem/InputNumber.vue';
import Select from '@/components/FormItem/Select.vue';
import DatePicker from '@/components/FormItem/DatePicker.vue';
import Textarea from '@/components/FormItem/Textarea.vue';
import Upload from '@/components/Upload/index.vue';
import {
  queryCompanyUser,
  uploadProcessAttachment,
} from '@/api/recurrentMIClaim';
import type {
  IApiMiClaimQueryUser,
  IApiMiClaimQueryUserItem,
} from '@/interface/type';

interface Props {
  visible: boolean;
  isEdit?: boolean;
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'submit', data: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: true,
  visible: true,
  loading: false,
});

const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const mode = computed(() => (props.isEdit ? 'edit' : 'view'));

// 对接人选项
const contactPersonOptions = ref<IApiMiClaimQueryUser>([]);
const contactPersonOptionsMap = computed(() => {
  return contactPersonOptions.value.map((item: IApiMiClaimQueryUserItem) => ({
    value: item.osUserId as number,
    label: item.name as string,
  }));
});
// 获取对接人
const getContactPerson = async () => {
  contactPersonOptions.value = await queryCompanyUser({});
};
getContactPerson();
// 表单数据
const form = ref({
  beHospitalizedTime: '',
  contactPerson: '' as string | number,
  claimAmount: '',
  summary: '',
  admissionFiles: [] as any[],
  files: [] as any[],
});

// 表单校验规则
const rules: FormRules = {
  beHospitalizedTime: [
    { required: true, message: '请选择再次入院时间', trigger: 'change' },
  ],
  contactPerson: [
    { required: true, message: '请选择对接人', trigger: 'change' },
  ],

  admissionFiles: [
    { required: true, message: '请上传再次入院资料', trigger: 'change' },
  ],
};

const uploadedDINGFiles = ref<any[]>([]);

watch(
  () => form.value.admissionFiles,
  async () => {
    const files = form.value.admissionFiles;
    const fileUrls = files.map(it => it.url);
    uploadedDINGFiles.value = uploadedDINGFiles.value.filter(it =>
      fileUrls.includes(it.url)
    );
    const existFileUrls = uploadedDINGFiles.value.map(it => it.url);
    const needToDINGFiles = fileUrls.filter(it => !existFileUrls.includes(it));
    if (needToDINGFiles.length) {
      submitLoading.value = true;
      const fileUploadRes = await uploadProcessAttachment({
        urls: needToDINGFiles,
      });
      submitLoading.value = false;
      if (fileUploadRes && fileUploadRes.length) {
        uploadedDINGFiles.value = uploadedDINGFiles.value.concat(fileUploadRes);
      }
    }
  },
  {
    deep: true,
  }
);

const submitLoading = ref(false);
// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    const contactUser = contactPersonOptions.value?.find(
      it => it.osUserId === form.value.contactPerson
    );
    if (!contactUser) throw new Error('contactUser not exist !');
    emit('submit', {
      ...form.value,
      contactUser: {
        osUserId: form.value.contactPerson,
        userPhone: contactUser?.phone ?? '',
        userName: contactUser?.name ?? '',
      },
      files: uploadedDINGFiles.value,
    });
  } catch (error) {
    console.error('表单验证失败:', error);
    ElMessage.error('请检查表单信息');
  }
};

// 取消操作
const handleCancel = () => {
  emit('update:visible', false);
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 监听visible变化，重置表单
watch(
  () => props.visible,
  newVal => {
    if (newVal && formRef.value) {
      formRef.value.resetFields();
      form.value.files = [];
    }
  }
);
defineOptions({
  name: 'ClaimDialog',
});
</script>

<style scoped lang="less">
:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
  width: 240px;
  height: 32px;
  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}
</style>
