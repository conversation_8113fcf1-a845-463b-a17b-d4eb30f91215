<template>
  <el-dialog
    v-model="props.objectiveVisibles"
    width="800px"
    :modal="false"
    :close-on-click-modal="false"
    class="refund-dialog"
    @close="close"
  >
    <div class="refund-wrapper">
      <div class="top-head flex items-center justify-between">
        <span>退款申请</span>
        <span>订单编号：{{ orderInfo.orderNo }}</span>
      </div>
      <!--  基础信息-->
      <el-form ref="ruleForm" :inline="true" :model="info" :rules="rules">
        <div class="baseInfo mt-28">
          <div class="info-title mb-20">
            <span class="ml-12">基础信息</span>
          </div>
          <div class="info-content flex items-center flex-wrap">
            <div class="info-item">
              <div>患者姓名：</div>
              <div>{{ orderInfo.patientName }}</div>
            </div>
            <div class="info-item">
              <div>患者性别：</div>
              <div>
                {{
                  orderInfo.patientGender === 1
                    ? '男'
                    : orderInfo.patientGender === 2
                      ? '女'
                      : '未知'
                }}
              </div>
            </div>
            <div class="info-item">
              <div>身份证号：</div>
              <div>{{ orderInfo.patientCardNo }}</div>
            </div>
            <div v-if="orderInfo.orderType != 'HARDWARE'" class="info-item">
              <div>加入天数：</div>
              <div>{{ isEffect ? joinDay : '0' }}</div>
            </div>
            <div v-if="orderInfo.orderType != 'HARDWARE'" class="info-item">
              <div>服务开始时间：</div>
              <div>
                {{ timestampToDateTime(orderInfo.joinDate) }}
              </div>
            </div>
            <div class="info-item">
              <div>医生：</div>
              <div>{{ orderInfo.assistantName }}</div>
            </div>
            <div class="info-item">
              <div>健康顾问：</div>
              <div>{{ orderInfo.sellerName || '--' }}</div>
            </div>
            <div class="info-item">
              <div>工作室：</div>
              <div>{{ orderInfo.groupName }}</div>
            </div>
            <div class="info-item">
              <div>产品服务：</div>
              <div>
                {{ orderInfo.productName }}
              </div>
            </div>
            <div class="info-item">
              <div>订单类型：</div>
              <div>
                {{ Number(orderInfo.goal) === 2 ? '续费订单' : '首购订单' }}
              </div>
            </div>
          </div>
          <div class="baseInfo-box">
            <el-form-item label="提出退款日期" prop="refundDate">
              <div
                class="date-box"
                :style="{
                  width: '240px',
                }"
              >
                <el-date-picker
                  v-model="info.refundDate"
                  type="date"
                  placeholder="选择日期"
                  class="datepicker"
                  style="width: 100%"
                  value-format="x"
                  format="YYYY-MM-DD"
                  :disabled-date="pickerOptions"
                  :clearable="false"
                  :disabled="isDisabled"
                  @change="countMoney()"
                />
                <img
                  :src="changeTimeIng"
                  alt=""
                  class="w-14 h-14 change-time-icon"
                />
              </div>
            </el-form-item>
            <el-form-item label="分公司名称" prop="companyId">
              <el-select
                v-model="info.companyId"
                :disabled="isDisabled"
                :style="{ width: '240px' }"
                placeholder="请选择分公司名称"
              >
                <el-option
                  v-for="item in companyList"
                  :key="item.companyId"
                  :label="item.companyName"
                  :value="item.companyId"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>
        <div class="refundInfo">
          <div class="info-title"><span class="ml-12">退费信息</span></div>
          <div class="refund-content mt-10">
            <!-- 押金退费 -->
            <DepositRefund
              v-if="orderInfo.orderType == 'DEPOSIT'"
              v-model:deposit-refund-info="info"
              :is-disabled="isDisabled"
              :refund-count-form="refundCountForm"
              :pay-price="orderInfo.payPrice"
              @choose-refund-type="chooseRefundType"
              @change-equipment-damaged="changeEquipmentDamaged"
            />
            <!-- 服务包、硬件退费 -->
            <template v-else>
              <div class="item">
                <div class="name">
                  1、
                  <span class="required-icon">*</span>
                  提出退款的证明（聊天截图、通话记录）
                  <span>最多可上传9张</span>
                </div>
                <div class="img-box mt-12">
                  <UploadImages
                    v-model:img-list="info.proposeRefundPictureList"
                    :show-status="false"
                    :disabled="info.proposeRefundPictureList.length > 9"
                  />
                </div>
              </div>
              <div v-if="orderInfo.orderType == 'HARDWARE'" class="item">
                <div class="name">
                  <span class="index-num">
                    2、
                    <span class="required-icon">*</span>
                    设备编码
                  </span>
                </div>
                <div class="input-content ml-24">
                  <el-form-item>
                    <div class="content-msg">
                      <div class="item-number">
                        {{ props.refundInfo.remarks.deviceSoNo }}
                      </div>
                      <div class="item-box mb-16">
                        <div class="title mb-8">
                          设备物流照片：
                          <span>（支持多张）</span>
                        </div>
                        <div class="img-box">
                          <UploadImages
                            v-model:img-list="
                              deviceInfoForm.returnDevicePictureList
                            "
                            :show-status="false"
                          />
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>
              <div v-else class="item">
                <div class="name">
                  <span class="index-num">
                    2、
                    <span class="required-icon">*</span>
                    是否退回硬件设备
                  </span>
                </div>
                <div class="input-content ml-24">
                  <el-form-item>
                    <div class="content-msg">
                      <div class="equipment-list">
                        <div
                          v-for="(item, index) in equipmentList"
                          :key="index"
                          class="item-equipment flex items-center justify-between"
                        >
                          <div class="item-left flex items-center">
                            <div>
                              {{
                                item.deviceType == 'HP'
                                  ? '掌护血压计'
                                  : item.deviceType == 'BPG'
                                    ? '台式血压计'
                                    : item.deviceType == 'WATCH'
                                      ? '智能手表'
                                      : '体重秤'
                              }}（{{
                                item.deviceType == 'HP'
                                  ? 'IMEI'
                                  : item.deviceType == 'BPG'
                                    ? 'SN'
                                    : item.deviceType == 'WATCH'
                                      ? 'IMEI'
                                      : 'MAC'
                              }}）：
                            </div>
                            <div>{{ item.deviceNo }}</div>
                          </div>
                          <el-radio-group
                            v-model="item.isReturn"
                            @change="countMoney()"
                          >
                            <el-radio :value="true" size="large">
                              退回公司
                            </el-radio>
                            <el-radio :value="false" size="large">
                              不退设备
                            </el-radio>
                          </el-radio-group>
                        </div>
                      </div>
                      <div
                        v-if="getShowContent.pictureFlag"
                        class="item-box mb-16"
                      >
                        <div class="title">不退回原因：</div>
                        <el-input
                          v-model="deviceInfoForm.noReturnReason"
                          class="mt-12"
                          type="textarea"
                          resize="none"
                          maxlength="100"
                          show-word-limit
                          :rows="3"
                        />
                      </div>
                      <div
                        v-if="getShowContent.reasonFlag"
                        class="item-box mb-16"
                      >
                        <div class="title mb-8">
                          设备物流照片：
                          <span>（支持多张）</span>
                        </div>
                        <div class="img-box">
                          <UploadImages
                            v-model:img-list="
                              deviceInfoForm.returnDevicePictureList
                            "
                            :show-status="false"
                          />
                        </div>
                      </div>
                      <div class="item-box">
                        <div class="title">是否扣除硬件费用?</div>
                        <el-radio-group
                          v-model="info.deductDeviceMoney"
                          @change="countMoney()"
                        >
                          <el-radio :value="1" size="large">是</el-radio>
                          <el-radio :value="0" size="large">否</el-radio>
                        </el-radio-group>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>
              <div class="item">
                <div class="name">
                  <template v-if="hasPayObject">
                    3、支付对象：
                    <span class="fixed-text">
                      {{
                        info.payObject === 1
                          ? '患者缴费'
                          : info.payObject === 2
                            ? '健康顾问缴费'
                            : info.payObject === 3
                              ? '公司账号缴费'
                              : ''
                      }}
                    </span>
                  </template>
                  <template v-if="!hasPayObject">
                    <span class="index-num">
                      3、
                      <span class="required-icon">*</span>
                      支付对象
                    </span>
                    <el-form-item prop="payObject">
                      <el-select
                        v-model="info.payObject"
                        placeholder="请选择支付对象"
                        :style="{ width: '240px' }"
                        :disabled="isDisabled"
                      >
                        <el-option
                          v-for="item in payObjectList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </template>
                </div>
              </div>
              <div class="item">
                <div class="name">
                  4、实缴金额：
                  <span class="fixed-text">{{ orderInfo.payPrice }}</span>
                </div>
              </div>
              <div class="item">
                <div class="name">
                  <span class="index-num">
                    5、
                    <span class="required-icon">*</span>
                    退款原因
                  </span>
                  <el-form-item prop="returnReason">
                    <el-select
                      v-model="info.returnReason"
                      collapse-tags
                      placeholder="请选择退款原因"
                      :style="{ width: '240px' }"
                      multiple
                      :disabled="isDisabled"
                    >
                      <el-option
                        v-for="item in refundReasonList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div class="item">
                <div class="name">
                  <span class="index-num">
                    6、
                    <span class="required-icon">*</span>
                    详细阐述退款原因
                  </span>
                </div>
                <div class="input-content ml-24">
                  <el-form-item prop="returnReasonDetails">
                    <el-input
                      v-model="info.returnReasonDetails"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入内容"
                      resize="none"
                      :disabled="isDisabled"
                      maxlength="200"
                    />
                  </el-form-item>
                </div>
              </div>
              <div class="item">
                <div class="name">
                  7、实退金额：
                  <span class="fixed-text">{{ info.refundMoney }}</span>
                </div>
              </div>
              <div class="item">
                <div class="name">
                  <span class="index-num">
                    8 、
                    <span class="required-icon">*</span>
                    退款方式
                  </span>
                  <el-form-item v-if="Number(info.refundMoney) < 0">
                    <div>：患者补差价</div>
                  </el-form-item>
                  <el-form-item v-else prop="refundType">
                    <el-radio-group
                      v-model="info.refundType"
                      :disabled="isDisabled"
                      @change="chooseRefundType"
                    >
                      <el-radio
                        v-if="info.payObject != 2 && info.payObject != 3"
                        :label="1"
                      >
                        原路退回
                      </el-radio>
                      <el-radio :label="2">退回指定账户</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div
                  v-if="info.refundType === 2"
                  class="blood-content mt-16 ml-24"
                >
                  <div class="count-info">
                    收款人姓名：{{ refundCountForm.payeeName }}
                  </div>
                  <div class="count-info mt-4">
                    收款人账号：{{ refundCountForm.proceedsAccount }}
                  </div>
                  <div class="count-info mt-4">
                    收款人开户行：{{ refundCountForm.bankOfDeposit }}
                  </div>
                </div>
              </div>
              <div class="item" style="border: none">
                <div class="name">
                  <span class="index-num">
                    9 、
                    <span class="required-icon">*</span>
                    是否开具发票
                    <span style="margin-left: 10px">最多可上传9张</span>
                  </span>
                  <el-form-item prop="isInvoicing">
                    <el-radio-group
                      v-model="info.isInvoicing"
                      :disabled="isDisabled"
                    >
                      <el-radio :label="1">是</el-radio>
                      <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <UploadImages
                    v-if="info.isInvoicing === 1"
                    v-model:img-list="info.invoicingPictureList"
                    :show-status="false"
                  />
                </div>
              </div>
            </template>
          </div>
        </div>
      </el-form>
      <div class="btmSure flex justify-end">
        <template v-if="!orderStatus || orderStatus === 2 || orderStatus === 3">
          <div
            v-if="!isCountdown"
            class="cancelBtn w-120 h-40 flex items-center justify-center cursor-pointer"
            @click="submitRefundApply(0)"
          >
            保存
          </div>
          <div
            v-if="isCountdown"
            :class="{
              sureBtn: countdownText,
            }"
            class="h-40 flex items-center justify-center ml-30"
            style="width: 108px"
          >
            {{ countdownText }}
          </div>
          <template v-else>
            <div
              v-if="Number(info.refundMoney) < 0"
              class="sureBtn h-40 flex items-center justify-center cursor-pointer ml-30"
              style="width: 108px"
              @click="pushPaymentLink()"
            >
              推送支付链接
            </div>
            <div
              v-else
              class="sureBtn w-120 h-40 flex items-center justify-center cursor-pointer ml-30"
              @click="submitRfundForm(ruleForm, 1)"
            >
              提交
            </div>
          </template>
        </template>
        <div
          v-if="orderStatus === 1"
          class="sureBtn w-120 h-40 flex items-center justify-center cursor-pointer ml-30"
          @click="revokeApply()"
        >
          撤销申请
        </div>
        <div
          v-if="Number(orderInfo.refundStatus) === 5"
          class="sureBtn w-120 h-40 flex items-center justify-center cursor-pointer ml-30"
          @click="againSubmit()"
        >
          再次提交
        </div>
      </div>

      <el-dialog
        v-if="info.refundType === 2"
        v-model="refundDialogVisible"
        width="506px"
        :modal="false"
        :close-on-click-modal="false"
        class="dialogFollow"
      >
        <el-form
          ref="refundForm"
          label-position="top"
          label-width="80px"
          :model="refundCountForm"
          :rules="refundCountFormRules"
        >
          <el-form-item label="收款人姓名" prop="payeeName" class="mt-16">
            <el-input v-model="refundCountForm.payeeName" maxlength="10" />
          </el-form-item>
          <el-form-item label="收款人账户" prop="proceedsAccount">
            <el-input
              v-model="refundCountForm.proceedsAccount"
              maxlength="40"
            />
          </el-form-item>
          <el-form-item label="收款人开户行" prop="bankOfDeposit">
            <el-input v-model="refundCountForm.bankOfDeposit" maxlength="40" />
          </el-form-item>
        </el-form>
        <div class="btmSure flex justify-end mt-34 mb-20">
          <div
            class="cancelBtn w-120 h-40 flex items-center justify-center cursor-pointer"
            @click="submitRefundCountForm(refundForm, 0)"
          >
            取消
          </div>
          <div
            class="sureBtn ml-30 w-120 h-40 flex items-center justify-center cursor-pointer"
            @click="submitRefundCountForm(refundForm, 1)"
          >
            确认
          </div>
        </div>
      </el-dialog>
    </div>

    <Dialog
      v-model:visible="visiblePurchaseEquipment"
      :width="320"
      title="推送支付链接"
      class="address-box"
    >
      <div class="ml-24 mt-20">确定给患者公众号推送补差价支付链接吗？</div>
      <template #footer>
        <!-- 这里是末尾的元素插槽，比如提交取消按钮-->
        <div class="btn-box purchase-btn">
          <div class="cancel-btn" @click="visiblePurchaseEquipment = false">
            取消
          </div>
          <div class="sure-btn" @click="pushLink">确定</div>
        </div>
      </template>
    </Dialog>
  </el-dialog>
</template>
<script setup lang="ts">
import { timestampToDateTime } from '../../hooks';
import changeTimeIng from '@/assets/imgs/callCenter/change-time.png';
import UploadImages from '@/components/UploadImages/index.vue';
import type { FormInstance } from 'element-plus';
import { debounce } from 'lodash-es';
import Dialog from '@/components/Dialog/index.vue';
import { dialogTip, isOrderEffect, mathOperation } from '../../hooks';
import { companyInfo, refundInformationinfo } from '../type';
import DepositRefund from './DepositRefund.vue';
import {
  refundReasonList,
  createOrderCode,
  refundRules,
  depositRefundRules,
  payObjectList,
} from '../hooks';
import {
  queryOrderRefundApi,
  queryCompanyListApi,
  querySubmitRefundApi,
  queryInitiateWxRefundApi,
  queryProcessInstancesTerminateApi,
  queryOrderByApi,
} from '@/api/managementSituation';
import { createOrderApi } from '@/api/overview';
import useGlobal from '@/store/module/useGlobal';
const useGlobalInfo = useGlobal();

const deviceInfoForm = ref<any>({});

const visiblePurchaseEquipment = ref(false);
const isCountdown = ref(false);
const pushLink = () => {
  submitRfundForm(ruleForm.value, -3);
};
const countdownText = ref('');
const timer = ref();
const getCountdown = (countdown: number) => {
  isCountdown.value = true;
  timer.value = setInterval(function () {
    const minutes = Math.floor(countdown / 60);
    const seconds = countdown % 60;
    countdownText.value = minutes + ':' + (seconds < 10 ? '0' : '') + seconds;
    countdown--;
    if (countdown < 0) {
      isCountdown.value = false;
      countdownText.value = '';
      clearInterval(timer.value);
      emit('close-dialog', true);
    }
  }, 1000);
};

const equipmentList = ref<any>([]);

const props = defineProps({
  objectiveVisibles: {
    type: Boolean,
    default: false,
  },
  refundInfo: {
    type: Object,
    default: () => {},
  },
});
const getShowContent = computed(() => {
  const reasonFlag = equipmentList.value.some(
    (item: { isReturn: any }) => item.isReturn
  );
  const pictureFlag = equipmentList.value.some(
    (item: { isReturn: any }) => !item.isReturn
  );

  return {
    reasonFlag,
    pictureFlag,
  };
});

onMounted(() => {
  getCompanyList();
});

watch(
  () => [props.refundInfo, props.objectiveVisibles],
  () => {
    if (props.refundInfo.orderId && props.objectiveVisibles) getRefundDetails();
  }
);

const orderStatus = ref<string | number>('');
const isEffect = ref(false);
const isDisabled = ref(false);
const orderInfo = ref({
  goal: '',
  orderNo: '',
  payTime: '',
  orderId: '',
  companyName: '',
  sellerName: '',
  refundStatus: '',
  invalidTime: '',
  patientName: '',
  patientCardNo: '',
  productName: '',
  assistantAame: '',
  assistantId: '',
  companyId: '',
  initiatorType: 1,
  groupName: '',
  payType: '',
  sellerId: '',
  joinDate: '',
  createTime: '',
  payPrice: '',
  payObject: 0,
  productType: '',
  status: '',
  patientGender: 0,
  assistantName: '',
  orderType: '',
});

const info = ref<refundInformationinfo>({
  companyId: '',
  refundProcessId: '',
  orderId: '',
  refundDate: '',
  proposeRefundPictureList: [],
  isReturnDevice: '',
  returnReason: '',
  returnReasonDetails: '',
  refundMoney: '',
  refundType: '',
  isInvoicing: '',
  invoicingPictureList: [],
  payObject: '',
  deductDeviceMoney: '',
  orderType: '',
  payPictureList: [],
  returnDeviceExpressNoList: [],
  returnDevicePictureList: [],
  deviceDamageStatus: false,
  remark: '',
});
const hasPayObject = ref('');

const rules = computed(() => {
  return info.value.orderType === 'DEPOSIT' ? depositRefundRules : refundRules;
});

const joinDay = computed(() => {
  return orderInfo.value.joinDate && info.value.refundDate
    ? Math.floor(
        (Number(info.value.refundDate) - Number(orderInfo.value.joinDate)) /
          (24 * 60 * 60 * 1000)
      )
    : '0';
});
const pickerOptions = (time: { getTime: () => number }) => {
  if (time.getTime() < new Date(orderInfo.value.joinDate).getTime()) {
    return true;
  } else if (time.getTime() > new Date().getTime()) {
    return true;
  } else {
    return false;
  }
};

// 分公司名称
const companyList = ref<companyInfo[]>([]);
const getCompanyList = () => {
  queryCompanyListApi().then((res: any) => {
    if (res.data) companyList.value = res.data;
  });
};

const refundCountForm = ref({
  payeeName: '',
  proceedsAccount: '',
  bankOfDeposit: '',
});

//选择退款方式
const refundDialogVisible = ref<boolean>(false);
const chooseRefundType = () => {
  if (info.value.refundType === 2) {
    refundDialogVisible.value = true;
    refundCountForm.value = {
      payeeName: '',
      proceedsAccount: '',
      bankOfDeposit: '',
    };
  }
};

const checkProceedsAccount = (
  rule: any,
  value: any,
  callback: (arg0?: Error | undefined) => void
) => {
  if (!value) {
    return callback(new Error('收款人账户不能为空'));
  } else if (!Number(value)) {
    callback(new Error('请输入数字值'));
  } else {
    callback();
  }
};
const refundCountFormRules = ref({
  payeeName: [{ required: true, message: '请输入退款人姓名', trigger: 'blur' }],
  proceedsAccount: [
    { required: true, validator: checkProceedsAccount, trigger: 'blur' },
  ],
  bankOfDeposit: [
    { required: true, message: '请输入收款人开户行', trigger: 'blur' },
  ],
});
const refundForm = ref<FormInstance>();
const submitRefundCountForm = async (formEl: any, status: any) => {
  if (status) {
    if (!formEl) return;
    await formEl.validate((valid: any) => {
      if (valid) {
        refundDialogVisible.value = false;
      } else {
        return false;
      }
    });
  } else {
    refundCountForm.value = {
      payeeName: '',
      proceedsAccount: '',
      bankOfDeposit: '',
    };
    info.value.refundType = '';
    refundDialogVisible.value = false;
  }
};

// 关闭弹窗
const emit = defineEmits(['close-dialog']);
const close = () => {
  clearInterval(timer.value);
  countdownText.value = '';
  emit('close-dialog', false);
};
//提交订单申请
const submitRefundApply = debounce((status: number) => {
  info.value.deductDeviceMoney =
    Number(info.value.deductDeviceMoney) === 0
      ? info.value.deductDeviceMoney
      : 1;

  const deviceList = ref<any>([]);
  if (orderInfo.value.orderType === 'HARDWARE') {
    deviceList.value = [
      {
        deviceType: props.refundInfo.remarks.deviceType,
        returnDeviceStatus: true,
        deviceSoNo: props.refundInfo.remarks.deviceSoNo,
      },
    ];
  } else {
    const arr = ref<any>([]);
    equipmentList.value.forEach(
      (item: { deviceType: any; isReturn: any; deviceNo: any }) => {
        arr.value.push({
          deviceType: item.deviceType,
          returnDeviceStatus: item.isReturn,
          deviceSoNo: item.deviceNo,
        });
      }
    );
    deviceList.value = arr.value;
  }

  const obj = {
    orderType: orderInfo.value.orderType,
    deviceList: deviceList.value,
    applyId: localStorage.getItem('accountId'),
    applyType: 'DOCTOR',
  };
  const returnDevicePictureList =
    orderInfo.value.orderType !== 'DEPOSIT'
      ? deviceInfoForm.value.returnDevicePictureList
      : info.value.returnDevicePictureList;
  const params = Object.assign(
    {},
    deviceInfoForm.value,
    refundCountForm.value,
    info.value,
    obj
  );
  if (orderInfo.value.orderType !== 'DEPOSIT') {
    params.returnReason = params.returnReason
      ? params.returnReason.join(',')
      : '';
  }
  params.orderId = props.refundInfo.orderId;
  params.returnDevicePictureList = returnDevicePictureList;
  params['status'] = status == -3 ? 0 : status;
  const loading = ElLoading.service({
    fullscreen: true, // 锁定整个屏幕
    text: `${status ? '提交中...' : '保存中'}`,
    spinner: 'el-icon-loading',
  });

  querySubmitRefundApi(params)
    .then(res => {
      loading.close();
      if (status === -3) {
        createOrder(res.data);
      } else if (status === 1) {
        dialogTip('退款发起成功!', 'success');
        getRefundDetails();
        emit('close-dialog', true);
      } else {
        dialogTip('保存成功!', 'success');
        emit('close-dialog', true);
      }
    })
    .catch(err => {
      loading.close();
      if (err.code == '60121') {
        dialogTip(
          '您的注册手机号与钉钉手机号不一致，请联系IT部门进行修改!',
          'error'
        );
      } else {
        if (status === 0) {
          dialogTip(`保存失败!${err.msg}`, 'error');
        } else {
          dialogTip(`退款发起失败!${err.msg}`, 'error');
        }
      }
    });
}, 500);

const createOrder = async (refundProcessId: any) => {
  const obj = {
    userId: useGlobalInfo.userId,
    patientId: useGlobalInfo.userId,
    wxPayType: 'WX_JSAPI',
    orderType: 'REPLACE',
    productId: props.refundInfo.productId,
    creatorType: 'DOCTOR',
    orderReplaceRemark: {
      refundType: 'PACKAGE',
      refundProcessId,
    },
    creatorId: localStorage.getItem('accountId'),
  };
  createOrderApi(obj).then((res: any) => {
    const { code } = res;
    if (code == 'E000000') {
      dialogTip('推送成功！', 'success');
      visiblePurchaseEquipment.value = false;
      emit('close-dialog', true);
      getCountdown(900);
    } else {
      const errMsg = createOrderCode(code);
      dialogTip(errMsg, 'error');
    }
  });
};
//撤销订单申请
const revokeApply = debounce(() => {
  queryProcessInstancesTerminateApi({
    processInstanceId: dingTalkProcessId.value,
  })
    .then(() => {
      dialogTip('撤销申请成功!', 'success');
      emit('close-dialog', true);
    })
    .catch(err => {
      if (err.code == '400') {
        dialogTip('撤销申请失败! 15秒内不得撤销申请!', 'error');
      } else {
        dialogTip(`撤销申请失败!${err.msg}`, 'error');
      }
    });
}, 500);

//再次提交
const againSubmit = debounce(() => {
  queryInitiateWxRefundApi({
    orderId: props.refundInfo.orderId,
    orderType: props.refundInfo.orderType,
  }).then(res => {
    if (res.code == 'E000000') {
      dialogTip('提交成功!', 'success');
      emit('close-dialog', true);
    }
  });
}, 500);

//获取退费订单详情
const dingTalkProcessId = ref('');
const getRefundDetails = () => {
  queryOrderRefundApi({
    orderId: props.refundInfo.orderId,
    orderType: props.refundInfo.orderType,
  }).then((res: any) => {
    orderInfo.value = res.data.orderInfo;
    info.value.payObject = res.data.orderInfo.payObject;
    info.value.companyId = res.data.orderInfo.companyId;
    isEffect.value = isOrderEffect(orderInfo.value.joinDate);

    equipmentList.value = res.data.refundDeviceInfo;

    if (res.data.refundInfo?.refundMoney < 0) {
      queryOrderByApi({
        refundProcessId: res.data.refundInfo.refundProcessId,
        orderType: res.data.orderInfo.orderType,
      }).then((res: any) => {
        if (res) {
          // 如果支付链接已经推送且没有超过15分钟执行
          // 获取当前时间的时间戳
          const currentDate: any = new Date();
          const currentTimeStamp = Date.parse(currentDate);
          const timeStamp = res.payInvalidTime;
          if (timeStamp - currentTimeStamp > 0) {
            const seconds = (timeStamp - currentTimeStamp) / 1000;
            getCountdown(seconds);
          }
        }
      });
    }

    info.value.payObject = res.data.orderInfo.payObject;
    hasPayObject.value = res.data.orderInfo.payObject;
    res.data.refundInfo = res.data.refundInfo || {};
    const {
      refundDate,
      proposeRefundPictureList,
      deductDeviceMoney,
      isReturnDevice,
      returnReason,
      returnReasonDetails,
      refundMoney,
      refundType,
      payeeName,
      bankOfDeposit,
      isInvoicing,
      refundProcessId,
      invoicingPictureList,
      returnDevicePictureList,
      noReturnReason,
      status,
      proceedsAccount,
      payPictureList,
      returnDeviceExpressNoList,
      remark,
      deviceDamageStatus,
    } = res.data.refundInfo;
    info.value.refundDate = refundDate;
    info.value.refundMoney = refundMoney;
    if (Number(info.value.refundMoney) < 0) info.value.refundType = 3;
    info.value.payPictureList = payPictureList;
    info.value.returnDeviceExpressNoList = returnDeviceExpressNoList;
    info.value.remark = remark;
    if (orderInfo.value.orderType === 'DEPOSIT')
      info.value.returnDevicePictureList = returnDevicePictureList;
    info.value.deductDeviceMoney = !res.data.refundDeviceInfo.length
      ? 0
      : deductDeviceMoney === 0
        ? deductDeviceMoney
        : 1;
    info.value.refundProcessId = refundProcessId;
    info.value.proposeRefundPictureList = proposeRefundPictureList
      ? proposeRefundPictureList
      : [];
    info.value.isReturnDevice = isReturnDevice;
    deviceInfoForm.value.returnDevicePictureList = returnDevicePictureList
      ? returnDevicePictureList
      : [];
    deviceInfoForm.value.noReturnReason = noReturnReason;
    if (orderInfo.value.orderType === 'DEPOSIT') {
      info.value.returnReason = returnReason ? Number(returnReason) : '';
    } else {
      info.value.returnReason = returnReason
        ? returnReason.split(',').map((item: string) => parseInt(item))
        : '';
    }

    info.value.returnReasonDetails = returnReasonDetails;
    info.value.refundType = refundType;
    refundCountForm.value.payeeName = payeeName;
    refundCountForm.value.proceedsAccount = proceedsAccount;
    refundCountForm.value.bankOfDeposit = bankOfDeposit;
    info.value.isInvoicing = isInvoicing;
    info.value.deviceDamageStatus = deviceDamageStatus;
    info.value.invoicingPictureList = invoicingPictureList
      ? invoicingPictureList
      : [];
    orderStatus.value = status;
    isDisabled.value =
      status == 1 ||
      status == 4 ||
      status == 100 ||
      Number(orderInfo.value.refundStatus) === 5;
    dingTalkProcessId.value = res.data.refundInfo.dingTalkProcessId;
  });
};
let ruleForm = ref<FormInstance>();
let submitRfundForm = async (
  formName: FormInstance | undefined,
  type: number
) => {
  if (!formName) return;
  await formName.validate((valid: any) => {
    if (valid) {
      const reasonFlag = equipmentList.value.some(
        (item: { isReturn: any }) => item.isReturn
      );
      const pictureFlag = equipmentList.value.some(
        (item: { isReturn: any }) => !item.isReturn
      );
      if (reasonFlag && !deviceInfoForm.value.returnDevicePictureList.length) {
        dialogTip('请上传设备物流照片！');
        return;
      }
      if (pictureFlag && !deviceInfoForm.value.noReturnReason) {
        dialogTip('请填写不退回原因！');
        return;
      } else if (
        (!info.value.payPictureList ||
          info.value.payPictureList?.length === 0) &&
        orderInfo.value.orderType === 'DEPOSIT'
      ) {
        dialogTip('请上传付款截图/押金条!');
        return;
      } else if (
        (!info.value.returnDevicePictureList ||
          info.value.returnDevicePictureList?.length === 0) &&
        orderInfo.value.orderType === 'DEPOSIT'
      ) {
        dialogTip('请上传退回设备实物图');
        return;
      } else if (
        (!info.value.returnDeviceExpressNoList ||
          info.value.returnDeviceExpressNoList?.length === 0) &&
        orderInfo.value.orderType === 'DEPOSIT'
      ) {
        dialogTip('请上传退回设备的快递单号!');
        return;
      } else {
        submitRefundApply(type);
      }
    } else {
      return;
    }
  });
};
// 推送支付链接
const pushPaymentLink = async () => {
  visiblePurchaseEquipment.value = true;
};

const changeEquipmentDamaged = (value: boolean) => {
  countMoney(value);
};

const countMoney = (flag?: boolean) => {
  let deductDeviceMoney = 0;

  // 押金退款
  if (orderInfo.value.orderType === 'DEPOSIT') {
    info.value.refundMoney = flag ? 0 : orderInfo.value.payPrice;
  } else {
    if (
      orderInfo.value.orderType != 'HARDWARE' &&
      Number(info.value.deductDeviceMoney) === 1
    ) {
      equipmentList.value.forEach(
        (item: { isReturn: any; deviceType: string }) => {
          if (!item.isReturn) {
            const price =
              item.deviceType == 'WS'
                ? 100
                : item.deviceType == 'WATCH'
                  ? 1200
                  : 600;
            deductDeviceMoney += price;
          }
        }
      );
    }

    // 免费会员
    if (Number(orderInfo.value.payType) == 4) {
      info.value.refundMoney =
        Number(orderInfo.value.payPrice) - deductDeviceMoney;
    } else {
      const joinDays =
        orderInfo.value.joinDate && info.value.refundDate
          ? Math.floor(
              (Number(info.value.refundDate) -
                Number(orderInfo.value.joinDate)) /
                (24 * 60 * 60 * 1000)
            )
          : '';

      const usedMoth = Math.floor(Number(joinDays) / 30);

      if (Number(joinDays) < 31) {
        info.value.refundMoney = mathOperation(
          [deductDeviceMoney],
          2,
          Number(orderInfo.value.payPrice)
        ).toFixed(2);
        if (Number(info.value.refundMoney) < 0) info.value.refundType = 3;
        return;
      }
      const priceTem1 = mathOperation(
        [orderInfo.value.payPrice, 12 - usedMoth],
        3
      );
      const priceTem2 = mathOperation([12], 4, priceTem1).toFixed(2);

      info.value.refundMoney = mathOperation(
        [deductDeviceMoney],
        2,
        Number(priceTem2)
      ).toFixed(2);
    }
  }

  if (Number(info.value.refundMoney) < 0) info.value.refundType = 3;
};
</script>
<style scoped lang="less">
.refund-wrapper {
  background-color: #ffffff;
  margin: 0 auto;
  height: 600px;
  text-align: left;
  box-sizing: border-box;
  padding: 28px 16px;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }
  &::-webkit-scrollbar-track {
    background: rgb(239, 239, 239);
    border-radius: 2px;
  }
  &::-webkit-scrollbar-thumb {
    background: #bfbfbf;
    border-radius: 0px;
  }
  &::-webkit-scrollbar-corner {
    background: #179a16;
  }

  .info-title {
    position: relative;
    border-left: 2px solid #0a73e4;
    box-sizing: border-box;
  }
  .top-head {
    span:first-child {
      font-size: 20px;
      font-weight: bold;
      color: #323233;
    }
  }
  .baseInfo {
    .info-content {
      width: 100%;
      box-sizing: border-box;
      padding: 0 14px;
      border-radius: 4px;
      background: #f7f8fa;
      padding-top: 16px;
      padding-bottom: 8px;
      .info-item {
        display: flex;
        font-size: 14px;
        width: 33%;
        margin-bottom: 8px;
        div:last-child {
          align-self: flex-end;
          color: #3a4762;
        }

        div:first-child {
          color: #7a8599;
          white-space: nowrap;
        }
      }
      .info-item:nth-child(3n + 1) {
        width: 25%;
      }
      .info-item:nth-child(3n + 2) {
        width: 42%;
      }
    }

    :deep(.baseInfo-box) {
      margin-top: 24px !important;
      .date-box {
        position: relative;
        .datepicker {
          .el-input__prefix {
            display: none;
          }
        }
        .change-time-icon {
          position: absolute;
          top: 8px;
          right: 14px;
        }
      }

      .el-form-item {
        color: #323233;

        .el-form-item__label {
          font-size: 14px;
          color: #323233;
          width: 110px;
        }

        .el-form-item__content {
          .el-input__inner {
            font-size: 12px;
            &::-webkit-input-placeholder {
              color: #bfbfbf;
              font-size: 12px;
            }
          }
          .el-icon-arrow-up:before {
            content: '';
          }
        }
      }
    }
  }
  .refundInfo {
    .content-msg {
      background: #f7f8fa;
      width: 712px;
      padding: 22px 16px;
      .item-number {
        color: #3a4762;
        border-bottom: 1px solid #dcdee0;
        padding-bottom: 16px;
      }
      .equipment-list {
        .item-left {
          color: #3a4762;
        }
      }
      .item-box {
        .title {
          color: #3a4762;
          span {
            color: #939cae;
          }
        }
      }
      .el-radio.el-radio--large {
        height: auto;
      }
    }
    .refund-content {
      .item {
        margin-top: 20px;
        padding-bottom: 24px;
        border-bottom: 1px solid #e0e0e0;
        :deep(.name) {
          font-size: 14px;
          color: #303133;
          .required-icon {
            color: red;
            opacity: 0.7;
          }
          .index-num {
            line-height: 40px;
            vertical-align: middle;
            color: #303133;

            .required-icon {
              color: red;
              opacity: 0.7;
            }
          }
          .fixed-text {
            color: #f56c6c;
          }
          .el-form-item {
            margin-bottom: 0;
            margin-left: 10px;

            .el-radio__inner {
              width: 18px;
              height: 18px;
              // 去掉默认的中心填充
              &::after {
                display: none;
                transition: none;
              }
            }
            .el-radio__input.is-checked {
              .el-radio__inner {
                padding: 4px;
                background-color: #409eff;
                background-clip: content-box;
              }
            }
            .el-radio__label {
              color: #323233;
            }
            .el-form-item__content {
              .el-form-item__error {
                width: 300px;
              }
              .el-input__inner {
                font-size: 12px;
                width: 230px;
                &::-webkit-input-placeholder {
                  color: #bfbfbf;
                  font-size: 12px;
                }
              }
              .el-icon-arrow-up:before {
                content: '';
              }
            }
          }
          span {
            color: #999999;
          }
        }

        .content {
          margin-left: 24px;
          margin-top: 16px;
          display: flex;
        }

        :deep(.blood-content) {
          background: #fafafa;
          border-radius: 4px;
          padding: 12px 14px;
          .required-icon {
            color: red;
            opacity: 0.7;
          }
          .el-form-item__label {
            font-size: 14px;
            color: #323233;
          }
          .el-input__inner {
            color: #303133;
          }
          .blood-item {
            vertical-align: top;
            .imgbox {
              display: inline-block;
              vertical-align: top;
              border-radius: 8px;
              position: relative;
              .el-icon-error {
                position: absolute;
                border-radius: 50%;
                top: -8px;
                right: -8px;
                background-color: #ffffff;
              }
            }
            img {
              border-radius: 8px;
              object-fit: cover;
            }
          }
        }

        :deep(.input-content) {
          .el-form-item {
            margin-bottom: 0;
            display: flex;
            width: 100% !important;
            .el-form-item__content {
              width: 100%;
              .el-textarea {
                .el-textarea__inner {
                  background-color: #fafafa;
                  border: none;
                  height: 93px;
                  font-size: 14px;
                  color: #303133;
                }
              }
            }
          }
        }
      }
    }
  }
}

:deep(.dialogFollow) {
  .el-dialog__headerbtn {
    top: 5px;
    right: 10px;
  }
  .el-dialog__body {
    padding: 16px;
    border-radius: 10px !important;
  }
  .el-input__inner {
    color: #303133;
  }
  .header-title {
    font-weight: bold;
    font-size: 14px;
    color: #15233f;
  }
  .equipment-list {
    .item-left {
      color: #3a4762;
    }
  }
  .item-box {
    .title {
      color: #3a4762;
      span {
        color: #939cae;
      }
    }
  }
}

.btmSure {
  .cancelBtn {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    color: #303133;
  }
  .sureBtn {
    background: #4facff;
    border-radius: 4px;
    color: #ffffff;
  }
}
</style>
<style lang="less">
.refund-dialog {
  .el-dialog__header,
  .el-dialog__body {
    padding: 0;
  }
  .el-dialog__headerbtn {
    top: -3px;
  }
}
</style>
