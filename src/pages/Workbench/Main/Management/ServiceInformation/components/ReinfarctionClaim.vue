<template>
  <div v-if="showClaim" class="mt-8 py-16 bg-[#f7f8fa]">
    <div class="text-sm flex items-center justify-between px-16">
      <h3 class="font-bold *:font-normal *:ml-12">再梗理赔</h3>
      <el-button type="text" class="text-sm" @click="handleOperate">
        去申请
        <el-icon>
          <ArrowRight />
        </el-icon>
      </el-button>
    </div>

    <ul class="flex flex-col px-16">
      <li
        v-for="(item, index) in claimList"
        :key="index"
        class="flex items-center justify-between py-8 px-6 rounded-[4px] cursor-pointer text-base hover:bg-white"
        @click="handleViewDetail(item)"
      >
        <span class="text-[#708293]">
          {{ timeFormat(item.claimTime) }}
        </span>
        <span class="text-[#3a4762]">
          {{ getReinfarctionClaimStatus(item.claimStatus)?.label }}
        </span>
      </li>
    </ul>
  </div>

  <!-- 申请理赔 -->
  <ClaimDialog
    :visible="visible"
    :loading="dialogLoading"
    @update:visible="visible = $event"
    @submit="handleSubmit"
  />

  <!-- 使用ClaimDrawer组件显示详情 -->
  <ClaimDrawer
    v-loading="true"
    :visible="drawerVisible"
    :status="currentClaimStatus"
    :claim-info="currentClaimInfo"
    @update:visible="drawerVisible = $event"
  />
</template>

<script setup lang="ts">
import { ArrowRight } from '@element-plus/icons-vue';
import ClaimDialog from './ClaimDialog.vue';
import ClaimDrawer from './ClaimDrawer.vue';
import { useGlobal } from '@/store';
import dayjs from 'dayjs';
import { getReinfarctionClaimStatus } from '../hooks';
import {
  getReinfarctionClaimList,
  checkReinfarctionClaimStatus,
  queryPatientProduct,
  applyReinfarctionClaim,
} from '@/api/recurrentMIClaim';
import type { IApiMiClaimList } from '@/interface/type';
import { dialogTip } from '../../hooks';

const globalStore = useGlobal();

const visible = ref(false);

const claimList = ref<IApiMiClaimList>([]);
const getList = async () => {
  claimList.value = [];
  const res = await getReinfarctionClaimList({ patientId: globalStore.userId });
  if (res && res.length) {
    claimList.value = res;
  }
};
getList();

const showClaim = ref(false);
// 获取用户服务包
const getUserProduct = async () => {
  const res = await queryPatientProduct({ patientId: globalStore.userId });
  console.log(res);
  if (res) {
    showClaim.value = res;
  }
};
getUserProduct();

const timeFormat = (date: number | undefined) =>
  date ? dayjs(date).format('YYYY-MM-DD') : '--';
// 状态管理
// pending, submitted, rejected, paid

const handleOperate = async () => {
  const res = await checkReinfarctionClaimStatus({
    patientId: globalStore.userId,
  });
  if (res) {
    visible.value = true;
  }
};

const dialogLoading = ref(false);
// 处理表单提交
const handleSubmit = async (formData: any) => {
  dialogLoading.value = true;
  const files = formData.files;

  const applyParams = {
    ...formData,
    files,
    patientId: globalStore.userId,
  };
  delete applyParams.admissionFiles;
  delete applyParams.contactPerson;
  try {
    const applyRes = await applyReinfarctionClaim(applyParams);
    if (applyRes) {
      visible.value = false;
      getList();
      dialogTip('操作成功', 'success');
    }
  } catch (error) {
    dialogTip('操作失败', 'error');
  } finally {
    dialogLoading.value = false;
  }
};

const drawerVisible = ref(false);
const currentClaimStatus = ref('pending');
const currentClaimInfo = ref<any>({});

const handleViewDetail = (item: IApiMiClaimList[0]) => {
  currentClaimInfo.value = item;
  drawerVisible.value = true;
};
</script>

<style scoped lang="less">
:deep(.el-steps-control) {
  .el-step {
    .el-step__title {
      font-size: 14px;
      padding-bottom: 8px;
      font-weight: 600;
      color: #b8becc;
      line-height: 1;
      &.is-process,
      &.is-finish {
        color: #3a4762;
      }
    }
    .el-step__description {
      font-size: 14px;
      color: #b8becc;
      &.is-process,
      &.is-finish {
        color: #7a8599;
      }
    }
    .el-step__icon {
      width: 12px;
      height: 12px;
      margin-left: 5px;
      border: 2px solid #b8becc;
      & * {
        display: none;
      }
    }
    .el-step__line {
      background: #e1e5ed;
    }
    .is-finish,
    .is-process {
      .el-step__icon {
        border-color: #2e6be6;
      }
    }
  }
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
  width: 240px;
  height: 32px;
  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}
</style>
