<template>
  <div class="manage-team-record-list-item flex p-12">
    <img :src="teamRecordImg" alt="" class="w-16 h-16" />
    <div class="item-panel ml-7">
      <div class="panel-time flex">
        {{ item.startTime }}
        <span class="ml-4" :class="{ 'mr-4': item.endTime }">至</span>
        {{ item.endTime || '今' }}
      </div>
      <div class="panel-msg flex flex-wrap">
        <div
          v-for="(value, key) in getPanelMsg"
          :key="key"
          class="flex mt-12"
          :class="{ 'item-msg': key !== 'remark' }"
        >
          <div class="item-title w-84">
            {{ ManageTeamRecordTItleType[key] }}：
          </div>
          <div class="max-w-[320px]">
            <HrtOverflowTooltip :content="value" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { IManageTeamRecord } from '../type.d';
import teamRecordImg from '@/assets/imgs/managementTeamRecord/team-record.png';

enum ManageTeamRecordTItleType {
  doctor = '医生',
  healthManager = '健康管理师',
  rehabilitation = '运动康复师',
  type = '生成方式',
  remark = '说明',
}

const props = defineProps<{
  item: IManageTeamRecord;
}>();
const getPanelMsg = computed(() => {
  const { doctor, healthManager, rehabilitation, type, remark } = props.item;
  return { doctor, healthManager, rehabilitation, type, remark };
});
</script>
<style lang="less" scoped>
.manage-team-record-list-item {
  width: 100%;
  background: #f7f8fa;
  border-radius: 4px;
  font-size: 14px;
  color: #3a4762;
}
.item-panel {
  margin-top: -3px;
  .panel-time {
    font-weight: bold;
  }
  .panel-msg {
    width: 100%;
    .item-msg {
      width: 50%;
    }
    .item-title {
      white-space: nowrap;
      color: #7a8599;
    }
  }
}
</style>
