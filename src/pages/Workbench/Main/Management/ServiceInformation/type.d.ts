export interface IrefundReasonInfo {
  label: string;
  value: number;
}

/* 分公司名称 */
export interface companyInfo {
  companyName: string;
  companyId: string;
}

/* 退费信息 */
export interface refundInformationinfo {
  companyId: string;
  refundProcessId: string;
  orderId: string;
  refundDate: string;
  proposeRefundPictureList: any;
  isReturnDevice: string | number;
  returnReason: string | number;
  returnReasonDetails: string;
  refundMoney: string | number;
  refundType: string | number;
  isInvoicing: string | number;
  invoicingPictureList: any;
  payObject: string | number;
  deductDeviceMoney: string | number;
  orderType: string;
  payPictureList: [];
  returnDeviceExpressNoList: [];
  returnDevicePictureList: [];
  deviceDamageStatus: boolean;
  remark: string;
}

/* 管理团队记录 */
export interface IManageTeamRecord {
  teamRecordId: number;
  startTime: string;
  endTime: string;
  doctor: string;
  healthManager: string;
  rehabilitation: string;
  type: string;
  remark: string;
}
