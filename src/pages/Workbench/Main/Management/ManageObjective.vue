<template>
  <CardWrapper title="管理目标">
    <div class="flex items-center flex-wrap justify-between">
      <div
        v-for="(item, index) in objectiveList"
        :key="index"
        class="item-objective mt-2xs flex items-center justify-between"
      >
        <div class="left flex-c">
          <img :src="item.path" alt="" class="w-16 h-16 mr-3xs" />
          <span>{{ item.title }}</span>
        </div>
        <div class="right flex-c">
          <span>{{ item.value }}</span>
          <!--          <img
            src="@/assets/imgs/manageObj/edit-img.png"
            alt="icon"
            class="w-16 h-16 ml-16 cursor-pointer"
            @click="edit(item)"
          />-->
        </div>
      </div>
    </div>
  </CardWrapper>
  <!-- 管理目标值修改 -->
  <Dialog v-model:visible="objectiveVisable" :width="360" :title="editTitle">
    <div class="edit-box">
      <div class="flex items-center justify-between">
        <InputNumber v-model="minValue" :min="0.1" placeholder="请输入" />
        <div v-if="currentId < 3" class="hr"></div>
        <InputNumber
          v-if="currentId < 3"
          v-model="maxValue"
          :min="0.1"
          placeholder="请输入"
        />
      </div>
      `
      <div class="btns flex items-center justify-end">
        <div class="save" @click="save">确定</div>
        <div class="cancel" @click="cancel">取消</div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import Dialog from '@/components/Dialog/index.vue';
import CardWrapper from '@/components/CardWrapper/index.vue';
import pressureImg from '@/assets/imgs/manageObj/pressure-img.png';
import heartRate from '@/assets/imgs/manageObj/heartRate.png';
import bloodGlucose from '@/assets/imgs/manageObj/bloodGlucose.png';
import bloodLipid from '@/assets/imgs/manageObj/bloodLipid.png';
import useGlobal from '@/store/module/useGlobal';
import { queryTargetValueApi } from '@/api/managementSituation';
import InputNumber from '@/components/InputNumber/index.vue';

const useGlobalInfo = useGlobal();

// 管理目标
const objectiveList = ref([
  { key: 'bloodPressure', title: '血压', value: '--', path: pressureImg },
  { key: 'hearRate', title: '心率', value: '--', path: heartRate },
  {
    key: 'bloodSugarEmpty',
    title: '空腹血糖',
    value: '--',
    path: bloodGlucose,
  },
  {
    key: 'bloodSugarNotEmpty',
    title: '非空腹血糖',
    value: '--',
    path: bloodGlucose,
  },
  { key: 'bloodFat', title: '血脂', value: '--', path: bloodLipid },
]);
const currentId = ref(0);
const minValue = ref('');
const maxValue = ref('');
const editTitle = ref('');
const objectiveVisable = ref(false);

const edit = (item: any) => {
  editTitle.value = item.title;
  currentId.value = item.id;
  minValue.value = item.mixNum;
  maxValue.value = item.maxNum;
  objectiveVisable.value = true;
};

const save = () => {
  if (
    (currentId.value < 3 && (!maxValue.value || !minValue.value)) ||
    (currentId.value > 3 && !minValue.value)
  ) {
    ElMessage({
      message: '请填写完整',
      type: 'warning',
    });
  } else {
    objectiveVisable.value = false;
  }
};
const cancel = () => {
  objectiveVisable.value = false;
};

const getObjectiveList = async () => {
  const res = await queryTargetValueApi({ patientId: useGlobalInfo.userId });
  objectiveList.value = objectiveList.value.map(item => {
    return {
      ...item,
      value: res[item.key] || '--',
    };
  });
};

watch(
  () => useGlobalInfo.userId,
  () => {
    getObjectiveList();
  },
  { immediate: true }
);
</script>

<style scoped lang="less">
.item-objective {
  width: 48%;
  background: #f7f8fa;
  border-radius: 2px;
  padding: 6px 12px;
  font-size: 14px;
  .left {
    color: #111111;
  }
  .right {
    color: #101b25;
  }
}
.edit-box {
  padding: 22px 40px 6px;
  .hr {
    background: #dcdee0;
    width: 20px;
    height: 1px;
    margin: 0 2px;
  }
  .btns {
    margin-top: 16px;
    font-size: 14px;
    .save,
    .cancel {
      border-radius: 2px;
      width: 76px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      cursor: pointer;
      margin: 0 8px;
    }
    .save {
      background: #0a73e4;
      color: #ffffff;
    }
    .cancel {
      background: #ffffff;
      border: 1px solid #dcdfe6;
      color: #606266;
    }
  }
}
</style>
