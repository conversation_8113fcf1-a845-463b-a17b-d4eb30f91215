<template>
  <Dialog v-model:visible="retransmissionVisibles" :width="400" title="转发">
    <div class="retransmission-box">
      <el-checkbox-group v-model="checkList">
        <el-checkbox
          v-for="item in retransmissionList"
          :key="item.teamNumber"
          :label="getChatType(item.teamType)"
          :value="getChatType(item.teamType)"
        />
      </el-checkbox-group>
    </div>
    <div class="btns flex items-center mt-16 justify-end">
      <div
        class="save w-76 h-32 flex items-center justify-center cursor-pointer"
        @click="save"
      >
        确认
      </div>
      <div
        class="cancel w-78 h-34 flex items-center justify-center ml-8 cursor-pointer mr-24"
        @click="cancel"
      >
        取消
      </div>
    </div>
  </Dialog>
</template>
<script setup lang="ts">
import Dialog from '@/components/Dialog/index.vue';
import store from '@/store';
const imStore = store.useIM();
import { dialogTip } from '../../hooks';
import { queryChatApi } from '@/api/managementSituation';

const props = defineProps({
  retransmissionInfo: {
    type: Object,
    required: true,
  },
});

interface info {
  teamNumber: number;
  teamType: number;
}
let reportId = ref('');
let retransmissionVisibles = ref(false);
let retransmissionList = ref<info[]>([]);
let checkList = ref([]);
let save = () => {
  if (!checkList.value.length) return dialogTip('请选择！');
  checkList.value.forEach(item => {
    retransmissionList.value.forEach(ite => {
      if (getChatType(ite.teamType) === item) {
        imStore.sendCustomMsg({
          to: ite.teamNumber + '',
          content: {
            name: props.retransmissionInfo.reportName || '阶段性总结报告',
            id: reportId.value,
            type: 8,
          },
        });
      }
    });
  });

  cancel();
};
let cancel = () => {
  checkList.value = [];
  retransmissionVisibles.value = false;
};

// 查询可转发的群聊
let getChatList = async (patientId: any) => {
  await queryChatApi({ patientId }).then((res: any) => {
    retransmissionList.value = res.data.teamList;
  });
};
// 获取群聊类型
let getChatType = (val: number) => {
  let str =
    val === 1
      ? '专家'
      : val === 2
        ? '患者'
        : val === 3
          ? '患者-专家群聊'
          : '团队内';

  return str;
};

watch(
  () => props.retransmissionInfo,
  val => {
    if (JSON.stringify(val) !== '{}') {
      getChatList(val.patientId);
      reportId.value = val.reportId;
      retransmissionVisibles.value = true;
    }
  },
  {
    deep: true,
  }
);
</script>
<style scoped lang="less">
.retransmission-box {
  padding: 16px 40px;
  box-sizing: border-box;
  border-bottom: 2px solid #e9e8eb;
}
.btns {
  .save {
    background: #0a73e4;
    border-radius: 2px;
    font-size: 14px;
    color: #ffffff;
  }
  .cancel {
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    color: #606266;
  }
}
</style>
