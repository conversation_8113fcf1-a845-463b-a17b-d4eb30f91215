<template>
  <div class="pre-dialog">
    <HrtDialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      destroy-on-close
      :modal="false"
      draggable
      size="middle"
      width="346px"
    >
      <template #title>
        <div class="header">阶段性总结报告预览</div>
      </template>
      <div class="main">
        <div class="content">
          <div class="content-title font-bold">报告详情</div>
          <el-scrollbar height="552px">
            <BaseInfo :info="baseInfo" />
            <template v-for="item in paragraphComponent" :key="item.key">
              <component
                :is="item.component"
                :info="item.info as any"
                :extra-info="extraCommonInfo"
              />
            </template>
            <TextSummary :info="reportInfo" />
          </el-scrollbar>
        </div>
      </div>
    </HrtDialog>
  </div>
</template>
<script setup lang="ts">
import { cloneDeep } from 'lodash-es';
import BaseInfo from './components/BaseInfo.vue';
import DrugInfo from './components/DrugInfo.vue';
import ImageArchive from './components/ImageArchive.vue';
import IndexAnalysis from './components/IndexAnalysis.vue';
import InspectionConclusion from './components/InspectionConclusion.vue';
import { HrtDialog } from '@hrt/components';
import SurgeryInfo from './components/SurgeryInfo.vue';
import TextSummary from './components/TextSummary.vue';

const props = defineProps({
  reportInfo: {
    type: Object,
    default: () => ({}),
  },
  baseInfo: {
    type: Object,
    default: () => ({
      operationTime: '--',
      patientName: '--',
      age: '--',
      gender: 0,
      theme: '--',
    }),
  },
});

const dialogVisible = defineModel('preVisible', {
  required: true,
  type: Boolean,
});

const extraCommonInfo = computed(() => {
  const { startTime, endTime, patientId } = props.reportInfo;
  return { startTime, endTime, patientId };
});

const paragraphList = [
  { key: 'patientIndex', component: markRaw(IndexAnalysis), info: {} },
  { key: 'ecgData', component: markRaw(InspectionConclusion), info: {} },
  { key: 'operation', component: markRaw(SurgeryInfo), info: {} },
  { key: 'medication', component: markRaw(DrugInfo), info: {} },
  { key: 'pictureList', component: markRaw(ImageArchive), info: {} },
];

const paragraphComponent = computed(() => {
  const { reportInfo } = props;
  return cloneDeep(paragraphList).filter(item => {
    const info = reportInfo[item.key];
    if (info?.checked) {
      item.info = info.value;
      return true;
    }
    return false;
  });
});
</script>
<style scoped lang="less">
.pre-dialog {
  :deep(.el-dialog) {
    padding: 0;
    border-radius: 12px;
    overflow: hidden;
  }
  :deep(.el-dialog__header) {
    padding: 0;
    .el-icon.el-dialog__close {
      color: #fff;
    }
  }
  .header {
    width: 100%;
    background: #3a4762;
    height: 48px;
    text-align: center;
    line-height: 48px;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
  }
}
.main {
  color: #333;
  padding: 20px 8px;
  border-radius: 0 0 12px 12px;
  background-color: #fff;

  .content {
    padding: 0 28px 28px;
    background-image: url('@/assets/imgs/iphone-bg.webp');
    background-size: 100% 100%;
    background-repeat: no-repeat;

    &-title {
      text-align: center;
      padding: 46px 0 8px;
      box-shadow: 0 4px 8px 0 rgba(155, 170, 194, 0.09);
    }

    :deep(.common-sty) {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #333;
    }
  }
}
</style>
