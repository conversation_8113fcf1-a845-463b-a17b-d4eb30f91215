<template>
  <div class="pt-xs">
    <div class="head pb-2xs">
      <div class="font-bold">
        {{ info.theme || '' }}
      </div>
    </div>
    <div class="person-info mt-3xs">
      <div>{{ info.patientName }}</div>
      <el-divider direction="vertical" />
      <div>
        {{ info.gender === 1 ? '男' : info.gender === 2 ? '女' : '--' }}
      </div>
      <el-divider direction="vertical" />
      <div>{{ info.age || '--' }}岁</div>
      <el-divider direction="vertical" />
      <div>手术时间：{{ info.operationTime }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
defineProps({
  info: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
});

// 患者信息
let baseMsg = ref();
</script>

<style scoped lang="less">
.head {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  border-bottom: 4px solid #333;
}
.person-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding-top: 16px;
  border-top: 1px solid #333;
}
.diagnostic-content {
  text-align: left;
  text-indent: 28px;
}
</style>
