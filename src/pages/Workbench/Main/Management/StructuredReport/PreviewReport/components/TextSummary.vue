<template>
  <div>
    <div v-if="info.diagnose?.checked" class="common-sty">
      <div class="font-bold">出院诊断</div>
      <div class="text-box">
        {{ info.diagnose?.value || '--' }}
      </div>
    </div>
    <div v-if="info.reviewCon?.checked" class="common-sty">
      <div class="font-bold">本次复查结论</div>
      <div class="text-box">
        {{ info.reviewCon?.value || '--' }}
      </div>
    </div>
    <div v-if="info.suggest?.checked" class="common-sty">
      <div class="font-bold">本次医生复查建议</div>
      <div class="text-box">
        {{ info.suggest?.value || '--' }}
      </div>
    </div>
    <div v-if="info.issue?.checked" class="common-sty">
      <div class="font-bold">需要专家确认的问题</div>
      <div class="text-box">
        {{ info.issue?.value || '--' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';

defineProps({
  info: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
});
</script>
<style scoped>
.text-box {
  padding-top: 8px;
  text-align: left;
  text-indent: 28px;
}
</style>
