<template>
  <div class="common-sty">
    <div class="font-bold">指标分析</div>
    <div v-if="info?.length" class="content">
      <el-scrollbar>
        <div class="flex mt-2xs">
          <div
            v-for="(item, index) in info"
            :key="index"
            class="cursor-pointer mr-lg pb-3xs item-change"
            :class="{ active: index === active }"
            @click="changeIndexChecked(item, index)"
          >
            {{ item.indexName }}
          </div>
        </div>
      </el-scrollbar>
      <Echarts :index-info="chartData" />
    </div>
    <div v-else class="text-center py-3xs">暂无数据</div>
  </div>
</template>

<script setup lang="ts">
import { queryStructuredDetailApi } from '@/api/managementSituation';
import { timestampToDate } from '../../../hooks';
import Echarts from '../../AddReport/components/Echarts.vue';
import { PropType } from 'vue';
const props = defineProps({
  info: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  extraInfo: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
});

let active = ref(-1);
let chartData = ref<any>({
  xData: [],
  yData: [],
  unit: '--',
  id: 0,
});

let changeIndexChecked = (item, index) => {
  active.value = index;
  getIndexData(item);
};

let getIndexData = async item => {
  const { startTime, endTime, patientId } = props.extraInfo;
  const { indexTermId, checkType, indexType, unit } = item;
  let paramsData = {
    indexTermId,
    checkType,
    indexType,
    startTime,
    endTime,
    patientId,
  };
  await queryStructuredDetailApi(paramsData).then((res: any) => {
    let xArr: any = [];
    let yArr: any = [];
    if (res.data && res.data.length) {
      xArr = res.data.map(item => {
        let time = timestampToDate(item.checkTime);
        return time;
      });
      if (item.indexTermId === 44) {
        let diastolicHigh = res.data.map(item => item.diastolicHigh || 0);
        let systolicHigh = res.data.map(item => item.systolicHigh || 0);
        yArr = [
          {
            name: '舒张压',
            color: '#0A73E4',
            data: systolicHigh,
          },
          {
            name: '收缩压',
            color: '#E58B48',
            data: diastolicHigh,
          },
        ];
      } else {
        let otherData = res.data.map(item => item.otherData);
        yArr = [otherData];
      }
    }
    chartData.value = {
      xData: xArr,
      yData: yArr,
      unit: unit || '--',
      id: indexTermId,
    };
  });
};
</script>

<style scoped lang="less">
.content {
  :deep(.el-scrollbar__wrap) {
    border-bottom: 1px solid #e9e8eb;
  }
  .item-change {
    color: #333;
    white-space: nowrap;

    &.active {
      color: #1255e2;
      border-bottom: 2px solid #1255e2;
    }
  }
}
</style>
