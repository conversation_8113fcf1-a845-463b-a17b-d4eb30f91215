<template>
  <div class="common-sty">
    <div class="font-bold">用药情况</div>
    <div v-if="info?.length" class="my-2xs">
      <div v-for="(drug, index) in drugInfoData" :key="index" class="drug">
        <div class="name font-bold">
          {{ drug.drugName }}（{{ drug.drugSpecStr }}）
        </div>
        <div class="usage">
          <span>用法:</span>
          <span class="option">{{ drug.drugUsage }}</span>
          <span class="option">{{ drug.drugmaxAmount }}</span>
          <span class="option">{{ drug.medicineTime }}</span>
          <span class="option">{{ drug.drugMode }}</span>
        </div>
      </div>
    </div>
    <div v-else class="text-center py-3xs">暂无数据</div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { medicineTimeList } from '@/components/DrugInfo/components/constants';

const props = defineProps({
  info: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
});

onMounted(() => {
  dealDrugInfoData();
});

let drugInfoData = ref<any[]>([]);

let dealDrugInfoData = () => {
  drugInfoData.value = props.info?.map(item => {
    try {
      return {
        medicineTime: getMedicineTime(item.medicineTime),
        drugmaxAmount:
          item.drugAmount.ingredients + item.drugAmount.contentUnit,
        drugUsage: item.drugUsage,
        drugName: item.drugName,
        drugMode: item.drugMode,
        drugSpecStr: getDrugSpecStr(item.drugSpec),
      };
    } catch (e) {
      return {
        medicineTime: getMedicineTime(item.medicineTime),
        drugmaxAmount: item.drugAmount,
        drugUsage: item.drugUsage,
        drugName: item.drugName,
        drugMode: item.drugMode,
        drugSpecStr: item.drugSpec,
      };
    }
  });
};

let getMedicineTime = val => {
  let str = '';
  medicineTimeList.forEach(item => {
    if (val === item.value) str = item.label;
  });
  return str;
};

let getDrugSpecStr = obj => {
  const ingredients = obj.ingredients + obj.contentUnit;
  const packageContent = obj.packageNum ? '*' + obj.packageNum + obj.unit : '';
  const packageUnit = obj.packageUnit ? '/' + obj.packageUnit : '';
  return ingredients + packageContent + packageUnit;
};
</script>

<style scoped lang="less">
.drug {
  padding: 8px;
  border-radius: 8px;
  background: #f7f7fb;
  .name {
    color: #15233f;
  }
  .usage {
    color: #3a4762;
    padding: 6px 0;
    .option {
      padding: 0 6px;
    }
    .option:not(:last-child) {
      border-right: 1px solid #d2d2d2;
    }
  }
}
</style>
