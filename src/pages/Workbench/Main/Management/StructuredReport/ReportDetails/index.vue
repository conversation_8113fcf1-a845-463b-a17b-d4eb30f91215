<template>
  <div class="add-report">
    <!-- 顶部结构 -->
    <div class="title-box flex items-center justify-between">
      <div class="left-title flex items-center">
        <img :src="conditionImg" alt="" class="w-14 h-18 mr-6" />
        <span>阶段性总结报告</span>
      </div>
      <div
        class="right-title cursor-pointer flex items-center"
        @click="previewRepor"
      >
        <el-icon>
          <i-ep-View />
        </el-icon>
        <span class="ml-4">预览</span>
      </div>
    </div>
    <!-- 患者信息 -->
    <CardWrapper class="mt-2xs" title="患者信息">
      <div class="base-box">
        <div class="base flex items-center">
          <div class="title">
            姓名：
            <span>{{ baseMsg.patientName }}</span>
          </div>
          <el-divider direction="vertical" />
          <div class="title">
            性别：
            <span>
              {{
                baseMsg.gender === 1
                  ? '男'
                  : baseMsg.gender === 2
                    ? '女'
                    : '未知'
              }}
            </span>
          </div>
          <el-divider direction="vertical" />
          <div class="title">
            年龄：
            <span>{{ baseMsg.age || '--' }}</span>
          </div>
          <el-divider direction="vertical" />
          <div class="title">
            手术时间：
            <span>{{ baseMsg.operationTime }}</span>
          </div>
        </div>
      </div>
    </CardWrapper>
    <!-- 报告主题 -->
    <CardWrapper class="mt-2xs" title="报告主题">
      {{ reportTheme }}
    </CardWrapper>
    <!-- 指标项 -->
    <div v-if="indexList?.checked" class="index-item">
      <div class="font-bold text-base mb-2xs">指标项</div>
      <div v-if="indexList.value.length" class="main">
        <el-scrollbar>
          <div class="index-box">
            <div class="change-data-box flex mt-8">
              <div
                v-for="item in indexList.value"
                :key="item.indexTermId"
                class="item-change cursor-pointer"
                :class="{ 'item-change-active': item.indexTermId === indexId }"
                @click="changeIndexChecked(item)"
              >
                {{ item.indexName }}
              </div>
            </div>
            <div class="hr"></div>
          </div>
        </el-scrollbar>
        <!-- echarts -->
        <Echarts :index-info="indexInfo" />
      </div>
      <div v-else class="text-center py-xs">暂无数据</div>
    </div>
    <!-- 检查项 -->
    <div v-if="detailsData?.ecgData?.checked" class="index-check mt-8">
      <div class="font-bold text-base mb-2xs">检查项</div>
      <div v-if="checkList.length" class="main">
        <div class="index-box">
          <div class="change-data-box mt-8">
            <el-tabs
              v-model="activeTermId"
              type="card"
              @tab-change="handleChange"
            >
              <el-tab-pane
                v-for="item in checkList"
                :key="item.indexTermId"
                :label="item.reportName"
                :name="item.indexTermId"
              >
                <div v-if="dataList.length" class="data-list-box mt-12">
                  <div
                    v-for="(dataItem, index) in dataList"
                    :key="index"
                    class="item-list flex items-center justify-between"
                    :style="{
                      'border-top': index > 0 ? '1px solid #E9E8EB' : 0,
                    }"
                  >
                    <span>{{ dataItem.title }}</span>
                    <span>{{ dataItem.time }}</span>
                  </div>
                </div>
                <div v-else class="text-center py-xs">暂无数据</div>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="hr"></div>
        </div>
      </div>
      <div v-else class="text-center py-xs">暂无数据</div>
    </div>
    <!-- 手术情况 -->
    <div v-if="operationSituation?.checked" class="report-box mt-8">
      <div class="title mb-8">手术情况</div>
      {{ operationSituation.value || '--' }}
    </div>
    <!-- 当前用药 -->
    <div v-if="detailsData.drugCheck" class="report-box mt-8">
      <div class="title mb-8">当前用药</div>
      <el-table
        v-if="editDrugDetailsData.length"
        :data="editDrugDetailsData"
        highlight-current-row
        style="width: 100%"
        :cell-class-name="cellClassName"
        header-row-class-name="head-class"
      >
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column property="drugName" label="药品名称" />
        <el-table-column prop="drugSpecStr" label="药品规格" />
        <el-table-column prop="drugmaxAmount" label="单次用量" />
        <el-table-column prop="drugUsage" label="频率" />
        <el-table-column prop="medicineTime" label="服药时间" />
        <el-table-column prop="drugMode" label="用法" />
      </el-table>
      <div v-else class="text-center py-xs">暂无数据</div>
    </div>
    <!-- 图片档案 -->
    <div v-if="detailsData.imageCheck" class="report-box mt-8">
      <div class="title mb-8">图片档案</div>
      <UploadImages v-model:img-list="imgList" is-view />
    </div>
    <!-- 出院诊断 -->
    <div v-if="detailsData?.diagnose?.checked" class="report-box mt-8">
      <div class="title mb-8">出院诊断</div>
      {{ detailsData.diagnose.value || '--' }}
    </div>
    <!-- 本次复查结论 -->
    <div v-if="reviewConclusion?.checked" class="report-box mt-8">
      <div class="title mb-8">本次复查结论</div>
      {{ reviewConclusion.value || '--' }}
    </div>
    <!-- 本次复查医生建议 -->
    <div v-if="doctorAdvice?.checked" class="report-box mt-8">
      <div class="title mb-8">本次复查医生建议</div>
      {{ doctorAdvice.value || '--' }}
    </div>
    <!-- 需专家确认问题 -->
    <div v-if="confirmationProblem?.checked" class="report-box mt-8">
      <div class="title mb-8">需专家确认问题</div>
      {{ confirmationProblem.value || '--' }}
    </div>

    <!-- 预览 -->
    <PreviewReport
      v-model:preVisible="previewReportVisible"
      :report-info="detailsData"
      :base-info="baseMsg"
    />
  </div>
</template>
<script setup lang="ts">
import conditionImg from '../../img/condition-img.png';
import CardWrapper from '@/components/CardWrapper/index.vue';
import UploadImages from '@/components/UploadImages/index.vue';
import PreviewReport from '../PreviewReport/index.vue';
import {
  queryReportingDetailsApi,
  queryStructuredDetailApi,
  queryStructuredEcgDetailApi,
  queryPatientBaseMsgApi,
} from '@/api/managementSituation';
import { timestampToDate, medicineTimeList, getDrugSpecStr } from '../../hooks';
import Echarts from '../AddReport/components/Echarts.vue';
import store from '@/store';
import useGlobal from '@/store/module/useGlobal';
import useReport from '@/store/module/useReport';
import dayjs from 'dayjs';

let useReportInfo = useReport();
const tabs = store.useTabs();
let useGlobalInfo = useGlobal();

watch(
  () => useReportInfo.reportInfo,
  () => {
    if (useReportInfo.reportInfo && useReportInfo.reportInfo.reportId)
      getDetails(useReportInfo.reportInfo.reportId);
  },
  {
    deep: true,
  }
);

onMounted(() => {
  if (useGlobalInfo.userId) {
    let tabsMap = tabs.tabsMap[useGlobalInfo.userId];
    let reportId = tabsMap[tabsMap.length - 1]?.data?.reportId;
    getDetails(reportId);
  }
});
let queryInfo = ref({
  startTime: '',
  endTime: '',
  patientId: '',
});
// 获取详情
let detailsData = ref<any>({});
let getDetails = (structuredId: any) => {
  queryReportingDetailsApi({ structuredId }).then((res: any) => {
    if (res.data) {
      detailsData.value = res.data;
      let {
        startTime,
        endTime,
        operation,
        reviewConclusions,
        summary,
        problem,
        patientIndex,
        patientId,
        patientDrug,
        accessory,
        theme,
        ecgData,
      } = res.data;

      operationSituation.value = operation;
      reviewConclusion.value = reviewConclusions;
      doctorAdvice.value = res.data.doctorAdvice;
      summarize.value = summary;
      confirmationProblem.value = problem;
      imgList.value = accessory;
      reportTheme.value = theme;
      baseMsg.value.theme = theme;

      queryInfo.value = {
        startTime,
        endTime,
        patientId,
      };

      // 指标项
      indexList.value = patientIndex;
      if (patientIndex.value?.length) changeIndexChecked(patientIndex.value[0]);

      // 药品列表
      if (patientDrug) {
        editDrugDetailsData.value = [];
        editDrugDetailsData.value = patientDrug.map(item => {
          try {
            return {
              medicineTime: getMedicineTime(item.medicineTime),
              drugmaxAmount:
                item.drugAmount.ingredients + item.drugAmount.contentUnit,
              drugUsage: item.drugUsage,
              drugName: item.drugName,
              drugMode: item.drugMode,
              drugSpecStr: getDrugSpecStr(item.drugSpec),
            };
          } catch (e) {
            return {
              medicineTime: getMedicineTime(item.medicineTime),
              drugmaxAmount: item.drugAmount,
              drugUsage: item.drugUsage,
              drugName: item.drugName,
              drugMode: item.drugMode,
              drugSpecStr: item.drugSpec,
            };
          }
        });
      }

      // 心电图
      if (ecgData?.value && ecgData.value.length) {
        let arr: any = [];
        ecgData.value.forEach((item: any) => {
          arr.push({
            ...item,
            flag: false,
          });
        });
        checkList.value = arr;
        const fIndexTermId = arr?.[0]?.indexTermId;
        if (fIndexTermId) {
          activeTermId.value = fIndexTermId;
          handleChange();
        }
      }

      getBaseInfo();
    }
  });
};

let getMedicineTime = (val: number) => {
  let str = '';
  medicineTimeList.forEach(item => {
    if (val === item.value) str = item.label;
  });
  return str;
};

// 患者信息
let baseMsg = ref({
  patientName: '',
  gender: 0,
  age: '',
  operationTime: '',
  theme: '--',
});
// 查询患者相关信息
let getBaseInfo = () => {
  queryPatientBaseMsgApi(queryInfo.value).then((res: any) => {
    let { data } = res;
    if (data) {
      let { patientName, gender, age, surgeryDate } = data;
      baseMsg.value.patientName = patientName;
      baseMsg.value.gender = gender;
      baseMsg.value.age = age;
      baseMsg.value.operationTime = timestampToDate(surgeryDate);
    }
  });
};

// 报告主题
let reportTheme = ref('');

// 指标项
interface info {
  indexTermId: number;
  indexName: string;
}
let indexList = ref<info[]>([]);
// 查看指标
let indexId = ref(0);
let changeIndexChecked = (item: any) => {
  indexId.value = item.indexTermId;
  getIndexDetails(item);
};
// 图表
let indexInfo = ref({
  unit: '--',
  id: 0,
  xData: [],
  yData: [],
});
// 获取详情指标数据
let getIndexDetails = async (form: {
  indexTermId: number;
  checkType: number;
  indexType: number;
  unit: string;
}) => {
  await queryStructuredDetailApi({ ...form, ...queryInfo.value }).then(
    (res: any) => {
      let xArr: any = [];
      let yArr: any = [];

      if (res.data && res.data.length) {
        xArr = res.data.map(item => {
          let time = timestampToDate(item.checkTime);
          return time;
        });
        if (form.indexTermId === 44) {
          let diastolicHigh = res.data.map(item => item.diastolicHigh);
          let systolicHigh = res.data.map(item => item.systolicHigh);
          yArr = [
            {
              name: '舒张压',
              color: '#0A73E4',
              data: systolicHigh,
            },
            {
              name: '收缩压',
              color: '#E58B48',
              data: diastolicHigh,
            },
          ];
        } else {
          let otherData = res.data.map(item => item.otherData);
          yArr = [otherData];
        }
      }
      indexInfo.value = {
        unit: form.unit || '--',
        id: form.indexTermId,
        xData: xArr,
        yData: yArr,
      };
    }
  );
};

// 检查项
interface info {
  chooseStatus: number;
  indexTermId: number;
  reportName: string;
  flag: boolean;
}
let checkList = ref<info[]>([]);
// 检查项内容
interface dataInfo {
  title: string;
  time: string;
}
let dataList = ref<dataInfo[]>([]);
const activeTermId = ref<string | number>('');
// 查看检查项
const handleChange = () => {
  if (!activeTermId.value) return;
  getDataDetails(activeTermId.value);
};

// 获取心电图详情数据
let getDataDetails = (indexTermId: number | string) => {
  let form = {
    patientId: queryInfo.value.patientId,
    indexTermId,
  };
  queryStructuredEcgDetailApi(form).then((res: any) => {
    let arr: dataInfo[] = [];
    if (res.data && res.data.length) {
      res.data.forEach(item => {
        let title = '';
        item.conclusions.forEach((ite, index) => {
          let flag = index < item.conclusions.length - 1 ? '；' : '';
          title += ite.name + flag;
        });
        arr.push({
          time:
            (item.checkTime && dayjs(item.checkTime).format('YYYY-MM-DD')) ||
            '--',
          title: title || '--',
        });
      });
    }
    dataList.value = arr;
  });
};

// 手术情况
let operationSituation = ref('');

// 当前用药
let editDrugDetailsData = ref<any>([]);
let cellClassName = ({ columnIndex }: { columnIndex: number }) => {
  if (columnIndex === 0) {
    return 'input-border';
  }
};

// 图片档案
let imgList = ref([]);

// 复查结论
let reviewConclusion = ref('');

// 医生建议
let doctorAdvice = ref('');

// 概述
let summarize = ref('');

// 专家确认问题
let confirmationProblem = ref('');

// 预览
let previewReportVisible = ref(false);
let previewRepor = () => {
  detailsData.value.reviewCon = detailsData.value.reviewConclusions;
  detailsData.value.suggest = detailsData.value.doctorAdvice;
  detailsData.value.overview = detailsData.value.summary;
  detailsData.value.issue = detailsData.value.problem;
  localStorage.setItem('reportInfo', JSON.stringify(detailsData.value));
  previewReportVisible.value = true;
};
</script>
<style scoped lang="less">
.add-report {
  font-size: 14px;
  color: #3a4762;
  .title-box {
    padding: 16px;
    background: #fff;
    .left-title {
      span {
        font-weight: bold;
        font-size: 16px;
        color: #3a4762;
      }
    }
    .right-title {
      font-size: 14px;
      color: #2e6be6;
    }
  }
  .report-box {
    padding: 16px;
    background: #ffffff;
    border-radius: 6px;
    font-size: 14px;
    color: #101b25;
    .title {
      font-weight: bold;
      font-size: 16px;
    }
    .head-class {
      .el-table__cell {
        background-color: #f7f8fa !important;
        .cell {
          font-size: 14px;
          font-family:
            PingFangSC-Medium,
            PingFang SC;
          font-weight: bold !important;
          color: #203549;
        }
      }
    }
  }
  .base-box {
    .base {
      .title {
        font-size: 14px;
        color: #7a8599;
        span {
          color: #3a4762;
        }
      }
      :deep(.el-divider) {
        margin: 0 24px;
      }
    }
    .diagnosis-box {
      font-size: 14px;
      .title {
        color: #7a8599;
      }
      .content {
        color: #3a4762;
      }
    }
  }
  .index-item {
    padding: 16px;
    background: #ffffff;
    border-radius: 6px;
    .main {
      .index-box {
        .hr {
          width: 100%;
          height: 1px;
          background: #e9e8eb;
          margin-top: -1px;
        }
        .change-data-box {
          .item-change {
            border: 1px solid #dcdee0;
            padding: 10px 28px;
            border-left: 0;
            white-space: nowrap;
            background: #f7f8fa;
            position: relative;
          }
          .item-change-active {
            border-bottom: 1px solid #fff;
            background: #fff;
          }
          .item-change:first-child {
            border-left: 1px solid #e9e8eb;
            border-top-left-radius: 4px;
          }
          .item-change:last-child {
            border-top-right-radius: 4px;
          }
        }
        .change-data-box::-webkit-scrollbar {
          /* 隐藏默认的滚动条 */
          -webkit-appearance: none;
        }
        .change-data-box::-webkit-scrollbar:vertical {
          /* 设置垂直滚动条宽度 */
          width: 0;
        }

        .change-data-box::-webkit-scrollbar:horizontal {
          /* 设置水平滚动条厚度 */
          height: 4px;
        }

        .change-data-box::-webkit-scrollbar-thumb {
          /* 滚动条的其他样式定制，注意，这个一定也要定制，否则就是一个透明的滚动条 */
          border-radius: 8px;
          border: 4px solid #cfcfcf;
          background: #cfcfcf;
        }
      }
    }
  }
  .index-check {
    padding: 16px;
    background: #ffffff;
    border-radius: 6px;
    .main {
      .index-box {
        .hr {
          width: 100%;
          height: 1px;
          background: #e9e8eb;
          margin-top: -1px;
        }
        .change-data-box {
          .el-button + .el-button {
            margin-left: 0;
          }
          .item-change {
            border: 1px solid #dcdee0;
            padding: 10px 28px;
            border-left: 0;
            white-space: nowrap;
            background: #f7f8fa;
          }
          .item-change-active {
            border-bottom: 1px solid #fff;
            background: #fff;
          }
          .item-change:first-child {
            border-left: 1px solid #e9e8eb;
            border-top-left-radius: 4px;
          }
          .item-change:last-child {
            border-top-right-radius: 4px;
          }
        }
      }
      .data-list-box {
        .item-list {
          padding: 12px 0;
          font-size: 14px;
        }
      }
    }
  }
}
.add-report::-webkit-scrollbar {
  /* 隐藏默认的滚动条 */
  width: 0;
}
.add-report::-webkit-scrollbar:vertical {
  /* 设置垂直滚动条宽度 */
  width: 0;
}
</style>
