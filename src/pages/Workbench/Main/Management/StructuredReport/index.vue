<template>
  <div class="report-box">
    <CardWrapper class="mt-2xs" title="阶段性总结报告">
      <div class="module-one flex items-center justify-between">
        <div class="add-box" @click="addReport">
          <img
            src="@/assets/imgs/callCenter/add.png"
            alt=""
            class="w-16 h-16 cursor-pointer ml-8 mt-2"
          />
        </div>
        <div
          class="all-record flex items-center cursor-pointer"
          @click="queryAllReportList"
        >
          历史报告
          <el-icon size="12px">
            <i-ep-arrow-right color="#2E6BE6" />
          </el-icon>
        </div>
      </div>
      <!-- 报告列表 -->
      <div v-if="reportList.length" class="module-two">
        <template v-for="(item, index) in reportList" :key="item.reportId">
          <div
            v-if="index < 2"
            class="item flex items-center justify-between mb-12"
          >
            <div class="item-left flex items-center">
              <img
                :src="index % 2 ? conditionImg : managerialImg"
                alt=""
                class="w-14 h-18 mt-3 mr-12"
              />
              <div class="w-200">
                <Text>{{ item.reportName }}</Text>
              </div>
              <span class="left-time ml-69">{{
                timestampToDateTime(Number(item.generatedTime))
              }}</span>
              <span v-if="item.userName" class="left-generator ml-12"
                >{{ item.userName }}生成</span
              >
            </div>
            <div class="item-right flex items-center">
              <div class="query cursor-pointer" @click="copyH5Link(item.id)">
                报告链接
              </div>
              <div class="query cursor-pointer" @click="queryDetails(item)">
                查看
              </div>
              <div
                class="query cursor-pointer"
                @click="retransmission(item, 1)"
              >
                转发
              </div>
            </div>
          </div>
        </template>
      </div>
      <el-empty v-else description="暂无数据" />

      <Tetransmission
        v-if="flagReport === 1"
        :retransmission-info="retransmissionInfo"
      />
    </CardWrapper>

    <!-- 转发 -->

    <!-- 历史报告 -->
    <Drawer v-model:visible="historicalReportVisible" title="历史报告">
      <div class="historical-report-box">
        <div class="search flex items-center">
          <el-input
            v-model="selectValue"
            style="width: 200px"
            placeholder="请输入"
            @input="input"
          />
          <el-button class="ml-3xs" type="primary" plain @click="reset">
            重置
          </el-button>
        </div>
        <ul class="module-two mt-16">
          <li
            v-for="(item, index) in reportList"
            :key="item.reportId"
            v-infinite-scroll="load"
            class="item mb-12 flex justify-between"
          >
            <div class="item-left flex items-center">
              <img
                :src="index % 2 ? conditionImg : managerialImg"
                alt=""
                class="w-14 h-18 mt-3 mr-12"
              />
              <div class="w-70">
                <Text>{{ item.reportName }}</Text>
              </div>
              <span class="left-time ml-40">{{
                timestampToDateTime(Number(item.generatedTime))
              }}</span>
              <span v-if="item.userName" class="left-generator ml-12"
                >{{ item.userName }}生成</span
              >
            </div>
            <div class="item-right flex items-center">
              <div class="query cursor-pointer" @click="queryDetails(item)">
                查看
              </div>
              <div
                class="query cursor-pointer"
                @click="retransmission(item, 2)"
              >
                转发
              </div>
            </div>
          </li>
        </ul>
      </div>
      <Tetransmission
        v-if="flagReport === 2"
        :retransmission-info="retransmissionInfo"
      />
    </Drawer>
  </div>
</template>
<script setup lang="ts">
import CardWrapper from '@/components/CardWrapper/index.vue';
import conditionImg from '../img/condition-img.png';
import managerialImg from '../img/managerial-img.png';
import AddReport from './AddReport/index.vue';
import { timestampToDateTime } from '../hooks';
import Drawer from '@/components/Drawer/index.vue';
import { getReportingListApi } from '@/api/managementSituation';
import useGlobal from '@/store/module/useGlobal';
import store from '@/store';
import ReportDetails from './ReportDetails/index.vue';
import useReport from '@/store/module/useReport';
import Text from '@/components/Text/index.vue';
import Tetransmission from './components/Tetransmission.vue';
import { INFORMATION_H5_URL } from '@/constant';

const useGlobalInfo = useGlobal();
const tabs = store.useTabs();
const useReportInfo = useReport();

// 历史报告
let queryAllReportList = () => {
  historicalReportVisible.value = true;
  reportList.value = [];
  queryInfo.value.pageNumber = 1;
  getReportList();
};
let historicalReportVisible = ref(false);

let selectValue = ref('');
let input = () => {
  queryInfo.value.theme = selectValue.value;
  reportList.value = [];
  queryInfo.value.pageNumber = 1;
  getReportList();
};

watch(
  () => useGlobalInfo.userId,
  () => {
    if (useGlobalInfo.userId) {
      queryInfo.value.patientId = useGlobalInfo.userId;
      getReportList();
    }
  }
);
// 报告列表
let reportList = ref<any>([]);
let getReportList = async () => {
  await getReportingListApi(queryInfo.value).then((res: any) => {
    reportList.value = [...reportList.value, ...res.data.contents];
    totalNumber.value = Math.ceil(res.data.total / 10);
  });
};

// 新增报告
let addReport = () => {
  tabs.addTab({
    name: '阶段性总结报告-新增',
    group: 'ManagementSituation',
    component: AddReport,
    mainTabCode: 4,
    data: { isNewReport: true },
  });
};
// 重置
let reset = () => {
  selectValue.value = '';
  queryInfo.value.theme = '';
  reportList.value = [];
  queryInfo.value.pageNumber = 1;
  getReportList();
};

// 加载到底部分页加载数据
let totalNumber = ref<number>(0);
interface queryinfo {
  pageNumber: number;
  pageSize: number;
  patientId: number | undefined;
  theme: any;
}
let queryInfo = ref<queryinfo>({
  pageNumber: 1,
  pageSize: 10,
  patientId: useGlobalInfo.userId,
  theme: selectValue.value,
});
const load = () => {
  if (queryInfo.value.pageNumber < totalNumber.value) {
    queryInfo.value.pageNumber++;
    getReportList();
  }
};

// 转发
let retransmissionInfo = ref({});
let flagReport = ref(1);
let retransmission = (item: { id: any; reportName: string }, index: number) => {
  flagReport.value = index;
  retransmissionInfo.value = {
    reportId: item.id,
    reportName: item.reportName,
    patientId: queryInfo.value.patientId,
  };
};

const copyH5Link = async (id: string) => {
  const url = INFORMATION_H5_URL[import.meta.env.MODE];
  if (!id || !url) return;
  await navigator.clipboard.writeText(`${url}structured?id=${id}`);
  ElMessage.success('链接复制成功');
};

// 查看详情
let queryDetails = (item: { reportName: string; id: string }) => {
  let data = {
    reportId: item.id,
  };
  tabs.addTab({
    name: '阶段性总结报告-' + item.reportName,
    group: 'ManagementSituation',
    component: ReportDetails,
    key: item.id,
    disableCache: true,
    mainTabCode: 4,
    data,
  });
};

const orderId = ref<string | number>(0);
watch(
  () => useReportInfo.reportInfo,
  reportInfo => {
    const curOrderId = reportInfo.orderId;
    if (!curOrderId) return;
    if (!orderId.value) orderId.value = curOrderId;
    if (curOrderId !== orderId.value) return;
    reportList.value = [];
    queryInfo.value.pageNumber = 1;
    getReportList();
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<style lang="less" scoped>
.module-two {
  .item {
    padding: 12px;
    background: #f7f8fa;
    border-radius: 4px;
    .item-left {
      font-size: 14px;
      color: #3a4762;
      .left-title {
        font-weight: bold;
      }
    }
    .item-right {
      .query {
        font-size: 14px;
        color: #2e6be6;
        margin-left: 16px;
      }
    }
  }
}
.report-box {
  position: relative;
  width: 100%;
  .module-one {
    .add-box {
      position: absolute;
      top: 14px;
      left: 126px;
    }
    .all-record {
      font-size: 14px;
      color: #2e6be6;
      position: absolute;
      right: 18px;
      top: 14px;
    }
  }
}
.historical-report-box {
  padding: 16px;
  .search {
    .date-box {
      position: relative;
      :deep(.datepicker) {
        .el-range__icon {
          display: none;
        }
      }
      .change-time-icon {
        position: absolute;
        top: 8px;
        right: 8px;
      }
    }
  }
}
</style>
