<template>
  <div class="index mt-8">
    <div class="check-box">
      <el-checkbox v-model="checked" label="选择" size="large" />
    </div>
    <div class="main mt-16">
      <div>检查项</div>
      <div class="change-data-box mt-8">
        <el-tabs v-model="activeTermId" type="card" @tab-change="handleChange">
          <el-tab-pane
            v-for="item in checkList"
            :key="item.indexTermId"
            :label="item.reportName"
            :name="item.indexTermId"
          >
            <div v-if="dataList.length" class="data-list-box mt-12">
              <div
                v-for="(dataItem, index) in dataList"
                :key="index"
                class="item-list flex items-center justify-between"
                :style="{ 'border-top': index > 0 ? '1px solid #E9E8EB' : 0 }"
              >
                <span>{{ dataItem.title }}</span>
                <span>{{ dataItem.time }}</span>
              </div>
            </div>
            <div v-else class="text-center py-xs">暂无数据</div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import useGlobal from '@/store/module/useGlobal';
import {
  queryStructuredEcgDetailApi,
  queryStructuredEcgDataApi,
} from '@/api/managementSituation';
import dayjs from 'dayjs';

const useGlobalInfo = useGlobal();

// 查询数据参数
interface formInfo {
  patientId: number | undefined;
}
let queryInfo = ref<formInfo>({
  patientId: useGlobalInfo.userId,
});

// 是否选择
const checked = defineModel('checked');

// 检查项
interface info {
  chooseStatus: number;
  indexTermId: number;
  reportName: string;
  flag: boolean;
}
let checkList = ref<info[]>([]);
const activeTermId = ref<string | number>('');
// 查看检查项
const handleChange = () => {
  if (!activeTermId.value) return;
  getDataDetails(activeTermId.value);
};
defineExpose({
  checkList,
});

// 检查项内容
interface dataInfo {
  title: string;
  time: string;
}
let dataList = ref<dataInfo[]>([]);

// 获取所有心电图数据
let getList = () => {
  queryStructuredEcgDataApi(queryInfo.value).then((res: any) => {
    if (res.data?.length) {
      checkList.value = res.data || [];
      const fIndexTermId = res.data[0]?.indexTermId;
      if (!fIndexTermId) return;
      activeTermId.value = fIndexTermId;
      getDataDetails(fIndexTermId);
    }
  });
};

// 获取心电图详情数据
let getDataDetails = (indexTermId: number | string) => {
  let form = {
    ...queryInfo.value,
    indexTermId,
  };
  queryStructuredEcgDetailApi(form).then((res: any) => {
    let arr: dataInfo[] = [];
    if (res.data && res.data.length) {
      res.data.forEach(item => {
        let title = '';
        item.conclusions.forEach((ite, index) => {
          let flag = index < item.conclusions.length - 1 ? '；' : '';
          title += ite.name + flag;
        });
        arr.push({
          time:
            (item.checkTime && dayjs(item.checkTime).format('YYYY-MM-DD')) ||
            '--',
          title: title || '--',
        });
      });
    }
    dataList.value = arr;
  });
};

onMounted(() => {
  getList();
});
</script>
<style lang="less" scoped>
.index {
  padding: 0 16px 16px;
  background: #ffffff;
  border-radius: 6px;
  .check-box {
    border-bottom: 1px solid #e9e8eb;
  }
  .main {
    .data-list-box {
      .item-list {
        padding: 12px 0;
        font-size: 14px;
      }
    }
  }
}
</style>
