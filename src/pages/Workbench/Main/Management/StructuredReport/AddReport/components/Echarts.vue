<template>
  <div class="echarts mt-16">
    <BaseChart type="line" :options="lineOptions" data-complete />
  </div>
</template>
<script setup lang="ts">
import { queryThresholdApi } from '@/api/managementSituation';
import useGlobal from '@/store/module/useGlobal';
import BaseChart, { LineECOption } from '@/components/BaseChart';
import { getLineEchartsOptions } from '@/components/BaseChart/options/line';

const props = defineProps({
  indexInfo: {
    type: Object,
    default: () => ({}),
  },
});
const useGlobalInfo = useGlobal();

const lineOptions = ref<LineECOption>({});

const getEchartsLineConfig = (markLine?: any) => {
  const { id, xData, yData, unit } = props.indexInfo;
  const dataZoom = { enable: xData.length >= 30 };
  const cSeriesConfig = yData?.map(item => {
    if (id === 44) {
      return {
        ...item,
        markLine,
      };
    }
    return {
      data: item,
      markLine: [45].includes(id) ? markLine : null,
    };
  });

  return getLineEchartsOptions({
    title: {
      subtext: unit ? `单位：${unit}` : undefined,
    },
    grid: {
      right: 90,
    },
    xAxisData: xData,
    dataZoom,
    seriesConfig: cSeriesConfig || [],
  });
};

// 患者血压、心率阈值查询
const queryThreshold = async (indexInfoId: number) => {
  const res = await queryThresholdApi({ patientId: useGlobalInfo.userId });
  if (!res?.data) return;
  const { blood, heart } = res.data;
  let data: any[] = [];
  if (indexInfoId === 44) {
    let { diastolicHigh, diastolicLow, systolicHigh, systolicLow } =
      blood || {};
    diastolicHigh = diastolicHigh || 0;
    diastolicLow = diastolicLow || 0;
    systolicHigh = systolicHigh || 0;
    systolicLow = systolicLow || 0;
    data = [
      {
        type: 'line',
        yAxis: diastolicHigh,
        label: {
          show: true, // 是否显示标签
          position: 'end', // 标签位置
          formatter: '舒张压高' + diastolicHigh, // 标签内容
        },
      },
      {
        type: 'line',
        yAxis: diastolicLow,
        label: {
          show: true, // 是否显示标签
          position: 'end', // 标签位置
          formatter: '舒张压低' + diastolicLow, // 标签内容
        },
      },
      {
        type: 'line',
        yAxis: systolicHigh,
        label: {
          show: true, // 是否显示标签
          position: 'end', // 标签位置
          formatter: '收缩压高' + systolicHigh, // 标签内容
        },
      },
      {
        type: 'line',
        yAxis: systolicLow,
        label: {
          show: true, // 是否显示标签
          position: 'end', // 标签位置
          formatter: '收缩压低' + systolicLow, // 标签内容
        },
      },
    ];
  } else if (indexInfoId === 45) {
    let { heartHigh, heartLow } = heart || {};
    heartHigh = heartHigh || 0;
    heartLow = heartLow || 0;
    data = [
      {
        type: 'line',
        yAxis: heartHigh,
        label: {
          show: true, // 是否显示标签
          position: 'end', // 标签位置
          formatter: '心率高' + heartHigh, // 标签内容
        },
      },
      {
        type: 'line',
        yAxis: heartLow,
        label: {
          show: true, // 是否显示标签
          position: 'end', // 标签位置
          formatter: '心率低' + heartLow, // 标签内容
        },
      },
    ];
  }

  lineOptions.value = getEchartsLineConfig({
    symbol: 'none',
    silent: true, // 默认为 false，设置为 true 则鼠标悬停时不显示提示框
    data,
  });
};

watch(
  () => props.indexInfo,
  indexInfo => {
    try {
      if ([44, 45].includes(indexInfo.id)) {
        queryThreshold(indexInfo.id);
      } else {
        lineOptions.value = getEchartsLineConfig();
      }
    } catch (e) {
      console.log('$debugZ：eeee', e);
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<style scoped lang="less">
.echarts {
  height: 296px;
}
</style>
