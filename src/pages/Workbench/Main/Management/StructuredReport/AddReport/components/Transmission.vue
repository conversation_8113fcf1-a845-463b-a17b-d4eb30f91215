<template>
  <Dialog v-model:visible="showSendMessageDialog" title="阶段性报告">
    <div class="flex items-center p-lg">
      <div class="w-120">是否直接转发？</div>
      <el-radio-group v-model="pushReportingInfo.isPush">
        <el-radio :value="1">是</el-radio>
        <el-radio :value="0">否</el-radio>
      </el-radio-group>
    </div>
    <div class="flex items-center p-lg pt-0">
      <div class="w-120">发送到</div>
      <el-select
        v-model="pushReportingInfo.teamType"
        placeholder="Select"
        size="large"
        style="width: 200px"
      >
        <el-option
          v-for="item in reportingGroupList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="flex justify-end pr-5xl"></div>
    <el-button class="ml-144 w-100" type="primary" @click="save">
      确认
    </el-button>
  </Dialog>
</template>

<script setup lang="ts">
import Dialog from '@/components/Dialog/index.vue';
import { pushSubscribeMsg, queryChatApi } from '@/api/managementSituation';
import useGlobal from '@/store/module/useGlobal';
import store from '@/store';
const props = defineProps<{
  reportId: string | number;
  reportName: string;
}>();
const imStore = store.useIM();
const useGlobalInfo = useGlobal();
const showSendMessageDialog = defineModel<boolean>('visible', {
  type: Boolean,
  default: false,
});
const pushReportingInfo = reactive({
  isPush: 1,
  teamType: 1,
});
const save = async () => {
  const { reportId, reportName = '阶段性总结报告' } = props;
  if (!reportId) return;
  const { teamType, isPush } = pushReportingInfo;
  if (teamType === 2 && isPush === 1 && props.reportId) {
    await pushSubscribeMsg({
      id: reportId,
      reportName,
      patientId: useGlobalInfo.userId,
    });
  }
  const teamNumber = reportingGroupList.value.find(
    i => i.value === teamType
  )?.teamNumber;

  if (isPush === 1 && teamNumber) {
    imStore.sendCustomMsg({
      to: teamNumber,
      content: {
        name: reportName,
        id: props.reportId,
        type: 8,
      },
    });
  }

  showSendMessageDialog.value = false;
};

const reportingGroupList = ref<
  { value: number; label: string; teamNumber: string }[]
>([]);

const chatTypeDes = {
  1: '专家',
  2: '患者',
  3: '患者-专家群聊',
  4: '团队内',
};

onMounted(async () => {
  const res = await queryChatApi({ patientId: useGlobalInfo.userId });
  reportingGroupList.value =
    res?.data?.teamList?.map(({ teamType, teamNumber }) => {
      return {
        teamNumber: teamNumber!,
        value: teamType || 0,
        label: (teamType && chatTypeDes[teamType]) || '',
      };
    }) || [];
  pushReportingInfo.teamType = reportingGroupList.value?.[0]?.value;
});
</script>

<style scoped lang="less"></style>
