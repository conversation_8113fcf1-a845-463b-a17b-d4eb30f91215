<template>
  <div>
    <el-dialog
      v-model="dialogVisibleTodo"
      :width="props.width"
      class="todo-dialog"
      @close="close"
    >
      <template #header>
        <h1 class="font-bold text-base">
          {{
            useTodoInfo.todoInfo.category === 1
              ? '整改回复'
              : qualityRecordStore.getTodoType(useTodoInfo.todoInfo.type)?.name
          }}
        </h1>
      </template>
      <div>
        <!-- 添加待办 -->
        <AddTodo v-if="useTodoInfo.status === 1" @close="cancel" />
        <!-- 延期待办 -->
        <PostponeTodo v-else-if="useTodoInfo.status === 2" @close="cancel" />
        <!-- 入组宣教、复查提醒、随访提醒 -->
        <GroupMission
          v-else-if="
            useTodoInfo.status === 999 ||
            useTodoInfo.status === 20 ||
            useTodoInfo.todoInfo.type === 2
          "
          @close="close"
          @cancel="cancel"
        />
        <!-- 整改回复 -->
        <RectificationReply
          v-else-if="useTodoInfo.todoInfo.category === 1"
          @cancel="cancel"
        />
        <!-- 调药跟踪、门诊跟踪、住院跟踪 -->
        <TrackDown v-else @close="close" @cancel="cancel" />
      </div>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { getUserRoles, todoTitle } from '../index';
import AddTodo from './AddTodo.vue';
import PostponeTodo from './PostponeTodo.vue';
import GroupMission from './GroupMission.vue';
import TrackDown from './TrackDown.vue';
import RectificationReply from './RectificationReply.vue';
import useUserStore from '@/store/module/useUserStore';
import { qualityCheck, createQualityRecord } from '@/api/qualityControl';
import useTodo from '@/store/module/useTodo';

import useQualityRecord from '@/store/module/useQualityRecord';

let dialogVisibleTodo = ref<boolean>(false);
let title = ref<string>('');

const props = defineProps({
  width: {
    default: '30%',
    type: String,
  },
  dialogVisible: {
    default: false,
    type: Boolean,
  },
});

const qualityRecordStore = useQualityRecord();

const userStore = useUserStore();
let useTodoInfo = useTodo();

const getQualityCheck = () => {
  const todoInfo = useTodoInfo.todoInfo;
  qualityCheck({
    userId: todoInfo.headId,
    userType: String(todoInfo.headRole),
    source: 2,
    dicType: todoInfo.type,
    backlogId: todoInfo.backlogId,
    patientId: todoInfo.patientId,
  })
    .then(res => {
      useTodoInfo.setQualityRecord(res);
      useTodoInfo.setRectificationReply('');
    })
    .catch(err => {
      console.log(err);
    });
};
watch(
  () => props.dialogVisible,
  newData => {
    todoTitle.value.forEach((item: any) => {
      if (item.type === useTodoInfo.status) title.value = item.title;
    });

    dialogVisibleTodo.value = newData;
    if (newData) {
      console.log('output->useTodoInfo.value', useTodoInfo.todoInfo);
      getQualityCheck();
    }
  },
  { immediate: true, deep: true }
);

// 关闭弹窗
const emit = defineEmits(['closeDialog']);
let close = () => {
  if (useTodoInfo.qualityRecord.length) {
    // 生成质控记录
    const todoInfo = useTodoInfo.todoInfo;
    const params = {
      sourceId: todoInfo.backlogId,
      patientId: todoInfo.patientId,
      userId: userStore.accountId,
      userType:
        userStore.userRoles && userStore.userRoles.length > 0
          ? userStore.userRoles[0]
          : '',
      groupId: todoInfo.groupId,
      sourceCode: 2,
      dicType: todoInfo.type,
      checkPoint: useTodoInfo.qualityRecord,
    };
    createQualityRecord(params)
      .then(res => {
        console.log(res);
      })
      .finally(() => emit('closeDialog'));
  }
  // 刷新下待办数量
  qualityRecordStore.getReminderNum({
    userId: userStore.accountId,
    userType: getUserRoles(),
  });
  emit('closeDialog');
};
const cancel = () => {
  // 刷新下待办数量
  qualityRecordStore.getReminderNum({
    userId: userStore.accountId,
    userType: getUserRoles(),
  });
  emit('closeDialog');
};
</script>
<style scoped lang="less">
:deep(.todo-dialog) {
  .el-dialog__header {
    padding: 12px 24px;
    border-bottom: 2px solid #e9e8eb;
    margin-right: 0;
    .el-dialog__title {
      font-size: 16px;
      font-weight: bold;
      color: #101b25;
    }
  }
  .el-dialog__headerbtn {
    top: 0;
  }
  .el-dialog__body {
    padding: 16px;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    .sure-btn {
      background: #2e6be6;
      border-radius: 2px;
      border-color: #2e6be6;
    }
    .cancel-btn {
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
