<template>
  <!-- 质量风险模块 -->
  <div class="bg-[#F7F8FA] rounded-[4px] px-16 pt-12 pb-14 text-sm">
    <div class="text-[#3A4762] mb-10 flex items-center">
      <img :src="warning" alt="" class="mr-6" />
      {{
        qualityRecordStore
          .getTodoType(useTodoInfo.todoInfo.type)
          .name.includes('超时')
          ? '超时风险'
          : '质量风险'
      }}
    </div>
    <ul :class="['text-[#7A8599] pl-22']">
      <li
        v-for="(item, index) in useTodoInfo.todoInfo.checkPoint"
        :key="index"
        :class="[
          liStr,
          item.complete ? 'text-[#2FB324] before:bg-[#2FB324]' : '',
        ]"
      >
        {{ item.desc }}
      </li>
      <li
        v-if="!useTodoInfo.todoInfo.checkPoint"
        class="flex items-center mb-14 last:mb-0 befo re:content-[''] before:block before:w-5 before:h-5 before:bg-[#7A8599] before:rounded-full before:mr-6"
      >
        超时{{
          dayjs(new Date()).diff(useTodoInfo.todoInfo.term, 'day') === 0
            ? 1
            : dayjs(new Date()).diff(useTodoInfo.todoInfo.term, 'day')
        }}天
      </li>
    </ul>
    <h4 class="mt-14 text-[#E37221]">
      整改要求：{{ useTodoInfo.todoInfo.content.split('|')[1] }}
    </h4>
  </div>
  <!-- 整改说明 -->
  <div class="mt-16 flex flex-col">
    <h4 class="text-[#3A4762] mb-8">整改说明</h4>
    <textarea
      v-model="formData.improvementDescription"
      :class="textStr"
      placeholder="请输入整改说明（最大限制200字）"
      maxlength="200"
    ></textarea>
    <h4 class="mt-8 text-[#7A8599]">以上说明将提交给质控负责人</h4>
  </div>
  <div class="text-center w-full pb-24">
    <el-button type="primary" @click="handleSubmit">确定</el-button>
    <el-button @click="close">取消</el-button>
  </div>
</template>
<script setup>
import warning from '@/assets/imgs/record/warning.svg';
import { handleTodoApi } from '@/api/todo';
import { getUserRoles } from '../index';
import useTodo from '@/store/module/useTodo';
import { useQualityRecord } from '@/store/module/useQualityRecord';
import dayjs from 'dayjs';
const useTodoInfo = useTodo();
const qualityRecordStore = useQualityRecord();

import useUserStore from '@/store/module/useUserStore';
const userStore = useUserStore();

const formData = ref({ improvementDescription: '' });
const liStr =
  'flex items-center mb-14 last:mb-0 before:content-[""] before:block before:w-5 before:h-5 before:bg-[#7A8599] before:rounded-full before:mr-6';
// 文本框样式
const textStr =
  'block  h-full border border-[#DCDFE6] border-solid rounded-[4px] min-h-[64px]  px-12 py-6 focus:border-[#2E6BE6] focus:outline-none placeholder:text-[#BAC8D4]';

const emits = defineEmits(['cancel', 'close']);
const handleSubmit = () => {
  const params = {
    backlogId: useTodoInfo.todoInfo.backlogId,
    type: useTodoInfo.todoInfo.typeName,
    content: formData.value.improvementDescription,
    headId: userStore.accountId,
    headRole: getUserRoles(),
    sourceId: useTodoInfo.todoInfo.sourceId,
  };
  handleTodoApi(params)
    .then(() => {
      close();
    })
    .catch(err => console.log(err));
};
const close = () => {
  emits('cancel');
};
</script>
<style></style>
