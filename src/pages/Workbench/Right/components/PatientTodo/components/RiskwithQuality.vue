<template>
  <!-- 质量风险模块 -->
  <template v-if="useTodoInfo.qualityRecord.length !== 0">
    <div
      class="bg-[#F7F8FA] rounded-[4px] px-16 pt-12 pb-14 text-sm *:mb-8 *:last:mb-0"
    >
      <div>
        <div class="text-[#3A4762] mb-10 flex items-center">
          <img :src="warning" alt="" class="mr-6" />
          质量风险
        </div>
        <ul
          :class="[
            'text-[#7A8599] pl-22 flex ',
            useTodoInfo.qualityRecord.length > 2
              ? 'flex-col justify-start items-start *:ml-0 *:mt-8'
              : 'items-center',
          ]"
        >
          <li
            v-for="(item, index) in useTodoInfo.qualityRecord"
            :key="index"
            :class="[
              liClass,
              item.complete ? 'text-[#2FB324] before:bg-[#2FB324]' : '',
            ]"
          >
            {{ item.desc }}
          </li>
        </ul>
      </div>
    </div>
    <!-- 整改说明 -->
    <div class="mt-16 flex flex-col">
      <h4 class="text-[#3A4762] mb-8">情况说明：</h4>
      <textarea
        v-model="improvementDescription"
        :class="textStr"
        placeholder="请输入风险说明（最大限制200字）"
        maxlength="200"
        @input="setReply"
      ></textarea>
      <h4 class="mt-8 text-[#7A8599]">以上说明将提交给质控负责人</h4>
    </div>
  </template>
</template>
<script setup>
import useTodo from '@/store/module/useTodo';

import warning from '@/assets/imgs/record/warning.svg';

// 文本框样式
const textStr =
  'block  h-full border border-[#DCDFE6] border-solid rounded-[4px] min-h-[64px]  px-12 py-6 focus:border-[#2E6BE6] focus:outline-none placeholder:text-[#BAC8D4]';
const liClass =
  'flex items-center ml-41 first:ml-0 before:content-[""] before:block before:w-5 before:h-5 before:bg-[#7A8599] before:rounded-full before:mr-6';

const useTodoInfo = useTodo();

const improvementDescription = ref('');
const setReply = e => {
  useTodoInfo.setRectificationReply(e.target.value);
};
</script>
<style></style>
