<template>
  <div>
    <el-checkbox-group v-model="checkList" @change="checklistChange">
      <el-checkbox label="整改要求" :value="1" />
      <el-checkbox label="科研质疑" :value="3" />
      <el-checkbox label="待办事项" :value="2" />
      <el-checkbox label="仅看我的" :value="0" />
    </el-checkbox-group>
  </div>
</template>

<script setup lang="ts">
type Iprop = Array<number>;
const checkList = defineModel<Iprop>({ default: () => [] });
const emit = defineEmits(['check-list-change']);
const checklistChange = (val: Iprop) => {
  emit('check-list-change', val);
};
</script>

<style scoped lang="less">
:deep(.el-checkbox) {
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #3a4762;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background: #2e6be6;
    border-color: #2e6be6;
  }
}
</style>
