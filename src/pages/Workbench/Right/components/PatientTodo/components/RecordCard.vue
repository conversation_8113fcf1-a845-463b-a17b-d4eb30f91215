<template>
  <div class="card bg-[#F7F8FA] rounded-[4px] px-12 pt-12 pb-10" v-bind="attrs">
    <div class="top flex items-center justify-between">
      <div class="top-left flex items-center">
        <ShowImg v-if="item.category !== 1" :type="item.type" />
        <img v-else :src="getCategoryType(item) ? overtime : quality" alt="" />
        <span class="top-title ml-8">
          {{ qualityRecordStore.getTodoType(item.type)?.name || item.type }}
          <template
            v-if="
              item.category !== 1 &&
              item.overdueTime >= new Date().getTime() &&
              !item.backlogResult
            "
          >
            <span v-if="getDueTime.includes('已逾期')" class="overdue">
              {{ getDueTime }}
            </span>
            <span
              v-if="getDueTime.includes('今天') || getDueTime.includes('明天')"
              class="time-num"
            >
              {{ getDueTime }}
            </span>
          </template>
          <template v-else-if="item.category === 1 && getCategoryType(item)">
            <span class="overdue">
              （超时{{ getCategoryType(item) ? getDiffDays(item) : '' }}天）
            </span>
          </template>
          <!-- <template v-else-if="item.overdueTime < new Date().getTime()">
            已超期{{ dayjs(item.overdueTime).format('YYYY-MM-DD') }}
          </template> -->
        </span>
      </div>
      <div class="top-right flex items-center">
        <slot name="operation"></slot>
      </div>
    </div>
    <div class="bottom flex items-center mt-4 mb-8 justify-between text-sm">
      <div :class="[' ml-24', item.category === 1 ? 'w-126' : 'w-166']">
        <Text v-if="item.category === 1">
          {{ item.content?.split('|')[0] }}
        </Text>
        <Text v-else>
          {{ item.content }}
        </Text>
      </div>
      <Text v-if="item.category === 1" class="w-[126px] ml-24">
        {{ item.content?.split('|')[1] }}
      </Text>
      <div class="flex items-center ml-12">
        <Text>
          {{ convertDateTime(item.term as any).integrityTime }}
        </Text>
        <div class="ml-24 max-w-106">
          <Text>{{ item.headName || '--' }}</Text>
        </div>
      </div>
    </div>

    <div
      v-if="item.status !== 1"
      :class="[
        'bottom-content',
        'bg-white',
        'rounded-[4px]',
        'text-sm',
        item.backlogResult ||
        (!item.backlogResult &&
          (item.overdueTime as any) < new Date().getTime())
          ? 'px-12 py-8'
          : '',
      ]"
    >
      <div
        v-if="item.overdueTime < new Date().getTime() && !item.backlogResult"
        class="no-handle text-[#3A4762] flex items-center"
      >
        <span
          class="block w-5 h-5 bg-[#E37221] rounded-full mr-6 text-[#3A4762]"
        ></span>
        超期未处理
      </div>
      <div v-else-if="item.backlogResult" class="hasData">
        <div class="flex items-center text-[#3A4762]">
          <span
            class="block w-5 h-5 bg-[#2E6BE6] rounded-full mr-6 text-[#3A4762]"
          ></span>
          处理记录
        </div>
        <div class="flex flex-col mt-6">
          <div class="flex justify-between items-center mb-6">
            <div class="item">
              <span class="label text-[#7A8599]">处理人：</span>
              <span class="value text-[#3A4762]">
                {{ item.backlogResult.headName || '--' }}
              </span>
            </div>
            <div class="item">
              <span class="label text-[#7A8599]">处理时间：</span>
              <span class="value text-[#3A4762]">
                {{
                  convertDateTime(item.backlogResult.time as any).integrityTime
                }}
              </span>
            </div>
          </div>
          <div class="item">
            <span class="label text-[#7A8599]">处理结论：</span>
            <span class="value text-[#3A4762]">
              <!-- 待办前端直接处理展示两种处理结论 -->
              <template v-if="item.category === 2">
                {{
                  item.backlogResult.time <= item.term ? '按时处理' : '超期处理'
                }}
              </template>
              <template v-else-if="item.category === 1">
                {{
                  item.backlogResult.content !== '""'
                    ? item.backlogResult.content
                    : '--'
                }}
              </template>
              <template v-else>
                {{ item.backlogResult.content?.split(';').join(':') }}
              </template>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="false"
      class="flex items-center justify-center h-60 bg-[#f7f8fa] text-[#bac8d4] rounded-[4px] p-12 text-sm text-center"
    >
      暂无处理记录
    </div>
  </div>
</template>

<script setup lang="ts">
import ShowImg from './ShowImg.vue';
import overtime from '@/assets/imgs/qualityControl/overtime.svg';
import quality from '@/assets/imgs/qualityControl/quality.svg';
import { convertDateTime, getTime } from '../index';
import type { IApiBacklogPageQueryBacklogResponseList } from '@/interface/type';
import useQualityRecord from '@/store/module/useQualityRecord';
import Text from '@/components/Text/index.vue';
import dayjs from 'dayjs';
const qualityRecordStore = useQualityRecord();
interface IProp {
  item: IApiBacklogPageQueryBacklogResponseList;
}
const props = withDefaults(defineProps<IProp>(), {});
const attrs = useAttrs();
// 处理弹窗

const getDueTime = computed(() => getTime(props.item.term as any));

const getCategoryType = computed(
  () => item =>
    qualityRecordStore.getTodoType(item.type)?.name?.includes('超时')
);

const getDiffDays = item =>
  dayjs(new Date()).diff(item.term, 'day') === 0
    ? 1
    : dayjs(new Date()).diff(item.term, 'day');
</script>

<style scoped lang="less">
.card {
  border-radius: 4px;
  padding: 12px;
  .top {
    .top-left {
      .top-title {
        font-size: 14px;
        font-weight: 700;
        color: #3a4762;
        .overdue {
          color: #e63746;
        }
        .time-num {
          color: #e37221;
        }
      }
    }
    .top-right {
      font-size: 14px;
      .detele {
        color: #e63746;
      }
      .postpone {
        color: #e37221;
      }
      .handle {
        color: #2e6be6;
      }
    }
  }
  .bottom {
    font-size: 14px;
    color: #7a8599;
    .bottom-content {
      white-space: nowrap; /* 防止换行 */
      overflow: hidden; /* 隐藏溢出部分 */
      text-overflow: ellipsis; /* 显示省略号 */
      flex: 1;
    }
  }
}
</style>
