<template>
  <div class="todo">
    <div class="postpone flex justify-between">
      <div class="title mt-6 mr-16">提醒时间</div>
      <div class="time-box">
        <el-date-picker
          v-model="postponeTime"
          type="datetime"
          placeholder="请选择时间"
          size="default"
          :disabled-date="disabledDateFn"
          :clearable="false"
        />
        <div class="tip mt-8 mb-26">选择新的待办提醒时间，到期自动提醒</div>
        <img :src="changeTime" alt="" class="w-13 h-13 change-time-icon" />
      </div>
    </div>
    <Btns @submit="submit" @cancel="cancel" />
  </div>
</template>
<script setup lang="ts">
import Btns from './Btns.vue';
import changeTime from '@/assets/imgs/callCenter/change-time.png';
import { dialogTip, convertDateTime } from '../index';
import useTodo from '@/store/module/useTodo';
import { delayTodoApi } from '@/api/todo';
let useTodoInfo = useTodo();

let postponeTime = ref<string>('');

// 限制日期选择时间
const disabledDateFn = (time: { getTime: () => number }) => {
  if (time.getTime() < Date.now() - 8.64e7) {
    return true;
  } else {
    return false;
  }
};

let submit = () => {
  if (!postponeTime.value) {
    return dialogTip('请选择时间！');
  }
  // 获取待办选择的延期时间的时间戳
  let postponeTimeTimestamp = new Date(postponeTime.value).getTime();
  // 获取待办超期时间的时间戳
  let overdueTimeTimestamp = new Date(
    useTodoInfo.todoInfo.overdueTime
  ).getTime();

  // 延期时间不能超过超期时间
  if (postponeTimeTimestamp > overdueTimeTimestamp) {
    return dialogTip(
      '延期时间不能超过超期时间，超期时间为：' +
        convertDateTime(useTodoInfo.todoInfo.overdueTime).integrityTime
    );
  }

  delayTodoApi({
    backlogId: useTodoInfo.todoInfo.backlogId,
    remindTime: postponeTimeTimestamp,
    type: useTodoInfo.todoInfo.typeName,
  }).then((res: any) => {
    if (res.code === 'E000000') {
      dialogTip('待办延期成功！', 'success');
      cancel();
    } else {
      dialogTip('待办延期失败：' + res.message, 'error');
    }
  });
};

const emit = defineEmits(['close']);
let cancel = () => {
  emit('close');
};
</script>
<style scoped lang="less">
.todo {
  .title {
    font-size: 14px;
    font-weight: bold;
    color: #3a4762;
  }
  .postpone {
    padding: 0 29px;
    :deep(.time-box) {
      flex: 1;
      position: relative;
      .change-time-icon {
        position: absolute;
        top: 9px;
        right: 10px;
      }
      .el-date-editor.el-input,
      .el-date-editor.el-input__wrapper {
        width: 100%;
        height: 32px;
      }
      .el-input__prefix {
        display: none;
      }
      .tip {
        font-size: 14px;
        color: #7a8599;
      }
    }
  }
}
</style>
