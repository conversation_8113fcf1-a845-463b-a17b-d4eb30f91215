<template>
  <div class="mission">
    <div class="postpone flex">
      <div class="title mt-4">办理方式：</div>
      <div class="time-box mb-16 ml-12">
        <el-checkbox-group v-model="checkTypeList" @change="changeMissionType">
          <el-checkbox label="电话" />
          <el-checkbox label="在线咨询" />
          <el-checkbox label="其他" />
        </el-checkbox-group>
        <div
          v-if="checkTypeList.some(item => item === '其他')"
          class="mission-type-reason mt-8"
        >
          <el-input
            v-model="miassionTypeInput"
            placeholder="其他方式"
            clearable
            show-word-limit
            maxlength="20"
            :rows="2"
            type="textarea"
            resize="none"
          />
        </div>
      </div>
    </div>
    <div class="postpone flex items-center mb-12">
      <div class="title">跟踪成功：</div>
      <div class="time-box ml-12">
        <div class="flex items-center">
          <div
            v-for="item in patientTypeList"
            :key="item.id"
            class="all-box flex items-center cursor-pointer mr-27"
            @click="changeResult(item.id)"
          >
            <div
              v-if="missionResult === item.id"
              class="change-box-checked mr-8 flex items-center justify-center"
            >
              <div class="interior-check"></div>
            </div>
            <div v-else class="change-box mr-8"></div>
            <span class="change-title">{{ item.title }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="postpone flex">
      <div class="title mt-4">跟踪结果：</div>
      <div class="time-box mb-16">
        <div v-if="missionResult === 1" class="result-box">
          <div class="checkbox-box ml-12">
            <el-checkbox-group
              v-model="checkResultSuccessList"
              @change="changeMissionResultSuccess"
            >
              <el-checkbox
                v-for="(item, index) in checkTypedResults"
                :key="index"
                :label="item"
              />
            </el-checkbox-group>
          </div>
          <div
            v-if="checkResultSuccessList.some(item => item === '其他')"
            class="mission-type-reason"
          >
            <el-input
              v-model="miassionResultSuccessInput"
              placeholder="请输入其他结果（100字内）"
              clearable
              show-word-limit
              maxlength="100"
              :rows="3"
              type="textarea"
              resize="none"
            />
          </div>
        </div>
        <div v-if="missionResult === 2" class="result-box">
          <div class="checkbox-box ml-12">
            <el-checkbox-group
              v-model="checkResultLoseList"
              @change="changeMissionResult"
            >
              <el-checkbox label="无法联系" />
              <el-checkbox label="拒绝沟通" />
              <el-checkbox label="其他" />
            </el-checkbox-group>
          </div>
          <div
            v-if="checkResultLoseList.some(item => item === '其他')"
            class="mission-type-reason mt-8"
          >
            <el-input
              v-model="miassionResultLoseInput"
              placeholder="请输入其他结果（100字内）"
              clearable
              show-word-limit
              maxlength="100"
              :rows="3"
              type="textarea"
              resize="none"
            />
          </div>
        </div>
      </div>
    </div>
    <RiskwithQuality />
    <Btns @submit="submit" @cancel="cancel" />
  </div>
</template>
<script setup lang="ts">
import Btns from './Btns.vue';
import RiskwithQuality from './RiskwithQuality.vue';
import { dialogTip, getUserRoles } from '../index';
import { handleTodoApi } from '@/api/todo';
import useTodo from '@/store/module/useTodo';
import useUserStore from '@/store/module/useUserStore';
let useTodoInfo = useTodo();

// 方式
const checkTypeList = ref([]);
const miassionTypeInput = ref<string>('');
let changeMissionType = () => {
  if (!checkTypeList.value.some(item => item === '其他')) {
    miassionTypeInput.value = '';
  }
};

const userStore = useUserStore();
// 跟踪成功
const checkResultSuccessList = ref([]);
const miassionResultSuccessInput = ref<string>('');
let changeMissionResultSuccess = () => {
  if (!checkResultSuccessList.value.some(item => item === '其他')) {
    miassionResultSuccessInput.value = '';
  }
};

// 跟踪失败
const checkResultLoseList = ref([]);

const checkResultMap = {
  '1': [
    '用药后好转',
    '未执行用药建议',
    '建议进一步门诊',
    '建议进一步住院',
    '其他',
  ],
  '24': ['患者考虑续费，后续再跟进', '患者明确拒绝续费', '其他'],
  '22': ['已安抚患者', '建议患者进一步就医', '其他'],
  '23': ['患者服药方案未改变', '需医生进一步跟进服药', '其他'],
  '9,11': ['患者已知晓，随后填写表单', '患者明确表示拒绝此次症状随访', '其他'],
  '3,4,13,21': [
    '患者已复查，随后上传报告',
    '患者未复查，但答应30天内前往复查',
    '患者明确表达拒绝此次复查，或30天内无法前往复查',
    '其他',
  ],
};

const checkTypedResults = computed(() => {
  const status = useTodoInfo.status;
  const targetKey = Object.keys(checkResultMap).find(it =>
    it
      .split(',')
      .map(it => Number(it))
      .includes(status)
  );
  return (!!targetKey && checkResultMap[targetKey]) || checkResultMap[1];
});

const miassionResultLoseInput = ref<string>('');
let changeMissionResult = () => {
  if (!checkResultLoseList.value.some(item => item === '其他')) {
    miassionResultLoseInput.value = '';
  }
};
let missionResult = ref<number>(1);
let patientTypeList = ref([
  { title: '是', id: 1 },
  { title: '否', id: 2 },
]);
let changeResult = (id: number) => {
  missionResult.value = id;
  if (id === 1) {
    checkResultLoseList.value = [];
    miassionResultLoseInput.value = '';
  }
  if (id === 2) {
    checkResultSuccessList.value = [];
    miassionResultSuccessInput.value = '';
  }
};

// 处理结果选项

let submit = () => {
  // 跟踪成功
  if (missionResult.value === 1 && !checkResultSuccessList.value.length) {
    return dialogTip('请选择跟踪结果！');
  } else if (
    checkResultSuccessList.value.some(item => item === '其他') &&
    !miassionResultSuccessInput.value
  ) {
    return dialogTip('请填写其他结果！');
  }

  //   跟踪失败
  if (missionResult.value === 2 && !checkResultLoseList.value.length) {
    return dialogTip('请选择跟踪结果！！');
  } else if (
    checkResultLoseList.value.some(item => item === '其他') &&
    !miassionResultLoseInput.value
  ) {
    return dialogTip('请填写其他结果！');
  } else {
    let params = {
      missionResult: missionResult.value,
      checkResultSuccessList: checkResultSuccessList.value,
      miassionResultSuccessInput: miassionResultSuccessInput.value,
      checkResultLoseList: checkResultLoseList.value,
      miassionResultLoseInput: miassionResultLoseInput.value,
      qualityRecordInput: useTodoInfo.rectificationReply,
    };
    handleTodoApi({
      backlogId: useTodoInfo.todoInfo.backlogId,
      patientId: useTodoInfo.todoInfo.patientId,
      type: useTodoInfo.todoInfo.typeName,
      content: JSON.stringify(params),
      headId: userStore.accountId,
      headRole: getUserRoles(),
      sourceId: useTodoInfo.todoInfo.sourceId,
    }).then(res => {
      let { code, message } = res;
      if (code === 'E000000') {
        dialogTip('处理成功！', 'success');
        close();
      } else {
        dialogTip(message);
      }
    });
  }
};
const emit = defineEmits(['close', 'cancel']);
let cancel = () => {
  emit('cancel');
};
const close = () => emit('close');
</script>
<style scoped lang="less">
.mission {
  .title {
    font-size: 14px;
    font-weight: bold;
    color: #3a4762;
  }
  .postpone {
    padding-left: 24px;
    margin-top: -4px;
    :deep(.time-box) {
      flex: 1;
      .result-box {
        .checkbox-box {
          .el-checkbox-group {
            display: flex;
            flex-direction: column;
          }
        }
      }
      .all-box {
        .change-box {
          width: 14px;
          height: 14px;
          background: #ffffff;
          border-radius: 8px;
          border: 1px solid #dcdee0;
        }
        .change-box-checked {
          width: 14px;
          height: 14px;
          background: #ffffff;
          border-radius: 8px;
          border: 1px solid #2e6be6;
          .interior-check {
            width: 10px;
            height: 10px;
            background: #0a73e4;
            border-radius: 8px;
          }
        }
        .change-title {
          font-size: 14px;
          color: #3a4762;
        }
      }
      .el-checkbox-group {
        .is-checked {
          .el-checkbox__label {
            color: #3a4762;
          }
          .el-checkbox__inner {
            border-color: #0a73e4;
            background: #0a73e4;
          }
        }
      }
      .mission-type-reason {
        width: 100%;
        padding: 12px;
        box-sizing: border-box;
      }
      .el-date-editor.el-input,
      .el-date-editor.el-input__wrapper {
        width: 100%;
        height: 32px;
      }
    }
  }
}
</style>
