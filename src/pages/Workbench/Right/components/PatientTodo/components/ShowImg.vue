<template>
  <img :src="typedImg" alt="" class="w-16 h-16" />
</template>
<script setup lang="ts">
const props = defineProps({
  type: {
    default: 1,
    type: Number,
  },
});

let { type } = props;

const typedImgList = ref([
  {
    type: [1, 18],
    img: 'patient-enrollment-img.png',
  },
  {
    type: [2, 19],
    img: 'group-mission-img.png',
  },
  {
    type: [3, 13, 16],
    img: 'review-reminder-img.png',
  },
  {
    type: [4],
    img: 'risk-treatment-img.png',
  },
  {
    type: [5],
    img: 'symptom-handling-img.png',
  },
  {
    type: [6],
    img: 'dosing-tracking-img.png',
  },
  {
    type: [7],
    img: 'outpatient-follow-up-img.png',
  },
  {
    type: [8],
    img: 'hospitalization-follow-up-img.png',
  },
  {
    type: [9, 11],
    img: 'follow-up-reminder-img.png',
  },
  {
    type: [10],
    img: 'customize-to-do-img.png',
  },
  {
    type: [12],
    img: 'inquiry-reply-img.png',
  },
  {
    type: [14, 15],
    img: 'clincal-event-img.png',
  },
  {
    type: [17],
    img: 'doubt-img.png',
  },
  {
    type: [1001],
    img: 'patient-erollment-time-img.png',
  },
  {
    type: [1002],
    img: 'patient-erollment-quality-img.png',
  },
  {
    type: [1003],
    img: 'erollment-advise-time-img.png',
  },
  {
    type: [1004],
    img: 'erollment-advise-quality-img.png',
  },
  {
    type: [22],
    img: 'test-img.png',
  },
  {
    type: [20],
    img: 'second-into-hospital-img.png',
  },
  {
    type: [23],
    img: 'medicine-check-img.png',
  },
  {
    type: [24],
    img: 'subscribe-talk-img.png',
  },
  {
    type: [14],
    img: 'bed-track-img.png',
  },
]);

// 动态导入所有图片
const images = import.meta.glob('@/assets/imgs/todo/*.png', {
  eager: true,
  import: 'default',
});

// 计算属性，返回真实图片路径
const typedImg = computed((): string => {
  const found = typedImgList.value.find(item => item.type.includes(type));
  const imgName = found ? found.img : 'patient-enrollment-img.png';
  const key = Object.keys(images).find(k => k.endsWith(imgName));
  return key ? (images[key] as string) : '';
});
</script>
