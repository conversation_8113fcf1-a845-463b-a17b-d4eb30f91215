<template>
  <span class="dialog-footer">
    <el-button
      :loading="confirmLoading"
      type="primary"
      class="sure-btn w-76 h-32"
      @click="sure"
    >
      <span
        v-if="useTodoInfo.qualityRecord.length && useTodoInfo.status === 233"
      >
        继续完成
      </span>
      <span v-else>确定</span>
    </el-button>
    <el-button class="cancel-btn w-76 h-32" @click="cancel">取消</el-button>
  </span>
</template>
<script setup lang="ts">
import useTodo from '@/store/module/useTodo';

const useTodoInfo = useTodo();
interface IProps {
  confirmLoading: boolean;
}
defineProps<IProps>();
const emit = defineEmits(['submit', 'cancel']);
let sure = () => {
  emit('submit');
};
let cancel = () => {
  emit('cancel');
};
</script>
