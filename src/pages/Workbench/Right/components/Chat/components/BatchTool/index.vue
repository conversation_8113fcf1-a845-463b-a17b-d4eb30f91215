<template>
  <div :class="['wrap', focused ? 'focused' : '']">
    <div class="item" @click.stop="download">
      <img :src="downloadImg" />
      <span>下载</span>
    </div>
    <div class="item" @click.stop="() => save()">
      <img :src="saveImg" />
      <span>定向保存</span>
    </div>
    <div class="close" @click.stop="close">
      <el-icon>
        <i-ep-close />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import saveImg from '@/assets/imgs/chat/save.png';
import downloadImg from '@/assets/imgs/chat/download.png';

interface IProps {
  focused: boolean;
  download: () => void;
  save: () => void;
  close: () => void;
}

defineProps<IProps>();
defineOptions({
  name: 'BatchTool',
});
</script>

<style scoped lang="less">
.wrap {
  position: relative;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  .item {
    margin: 0 10px;
    height: 48px;
    display: flex;
    align-items: center;
    color: #3a4762;
    font-size: 14px;
    cursor: pointer;
    &:first-child {
      margin-right: 40px;
    }
    > img {
      margin-right: 4px;
      width: 16px;
      height: 16px;
    }
  }
}
.close {
  position: absolute;
  cursor: pointer;
  right: 16px;
  top: 16px;
}
.focused {
  .item {
    margin-top: 140px;
    font-size: 16px;
    flex-direction: column;
    &:first-child {
      margin-right: 40px;
    }
    > img {
      width: 24px;
      height: 24px;
      margin-bottom: 2px;
    }
  }
}
</style>
