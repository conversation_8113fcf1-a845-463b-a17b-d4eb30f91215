<template>
  <div>
    <Dialog :visible="dialogVisible" @close="close">
      <template #header>
        <img
          src="@/assets/imgs/chat/expressions.png"
          alt=""
          class="expressionsImg"
        />
        常用语
      </template>
      <div class="content">
        <div class="searchBox">
          <el-checkbox
            v-model="onlyMine"
            label="仅显示个人常用"
            size="large"
            @change="checkboxChange"
          />
          <el-input
            v-model.trim="keywords"
            class="phraseInput"
            :suffix-icon="Search"
            placeholder="请输入关键字模糊检索"
            @input="searchChange"
          />
        </div>
        <div v-loading="listLoading" class="phraseList">
          <el-table
            :data="phraseTableData"
            height="300"
            stripe
            @row-click="rowClick"
          >
            <el-table-column type="index" width="80" label="序号">
              <template #default="scope">
                <span>
                  {{
                    (currentPage - 1) * pageSize + (scope.$index + 1) > 9
                      ? (currentPage - 1) * pageSize + (scope.$index + 1)
                      : '0' +
                        ((currentPage - 1) * pageSize + (scope.$index + 1))
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="类别" width="120">
              <template #default="scope">
                <span>{{ scope.row.type === 1 ? '通用' : '个人' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="speechcraftContent"
              label="常用语"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div
                  class="w-full max-h-33 whitespace-nowrap"
                  v-text="filterHTML(scope.row.speechcraftContent)"
                ></div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="showOperationColumn"
              width="120"
              label="操作"
            >
              <template #default="{ row }">
                <div v-if="row.type === 2">
                  <el-button type="text" @click.stop="editHandle(row)">
                    编辑
                  </el-button>
                  <el-button type="text" @click.stop="deleteHandle(row)">
                    <span class="text-red-400">删除</span>
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
              :current-page="currentPage"
              :page-sizes="[10, 15, 20, 30]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
      <EditExpression
        :visible="showEditExpression"
        :content="currentRow?.speechcraftContent"
        :max-char-count="maxCharCount"
        @close="closeEditDialog"
        @save="handleSaveExpression"
      />
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue';
import Dialog from '../Dialog/index.vue';
import EditExpression from './editExpression.vue';
import { dialogTip } from '@/pages/Workbench/Main/Management/hooks';
import type {
  IApiPatientConversationSpeechcraftList,
  IApiPatientConversationSpeechcraftListSpeechcraftList,
} from '@/interface/type';
import {
  getSpeechCraftList,
  editSpeechCraft,
  deleteSpeechCraft,
} from '@/api/chat';
import { useHandleData } from '@/hooks';
import { debounce } from 'lodash-es';

interface IProps {
  expressionsVisible: boolean;
}
defineOptions({
  name: 'UsefulExpression',
});
const props = defineProps<IProps>();
const emits = defineEmits(['close', 'change-text']);
const dialogVisible = ref(false);
const keywords = ref('');
const onlyMine = ref(false);
const pageSize = ref(10);
const currentPage = ref(1);
const total = ref(0);
const listLoading = ref(false);
const phraseTableData = ref<any[]>([]);

const showOperationColumn = computed(
  () =>
    phraseTableData.value &&
    phraseTableData.value.length &&
    phraseTableData.value.some(it => it.type === 2)
);

const showEditExpression = ref(false);

const currentRow =
  ref<IApiPatientConversationSpeechcraftListSpeechcraftList | null>(null);

const maxCharCount = ref(2500); // 最大字符数限制
// 增加一个显示过滤

const filterHTML = (_html = '') =>
  _html
    .replaceAll('<b>', '')
    .replaceAll('</b>', '')
    .replaceAll('<i>', '')
    .replaceAll('</i>', '')
    .replaceAll('<br>', '')
    .replaceAll('</br>', '')
    .replaceAll('<br/>', '')
    .replaceAll('<u>', '')
    .replaceAll('</u>', '')
    .replaceAll('</div>', '')
    .replaceAll('<div>', '');

const editHandle = row => {
  currentRow.value = row;
  showEditExpression.value = true;
};

// 关闭编辑对话框
const closeEditDialog = () => {
  showEditExpression.value = false;
};

// 处理保存表达式
const handleSaveExpression = async (content: string) => {
  if (!currentRow.value?.speechcraftId) return;
  await editSpeechCraft({
    speechcraftId: currentRow.value.speechcraftId,
    speechcraftContent: content,
  });
  dialogTip('保存常用语成功', 'success');
  showEditExpression.value = false;
  getExpressionList();
};

const deleteHandle = async row => {
  if (!row.speechcraftId) return;
  await useHandleData(
    deleteSpeechCraft,
    {
      speechcraftId: row.speechcraftId,
    },
    '是否删除常用语？',
    '删除后无法恢复'
  );
  currentPage.value = 1;
  getExpressionList();
};

const close = () => {
  onlyMine.value = false;
  pageSize.value = 10;
  currentPage.value = 1;
  keywords.value = '';
  emits('close');
};
const searchChange = debounce(() => {
  currentPage.value = 1;
  getExpressionList();
}, 100);
const checkboxChange = () => {
  currentPage.value = 1;
  getExpressionList();
};
const rowClick = (row: any) => {
  emits('change-text', { val: row.speechcraftContent, type: 'expression' });
};
const getExpressionList = async () => {
  const params = {
    keyword: keywords.value,
    onlyMine: onlyMine.value ? 2 : 1,
    pageSize: pageSize.value,
    page: currentPage.value,
  };
  listLoading.value = true;
  let res: IApiPatientConversationSpeechcraftList | null = null;
  try {
    res = await getSpeechCraftList(params);
  } catch (error) {
    console.log(error);
  } finally {
    phraseTableData.value = res?.speechcraftList ?? [];
    total.value = res?.totals ?? 0;
    listLoading.value = false;
  }
};
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getExpressionList();
};
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  getExpressionList();
};
watch(
  () => props.expressionsVisible,
  val => {
    dialogVisible.value = val;
    if (val) {
      getExpressionList();
    }
  }
);
</script>
<style scoped lang="less">
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}
.searchBox {
  height: 32px;
  display: flex;
  align-items: center;
  .phraseValue {
    width: 120px;
  }
  .phraseInput {
    width: 240px;
    margin-left: 8px;
  }
}
</style>
