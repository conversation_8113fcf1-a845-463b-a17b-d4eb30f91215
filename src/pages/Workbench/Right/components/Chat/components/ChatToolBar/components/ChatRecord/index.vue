<template>
  <el-dialog
    v-model="dialogVisible"
    destroy-on-close
    :width="600"
    draggable
    title="历史记录"
    :modal="false"
    class="chat-record-dialog"
    @close="close"
  >
    <div v-loading="isLoading" class="content">
      <el-tabs
        v-model="activeName"
        type="card"
        class="demo-tabs"
        @tab-change="handleChange"
      >
        <el-tab-pane label="文字/语音" name="first">
          <el-input
            v-model="searchWords"
            clearable=""
            :style="{ width: '360px' }"
            :prefix-icon="Search"
            placeholder="最多十个汉字"
            @input="inputChange"
            @keyup.enter="enterHandler"
          />
          <LoadMore
            v-if="data.length"
            :height="maxHeight - 32"
            @load="loadHandler"
          >
            <SearchCard
              v-for="(item, index) in data"
              :key="index"
              :data="item"
              class="dialog-card-item"
              @click="() => clickHandler(item)"
            >
              <div class="flex-1 items-center ml-16 overflow-hidden">
                <div class="font-bold">{{ item.senderTitle }}</div>
                <div
                  class="flex justify-between h-26 text-[14px] text-[#7A8599] pt-6"
                >
                  <!-- <Text :custom-text="item.chatDetail" /> -->
                  <span
                    class="flex-1 leading-[20px] whitespace-nowrap text-ellipsis overflow-hidden"
                    v-html="item.chatDetail"
                  ></span>
                  <div class="shrink-0 pl-8">
                    {{ formatDate(item.chatTime) }}
                  </div>
                </div>
              </div>
            </SearchCard>
          </LoadMore>
          <div v-else class="empty">
            {{ isLoading ? '' : '暂无记录!' }}
          </div>
        </el-tab-pane>
        <el-tab-pane label="图片/视频" name="second">
          <LoadMore v-if="data.length" :height="maxHeight" @load="loadHandler">
            <div
              v-for="(item, index) in data"
              :key="index"
              class="media-item"
              @click="() => clickHandler(item)"
            >
              <div class="pb-8 pt-24">
                <span class="font-bold text-[#3a4762]">{{
                  item.senderTitle
                }}</span>
                <span class="ml-12">{{ formatDate(item.chatTime) }}</span>
              </div>
              <div class="item-wrap">
                <img
                  v-if="item.type === 'image'"
                  :src="item.chatDetail"
                  :style="{
                    width: '120px',
                    height: (120 / item.file.w) * item.file.h + 'px',
                  }"
                />
                <video
                  v-if="item.type === 'video'"
                  class="video"
                  controls
                  controlslist="nodownload"
                  :src="item.chatDetail"
                ></video>
              </div>
            </div>
          </LoadMore>
          <div v-else class="empty">{{ isLoading ? '' : '暂无记录!' }}</div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { escapeHtml, escapeHilightChatRecords, formatDate } from '@/utils';
import { Search } from '@element-plus/icons-vue';
// import Text from '@/components/Text/index.vue';
import SearchCard from '@/pages/Workbench/Left/components/Card/SearchCard.vue';
import LoadMore from '@/pages/Workbench/Left/components/LoadMore/index.vue';
import store from '@/store';
import { CHAT_MEMBER_TYPES, CHAT_ROLE_MAP } from '@/constant/chat';
import { debounce } from 'lodash-es';
import { getPatientChatList } from '@/api/userList';
interface IProps {
  visible: boolean;
  teamId: string | number;
}
defineOptions({
  name: 'ChatRecord',
});
const emits = defineEmits(['close']);
const props = defineProps<IProps>();

const imStore = store.useIM();
const dialogVisible = ref(false);
const maxHeight = ref(300);
const activeName = ref('first');
const searchWords = ref('');
const data = ref<any[]>([]);
const pageNumber = ref(1);
const total = ref(0);
const loading = ref(false);

const close = () => {
  emits('close');
};
const getSender = (msg: any) => {
  if (msg.istofrom === 'right') return '我';
  if (msg.from.startsWith('user@')) {
    const name = msg.custom?.senderName ?? '';
    return msg.custom?.sender === 'null'
      ? `${name}(本人)`
      : `${name}(${msg.custom?.sender})`;
  }
  if (msg.from.startsWith('doctor@')) {
    return msg.custom?.sender + '(专家)';
  }
  if (msg.custom?.senderRole) {
    const roleName =
      CHAT_MEMBER_TYPES[
        msg.custom.senderRole as keyof typeof CHAT_MEMBER_TYPES
      ];
    return `${msg.custom.senderName}${roleName ? '(' + roleName + ')' : ''}`;
  }
  if (msg.from.startsWith('assistant@')) {
    return msg.fromNick + '(医生)';
  }
  return '';
};
const isLoading = computed(() => {
  return loading.value || imStore.recordLoading;
});
const clickHandler = (item: any) => {
  imStore.findMsg({
    idServer: item.chatId,
    time: item.chatTime,
  });
  close();
};
const enterHandler = () => {
  pageNumber.value = 1;
  searchRecordsHandler();
};
const inputChange = (val: string) => {
  if (val) {
    // searchRecordsHandler();
  } else {
    pageNumber.value = 1;
    imStore.getHistoryRecordByTypes(['text', 'audio']);
  }
};
const handleChange = (val: string) => {
  activeName.value = val;
  searchWords.value = '';
  pageNumber.value = 1;
  data.value = [];
  imStore.hasRecordMore = true;
  if (val === 'first') {
    imStore.getHistoryRecordByTypes(['text', 'audio']);
  } else {
    imStore.getHistoryRecordByTypes(['image', 'video']);
  }
};
const loadHandler = () => {
  console.log('聊天记录滚动到底部，准备请求下一页数据');
  if (activeName.value === 'first') {
    if (searchWords.value) {
      pageNumber.value += 1;
      queryRecords();
    } else {
      console.log('【nim】:请求下一页文字和语音记录');
      imStore.getMoreHistoryRecordByTypes(['text', 'audio']);
    }
  } else {
    console.log('【nim】:请求下一页图片和视频记录');
    imStore.getMoreHistoryRecordByTypes(['image', 'video']);
  }
};
const searchRecordsHandler = debounce(() => {
  queryRecords();
}, 100);
const queryRecords = async () => {
  console.log('关键字搜素请求文字和语音记录', pageNumber.value);
  const params = {
    pageNumber: pageNumber.value,
    pageSize: 10,
    teamNumber: props.teamId,
    patientChat: searchWords.value,
    recordTypes: ['TEXT'],
  };
  loading.value = true;
  try {
    const res = await getPatientChatList(params as any);
    total.value = res.totalPatientChats ?? 0;
    const result = transformSearchMsgs(res.patientChats);
    if (pageNumber.value > 1) {
      data.value.push(...result);
    } else {
      data.value = result;
    }
  } catch (error) {
    console.log('$debug: error', error);
  }
  loading.value = false;
};
const getSearchMsgTitle = (chat: any) => {
  const senderUserName = chat?.sendUserName;
  const sendUserType = chat?.sendUserType;
  const curRole = (window as any)._currentRole;

  const _sendUserName =
    CHAT_ROLE_MAP[sendUserType as keyof typeof CHAT_ROLE_MAP] === curRole
      ? '我'
      : senderUserName;
  return _sendUserName;
};
const transformSearchMsgs = (msgs: any) => {
  const { teamType, teamName = '' } = getCurTeam() ?? {};
  const res = msgs.map(v => ({
    ...v,
    chatDetail: escapeHilightChatRecords(v.chatDetail),
    senderTitle: getSearchMsgTitle(v),
    patientName: teamName,
    teamType,
  }));
  return res;
};
const getCurTeam = () => {
  return imStore.patientTeamList.find(v => v.teamNumber === imStore.curTeamId);
};
const transformMsgs = (msgs: any) => {
  const { teamType, teamName } = getCurTeam() ?? {};
  const res = msgs.map(v => ({
    chatId: v.idServer,
    chatTime: v.time,
    chatDetail: v.type === 'audio' ? '[语音]' : escapeHtml(v.text),
    file: v.file,
    type: v.type,
    senderTitle: getSender(v),
    patientName: teamName,
    teamType,
  }));
  return res;
};
watch(
  () => imStore.historyRecords,
  val => {
    data.value = transformMsgs(val);
  },
  { deep: true }
);
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
    activeName.value = 'first';
    searchWords.value = '';
    imStore.hasRecordMore = true;
    imStore.getHistoryRecordByTypes(['text', 'audio']);
  }
);
</script>

<style scoped lang="less">
.content {
  padding: 12px 24px 0 24px;
  height: 400px;
  :deep(.el-tabs__item) {
    background: #f7f8fa;
    color: #323233;
    &.is-active {
      background: #fff;
    }
  }
  :deep(.el-tabs__content) {
    min-height: 200px;
  }
}
.dialog-card-item {
  font-size: 14px;
  background: unset;
  height: 78px;
  margin-bottom: 0;
  box-shadow: unset;
  border-radius: 6px;
  display: flex;
  align-items: center;
  &:after {
    position: absolute;
    content: '/';
    font-size: 0;
    width: calc(100% - 72px);
    height: 0;
    border-bottom: 1px solid #e9e8eb;
    right: 0;
    bottom: 0px;
  }
}
.media-item {
  .item-wrap {
    background: #f7f8fa;
    border-radius: 4px;
    padding: 8px;
    img {
      width: 120px;
      border-radius: 4px;
    }
    .video {
      width: 160px;
      height: 160px;
      object-fit: cover;
    }
  }
}
.empty {
  text-align: center;
  padding: 120px 0;
}
</style>
<style lang="less">
.chat-record-dialog {
  .el-dialog__header {
    margin: 0;
    padding: 16px 0 12px 24px;
    font-weight: bold;
    border-bottom: 1px solid #e9e8eb;
  }
  .el-dialog__body {
    padding: 0;
  }
}
</style>
