<template>
  <Dialog
    :visible="visible"
    :width="600"
    :append-to-body="true"
    @close="handleClose"
  >
    <template #header>
      <img
        src="@/assets/imgs/chat/expressions.png"
        alt=""
        class="expressionsImg"
      />
      编辑个人常用语
    </template>

    <div class="rich-text-editor">
      <!-- 编辑区域 -->
      <div
        ref="contentEditableRef"
        contenteditable="true"
        class="editor-content"
        @input="handleInput"
        @paste="handlePaste"
        @keydown="handleKeydown"
        @mouseup="updateToolbar"
        @keyup="updateToolbar"
      ></div>
    </div>

    <div class="flex justify-between items-center mt-12">
      <span
        class="text-sm"
        :class="{
          'text-gray-500': currentCharCount < maxCharCount * 0.8,
          'text-yellow-500':
            currentCharCount >= maxCharCount * 0.8 &&
            currentCharCount < maxCharCount,
          'text-red-500': currentCharCount >= maxCharCount,
        }"
      >
        字符数: {{ currentCharCount }}/{{ maxCharCount }}
      </span>
      <div class="flex gap-8">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveContent">保存</el-button>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { nextTick } from 'vue';
import Dialog from '../Dialog/index.vue';
import { ElMessage } from 'element-plus';

interface IProps {
  visible: boolean;
  content?: string;
  maxCharCount?: number;
}

interface IEmits {
  (e: 'close'): void;
  (e: 'save', content: string): void;
}

defineOptions({
  name: 'EditExpression',
});

const props = withDefaults(defineProps<IProps>(), {
  content: '',
  maxCharCount: 500,
});

const emits = defineEmits<IEmits>();

const contentEditableRef = ref<HTMLElement>();
const editContent = ref('');
const currentCharCount = computed(() => editContent.value.length);

// 富文本工具栏状态
const isBold = ref(false);
const isItalic = ref(false);
const isUnderline = ref(false);

// 初始化编辑器内容
const initEditorContent = () => {
  if (contentEditableRef.value && props.visible) {
    editContent.value = props.content;
    contentEditableRef.value.innerHTML = props.content;
  }
};

// 组件挂载时初始化内容
onMounted(() => {
  if (props.visible) {
    nextTick(() => {
      initEditorContent();
    });
  }
});

// 更新工具栏状态
const updateToolbar = () => {
  if (contentEditableRef.value) {
    isBold.value = document.queryCommandState('bold');
    isItalic.value = document.queryCommandState('italic');
    isUnderline.value = document.queryCommandState('underline');
  }
};

// 处理输入事件
const handleInput = (event: Event) => {
  const target = event.target as HTMLElement;
  const text = target.textContent || '';

  // 限制字符数（基于纯文本长度）
  if (text.length > props.maxCharCount) {
    // 截断纯文本内容
    const truncated = text.slice(0, props.maxCharCount);
    // 用纯文本替换内容（保留格式可选，这里直接替换为纯文本）
    target.innerText = truncated;
    // 将光标移到末尾
    const range = document.createRange();
    const selection = window.getSelection();
    range.selectNodeContents(target);
    range.collapse(false);
    selection?.removeAllRanges();
    selection?.addRange(range);
  }

  editContent.value = target.innerHTML;
};

// 处理粘贴事件，只保留纯文本
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault();
  const text = event.clipboardData?.getData('text/plain') || '';
  document.execCommand('insertText', false, text);
};

// 处理键盘事件，限制输入
const handleKeydown = (event: KeyboardEvent) => {
  const target = event.target as HTMLElement;
  const text = target.innerHTML || '';

  // 如果已达到最大字符数，阻止输入（除了删除键）
  if (
    text.length >= props.maxCharCount &&
    ![
      'Backspace',
      'Delete',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown',
      'Tab',
    ].includes(event.key)
  ) {
    event.preventDefault();
  }
};

// 保存内容
const saveContent = () => {
  if (contentEditableRef.value) {
    const content = contentEditableRef.value.innerHTML;

    // 检查内容是否为空或只包含空白字符
    const trimmedContent = content.replace(/<[^>]*>/g, '').trim();
    if (!trimmedContent) {
      ElMessage.warning('请输入内容后再保存');
      return;
    }
    if (content.length > props.maxCharCount) {
      ElMessage.error('字符内容超出限制,请检查后保存');
      return;
    }
    emits('save', content);
  }
};

// 关闭弹窗
const handleClose = () => {
  emits('close');
};

// 监听visible变化，初始化内容
watch(
  () => props.visible,
  val => {
    if (val) {
      // 等待弹窗完全显示后再初始化内容
      setTimeout(() => {
        initEditorContent();
      }, 100);
    }
  },
  { immediate: true }
);

// 监听content变化，更新编辑器内容
watch(
  () => props.content,
  newContent => {
    if (props.visible && contentEditableRef.value) {
      editContent.value = newContent;
      contentEditableRef.value.innerHTML = newContent;
    }
  }
);
</script>

<style scoped lang="less">
// 富文本编辑器样式
.rich-text-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;

  .toolbar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;
    gap: 8px;

    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 4px;

      &:not(:last-child) {
        border-right: 1px solid #dcdfe6;
        padding-right: 8px;
      }
    }

    .el-button {
      padding: 4px 8px;
      min-width: 32px;

      i {
        font-size: 14px;
      }
    }
  }

  .editor-content {
    min-height: 120px;
    max-height: 300px;
    padding: 8px;
    outline: none;
    border: none;
    resize: vertical;
    overflow-y: auto;
    word-wrap: break-word;
    white-space: pre-wrap;

    &:focus {
      box-shadow: none;
    }

    &:empty:before {
      content: '请输入内容...';
      color: #c0c4cc;
      pointer-events: none;
    }

    // 富文本内容样式
    p {
      margin: 0 0 8px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul,
    ol {
      margin: 8px 0;
      padding-left: 20px;
    }

    li {
      margin: 4px 0;
    }
  }
}
</style>
