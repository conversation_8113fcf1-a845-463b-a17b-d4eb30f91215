<template>
  <div>
    <Dialog :visible="dialogVisible" @close="close">
      <template #header>
        <img src="@/assets/imgs/chat/answer.png" />
        问题答复
      </template>
      <div class="content">
        <div class="searchBox">
          <el-select
            v-model="phraseValue"
            class="phraseValue"
            placeholder="请选择"
            filterable
            clearable
            @change="changePhrase"
          >
            <el-option
              v-for="item in phraseOptions"
              :key="item.advisoryTypeId"
              :label="item.name"
              :value="item.advisoryTypeId"
            />
          </el-select>
          <el-input
            v-model.trim="phraseInput"
            class="phraseInput"
            :suffix-icon="Search"
            placeholder="请输入关键字模糊检索"
            @input="searchPhrase"
          />
        </div>
        <div class="phraseList">
          <el-table :data="phraseTableData" height="300" stripe>
            <el-table-column type="index" width="80" label="序号">
              <template #default="scope">
                <span>
                  {{
                    (currentPage - 1) * pageSize + (scope.$index + 1) > 9
                      ? (currentPage - 1) * pageSize + (scope.$index + 1)
                      : '0' +
                        ((currentPage - 1) * pageSize + (scope.$index + 1))
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="speechcraftContent"
              label="问题"
              show-overflow-tooltip
            >
              <template #default="scope">
                <span>{{ scope.row.questionsContent }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="speechcraftContent"
              label="答案"
              show-overflow-tooltip
              width="300"
            >
              <template #default="scope">
                <div class="answerBox">
                  <el-popover
                    v-for="(item, index) in scope.row.answerList"
                    :key="index"
                    placement="top-end"
                    width="400"
                    effect="dark"
                    trigger="hover"
                  >
                    <div>{{ item.answerContent }}</div>
                    <template #reference>
                      <div
                        class="itemAnswer"
                        @click="changeCell(item.answerContent)"
                      >
                        答案{{ getChinaNum(index + 1) }}
                      </div>
                    </template>
                  </el-popover>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
              :current-page="currentPage"
              :page-sizes="[10, 15, 20, 30]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue';
import { getQuestionType, getQuestinAnwser } from '@/api/chat';
import { debounce } from 'lodash-es';
import Dialog from '../Dialog/index.vue';
import { IApiPatientConversationQuestionTypeQuestionTypeList } from '@/interface/type';
interface IProps {
  questionAnswerVisible: boolean;
}
defineOptions({
  name: 'QuestionAnswer',
});
const props = defineProps<IProps>();
const emits = defineEmits(['close', 'change-text']);
const dialogVisible = ref(false);
const phraseValue = ref('');
const phraseInput = ref('');
const phraseOptions = ref<
  IApiPatientConversationQuestionTypeQuestionTypeList[]
>([]);
const pageSize = ref(10);
const currentPage = ref(1);
const total = ref(0);
const phraseTableData = ref<any[]>([]);

const close = () => {
  phraseValue.value = '';
  phraseInput.value = '';
  pageSize.value = 10;
  currentPage.value = 1;
  emits('close');
};
const changePhrase = () => {
  getSpeechcraftList();
};
const searchPhrase = debounce(() => {
  getSpeechcraftList();
}, 100);
const changeCell = (row: string) => {
  emits('change-text', { val: row, type: 'questionAnswer' });
};
const getSpeechcraftType = async () => {
  const res = await getQuestionType();
  phraseOptions.value = res.questionTypeList ?? [];
};
const getSpeechcraftList = async () => {
  const params = {
    keyword: phraseInput.value,
    advisoryTypeId: phraseValue.value as unknown as number,
    pageSize: pageSize.value,
    page: currentPage.value,
  };
  const res = await getQuestinAnwser(params);
  phraseTableData.value = res.questionAnswerList ?? [];
  total.value = res.total ?? 0;
};
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getSpeechcraftList();
};
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  getSpeechcraftList();
};
const getChinaNum = computed(() => (num: number) => {
  const changeNum = [
    '零',
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
    '七',
    '八',
    '九',
  ];
  const unit = ['', '十', '百'];
  const getWan = (temp: number) => {
    const strArr = temp.toString().split('').reverse();
    let newNum = '';
    for (var i = 0; i < strArr.length; i++) {
      newNum =
        (i == 0 && strArr[i] === '0'
          ? ''
          : i > 0 && strArr[i] === '0' && strArr[i - 1] === '0'
            ? ''
            : changeNum[Number(strArr[i])] +
              (strArr[i] === '0' ? unit[0] : unit[i])) + newNum;
    }
    return newNum;
  };
  const overWan = Math.floor(num / 100);
  let noWan = (num % 100).toString();
  if (noWan.length < 2) {
    noWan = '0' + noWan;
  }
  let strr = overWan
    ? getWan(overWan) + '百' + getWan(Number(noWan))
    : getWan(num);
  if (strr.split('')[0] == '一') {
    let showNum = '';
    if (strr == '一') {
      showNum = strr.substring(0);
    } else {
      showNum = strr.substring(1);
    }
    return showNum;
  } else {
    let showNum = overWan
      ? getWan(overWan) + '百' + getWan(Number(noWan))
      : getWan(num);
    return showNum;
  }
});
watch(
  () => props.questionAnswerVisible,
  val => {
    dialogVisible.value = val;
    if (val) {
      getSpeechcraftType();
      getSpeechcraftList();
    }
  }
);
</script>

<style scoped lang="less">
.content {
  width: 100%;
  .searchBox {
    height: 32px;
    background: #ffffff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    .phraseValue {
      width: 120px;
    }
    .phraseInput {
      width: 240px;
      margin-left: 8px;
    }
  }
  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}
.answerBox {
  display: flex;
  flex-wrap: wrap;
  .itemAnswer {
    padding: 6px 8px;
    background: #f7f8fa;
    border-radius: 2px;
    border: 1px solid #dcdee0;
    font-size: 14px;
    font-family:
      PingFangSC-Medium,
      PingFang SC;
    font-weight: 600;
    color: #0a73e4;
    margin-right: 8px;
    margin-bottom: 8px;
    cursor: pointer;
  }
  .itemAnswer:hover {
    border: 1px solid #0a73e4;
    background: #ecf4fc;
  }
}
</style>
