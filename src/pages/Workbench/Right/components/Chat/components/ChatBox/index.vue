<template>
  <div class="h-150 bg-[#F6F8FB] rounded-b-[4px]">
    <div
      id="chat-input"
      ref="boxRef"
      class="chat-input"
      contentEditable="true"
      placeholder="请输入内容"
      @paste="pasteHandler"
      @input="changeHandler"
      @click="handleClick"
    ></div>
    <div class="btn-wrap">
      <span class="send-btn" @click.stop="sendMsgHandler">发送</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useEventListener } from '@vueuse/core';
import { convertBase64UrlToBlob } from '@/utils';
import store from '@/store';

const imStore = store.useIM();
const boxRef = shallowRef<HTMLElement>();

interface AtMentionUser {
  id: number | string;
  name: string;
  isAll?: boolean;
  count?: number;
}

interface AtMentionPosition {
  range: Range;
  atIndex: number;
}

const isAtMsg = ref<boolean>(false);

class AtMention {
  editor: HTMLElement;
  panel: HTMLDivElement | null;
  users: AtMentionUser[];
  currentAtPos: AtMentionPosition | null;
  header: HTMLDivElement | null;
  title: HTMLSpanElement | null;
  userList: HTMLDivElement | null;

  constructor(editorElement: HTMLElement) {
    this.editor = editorElement;
    this.panel = null;
    this.users = [];
    this.currentAtPos = null;
    this.header = null;
    this.title = null;
    this.userList = null;
    this.init();
  }

  init() {
    this.createPanel();
    document.addEventListener('click', this.handleOutsideClick.bind(this));
  }

  handleOutsideClick(e: MouseEvent) {
    if (this.panel && !this.panel.contains(e.target as Node)) {
      this.hidePanel();
    }
  }

  createPanel() {
    this.panel = document.createElement('div');
    this.panel.className = 'at-panel';
    this.panel.style.display = 'none';

    // 头部 - 标题
    this.header = document.createElement('div');
    this.header.className = 'at-header';

    this.title = document.createElement('span');
    this.title.className = 'at-title';
    this.title.textContent = '群成员';

    this.header.appendChild(this.title);

    // 用户列表
    this.userList = document.createElement('div');
    this.userList.className = 'at-user-list';

    this.panel.appendChild(this.header);
    this.panel.appendChild(this.userList);

    document.body.appendChild(this.panel);
  }

  handleInput() {
    const selection = window.getSelection();
    if (!selection || !selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    const offset = range.startOffset;

    const textBeforeCursor = this.getTextBeforeCursor(range);

    const atIndex = textBeforeCursor.lastIndexOf('@');
    if (atIndex >= 0 && offset === atIndex + 1) {
      this.showPanel(range, atIndex);
      return;
    }

    this.hidePanel();
  }

  getTextBeforeCursor(range: Range) {
    const node = range.startContainer;
    const offset = range.startOffset;

    if (node.nodeType === Node.TEXT_NODE) {
      return node.textContent?.slice(0, offset) || '';
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      let text = '';
      for (let i = 0; i < offset; i++) {
        const child = node.childNodes[i];
        if (child.nodeType === Node.TEXT_NODE) {
          text += child.textContent;
        } else if (child.nodeType === Node.ELEMENT_NODE) {
          text += child.textContent;
        }
      }
      return text;
    }
    return '';
  }

  handleClick(e: MouseEvent) {
    const target = e.target as HTMLElement;
    const atTag = this.findAtTag(target);
    if (atTag) {
      const range = document.createRange();
      range.selectNode(atTag);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  }

  showPanel(range: Range, atIndex: number) {
    this.currentAtPos = { range, atIndex };
    this.users = [...allList.value, ...personList.value].sort(
      (a, b) => (b.isAll ? 1 : 0) - (a.isAll ? 1 : 0)
    );
    this.renderUserList();
    this.positionPanel();
  }

  renderUserList() {
    if (!this.userList) return;
    this.userList.innerHTML = '';

    if (this.users.length === 0) {
      const empty = document.createElement('div');
      empty.className = 'at-empty';
      empty.textContent = '无成员';
      this.userList.appendChild(empty);
      return;
    }

    this.users.forEach((user: any) => {
      const item = document.createElement('div');
      item.className = 'at-item';
      item.dataset.userId = user.id;

      const avatar = document.createElement('div');
      avatar.className = 'at-avatar flex items-center justify-center w-28 h-28';
      avatar.textContent = user.isAll ? '@' : user.name.slice(0, 1);
      item.appendChild(avatar);

      const name = document.createElement('span');
      name.className = 'flex-1 at-name';
      name.textContent = user.isAll ? `${user.name}(${user.count})` : user.name;
      item.appendChild(name);

      item.addEventListener('click', () => {
        this.selectUser(user);
        isAtMsg.value = true;
      });

      this.userList?.appendChild(item);
    });

    if (this.panel && imStore.isHrtTeamChat) this.panel.style.display = 'block';
  }

  positionPanel() {
    if (!this.currentAtPos || !this.panel) return;
    const box = document.querySelector('.at-panel') as HTMLElement;
    const offsetHeight = box?.offsetHeight || 0;
    const rect = this.currentAtPos.range.getBoundingClientRect();
    const screenWidth = window.screen.width;
    const left = rect?.left || 0;
    this.panel.style.left = `${Math.min(left, screenWidth - 200)}px`;
    this.panel.style.top = `${rect.bottom - offsetHeight - 20}px`;
  }

  hidePanel() {
    if (this.panel) this.panel.style.display = 'none';
    this.currentAtPos = null;
  }

  selectUser(user: AtMentionUser) {
    this.insertAtTag(user);
    this.hidePanel();
    if (this.editor) this.editor.focus();
  }

  insertAtTag(user: AtMentionUser) {
    if (!this.currentAtPos) return;
    const { range, atIndex } = this.currentAtPos;
    const textNode = range.startContainer;
    const text = textNode.textContent || '';

    const atTag = document.createElement('span');
    atTag.className = 'at-tag';
    atTag.contentEditable = 'false';
    atTag.dataset.userId = String(user.id);

    const atSymbol = document.createElement('span');
    atSymbol.className = 'at-symbol';
    atSymbol.textContent = '@';

    const userName = document.createElement('span');
    userName.className = 'at-username';
    userName.textContent = user.isAll ? '所有人' : user.name;

    atTag.appendChild(atSymbol);
    atTag.appendChild(userName);

    const newText = text.substring(0, atIndex);
    const afterText = text.substring(atIndex + 1);

    const parent = textNode.parentNode;
    const nextSibling = textNode.nextSibling;

    textNode.textContent = newText;

    if (parent) {
      parent.insertBefore(atTag, nextSibling);

      if (afterText) {
        const afterNode = document.createTextNode(afterText);
        parent.insertBefore(afterNode, atTag.nextSibling);
      }

      this.setCursorAfterAtTag(atTag, parent);
    }
  }

  setCursorAfterAtTag(lastTag: Node | null, parent: Node | null) {
    const newRange = document.createRange();
    const spaceNode = document.createTextNode('\u200B');

    if (parent && lastTag && 'nextSibling' in lastTag) {
      parent.insertBefore(spaceNode, lastTag.nextSibling);
      newRange.setStart(spaceNode, 0);
      newRange.setEnd(spaceNode, 0);
    } else {
      if (this.editor) {
        this.editor.appendChild(spaceNode);
        newRange.setStart(spaceNode, 0);
        newRange.setEnd(spaceNode, 0);
      }
    }

    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(newRange);
    }
  }

  findAtTag(node: Node | null) {
    if (!node) return null;
    if (
      node.nodeType === Node.ELEMENT_NODE &&
      (node as Element).classList.contains('at-tag')
    ) {
      return node as HTMLElement;
    }
    return null;
  }
}
const atMention = shallowRef<AtMention>();

const pasteHandler = (e: any) => {
  const files = e.clipboardData?.files;
  if (files.length) {
    e.preventDefault();
    for (const file of files) {
      if (file.type.startsWith('image/')) {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          addValue(`<img src=${fileReader.result} />`);
        };
        fileReader.readAsDataURL(file);
      }
    }
  }
};

const enterSendMsgHandler = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    e.preventDefault();
    sendMsgHandler();
  }
};

onMounted(() => {
  if (boxRef.value) {
    boxRef.value.focus();
    atMention.value = new AtMention(boxRef.value);

    const userId = store.useGlobal().userId;
    if (userId) {
      const _html = imStore.draftMap[userId]?.[imStore.curTeamId];
      if (_html) {
        setValue(_html);
      }
    }
  }

  useEventListener(document, 'keydown', enterSendMsgHandler);
});

const userList = imStore.patientTeamList[1].userList as any[];
const personList = computed(() => {
  return userList.map(item => {
    return { id: item.userId, name: item.userName, isAll: false };
  });
});
const allList = ref<AtMentionUser[]>([
  { id: 1, name: '所有人', isAll: true, count: personList.value.length },
]);

const handleClick = (e: MouseEvent) => {
  if (atMention.value) {
    atMention.value.handleClick(e);
  }
};

const changeHandler = (e: Event) => {
  const _html = (e.target as HTMLElement).innerHTML;
  imStore.insertDraft(_html);

  if (atMention.value) {
    atMention.value.handleInput();
  }
};

const sendMsgHandler = () => {
  if (!boxRef.value) return;
  const _sendText = boxRef.value.innerText;
  const _children = boxRef.value.children;
  if (isAtMsg.value) {
    imStore.sendCustomMsg({
      to: imStore.curTeamId,
      content: {
        text: _sendText,
        type: 10,
      },
    });
  } else if (_sendText) {
    imStore.sendTextMsg({
      to: imStore.curTeamId,
      text: _sendText,
    });
  }
  const imgBlobs: any = [];
  for (let i = 0; i < _children.length; i++) {
    const item: any = _children[i];
    if (item.localName === 'img') {
      if (/(http|https):\/\//.test(item.src)) {
        fetch(item.src)
          .then(res => res.blob())
          .then(res => {
            imStore.sendBlobMsg({
              to: imStore.curTeamId,
              blob: res,
              type: 'image',
            });
            clearBox();
          });
      } else {
        imgBlobs.push(convertBase64UrlToBlob(item.src));
      }
    }
  }
  for (let v of imgBlobs) {
    imStore.sendBlobMsg({
      to: imStore.curTeamId,
      blob: v,
      type: 'image',
    });
  }

  clearBox();
  imStore.resetSessionUnread();
};

const clearBox = () => {
  imStore.insertDraft('');
  nextTick(() => {
    if (boxRef.value) boxRef.value.innerHTML = '';
  });
};

const setValue = (val: string) => {
  if (!boxRef.value) return;

  imStore.insertDraft(val);
  boxRef.value.innerHTML = val;
  const range = document.createRange();
  range.selectNodeContents(boxRef.value);
  range.collapse(false);
  const sel = window.getSelection();
  sel?.removeAllRanges();
  sel?.addRange(range);
};

const addValue = (val: string) => {
  if (!boxRef.value) return;

  const _html = boxRef.value.innerHTML + (val ?? '');
  setValue(_html);
};

defineExpose({ setValue, addValue });
</script>

<style scoped lang="less">
.chat-input {
  width: 100%;
  height: 110px;
  padding: 8px;
  font-size: 16px;
  border: none;
  background: none;
  resize: none;
  color: #2b2b2b;
  text-align: left;
  outline: none;
  word-break: break-all;
  overflow-y: scroll;

  > div {
    width: 100%;
  }

  :deep(img) {
    width: 60px !important;
    object-fit: cover;
  }

  :deep(span),
  :deep(div) {
    background: none !important;
    color: #2b2b2b !important;
  }

  :deep(.at-username),
  :deep(.at-symbol) {
    color: #2e6be6 !important;
  }

  &::-webkit-scrollbar {
    width: 0;
    background: none;
  }

  .cutpic {
    display: flex;
    img {
      width: 55px;
      height: 45px;
      object-fit: cover;
    }
  }

  img {
    width: 55px;
    object-fit: cover;
    height: 45px;
  }
}

.chat-input:empty:before {
  content: '\8bf7\586b\5199\5185\5bb9';
  color: gray;
  font-size: 14px;
}

.chat-input:focus:before {
  content: none;
}

.btn-wrap {
  text-align: right;
}

.send-btn {
  display: inline-block;
  width: 72px;
  height: 32px;
  background: #2e6be6;
  border-radius: 2px;
  font-size: 14px;
  color: #ffffff;
  justify-content: center;
  text-align: center;
  line-height: 32px;
  margin-right: 16px;
  cursor: pointer;
}
</style>

<style lang="less">
.at-panel {
  position: absolute;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 180px;
  max-height: 300px;
  overflow: hidden;
  z-index: 1000;
  box-sizing: border-box;

  .at-header {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
  }

  .at-title {
    font-weight: bold;
    font-size: 16px;
    color: #101b25;
  }
}

.at-tag {
  margin: 0 2px;
}

.at-user-list {
  max-height: 200px;
  overflow-y: auto;
}

.at-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.at-item:hover {
  background-color: #edf4ff;
}

.at-avatar {
  background: #ffffff;
  border: 1px solid #efefef;
  border-radius: 50%;
  font-size: 16px;
  color: #2e6be6;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.at-name {
  font-size: 14px;
  color: #3a4762;
  flex: 1;
}

.at-empty {
  padding: 8px 12px;
  color: #999;
  text-align: center;
}

.at-symbol {
  color: #2e6be6 !important;
  font-weight: bold;
}

.at-username {
  padding-right: 4px;
}
</style>
