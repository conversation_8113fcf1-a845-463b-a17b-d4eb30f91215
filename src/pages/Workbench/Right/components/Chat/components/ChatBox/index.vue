<template>
  <div class="h-150 bg-[#F6F8FB] rounded-b-[4px]">
    <div
      id="chat-input"
      ref="boxRef"
      class="chat-input"
      contentEditable="true"
      @paste="pasteHandler"
      @input="changeHandler"
    ></div>
    <div class="btn-wrap">
      <span class="send-btn" @click.stop="sendMsgHandler">发送</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useEventListener } from '@vueuse/core';
import { convertBase64UrlToBlob } from '@/utils';
import store from '@/store';
const imStore = store.useIM();
const boxRef = shallowRef();

const pasteHandler = (e: any) => {
  const files = e.clipboardData?.files;
  if (files.length) {
    e.preventDefault();
    for (const file of files) {
      if (file.type.startsWith('image/')) {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          addValue(`<img src=${fileReader.result} />`);
        };
        fileReader.readAsDataURL(file);
      }
    }
  }
};
onMounted(() => {
  boxRef.value.focus();
  const userId = store.useGlobal().userId;
  if (userId) {
    const _html = imStore.draftMap[userId]?.[imStore.curTeamId];
    if (_html) {
      setValue(_html);
    }
  }
});

defineOptions({
  name: 'ChatBox',
});

const sendMsgHandler = () => {
  const _sendText = boxRef.value.innerText;
  const _children = boxRef.value.children;
  if (_sendText) {
    imStore.sendTextMsg({
      to: imStore.curTeamId,
      text: _sendText,
    });
  }
  const imgBlobs: any = [];
  for (let i = 0; i < _children.length; i++) {
    const item = _children[i];
    if (item.localName === 'img') {
      if (/(http|https):\/\//.test(item.src)) {
        fetch(item.src)
          .then(res => res.blob())
          .then(res => {
            imStore.sendBlobMsg({
              to: imStore.curTeamId,
              blob: res,
              type: 'image',
            });
            clearBox();
          });
      } else {
        imgBlobs.push(convertBase64UrlToBlob(item.src));
      }
    }
  }
  for (let v of imgBlobs) {
    imStore.sendBlobMsg({
      to: imStore.curTeamId,
      blob: v,
      type: 'image',
    });
  }

  clearBox();
  imStore.resetSessionUnread();
};
const clearBox = () => {
  imStore.insertDraft('');
  nextTick(() => {
    boxRef.value.innerHTML = '';
  });
};
const changeHandler = (e: Event) => {
  const _html = (e.target as HTMLElement).innerHTML;
  imStore.insertDraft(_html);
};
const enterSendMsgHandler = (e: KeyboardEvent) => {
  if (e.keyCode == 13) {
    e.preventDefault();
    sendMsgHandler();
  }
};
const setValue = (val: string) => {
  imStore.insertDraft(val);
  boxRef.value.innerHTML = val;
  const range = document.createRange();
  range.selectNodeContents(boxRef.value);
  range.collapse(false);
  const sel = window.getSelection();
  sel?.removeAllRanges();
  sel?.addRange(range);
};
const addValue = (val: string) => {
  const _html = boxRef.value.innerHTML + (val ?? '');
  setValue(_html);
};
defineExpose({ setValue, addValue });
onMounted(() => {
  useEventListener(document, 'keydown', enterSendMsgHandler);
});
</script>

<style scoped lang="less">
.chat-input {
  width: 100%;
  height: 110px;
  padding: 8px;
  font-size: 16px;
  border: none;
  background: none;
  resize: none;
  color: #2b2b2b;
  text-align: left;
  outline: none;
  word-break: break-all;
  overflow-y: scroll;

  > div {
    width: 100%;
  }

  :deep(img) {
    width: 60px !important;
    object-fit: cover;
  }

  :deep(span),
  :deep(div) {
    background: none !important;
    color: #2b2b2b !important;
  }

  &::-webkit-scrollbar {
    width: 0;
    background: none;
  }

  .cutpic {
    display: flex;
    img {
      width: 55px;
      height: 45px;
      object-fit: cover;
    }
  }

  img {
    width: 55px;
    object-fit: cover;
    height: 45px;
  }
}

.chat-input:empty:before {
  content: '\8bf7\586b\5199\5185\5bb9';
  color: gray;
  font-size: 14px;
}

.chat-input:focus:before {
  content: none;
}
.btn-wrap {
  text-align: right;
}
.send-btn {
  display: inline-block;
  width: 72px;
  height: 32px;
  background: #2e6be6;
  border-radius: 2px;
  font-size: 14px;
  color: #ffffff;
  justify-content: center;
  text-align: center;
  line-height: 32px;
  margin-right: 16px;
  cursor: pointer;
}
</style>
