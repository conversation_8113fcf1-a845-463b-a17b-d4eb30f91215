<template>
  <div
    v-loading="imStore.loading"
    class="flex-1 bg-[#F6F8FB] overflow-y-hidden content-wrap rounded-t-[4px] border-solid border-[1px] border-[#DCDEE0]"
    :style="{ borderBottom: focused ? 0 : '' }"
  >
    <el-empty
      v-if="!getCrtHisMsg.length"
      :image-size="80"
      description="暂无消息记录"
    />

    <el-scrollbar ref="contentRef" @scroll="scrollHandler">
      <div
        v-for="(msg, i) in getCrtHisMsg"
        :key="msg.idClient"
        class="news-items"
      >
        <p v-if="showTimeLine(i)" class="time">{{ msg.timeText }}</p>
        <BaseMsgCard
          :msg="msg"
          :active-id="idClient"
          :direction="msg.istofrom"
          :type="['custom'].includes(msg.type) ? 'custom' : 'normal'"
          :is-read="!msg.needMsgReceipt || msg.userIsRead"
          :sender="getSender(msg)"
          :show-status="
            showReadStatus &&
            msg.status === 'success' &&
            msg.istofrom === 'right'
          "
          :is-checking="isMultipleSelecting"
          :is-checked="selectedMsgs.includes(msg)"
          :show-contextmenu="idClient === msg.idClient"
          :widthdraw-msg="() => widthdrawMsg(msg)"
          :save-expression="() => saveExpressions(msg)"
          :answer="() => answerHandler()"
          :retry="() => retryHandler(msg)"
          :direct-save="() => directSaveHandler(msg)"
          :multiple-select="type => multipleSelectHanlder(type, msg)"
          @right-menu="() => rightMenuHandler(msg)"
        >
          <TextView
            v-if="['text', 'tip'].includes(msg.type)"
            :text="msg.text"
            :show-highlight="msg.istofrom === 'left'"
          />
          <ImageView
            v-if="['image'].includes(msg.type)"
            :file="msg.file"
            :team-id="imStore.curTeamId"
            :url="msg.text"
          />
          <Card
            v-if="['custom'].includes(msg.type)"
            :title="getCustomTitle(msg)"
            :type="msg.customContent?.type"
            :istofrom="msg.istofrom"
            @show-detail="() => showDetail(msg)"
          />
          <VedioView v-if="['video'].includes(msg.type)" :url="msg.text" />
          <AudioView
            v-if="['audio'].includes(msg.type)"
            :url="msg.text"
            :msg="msg"
            :choosed-id="choosedAudioId"
            @choosed="audioChoosedHandler"
          />
        </BaseMsgCard>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { useEventListener } from '@vueuse/core';
import store from '@/store';
import { getMsgSession, getFailTime, clearSessionMsg } from '@/lib/nim/cache';
import { MSGT_TYPES, CHAT_MEMBER_TYPES } from '@/constant/chat';
import { addSpeechCraft } from '@/api/chat';
import bus from '@/lib/bus';
import BaseMsgCard from './components/BaseMsgCard/index.vue';
import TextView from './components/TextView/index.vue';
import ImageView from './components/ImageView/index.vue';
import Card from './components/Card/index.vue';
import VedioView from './components/Vedio/index.vue';
import AudioView from './components/Audio/index.vue';

interface IProps {
  focused: boolean;
  showReadStatus: boolean;
  showQuestionAnswer: () => void;
  showDrugInfo: (data: any) => void;
  showBatchTool: () => void;
  save: (msg: any) => void;
}
const imStore = store.useIM();
const globalStore = store.useGlobal();
const tabStore = store.useTabs();
const tabAction = store.useComponentsTabAction();
const idClient = ref('');
const oldScrollHeight = ref(0);
const offsetHeight = ref(0);
const contentRef = shallowRef();
const scrollReady = ref(false);
const choosedAudioId = ref();
const retryIds = ref<string[]>([]);
const selectedMsgs = ref<any[]>([]);
const isMultipleSelecting = ref(false);
const props = defineProps<IProps>();
defineOptions({
  name: 'ChatContent',
});
watch([() => imStore.curTeamId, () => imStore.ready], ([newVal, newReady]) => {
  if (newReady && newVal) {
    scrollReady.value = false;
    imStore.getHisMsg(newVal);
  }
});
watch(
  () => props.focused,
  val => {
    if (val) {
      nextTick(() => {
        offsetHeight.value = contentRef.value.wrapRef.offsetHeight;
        if (!imStore.findMsgFlag) {
          contentRef.value.setScrollTop(1000000);
        }
      });
    }
  }
);
watch(
  () => imStore.withdrawStatus,
  val => {
    if (false === val) {
      ElMessage.error('不能撤回超过1周的信息！');
      imStore.withdrawStatus = null;
    }
  }
);

watch(
  () => imStore.findMsgFlag,
  val => {
    if (val) {
      nextTick(() => {
        const scrollView = contentRef.value.wrapRef.children[0];
        const itemList = scrollView.children;
        const index = getCrtHisMsg.value.findIndex(
          v => v.idServer === imStore.findMsgId
        );
        let top = -5;
        for (let i = 0; i < index; i++) {
          top += itemList[i].offsetHeight;
        }
        contentRef.value.setScrollTop(top);
      });
    }
  }
);
const getSender = (msg: any) => {
  if (msg.istofrom === 'right') return '';
  if (msg.from?.startsWith('user@')) {
    const name = msg.custom?.senderName ?? '';
    return msg.custom?.sender === 'null'
      ? `${name}(本人)`
      : `${name}(${msg.custom?.sender})`;
  }
  if (msg.from.startsWith('doctor@')) {
    return msg.custom?.sender + '(专家)';
  }
  if (msg.custom?.senderRole) {
    const roleName =
      CHAT_MEMBER_TYPES[
        msg.custom.senderRole as keyof typeof CHAT_MEMBER_TYPES
      ];
    return `${msg.custom.senderName}${roleName ? '(' + roleName + ')' : ''}`;
  }
  if (msg.from?.startsWith('assistant@')) {
    return msg.fromNick + '(医生)';
  }
  if (msg.from?.startsWith('customer@')) {
    return msg.fromNick + '(健康管理师)';
  }
  if (msg.from?.startsWith('rehab@')) {
    return msg.fromNick + '(运动康复师)';
  }
  return '';
};
const getCustomTitle = (msg: any) => {
  const type = msg.customContent.type;
  if (type === 8) return msg.customContent?.name || '阶段性总结报告';
  if (type === 9) return msg.customContent?.name;
  if (type === 10) return msg.customContent?.text;
  return '';
};
const directSaveHandler = (msg: any) => {
  props.save(msg);
};
const multipleSelectHanlder = (type: 'add' | 'remove', msg: any) => {
  if (type === 'add') {
    selectedMsgs.value.push(msg);
  } else {
    selectedMsgs.value = selectedMsgs.value.filter(v => v !== msg);
  }
  if (!isMultipleSelecting.value) {
    isMultipleSelecting.value = true;
    props.showBatchTool();
  }
};
const audioChoosedHandler = (id: string) => {
  choosedAudioId.value = id;
};
const widthdrawMsg = (msg: any) => {
  imStore.withdrawMsg(msg);
};
const showDetail = (msg: any) => {
  const { type, id, name } = msg.customContent ?? {};
  if (type === 8) {
    tabStore.mainActiveTab = 4;
    const fn = () => {
      tabAction.setAction({
        name,
        group: 'ManagementSituation',
        componentType: 7,
        disableCache: true,
        mainTabCode: 4,
        data: {
          reportId: id,
        },
      });
      bus.emit('open-component-tab');
    };
    if (globalStore.manageTabReady) {
      fn();
    } else {
      globalStore.manageTabReadyCallback = fn;
    }
  } else if (type === 7) {
    const drugObj = JSON.parse(msg.content);
    props.showDrugInfo(drugObj);
  }
};
const saveExpressions = async (msg: any) => {
  await addSpeechCraft({ speechcraftContent: msg.text });
  ElMessage.success('添加成功！');
};
const answerHandler = () => {
  props.showQuestionAnswer();
};
const retryHandler = (msg: any) => {
  const isHaveOnLine = window.navigator.onLine;
  if (isHaveOnLine) {
    if (msg.type === 'text') {
      const msgSession = getMsgSession();
      const failTime = getFailTime();
      const isTimeout = failTime
        ? Date.now() - getFailTime() > 30 * 1000
        : false;
      if (msgSession[msg.idClient] && !isTimeout) {
        setTimeout(() => {
          clearSessionMsg();
          retryIds.value = [];
          imStore.getHisMsg(imStore.curTeamId);
        }, 2000);
      } else {
        retryIds.value.push(msg.idClient as string);
        imStore.resendTextMsg({
          to: imStore.curTeamId,
          idClient: msg.idClient,
          text: msg.text,
        });
      }
    } else {
      imStore.resendFileMsg({
        to: imStore.curTeamId,
        idClient: msg.idClient,
        file: msg.file,
        resend: true,
      });
    }
  } else {
    ElMessage.error('未连接网络，发送失败!');
  }
};
const rightMenuHandler = (msg: any) => {
  console.log('$debug: msg', msg);
  idClient.value = msg.idClient;
};
const scrollHandler = (e: any) => {
  const { scrollTop } = e;

  if (scrollReady.value && scrollTop === 0) {
    console.log('【nim】: 到达顶部，请求网易下一页数据');
    imStore.getMoreHisMsg(imStore.curTeamId);
  }
  if (
    (imStore.findMsgFlag || imStore.needGetHistory) &&
    Math.abs(scrollTop - (oldScrollHeight.value - offsetHeight.value)) < 3
  ) {
    console.log('【nim】: 消息定位到达底部，请求网易最新数据');
    scrollReady.value = false;
    imStore.findMsgId = '';
    imStore.findMsgFlag = 0;
    imStore.getHisMsg(imStore.curTeamId);
    imStore.needGetHistory = false;
  }
};
const getCrtHisMsg = computed(() => {
  const msgs = imStore.historyMsgs;
  scrollReady.value = false;
  const _msgList = msgs
    .filter(v => v.type !== MSGT_TYPES.NOTIFICATION) // 过滤通知类型
    .filter(
      v => !(v.type === MSGT_TYPES.CUSTOM && [6, 5].includes(v.customType - 0))
    ) // 历史数据智能问诊, 阶段性总结报告过滤
    .filter(v => v.to === imStore.curTeamId)
    .filter(v => !retryIds.value.includes(v.idClient));
  setTimeout(() => {
    if (_msgList.length) {
      scrollReady.value = true;
    }
  }, 100);
  console.log('【nim】: 获取历史数据', _msgList);

  return _msgList;
});
const showTimeLine = (i: number) => {
  return (
    i === 0 ||
    getCrtHisMsg.value[i].timestap - getCrtHisMsg.value[i - 1].timestap > 120000
  );
};
const scrollToBottom = () => {
  nextTick(() => {
    contentRef.value.setScrollTop(1000000);
    const ele = contentRef.value.wrapRef;
    const contentHeight = ele.children[0].offsetHeight;
    offsetHeight.value = ele.offsetHeight;
    oldScrollHeight.value = contentHeight;
    imStore.isScrollBottom = false;
  });
};
const resetRightMenu = () => {
  idClient.value = '';
};

defineExpose({
  cancelMultiple: () => {
    selectedMsgs.value = [];
    isMultipleSelecting.value = false;
  },
  getSelectedMsgs: () => selectedMsgs.value,

  // 存在AI横幅消息时，若在滚动条在底部范围内则滚动到底部
  checkAndScroll: () => {
    contentRef.value.update();
    nextTick(() => {
      const scrollElement = contentRef.value.wrapRef;
      const { scrollHeight, scrollTop, clientHeight } = scrollElement || {};
      const isAtBottomRange = scrollHeight - scrollTop <= clientHeight + 40 + 6;
      if (isAtBottomRange) contentRef.value.setScrollTop(scrollHeight);
    });
  },
});
watch(
  () => getCrtHisMsg.value,
  () => {
    if (imStore.isScrollBottom) {
      scrollToBottom();
    } else {
      nextTick(() => {
        const contentHeight = contentRef.value.wrapRef.children[0].offsetHeight;
        if (oldScrollHeight.value !== contentHeight) {
          const scrollTop = contentHeight - oldScrollHeight.value;
          oldScrollHeight.value = contentHeight;
          contentRef.value.setScrollTop(scrollTop);
        }
      });
    }
  }
);

onMounted(() => {
  imStore.getHisMsg(imStore.curTeamId);
  useEventListener(document, 'click', resetRightMenu);
});
</script>

<style scoped lang="less">
.content-wrap {
  position: relative;
  .time {
    font-size: 16px;
    color: #aaaaaa;
    margin: 0;
    margin-top: 12px;
    text-align: center;
  }
}
.news-items {
  border-top: 1px solid transparent;
}
</style>
