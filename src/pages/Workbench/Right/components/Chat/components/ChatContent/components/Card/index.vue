<template>
  <div v-if="type === 7 || type === 8" class="card">
    <div class="noticeBox">
      <img
        :src="type === 8 ? reportImg : noticeImg"
        alt="icon"
        class="medicationNoticeImg"
      />
      <span class="notice">{{ title || '药物调整通知' }}</span>
    </div>
    <div class="confirm" @click="clickHandler">
      {{ confirmText || '查看详情' }}
    </div>
  </div>
  <div v-if="type === 9" class="inquiry-reply p-12">
    {{ title }}
    <span class="triangle"></span>
    <span class="doctor" :class="[istofrom]">医生助手</span>
  </div>
</template>

<script setup lang="ts">
import noticeImg from '@/assets/imgs/chat/medicationNotice.png';
import reportImg from '@/assets/imgs/chat/report.png';
interface IProps {
  title: string;
  type?: number;
  confirmText?: string;
  istofrom?: string;
}

const emits = defineEmits(['showDetail']);
defineProps<IProps>();
defineOptions({
  name: 'Card',
});

const clickHandler = () => {
  emits('showDetail');
};
</script>

<style scoped lang="less">
.card {
  background: #fff;
  width: 210px;
  height: 110px;
  padding: 16px;
  border-radius: 6px;
  overflow: hidden;
}
.noticeBox {
  height: 55px;
  border-bottom: 1px solid #e9e8eb;
  display: flex;
  align-items: center;
  padding-bottom: 5px;
  .notice {
    margin-left: 16px;
    color: #101b25;
    font-weight: bold;
  }
  img {
    width: 42px;
    height: 42px;
  }
}
.confirm {
  font-size: 14px;
  font-weight: 400;
  color: #0a73e4;
  text-align: center;
  margin-top: 8px;
  cursor: pointer;
}
.inquiry-reply {
  position: relative;
  background: #e6e6e6;
  font-size: 16px;
  color: #3a4762;
  border-radius: 4px;
  border-top-right-radius: 0;
  .triangle {
    display: inline-block;
    position: absolute;
    width: 0;
    height: 0;
    right: -7px;
    top: 0;
    border: 4px solid #e6e6e6;
    border-bottom-color: transparent;
    border-right-color: transparent;
  }
  .doctor {
    position: absolute;
    top: -20px;
    font-size: 12px;
    color: #7a8599;
  }
  .left,
  .right {
    left: 0;
  }
}
</style>
