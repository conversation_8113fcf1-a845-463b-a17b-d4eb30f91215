<template>
  <div v-html="hightLightText(text)"></div>
</template>

<script setup lang="ts">
import { escapeHtml } from '@/utils';
interface IProps {
  text: string;
  showHighlight: boolean;
}
const keywords = ['胸痛', '晕', '血', '呼吸困难', '心悸'];
const props = defineProps<IProps>();
defineOptions({
  name: 'TextView',
});

const hightLightText = (text: string) => {
  const setHTML = (str, strReplace, strSymbol) => {
    return str.replaceAll(strReplace, strSymbol);
  };
  let resText = escapeHtml(text);
  resText = resText.replaceAll('&lt;br/&gt;', '<br/>');
  resText = resText.replaceAll('&lt;b&gt;', '<b>');
  resText = resText.replaceAll('&lt;/b&gt;', '</b>');
  resText = setHTML(resText, '&lt;i&gt;', '<i>');
  resText = setHTML(resText, '&lt;/i&gt;', '</i>');
  resText = setHTML(resText, '&lt;u&gt;', '<u>');
  resText = setHTML(resText, '&lt;/u&gt;', '</u>');
  resText = setHTML(resText, '&lt;div&gt;', '<div>');
  resText = setHTML(resText, '&lt;/div&gt;', '</div>');
  if (!props.showHighlight) {
    return resText;
  }
  for (const keyword of keywords) {
    resText = resText.replace(
      new RegExp(keyword, 'gi'),
      match => `<span style="color:#27b07f">${match}</span>`
    );
  }
  return resText;
};
</script>

<style scoped lang="less">
// todo
</style>
