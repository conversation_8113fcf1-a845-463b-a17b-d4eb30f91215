<template>
  <div class="image-con">
    <ImgPreview
      :url="url"
      :type="'chat_' + teamId"
      :width="100"
      :height="(100 / file.w) * file.h"
    />
  </div>
</template>

<script setup lang="ts">
import ImgPreview from '@/components/ImgPreview/index.vue';
interface IProps {
  teamId: string;
  file: { w: number; h: number };
  url: string;
}
defineProps<IProps>();
defineOptions({
  name: 'ImageView',
});
</script>

<style scoped lang="less">
.img {
  max-width: 100px;
}
</style>
