<template>
  <div
    ref="chatContainer"
    class="chat-container"
    :class="individual ? 'individual' : ''"
    :style="{ width: individual ? individualWidth + 'px' : '' }"
  >
    <div class="icons" @click="resizeToggle">
      <img :src="individual ? shrinkImg : enlargeImg" />
    </div>
    <div v-if="individual" class="resize" @mousedown="resizeStart"></div>
    <ChatHeader :team-id="imStore.curTeamId" @change="headerChange" />
    <template v-if="imStore.curTeamId">
      <ChatContent
        ref="contentRef"
        :focused="focused"
        :show-read-status="
          imStore.patientTeamList?.[0]?.teamNumber === imStore.curTeamId
        "
        :show-question-answer="showQuestionAnswer"
        :show-drug-info="showDrugInfo"
        :show-batch-tool="showBatchTool"
        :save="directSave"
      />
      <!-- AI智能聊天横幅 -->
      <AiConversation
        @on-message="() => contentRef.checkAndScroll()"
        @change-text="onChange"
      />
    </template>
    <template v-if="focused && imStore.curTeamId">
      <div class="focus-box rounded-b-[4px]">
        <ChatToolbar
          ref="toolbarRef"
          :focused="focused"
          :show-question-answer="showQuestionAnswer"
          :show-expression="showExpression"
          @change-text="onChange"
        />
        <ChatBox ref="chatboxRef" />
      </div>
    </template>
    <div v-if="!focused && imStore.curTeamId" class="box rounded-b-[4px]">
      <div class="input" @click="focusHandler" v-html="inputText"></div>
      <ChatToolbar
        :focused="focused"
        :show-question-answer="showQuestionAnswer"
        :show-expression="showExpression"
        @change-text="onChange"
      />
    </div>
    <div
      v-if="batchToolVisible"
      class="batch rounded-b-[4px]"
      :style="{ height: focused ? '200px' : '48px' }"
    >
      <BatchTool
        :focused="focused"
        :download="batchDownloadHandler"
        :save="directSave"
        :close="closeBatch"
      />
    </div>
    <QuestionAnswer
      :question-answer-visible="questionAnswerVisible"
      @change-text="onChange"
      @close="questionAnswerVisible = false"
    />
    <DrugInfo v-model:drugInfoVisible="drugVisible" :drug-info-id="drugId" />
    <UsefulExpressions
      :expressions-visible="expressionVisible"
      @change-text="onChange"
      @close="expressionVisible = false"
    />
    <audio ref="msgAudio">
      <source src="@/assets/notify.wav" type="audio/wav" />
    </audio>
    <DirectionalSave
      v-model:visible="directionalSaveVisible"
      :urls="selectedUrls"
    />
  </div>
</template>

<script setup lang="ts">
import ChatHeader from './components/ChatHeader/index.vue';
import ChatContent from './components/ChatContent/index.vue';
import ChatToolbar from './components/ChatToolBar/index.vue';
import ChatBox from './components/ChatBox/index.vue';
import QuestionAnswer from './components/ChatToolBar/components/QuestionAnswer/index.vue';
import DrugInfo from '@/components/DrugInfo/index.vue';
import UsefulExpressions from './components/ChatToolBar/components/UsefulExpressions/index.vue';
import store from '@/store';
import { getTeamList } from '@/api/chat';
import { useEventListener } from '@vueuse/core';
import { IApiPatientConversationTeamListTeamList } from '@/interface/type';
import { messageType } from '@/constant/message';
import bus from '@/lib/bus';
import { throttle } from 'lodash-es';
import shrinkImg from '@/assets/imgs/chat/shrink.png';
import enlargeImg from '@/assets/imgs/chat/enlarge.png';
import BatchTool from './components/BatchTool/index.vue';
import DirectionalSave from './components/DirectionalSave/index.vue';
import AiConversation from './components/AiConversation/index.vue';
import { batchDownload } from '@/utils/index';

const minWidth = 540;
const globalStore = store.useGlobal();
const userListStore = store.useUserList();
const chatContainer = shallowRef();
const imStore = store.useIM();
const focused = ref(false);
const individual = ref(false);
const individualWidth = ref(0);
const individualMaxWidth = ref(minWidth);
const clientX = ref(0);
const questionAnswerVisible = ref(false);
const expressionVisible = ref(false);
const drugVisible = ref(false);
const drugId = ref();
const msgAudio = shallowRef();
const chatboxRef = shallowRef();
const toolbarRef = shallowRef();
const contentRef = shallowRef();
const batchToolVisible = ref(false);
const directionalSaveVisible = ref(false);
const selectedUrls = ref<string[]>([]);

const batchDownloadHandler = () => {
  const selectedMsgs = contentRef.value.getSelectedMsgs();
  if (!selectedMsgs.length) {
    ElMessage.error('请选择图片！');
    return;
  }
  const urls = selectedMsgs.map(v => {
    let filename = v.file.name;
    if (v.type === 'image' && v.file.name === 'blob-undefined') {
      filename = v.idClient + '.' + v.file.ext;
    }
    return {
      filename,
      url: v.file.url,
    };
  });
  batchDownload(urls);
  closeBatch();
};
const directSave = (data?: any) => {
  if (data) {
    const url = data.file.url as string;
    selectedUrls.value = [url];
  } else {
    const selectedMsgs = contentRef.value.getSelectedMsgs();
    if (!selectedMsgs.length) {
      ElMessage.error('请选择图片！');
      return;
    }
    const urls = selectedMsgs.map(v => v.file.url);
    selectedUrls.value = urls;
  }
  directionalSaveVisible.value = true;
};
const closeBatch = () => {
  batchToolVisible.value = false;
  contentRef.value?.cancelMultiple();
};
const showBatchTool = () => {
  batchToolVisible.value = true;
};
const focusHandler = () => {
  focused.value = true;
  userListStore.keepUser = true;
};
const playAudio = () => {
  msgAudio.value.play();
};

const showDrugInfo = (data: any) => {
  drugVisible.value = true;
  drugId.value = data.id - 0;
};
const showExpression = () => {
  expressionVisible.value = true;
};
const showQuestionAnswer = () => {
  questionAnswerVisible.value = true;
};
const onChange = (data: { val: string; type: string }) => {
  if (!focused.value) {
    setTimeout(() => {
      if (data.type === 'emote') {
        toolbarRef.value.setEmoteStatus(true);
      }
    }, 16);
  }

  focused.value = true;
  userListStore.keepUser = true;
  nextTick(() => {
    if (data.type === 'expression') {
      chatboxRef.value.addValue(data.val);
    } else {
      chatboxRef.value.addValue(data.val);
    }
  });
};

const inputText = computed(() => {
  if (globalStore.userId) {
    const _html = imStore.draftMap[globalStore.userId]?.[imStore.curTeamId];
    return _html ? '<span style="color:red">[草稿]</span>' + _html : '输入内容';
  }
  return '输入内容';
});
const emitClearMessage = (data: any) => {
  bus.emit('clear-message', data);
};
const getMsgType = (teamType?: number) => {
  if (teamType === 1) {
    return messageType.EXPERT_CHAT_RECORD;
  } else if (teamType === 4) {
    return messageType.TEAM_CHAT_RECORD;
  }
  return messageType.PATIENT_CHAT_RECORD;
};
const headerChange = (data: IApiPatientConversationTeamListTeamList) => {
  focused.value = false;
  userListStore.keepUser = false;
  imStore.curTeamId = data.teamNumber ?? '';
  imStore.hasMore = true;
  imStore.resetSessionUnread();
  closeBatch();
  emitClearMessage({
    sourceId: imStore.curTeamId,
    msgType: getMsgType(data.teamType!),
  });
};
const getTeamListHandler = async (patientId: number) => {
  imStore.patientTeamList = [];
  imStore.curTeamId = '';
  const { teamList } = await getTeamList({ patientId });
  if (teamList?.length) {
    imStore.patientTeamList = teamList;
    if (!imStore.findTeamId) {
      imStore.curTeamId = teamList[0].teamNumber ?? '';
    }

    if (imStore.findMsgData && imStore.findTeamId) {
      imStore.curTeamId = imStore.findTeamId;
      imStore.findMsg(imStore.findMsgData as any);
      imStore.findMsgData = null;
      imStore.findTeamId = '';
      if (imStore.findMsgFocus) {
        focused.value = true;
        imStore.findMsgFocus = false;
      }
    }
    emitClearMessage({
      sourceId: imStore.curTeamId,
      msgType: getMsgType(
        teamList?.find(v => v.teamNumber === imStore.curTeamId)?.teamType
      ),
    });
    imStore.resetSessionUnread();
  }
};
const calculateIndividualSize = () => {
  const offsetHeight = chatContainer.value.offsetHeight;
  const maxHeight = document.body.offsetHeight - 60;
  const maxWidth = ~~((maxHeight / offsetHeight) * minWidth);
  individualMaxWidth.value = maxWidth;

  if (!individualWidth.value) {
    individualWidth.value = maxWidth;
  }
};
const resizeHandler = throttle((e: MouseEvent) => {
  const deltaX = e.clientX - clientX.value;
  clientX.value = e.clientX;
  const newWidth = individualWidth.value - deltaX;
  individualWidth.value = Math.min(
    individualMaxWidth.value,
    Math.max(newWidth, minWidth)
  );
  if (newWidth < minWidth || newWidth > individualMaxWidth.value) {
    resizeEnd();
  }
}, 16);
const resizeEnd = () => {
  window.removeEventListener('mousemove', resizeHandler);
  window.removeEventListener('mouseup', resizeEnd);
};
const resizeStart = (e: MouseEvent) => {
  e.preventDefault();
  clientX.value = e.clientX;
  window.addEventListener('mousemove', resizeHandler);
  window.addEventListener('mouseup', resizeEnd);
};
const resizeToggle = () => {
  individual.value = !individual.value;
  if (individual.value) {
    calculateIndividualSize();
    focused.value = true;
    userListStore.keepUser = true;
  }
};
onMounted(() => {
  store.useIM().initNimSdk();
  nextTick(() => {
    msgAudio.value.addEventListener('ended', function () {
      imStore.isPlaying = false;
    });
  });
  useEventListener(document, 'keydown', (e: KeyboardEvent) => {
    if (e.ctrlKey && e.shiftKey && e.keyCode === 89) {
      resizeToggle();
    }
  });
});
watch(
  () => globalStore.userId,
  val => {
    focused.value = false;
    if (val) {
      imStore.hasMore = true;
      getTeamListHandler(val);
    } else {
      imStore.reset();
    }
    closeBatch();
  },
  { immediate: true }
);
watch(
  () => imStore.findMsgFocus,
  val => {
    if (val) {
      focused.value = true;
      imStore.findMsgFocus = false;
      closeBatch();
    }
  }
);
watch(
  () => imStore.isPlaying,
  val => {
    if (val) {
      playAudio();
    }
  }
);
</script>
<script lang="ts">
export default {
  name: 'Chat',
};
</script>
<style scoped lang="less">
.chat-container {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 10px 16px;
  background: #fff;
  height: calc(100vh - 430px);
}
.input {
  cursor: pointer;
  width: 200px;
  height: 32px;
  background: #f6f8fb;
  border-radius: 4px;
  margin-right: 12px;
  line-height: 32px;
  color: #7a8599;
  font-size: 14px;
  padding-left: 16px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.focus-box {
  border: 1px solid #dcdee0;
  border-top: 0;
}
.box {
  display: flex;
  align-items: center;
  border: 1px solid #dcdee0;
  padding-left: 16px;
  border-top: 0;
}
.individual {
  position: fixed;
  width: 540px;
  z-index: 2000;
  top: 60px;
  right: 0;
  height: calc(100vh - 60px);
}
.resize {
  position: absolute;
  width: 12px;
  top: 0;
  bottom: 0;
  left: 2px;
  cursor: w-resize;
}
.icons {
  position: absolute;
  right: 16px;
  top: 10px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  &:hover {
    background: #f6f8fb;
  }
}
.batch {
  position: absolute;
  height: 200px;
  background: #fff;
  bottom: 10px;
  left: 16px;
  right: 16px;
  z-index: 10;
  border: 1px solid #ccc;
  border-top: 0;
}
</style>
