<template>
  <Drawer
    v-model:visible="drawerVisible"
    title="质疑信息"
    :custom-width="540"
    :custom-modal-class="'doubt-drawer'"
  >
    <div v-loading="doubtStore.loading" class="content text-[14px]">
      <div class="p-16 flex-1 overflow-auto">
        <div class="flex mb-12">
          <div class="label">项目名称:</div>
          <div class="value">{{ detail.projectName }}</div>
        </div>
        <div class="detail mb-12">
          <div class="item">
            <span>优先级:</span>
            <span>{{ DoubtPriorityMap[detail.doubtPriority!] }}</span>
          </div>
          <div class="item">
            <span>创建人:</span>
            <span>{{ detail.createName }}</span>
          </div>
          <div class="item">
            <span>创建时间:</span>
            <span>
              {{ formatTime(detail.createTime) }}
            </span>
          </div>
          <div class="item">
            <span>责任人:</span>
            <span>{{ detail.directorName }}</span>
          </div>
          <div class="item">
            <span>解决时间:</span>
            <span>{{ formatTime(detail.solveTime) }}</span>
          </div>
          <div class="item">
            <span>单元数量:</span>
            <span>{{ detail.unitCount }}</span>
          </div>
          <div class="item">
            <span>质疑类型:</span>
            <span>{{ DoubtTypeMap[detail.doubtType!] }}</span>
          </div>
        </div>
        <h2 class="font-bold text-[#15233F] text-[16px] mb-12">质疑详情</h2>
        <div class="flex mb-12">
          <div class="label">质疑记录:</div>
          <div class="value">{{ detail.doubtRecordName || '--' }}</div>
        </div>
        <div class="flex mb-12">
          <div class="label">质疑单元:</div>
          <div class="value">{{ detail.doubtUnitName || '--' }}</div>
        </div>
        <div class="flex mb-12">
          <div class="label">质疑指标:</div>
          <div class="value">--</div>
        </div>
        <div class="line"></div>
        <div class="mb-12">
          {{ detail.content }}
        </div>
        <div v-if="!!detail.faultUnits?.length" class="fault-list">
          <el-checkbox-group
            :model-value="
              detail.faultUnits?.filter(v => !v.fault).map(v => v.unitId)
            "
          >
            <el-checkbox
              v-for="v in detail.faultUnits"
              :key="v.unitId"
              :label="v.unitId"
              :value="v.unitId"
              class="w-[460px]"
            >
              {{ v.unitName }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="file mb-12">
          <span class="font-bold">全部附件</span>
          <div class="mt-8">
            <FilePreview
              v-if="!!detail.accessoryList?.length"
              :file-list="detail.accessoryList"
            />
            <span v-else>--</span>
          </div>
        </div>
        <div class="gap"></div>
        <h2 class="font-bold text-[#15233F] mt-24 text-[16px]">处理情况说明</h2>
        <ReplyList
          :data="detail.doubtReplies"
          @show-snapshot="getSnapshotHandler"
        />
        <div v-if="showDeal" class="textarea mt-16">
          <el-input
            v-model="dealContent"
            type="textarea"
            :rows="5"
            placeholder="请输入内容"
          />
        </div>
        <div v-if="showDeal" class="img-upload mt-12">
          <Upload
            v-model="imgList"
            :file-types="[
              'jpg',
              'jpeg',
              'png',
              'doc',
              'docx',
              'pdf',
              'xlsx',
              'xls',
            ]"
            :drag-trigger-style="{ height: '60px' }"
            tip="格式为图片、word、excel、pdf且大小不超过50MB"
          />
        </div>
      </div>
      <div class="footer">
        <div class="left">
          <span @click="preHandler">上一条记录</span>
          <span @click="nextHandler">下一条记录</span>
        </div>
        <div class="right">
          <!-- <el-button class="reject" @click="rejectHandler">拒绝</el-button> -->
          <el-button @click="cancelHandler">取消</el-button>
          <!-- <el-button @click="withDrawHandler">撤回</el-button> -->
          <el-button
            v-if="showDeal"
            :disabled="!dealContent && !imgList?.length"
            type="primary"
            @click="replyHandler"
          >
            提交
          </el-button>
        </div>
      </div>
    </div>
  </Drawer>
  <SnapshotDialog
    v-model="snapShow"
    :origin-data="snapData[0]"
    :current-data="snapData[1]"
    :category="snapCategory"
  />
</template>

<script setup lang="ts">
import { type UploadRawFile } from 'element-plus';
import Drawer from '@/components/Drawer/index.vue';
import Upload from '@/components/Upload/index.vue';
import FilePreview from '@/features/FilePreview/index.vue';
import SnapshotDialog from './SnapshotDialog.vue';
import { DoubtPriorityMap, DoubtStatus, DoubtTypeMap } from '@/constant/doubt';
import ReplyList from './ReplyList.vue';
import useDoubt from '@/store/module/useDoubt';
import dayjs from 'dayjs';
import bus from '@/lib/bus';

interface IProps {}

defineProps<IProps>();
const drawerVisible = defineModel<boolean>({ default: false });
const imgList = ref<UploadRawFile[]>([]);
const dealContent = ref('');
const snapShow = ref(false);
const doubtStore = useDoubt();
const detail = computed(() => doubtStore.doubtDetail);
const snapData = ref<any[]>([]);
const snapCategory = ref('');

const showDeal = computed(() => {
  const { doubtStatus } = detail.value;
  return (
    doubtStatus === DoubtStatus.WAIT_RECEIVE ||
    doubtStatus === DoubtStatus.INSPECTING
  );
});
const formatTime = time => {
  if (!time) return '--';
  return dayjs(time as number).format('YYYY年M月D日 HH:mm');
};
const getSnapshotHandler = async id => {
  const res = await doubtStore.getSnapshot(id);
  const result = res.map(v => JSON.parse(v));
  const item = result?.[1];
  snapData.value = result.map(v => v.detail);
  snapCategory.value = item?.secondUnitKey || item?.firstUnitKey;
  snapShow.value = true;
};
// const rejectHandler = () => {
//   doubtStore.rejectHandler();
//   drawerVisible.value = false;
// };
// const withDrawHandler = () => {
//   doubtStore.withDrawHandler();
// };
const clearForm = () => {
  dealContent.value = '';
  imgList.value = [];
};
const replyHandler = async () => {
  const params = {
    remark: dealContent.value,
    accessories: imgList.value.map(v => ({ fileName: v.name, fileUrl: v.url })),
  };
  const hasNextDoubt = await doubtStore.replyHandler(params);
  if (!hasNextDoubt) {
    drawerVisible.value = false;
  }
  clearForm();
  bus.emit('updete-todo-list');
};
const cancelHandler = () => {
  doubtStore.reset();
  drawerVisible.value = false;
};
const preHandler = () => {
  clearForm();
  doubtStore.getAnotherHandler('PREVIOUS');
};
const nextHandler = () => {
  clearForm();
  doubtStore.getAnotherHandler('NEXT');
};

watch(
  () => drawerVisible,
  val => {
    console.log('$debug: drawerVisible', val);
  }
);
defineOptions({
  name: 'Doubt',
});
</script>

<style scoped lang="less">
.detail {
  display: flex;
  flex-wrap: wrap;
  background: #f7f8fa;
  padding: 12px;
  font-family:
    PingFangSC,
    PingFang SC;
  .item {
    width: 50%;
    display: flex;
    height: 24px;
    align-items: center;
    > span {
      &:first-child {
        width: 72px;
        color: #7a8599;
      }
      &:last-child {
        color: #3a4762;
      }
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background: #e1e5ed;
  margin-bottom: 12px;
}
.gap {
  width: 100%;
  height: 6px;
  background: #f7f8fa;
  margin-bottom: 12px;
}
.label {
  color: #15233f;
  font-weight: bold;
  margin-right: 8px;
}
.value {
  color: #3a4762;
}
.content {
  position: relative;
  height: calc(100% - 24px);
  display: flex;
  flex-direction: column;
  color: #3a4762;
}
.textarea {
  margin-top: 12px;
  :deep(.el-textarea__inner) {
    background: #f7f8fa;
  }
}
.img-upload {
  :deep(.el-upload-dragger) {
    background: #f7f8fa;
  }
}
.footer {
  display: flex;
  align-items: center;
  height: 64px;
  padding: 12px;
  box-shadow: 0px 0px 4px 0px rgba(186, 200, 212, 0.5);
}
.left {
  width: 40%;
  color: #2e6be6;
  text-align: left;
  > span {
    cursor: pointer;
    margin-right: 12px;
  }
}
.right {
  width: 60%;
  text-align: right;
  :deep(.el-button) {
    width: 70px;
  }
}
.reject {
  color: #e63746;
}
.fault-list {
  margin-bottom: 12px;
  :deep(.el-checkbox__label) {
    color: #15233f;
  }
  :deep(.is-checked .el-checkbox__label) {
    color: #7a8599;
    text-decoration: line-through;
  }
}
</style>
<style lang="less">
.doubt-drawer {
  pointer-events: none;
  &.default-size .el-drawer {
    inset: 60px 0 0 calc(100% - 540px) !important;
  }
  .el-drawer {
    pointer-events: all;
  }
}
</style>
