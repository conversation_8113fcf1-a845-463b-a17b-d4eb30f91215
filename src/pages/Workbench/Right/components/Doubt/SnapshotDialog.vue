<template>
  <DataComparison
    v-model:visible="visible"
    class="doubt-snp-wrapper"
    :title-info="{
      dialogTitle: '数据快照',
      oldTitle: '质疑数据',
      newTitle: '提交数据',
      newTitleStyle: { 'font-weight': 'bold' },
      oldTitleStyle: { 'font-weight': 'bold' },
    }"
    :tips="''"
    :old-style="{ paddingLeft: '12px' }"
    hide-footer
  >
    <template #old>
      <SingleDynamicForm
        :model-value="originData"
        class="mt-12"
        mode="view"
        :category="category"
        hidden-file-upload
        block-label
      />
    </template>
    <template #new>
      <SingleDynamicForm
        :model-value="currentData"
        class="mt-12"
        mode="view"
        :category="category"
        hidden-file-upload
        block-label
      />
    </template>
    <template #footer>''</template>
  </DataComparison>
</template>

<script setup lang="ts">
import DataComparison from '@/features/DataComparison/index.vue';
import SingleDynamicForm from '@/features/SingleDynamicForm/index.vue';

interface IProps {
  originData: any;
  currentData: any;
  category: string;
}
defineProps<IProps>();
const visible = defineModel<boolean>({ default: false });
</script>

<style lang="less">
.doubt-snp-wrapper {
  color: red;
  .el-dialog__body {
    > div {
      padding: 12px 0 0 0;
      .content {
        padding-left: 12px;
      }
    }
  }
}
</style>
