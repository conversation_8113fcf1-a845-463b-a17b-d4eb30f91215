<template>
  <div class="h-90 address-book">
    <div class="header flex items-center justify-between">
      <div class="left flex items-center">
        医患沟通
        <span
          class="address flex items-center ml-6 cursor-pointer"
          @click="queryAddress"
        >
          <img :src="addressBookImg" alt="" class="w-18 h-18 mr-2" />
          通讯录
        </span>
      </div>
      <div class="right flex items-center cursor-pointer" @click="queryAllBook">
        通话记录
        <el-icon size="12px">
          <i-ep-arrow-right color="#2E6BE6" />
        </el-icon>
      </div>
    </div>
    <div class="book-list flex items-center mt-8 justify-between px-12">
      <span class="flex items-center gap-8">
        <img class="w-16 h-16" :src="exhalation" alt="" />
        <span>{{ bookObj.name }}</span>
      </span>
      <span>{{ bookObj.time }}</span>
      <span>{{ bookObj.num }}</span>
      <span>{{ bookObj.relation }}：{{ bookObj.iphone }}</span>
    </div>

    <!-- 呼叫列表 -->
    <TelRecord
      :visible="drawer"
      :record-type="2"
      :immediate="true"
      @close="drawer = false"
    />
    <!-- 通讯录 -->
    <TelephoneDirectory />
  </div>
</template>

<script setup lang="ts">
import { addressCallListApi } from '@/api/addressBook';
import addressBookImg from '@/assets/imgs/callCenter/address-book-img.png';
import exhalation from '@/assets/imgs/callCenter/exhalation.png';
import bus from '@/lib/bus';
import { useCall, useRLYSoftphone } from '@/store';
import useGlobal from '@/store/module/useGlobal';
import { formatTime } from '@/utils/index';
import { useQuery } from '@tanstack/vue-query';
import { computed, provide } from 'vue';
import { calculateBridgeDuration } from '.';
import TelephoneDirectory from './components/TelephoneDirectory.vue';
import TelRecord from './components/TelRecord.vue';

const INIT_DATA = {
  time: '--',
  num: '--',
  relation: '--',
  iphone: '--',
  name: '--',
};

const useGlobalInfo = useGlobal();
const rlySoftphone = useRLYSoftphone();
const call = useCall();

// 通讯录
// 查看所有的通讯录
let isTelephone = ref<boolean>(false);
provide('isTelephone', isTelephone);
let drawer = ref(false);

const patientId = computed(() => useGlobalInfo.userInfo.patientId);
const lastCallTime = computed(() => rlySoftphone.lastCallTime);
const aiccLastCallTime = computed(() => call.lastCallTime);

const { data: bookObj, refetch } = useQuery({
  queryKey: ['获取最新通话记录', patientId],
  queryFn: async () => {
    try {
      const res = await addressCallListApi({
        page: 1,
        pageNumber: 1,
        patientId: patientId.value,
        pageSize: 1,
      });
      let { code, data } = res;
      if (code === 'E000000' && data && data.contents && data.contents.length) {
        // 首页展示的一条数据
        const item = data.contents[0];
        return {
          iphone: item.customerNumber,
          time: formatTime(item.callTime),
          name: item.name,
          relation: item.relation,
          num: calculateBridgeDuration(item.bridgeDuration),
        };
      }
      return INIT_DATA;
    } catch (error) {
      return INIT_DATA;
    }
  },
  enabled: true,
  initialData: INIT_DATA,
});

watch([lastCallTime, aiccLastCallTime], () => {
  setTimeout(() => {
    refetch();
  }, 1000);
});

const queryAddress = () => {
  if (useGlobalInfo.userId) {
    isTelephone.value = true;
    provide('isTelephone', isTelephone);
    bus.emit('get-address-books');
  }
};

// 查看所有的通话记录
const queryAllBook = () => {
  if (useGlobalInfo.userId) {
    drawer.value = true;
  }
};
</script>
<style scoped lang="less">
.address-book {
  padding: 16px;
  box-sizing: border-box;
  background: #fff;
  box-shadow: -4px 2px 7px 0px rgba(200, 201, 204, 0.3);
  border-radius: 6px 0px 0px 6px;
  .header {
    .left {
      font-size: 16px;
      font-weight: 600;
      color: #101b25;
      .address {
        font-size: 14px;
        color: #2e6be6;
      }
    }
    .right {
      font-size: 14px;
      color: #2e6be6;
    }
  }
  .book-list {
    width: 100%;
    height: 32px;
    background: #f7f8fa;
    border-radius: 4px;
    font-size: 14px;
  }
}
</style>
