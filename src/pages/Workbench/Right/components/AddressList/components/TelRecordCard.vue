<script setup lang="ts">
import type { AddressCallListApiItem } from '@/@types';
import { markCallRecordRead } from '@/api';
import exhalationImg from '@/assets/imgs/callCenter/exhalation-img.png';
import incomingCall from '@/assets/imgs/callCenter/incoming-call.png';
import AudioPlayer from '@/components/AudioPlayer/index.vue';
import PhoneCall from '@/components/PhoneCall/index.vue';
import { CallStatus, CallStatusLabelMap } from '@/constant';
import { CallSource } from '@/interface';
import useGlobal from '@/store/module/useGlobal';
import useUserStore from '@/store/module/useUserStore';
import { formatTime } from '@/utils';
import { ArrowRight } from '@element-plus/icons-vue';
import { ElIcon } from 'element-plus';
import { toRef } from 'vue';
import { calculateBridgeDuration } from '../index';
import Details from './Details.vue';

const props = defineProps<{
  item: AddressCallListApiItem;
  showToDetail?: boolean;
  recordType?: number; // 1: 通话记录, 2: 呼叫记录
}>();
const emit = defineEmits<{
  (e: 'clear'): void;
  (e: 'close'): void;
}>();
const item = toRef(() => props.item);
const useGlobalInfo = useGlobal();
const userStore = useUserStore();
const flag = ref(false);

const callStatus = computed(() => {
  // 呼入
  if (item.value.type === 1) {
    if (item.value.callStatus === 1) {
      return CallStatus.IC_Answered;
    }
    if (item.value.callStatus === 2) {
      if (item.value.isRead === 0) {
        return CallStatus.IC_NotAnswered_NR;
      }
      if (item.value.isRead === 1) {
        return CallStatus.IC_NotAnswered_R;
      }
      // 兼容无 isRead 字段
      return CallStatus.IC_NotAnswered_NR;
    }
    if (item.value.callStatus === 3) {
      return CallStatus.IC_AnsweredBySystem;
    }
  }
  // 呼出
  if (item.value.type === 2) {
    if (item.value.callStatus === 3) {
      return CallStatus.OC_Answered;
    }
    if (item.value.callStatus === 2) {
      return CallStatus.OC_NotAnswered;
    }
    if (item.value.callStatus === 1) {
      return CallStatus.OC_Failed;
    }
  }
  return CallStatus.All;
});

async function clearUnread(uniqueId: string) {
  const res = await markCallRecordRead({
    uniqueId,
    operatorId: userStore.accountId,
  });
  if (res) {
    emit('clear');
    item.value.isRead = 1;
  }
}

function toDetail(item: AddressCallListApiItem) {
  if (item.patientId) {
    useGlobalInfo.userId = item.patientId;
    emit('close');
  }
}

/** 切换查看详情的状态 */
function toggleFlag() {
  flag.value = !flag.value;
  if (item.value.userId === userStore.accountId && item.value.isRead === 0) {
    clearUnread(item.value.uniqueId);
  }
}

/** 拨打电话☎️ */
function handleCall() {
  if (item.value.userId === userStore.accountId && item.value.isRead === 0) {
    clearUnread(item.value.uniqueId);
  }
}
</script>

<template>
  <li class="item mb-12 relative box-border rounded bg-white">
    <div
      class="flex items-center justify-center mb-8 w-54 h-24 text-sm rounded-tl rounded-br absolute top-0 left-0 z-10"
      :class="{
        'bg-[#E6FBFF] text-[#18A3BF]': item.source === CallSource.MOOR,
        'bg-[#E6EEFF] text-blue': item.source !== CallSource.MOOR,
      }"
    >
      <span v-if="item.source !== CallSource.MOOR">线路一</span>
      <span v-else>线路二</span>
    </div>
    <div
      v-if="showToDetail"
      class="absolute text-blue cursor-pointer top-8 right-16 text-sm inline-flex gap-4 items-center"
      @click="() => toDetail(item)"
    >
      患者资料
      <ElIcon :size="12" style="font-weight: 500">
        <ArrowRight />
      </ElIcon>
    </div>
    <div class="item-header-box">
      <div class="module-one flex items-center justify-between mb-8">
        <div class="one-left flex items-center font-medium">
          {{ item.name }} &nbsp;
          <span class="relation">
            {{ item.relation ? '(' + item.relation + ')' : '' }}
          </span>
        </div>
        <div class="one-right flex items-center">
          <img
            :src="item.type === 2 ? exhalationImg : incomingCall"
            alt=""
            class="w-18 h-18 mr-8"
          />
          <span v-if="item.type === 1" class="leading-5">呼叫</span>
          <span class="leading-5">{{ item.agentName }}</span>
          <span v-if="item.type === 2" class="leading-5">呼出</span>
          <span class="ml-8 leading-5">{{ formatTime(item.callTime) }}</span>
        </div>
      </div>
      <div
        class="module-one flex items-center justify-between"
        :class="{
          'pb-8': !item.fileUrl,
        }"
      >
        <div class="one-left flex items-center">
          <span class="relation">{{ item.customerNumber }}</span>
          <span class="relation">
            （{{ item.customerProvince }}/{{ item.customerCity }}）
          </span>
          <PhoneCall
            :tel="item.customerNumber"
            :patient-id="item.patientId"
            @call="handleCall"
          >
            <span
              class="text-blue text-sm leading-5 inline-flex items-center gap-4 cursor-pointer"
            >
              拨号
              <ElIcon :size="12">
                <ArrowRight />
              </ElIcon>
            </span>
          </PhoneCall>
        </div>
        <div class="one-right flex items-center">
          <span
            :class="{
              'text-[#e63746]': ![
                CallStatus.IC_Answered,
                CallStatus.OC_Answered,
              ].includes(callStatus),
            }"
          >
            {{ CallStatusLabelMap[callStatus] }}
            ({{ item.type === 1 ? '来电' : '去电'
            }}{{ calculateBridgeDuration(item.bridgeDuration) }})
          </span>
        </div>
      </div>
      <AudioPlayer class="mt-[12px]" :audio-url="item.fileUrl" />
    </div>
    <div class="query-details flex items-center justify-center mt-12">
      <span class="mr-8 cursor-pointer" @click="toggleFlag">
        {{ flag ? '收起详情' : '展开详情' }}
      </span>
      <el-icon
        v-if="flag"
        size="12px"
        class="cursor-pointer"
        @click="toggleFlag"
      >
        <i-ep-arrow-up-bold color="#2E6BE6" />
      </el-icon>
      <el-icon v-else size="12px" class="cursor-pointer" @click="toggleFlag">
        <i-ep-arrow-down-bold color="#2E6BE6" />
      </el-icon>
    </div>
    <div v-if="flag" class="mt-16">
      <Details :details-info="item" />
    </div>
  </li>
</template>

<style lang="css" scoped>
.item {
  box-shadow: 0 0 4px 0 rgba(10, 42, 97, 0.1);
  padding: 32px 16px 8px;
}

.item-header-box {
  border-bottom: 1px solid #e9e8eb;
  padding-bottom: 8px;
  box-sizing: border-box;

  .module-one {
    .one-left {
      font-size: 14px;
      color: #101b25;
      .relation {
        color: #708293;
        font-weight: 500;
        line-height: 20px;
      }
    }
    .one-right {
      font-size: 14px;
      color: #708293;
    }
  }
}
.query-details {
  font-size: 14px;
  color: #2e6be6;
}
</style>
