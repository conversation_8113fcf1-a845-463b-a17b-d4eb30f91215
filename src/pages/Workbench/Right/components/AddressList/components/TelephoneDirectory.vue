<template>
  <HrtDialog v-model="isTelephone" title="通讯录" class="telephone-box">
    <div v-if="userOpenList?.length > 0">
      <h3 class="font-medium text-sm text-[#101b25] leading-5 mb-8">
        亲情账号
      </h3>
      <div class="mb-16">
        <div
          v-for="(item, index) in userOpenList"
          :key="item.phone"
          class="grid grid-cols-[120px_1fr] border border-solid border-[#D2DCE6] h-44 leading-5 box-border"
          :class="{ 'border-t-0': index !== 0 }"
        >
          <div
            class="bg-[#F7F8FA] px-12 border-r border-[#D2DCE6 text-[#7A8599] items-center flex"
          >
            账号:
          </div>
          <div class="px-12 text-[#3A4762] flex items-center">
            <span>{{ item.name }}（{{ item.relation }}）{{ item.phone }}</span>
            <span
              v-for="tag in item.tags"
              :key="tag"
              class="bg-blue-100 rounded-sm mx-4 px-4 py-2 text-blue-400 leading-5 text-xs"
            >
              {{ tag === 'USUAL' ? '常用' : '上次' }}
            </span>
            <PhoneCall
              :name="item.name"
              :tel="item.phone"
              :patient-id="useGlobalInfo.userId"
              class="ml-auto h-16"
            />
          </div>
        </div>
      </div>
    </div>
    <div>
      <h3 class="font-medium text-sm text-[#101b25] leading-5 mb-8">联系人</h3>
      <HrtTable
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{
          background: '#F7F8FA',
        }"
      >
        <HrtTableColumn prop="name" label="姓名" width="110">
          <template #default="scope">
            <div v-if="!scope.row.isEdit">
              {{ scope.row.name }}
            </div>
            <HrtInput
              v-else
              v-model="scope.row.name"
              placeholder="请输入姓名"
            />
          </template>
        </HrtTableColumn>
        <HrtTableColumn prop="relation" label="关系" width="114">
          <template #default="scope">
            <div v-if="!scope.row.isEdit">
              {{ scope.row.relation ?? '本人' }}
            </div>
            <HrtSelect
              v-else
              v-model="scope.row.relation"
              placeholder="请选择关系"
              :style="{ width: '90px' }"
            >
              <HrtOption
                v-for="item in freeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </HrtSelect>
          </template>
        </HrtTableColumn>
        <HrtTableColumn prop="phone" label="手机号">
          <template #default="scope">
            <div v-if="!scope.row.isEdit">
              {{ scope.row.phone }}
              <span
                v-for="tag in scope.row.tags"
                :key="tag"
                class="bg-blue-100 rounded-sm mx-4 px-4 py-2 text-blue-400 leading-5 text-xs whitespace-nowrap"
              >
                {{ tag === 'USUAL' ? '常用' : '上次' }}
              </span>
            </div>
            <HrtInput
              v-else
              v-model="scope.row.phone"
              maxlength="11"
              placeholder="请输入手机号"
            />
          </template>
        </HrtTableColumn>
        <HrtTableColumn label="操作" align="right" width="130">
          <template #default="scope">
            <div v-if="!scope.row.isEdit" class="flex items-center">
              <div class="mr-16">
                <PhoneCall
                  :name="scope.row.name"
                  :need-clear="true"
                  :patient-id="useGlobalInfo.userId"
                  :tel="scope.row.phone"
                  class="h-16"
                />
              </div>
              <div
                v-if="scope.$index > 0"
                class="cursor-pointer edit-box mr-16 text-sm"
                @click="handleEdit(scope.row)"
              >
                编辑
              </div>
              <div
                v-if="scope.$index > 0"
                class="cursor-pointer delete-box text-sm"
                @click="handleDelete(scope.row, scope.$index)"
              >
                删除
              </div>
            </div>
            <div v-else class="flex items-center">
              <div
                class="flex items-center justify-center sure-box cursor-pointer text-sm mr-10 w-56 h-30"
                @click="sure(scope.row)"
              >
                确定
              </div>
              <div
                class="flex items-center justify-center text-sm cursor-pointer calce-box w-58 h-32 border border-[#DCDFE6]"
                @click="cancel(scope.row, scope.$index)"
              >
                取消
              </div>
            </div>
          </template>
        </HrtTableColumn>
      </HrtTable>
      <div class="add flex items-center mt-16">
        <img
          :src="addImg"
          alt=""
          class="w-14 h-14 cursor-pointer mt-2"
          @click="add"
        />
        <span class="ml-4 cursor-pointer add-box" @click="add">新增</span>
      </div>
    </div>
  </HrtDialog>
</template>
<script setup lang="ts">
import { fetchPatientAddressBookList, fetchPatientUserOpenList } from '@/api';
import { updateApi } from '@/api/addressBook';
import addImg from '@/assets/imgs/callCenter/add.png';
import PhoneCall from '@/components/PhoneCall/index.vue';
import { AddressBookTag } from '@/interface';
import useGlobal from '@/store/module/useGlobal';
import { useQuery } from '@tanstack/vue-query';
import { debounce } from 'lodash-es';
import { computed, inject } from 'vue';

const useGlobalInfo = useGlobal();

const isTelephone = inject<Ref<boolean, boolean>>('isTelephone');
const freeOptions = [
  {
    value: '本人',
    label: '本人',
  },
  {
    value: '配偶',
    label: '配偶',
  },
  {
    value: '父母',
    label: '父母',
  },
  {
    value: '子女',
    label: '子女',
  },
  {
    value: '孙儿女',
    label: '孙儿女',
  },
  {
    value: '其他',
    label: '其他',
  },
];
const tableData = ref<
  {
    isEdit: boolean;
    isNewAdd: boolean;
    phone: string;
    name: string;
    relation: string;
    addressBookId?: number;
    tags: AddressBookTag[];
  }[]
>([]);

const queryEnable = computed(
  () => !!useGlobalInfo.userId && !!isTelephone?.value
);

const { data: userOpenList } = useQuery({
  queryKey: ['患者亲情账号列表', useGlobalInfo.userId],
  queryFn: () => {
    if (useGlobalInfo.userId) {
      return fetchPatientUserOpenList({ patientId: useGlobalInfo.userId });
    }
    return [];
  },
  enabled: queryEnable,
  initialData: [],
});

const { data, refetch } = useQuery({
  queryKey: ['患者通讯录列表', useGlobalInfo.userId],
  queryFn: () => {
    if (useGlobalInfo.userId) {
      return fetchPatientAddressBookList({ patientId: useGlobalInfo.userId });
    }
    return [];
  },
  enabled: queryEnable,
  initialData: [],
});

watch(
  data,
  newData => {
    tableData.value = [...newData];
  },
  { immediate: true }
);

// 编辑
const handleEdit = (item: { isEdit: boolean }) => {
  const flag = tableData.value.some(item => item.isEdit);
  if (flag) {
    ElMessage({
      message: '请先保存编辑中的联系人信息！',
      type: 'warning',
    });
  } else {
    item.isEdit = true;
  }
};

// 删除
const handleDelete = (item: any, index: number) => {
  const flag = tableData.value.some(item => item.isEdit);
  if (flag) {
    ElMessage({
      message: '请先保存编辑中的联系人信息！',
      type: 'warning',
    });
  } else {
    ElMessageBox.confirm('确定要删除当前联系人吗?', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        tableData.value.splice(index, 1);
        addBook();
      })
      .catch(() => {});
  }
};

// 确定
const sure = (item: {
  name: any;
  phone: any;
  relation: any;
  isEdit: boolean;
}) => {
  // let regex = /^1[3-9]\d{9}$/;
  if (!item.name || !item.phone || !item.relation) {
    ElMessage({
      message: '请填写完整！',
      type: 'warning',
    });
  } else if (!Number(item.phone)) {
    // || !regex.test(item.phoneNumber)
    ElMessage({
      showClose: true,
      message: '请输入正确的手机号！',
      type: 'error',
    });
  } else {
    addBook();
  }
};

// 新增api
interface addInfo {
  name: string;
  phone: string;
  relation: null | string;
  addressBookId: number | null;
}
const addressBookList = ref<addInfo[]>([]);
const addBook = debounce(() => {
  tableData.value.forEach((item, index) => {
    if (index) {
      addressBookList.value.push({
        name: item.name,
        phone: item.phone,
        relation: item.relation,
        addressBookId: item.addressBookId || null,
      });
    }
  });

  updateApi({
    userId: useGlobalInfo.userId,
    addressBookList: addressBookList.value,
  }).then(res => {
    if (res.code === 'E000000') {
      ElMessage({
        message: '操作成功！',
        type: 'success',
      });
      refetch();
      // getList();
    } else {
      ElMessage({
        showClose: true,
        message: '操作失败！',
        type: 'error',
      });
    }
  });
}, 200);

// 取消
const cancel = (item: { isNewAdd: boolean }, index: number) => {
  //   如果是自己新增的点击取消就删除当前新增的数据
  if (item.isNewAdd) {
    tableData.value.splice(index, 1);
  } else {
    // 如果是数据库查出来的数据点击取消，就恢复到之前
    refetch();
  }
};

// 新增
const add = () => {
  const flag = tableData.value.some(item => item.isEdit);
  if (flag) {
    ElMessage({
      message: '请先保存编辑中的联系人信息！',
      type: 'warning',
    });
  } else {
    tableData.value.push({
      name: '',
      relation: '',
      phone: '',
      isEdit: true,
      isNewAdd: true,
      tags: [],
    });
  }
};
</script>
<style lang="less">
.telephone-box {
  padding: 0;
  .el-dialog {
    padding: 0;
  }
  .el-dialog__header {
    padding: 16px 24px 12px;
    font-size: 16px;
    font-weight: 700;
    color: #101b25;
    border-bottom: 1px solid #e9e8eb;
    margin-right: 0;
    .el-dialog__title {
      font-size: 16px;
    }
  }
  .el-table__inner-wrapper:before {
    background-color: transparent;
  }
  .el-dialog__headerbtn {
    top: 4px;
    right: 8px;
  }
  .el-dialog__body {
    padding: 16px 24px;
    .add-box {
      font-size: 14px;
      color: #2e6be6;
    }
    .edit-box {
      color: #2e6be6;
    }
    .delete-box {
      color: #e63746;
    }
    .sure-box {
      background: #2e6be6;
      border-radius: 2px;
      color: #ffffff;
    }
    .cancel-box {
      background: #ffffff;
      border-radius: 2px;
      color: #606266;
    }
  }
}
</style>
