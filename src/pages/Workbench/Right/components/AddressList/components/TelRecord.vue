<template>
  <el-drawer
    v-model="drawerVisible"
    :direction="direction || 'rtl'"
    :close-delay="200"
    :size="540"
    class="tel-record-drawer"
    @close="close"
  >
    <template #header>
      <div>通话记录</div>
    </template>
    <template #default>
      <div class="search-box">
        <div
          v-if="recordType === 1"
          class="search-item flex items-center mb-12"
        >
          <div class="title mr-8">患者姓名</div>
          <div class="item-content flex items-center flex-1">
            <el-select
              v-model="form.keyword"
              filterable
              remote
              reserve-keyword
              placeholder="患者姓名/联系人姓名/手机号码"
              :remote-method="remoteMethod"
              :loading="loading"
              style="width: 100%"
              class="search-input"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
        <div class="grid grid-cols-1 items-center mb-12 flex-wrap gap-12">
          <div class="search-item flex items-center">
            <div class="title mr-8">接听状态</div>
            <div class="item-content flex items-center flex-1">
              <el-select
                v-model="form.status"
                class="m-2"
                placeholder="请选择"
                style="width: 100%"
                multiple
                collapse-tags
                :max-collapse-tags="1"
              >
                <template #header>
                  <el-checkbox
                    v-model="checkAll"
                    :indeterminate="indeterminate"
                    @change="handleCheckAll"
                  >
                    全部
                  </el-checkbox>
                </template>
                <el-option
                  v-for="item in AnswerStatusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
        </div>
        <div class="search-item flex items-center">
          <div class="title mr-8 shrink-0">呼叫时间</div>
          <div class="item-content change-time-box">
            <ElRadioGroup v-model="dateRadio" @change="changeDateRadio">
              <ElRadio value="0">全部</ElRadio>
              <ElRadio value="1">今日</ElRadio>
              <ElRadio value="2">3日内</ElRadio>
              <ElRadio value="3">7日内</ElRadio>
            </ElRadioGroup>
          </div>
        </div>
        <div class="search-item flex items-center mb-24">
          <div class="item-content change-time-box ml-64 w-full">
            <el-date-picker
              v-model="form.callTime"
              type="daterange"
              clearable
              class="date-picker-box"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDateFn"
              style="width: 100%"
              @change="changeTime"
            />
          </div>
        </div>
        <div class="btns flex items-center justify-center">
          <div
            class="search flex items-center justify-center mr-8 cursor-pointer"
            @click="search"
          >
            搜索
          </div>
          <div
            class="reset flex items-center justify-center cursor-pointer"
            @click="reset"
          >
            重置
          </div>
        </div>
      </div>
      <ul
        v-infinite-scroll="load"
        class="list-box"
        :infinite-scroll-immediate="false"
      >
        <TelRecordCard
          v-for="item in recordList"
          :key="item.uniqueId"
          :item="item"
          :show-to-detail="showToDetail"
          @clear="emits('clear')"
          @close="emits('close')"
        />
      </ul>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import {
  addressCallListApi,
  queryPatientRelevanceMsgApi,
} from '@/api/addressBook';
import { AnswerStatusList, CallStatus, EmployeeUserType } from '@/constant';
import useGlobal from '@/store/module/useGlobal';
import { formatTime } from '@/utils/index';
import { useStorage } from '@vueuse/core';
import dayjs from 'dayjs';
import { CheckboxValueType, DrawerProps, ElRadioGroup } from 'element-plus';
import { cloneDeep, debounce } from 'lodash-es';
import { calculateBridgeDuration } from '../index';
import { formInfo, ListItem } from '../type';
import TelRecordCard from './TelRecordCard.vue';

interface TelRecordProps {
  visible: boolean;
  direction?: DrawerProps['direction'];
  showToDetail?: boolean;
  /** 记录类型：1-所有记录; 2-当前患者; */
  recordType: 1 | 2;
  immediate?: boolean;
  /** 默认通话类型 */
  defaultStatus?: CallStatus;
}

const props = withDefaults(defineProps<TelRecordProps>(), {
  defaultStatus: undefined,
  direction: undefined,
});
const emits = defineEmits(['change', 'close', 'clear']);
const useGlobalInfo = useGlobal();
const userRoles = useStorage<string[]>('userRoles', [], localStorage);
const accountId = useStorage<number>('accountId', null, localStorage, {
  serializer: {
    read: (v: string) => Number(v),
    write: (v: number | string) => (typeof v === 'number' ? v.toString() : v),
  },
});

const defaultForm = () => ({
  keyword: '',
  callTime: undefined,
  startTime: undefined,
  endTime: undefined,
  status: props.defaultStatus ? ([props.defaultStatus] as CallStatus[]) : [],
  pageSize: 10,
  page: 1,
  patientId: useGlobalInfo.userId,
});
const drawerVisible = ref(false);
// 通话记录列表
const recordList = ref<any>([]);
// 数据总页数
const totalNumber = ref<number>(0);
const form = ref<formInfo>({ ...defaultForm() });
const dateRadio = ref<string | undefined>('0');

const loading = ref(false);
const options = ref<ListItem[]>([]);
const checkAll = ref(false);
const indeterminate = ref(false);

watch(
  () => props.visible,
  () => {
    form.value = { ...defaultForm() };
  },
  { immediate: true }
);

watch(
  () => form.value.callTime,
  newValue => {
    if (!Array.isArray(newValue)) {
      form.value.startTime = undefined;
      form.value.endTime = undefined;
      return;
    }

    form.value.startTime = dayjs(newValue[0]).valueOf();
    form.value.endTime = dayjs(newValue[1]).valueOf();
    /** 结束日期是当日 */
    if (dayjs(newValue[1]).isSame(dayjs(), 'date')) {
      if (dayjs(newValue[0]).isSame(dayjs(), 'date')) {
        dateRadio.value = '1';
      } else if (
        dayjs(newValue[0]).isSame(dayjs().subtract(2, 'day'), 'date')
      ) {
        dateRadio.value = '2';
      } else if (
        dayjs(newValue[0]).isSame(dayjs().subtract(6, 'day'), 'date')
      ) {
        dateRadio.value = '3';
      } else {
        dateRadio.value = undefined;
      }
    } else {
      dateRadio.value = undefined;
    }
  },
  { immediate: true }
);

watch(
  () => form.value.status,
  newValue => {
    checkAll.value = newValue?.length === AnswerStatusList.length;
    indeterminate.value = (newValue?.length ?? 0) > 0 && !checkAll.value;
  },
  { immediate: true }
);

/** 重置查询条件 */
function reset() {
  resetForm();
  recordList.value = [];
  getList();
}

const getReqParams = () => {
  const res: any = cloneDeep(form.value);
  if (props.recordType === 1) {
    res.patientId = '';
  }
  const callStatus = res.status?.map(item => getCallStatusParams(item)) ?? [];
  delete res.callTime;
  delete res.status;
  const userInfo = {
    userType: EmployeeUserType[userRoles.value[0]],
    userId: accountId.value,
  };
  return {
    ...res,
    callStatus,
    startTime: res.startTime
      ? dayjs(res.startTime).startOf('date').valueOf()
      : null,
    endTime: res.endTime ? dayjs(res.endTime).endOf('date').valueOf() : null,
    ...(props.defaultStatus ? userInfo : {}),
    pageNumber: res.page,
  };
};

/**
 * 处理日期切换的快捷方式值变更
 * @param val 1-今日; 2-3日内; 3-7日内;
 */
function changeDateRadio(val: string | number | boolean | undefined) {
  switch (val) {
    case '1':
      form.value.callTime = [
        dayjs().startOf('date').format('YYYY-MM-DD'),
        dayjs().endOf('date').format('YYYY-MM-DD'),
      ];
      break;
    case '2':
      form.value.callTime = [
        dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ];
      break;
    case '3':
      form.value.callTime = [
        dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ];
      break;
    case '0':
      form.value.callTime = undefined;
      break;
  }
}

function handleCheckAll(val: CheckboxValueType) {
  indeterminate.value = false;
  if (val) {
    form.value.status = AnswerStatusList.map(_ => _.value);
  } else {
    form.value.status = [];
  }
}

/**
 * 获取通话记录列表
 */
const getList = () => {
  if (!drawerVisible.value) {
    return;
  }
  const params = getReqParams();
  addressCallListApi(params).then((res: any) => {
    let { code, data } = res;
    if (code === 'E000000' && data && data.contents && data.contents.length) {
      // 首页展示的一条数据
      const item = data.contents[0];
      emits('change', {
        ipone: item.customerNumber,
        time: formatTime(item.callTime),
        name: item.name,
        relation: item.relation,
        num: calculateBridgeDuration(item.bridgeDuration),
      });
      data.contents.forEach((item: { flag: boolean }) => (item.flag = false));
      recordList.value = [...recordList.value, ...data.contents];

      totalNumber.value = Math.ceil(data.total / 10);
    } else {
      recordList.value = [];
      emits('change', {
        time: '--',
        num: '--',
        relation: '--',
        ipone: '--',
        name: '--',
      });
    }
  });
};
const remoteMethod = (query: string) => {
  options.value = [];
  remoteMethodQuery(query);
};
const remoteMethodQuery = debounce((query: string) => {
  if (query) {
    loading.value = true;
    queryPatientRelevanceMsgApi({ keyword: query })
      .then((res: any) => {
        if (res.code === 'E000000' && res.data && res.data.length) {
          options.value = res.data.map((item: { phone: any; name: any }) => {
            return {
              value: item.phone,
              label: item.name,
            };
          });
        } else {
          options.value = [];
        }
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    options.value = [];
  }
}, 500);

/** 选择时间 */
function changeTime() {
  if (!form.value.callTime || form.value.callTime.length < 2) return;

  const startDate = dayjs(form.value.callTime[0]);
  const endDate = dayjs(form.value.callTime[1]);
  const diffDays = endDate.diff(startDate, 'day');

  if (diffDays > 31) {
    ElMessage.warning('呼叫时间选择范围不能超过31天');
    form.value.callTime = [
      startDate.format('YYYY-MM-DD'),
      startDate.add(30, 'day').format('YYYY-MM-DD'),
    ];
    return;
  }

  form.value.startTime = form.value.callTime[0];
  form.value.endTime = form.value.callTime[1];
}

/** 重置表单 */
function resetForm() {
  form.value = { ...defaultForm(), status: [] };
  dateRadio.value = '0';
}

// 抽屉关闭
function close() {
  resetForm();
  emits('close');
  // getList();
}

/**
 * 限制日期选择时间 - 时间跨度不超过31天，不可选未来的时间
 * @param time 选择的时间
 */
function disabledDateFn(time: { getTime: () => number }) {
  return time.getTime() > Date.now();
}

/**
 * 加载到底部分页加载数据
 */
function load() {
  if (props.visible && form.value.page < totalNumber.value) {
    form.value.page++;
    getList();
  }
}

/** 搜索 */
function search() {
  const { startTime, endTime } = form.value;
  form.value.page = 1;
  form.value.startTime = startTime
    ? dayjs(startTime).startOf('date').valueOf()
    : '';
  form.value.endTime = endTime ? dayjs(endTime).endOf('day').valueOf() : '';
  recordList.value = [];
  getList();
}

watch(
  () => useGlobalInfo.userId,
  () => {
    recordList.value = [];
    if (useGlobalInfo.userId && props.immediate) {
      form.value.patientId = useGlobalInfo.userId;
      getList();
    }
  },
  { deep: true }
);
watch(
  () => props.visible,
  val => {
    drawerVisible.value = val;
    if (val) {
      recordList.value = [];
      getList();
    }
  }
);

function getCallStatusParams(status: CallStatus) {
  switch (status) {
    case CallStatus.IC_Answered:
      return { type: 1, status: 1 };
    case CallStatus.IC_NotAnswered_NR:
      return { type: 1, status: 2, isRead: 0 };
    case CallStatus.IC_NotAnswered_R:
      return { type: 1, status: 2, isRead: 1 };
    case CallStatus.IC_AnsweredBySystem:
      return { type: 1, status: 3 };
    case CallStatus.OC_Answered:
      return { type: 2, status: 3 };
    case CallStatus.OC_NotAnswered:
      return { type: 2, status: 2 };
    case CallStatus.OC_Failed:
      return { type: 2, status: 1 };
    default:
      return {};
  }
}
</script>

<style lang="less">
.tel-record-drawer {
  height: calc(100% - 60px);
  top: 60px;

  .el-drawer__header {
    margin-bottom: 0;
    padding: 12px 16px;
    border-bottom: 1px solid #e9e8eb;
    font-size: 16px;
    font-weight: 600;
    color: #101b25;
  }
  .el-drawer__body {
    display: flex;
    flex-direction: column;
    padding: 0;
    .search-box {
      padding: 16px;

      .search-item {
        .title {
          font-size: 14px;
          color: #111111;
        }
        .item-content {
          .date-picker-box {
            width: 240px;
            .el-range-separator {
              width: 50px;
            }
            .el-range-input {
              width: 45%;
            }
            .el-range__icon {
              display: none;
            }
          }
          .all-box {
            .change-box {
              width: 14px;
              height: 14px;
              background: #ffffff;
              border-radius: 8px;
              border: 1px solid #dcdee0;
            }
            .change-box-checked {
              width: 14px;
              height: 14px;
              background: #ffffff;
              border-radius: 8px;
              border: 1px solid #2e6be6;
              .interior-check {
                width: 10px;
                height: 10px;
                background: #0a73e4;
                border-radius: 8px;
              }
            }
            .change-title {
              font-size: 14px;
              color: #3a4762;
            }
          }
          .search-input {
            width: 240px;
          }
        }
        .change-time-box {
          position: relative;
          .change-time-img {
            position: absolute;
            right: 8px;
          }
        }
      }
      .btns {
        .search {
          width: 76px;
          height: 32px;
          background: #2e6be6;
          border-radius: 2px;
          font-size: 14px;
          color: #ffffff;
        }
        .reset {
          width: 76px;
          height: 32px;
          border-radius: 2px;
          border: 1px solid #dcdfe6;
          box-sizing: border-box;
          font-size: 14px;
          color: #606266;
        }
      }
    }
    .list-box {
      flex: 1;
      background: #f7f8fa;
      overflow-y: scroll;
      box-sizing: border-box;
      padding: 24px 16px;
    }

    /*修改滚动条样式*/
    .list-box::-webkit-scrollbar {
      width: 0;
      height: 0;
    }

    .list-box::-webkit-scrollbar-track {
      background: rgb(239, 239, 239);
      border-radius: 2px;
    }

    .list-box::-webkit-scrollbar-thumb {
      background: #bfbfbf;
      border-radius: 0;
    }
  }
}
</style>
