<template>
  <div class="pb-8">
    <p class="text-[#101B25] font-medium text-sm mb-16">通话详情</p>
    <div class="w-full grid grid-cols-2 items-center flex-wrap gap-y-16">
      <div
        v-for="(item, index) in detailsData"
        :key="index"
        class="flex items-center"
      >
        <div class="mr-16 w-56 text-sm text-[#708293]">
          {{ item.title }}
        </div>
        <div class="text-sm text-[#101b25]">
          {{ item.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AddressCallListApiItem } from '@/@types';
import dayjs from 'dayjs';
import { computed } from 'vue';
import { calculateBridgeDuration } from '..';

const { detailsInfo } = defineProps<{
  detailsInfo: AddressCallListApiItem;
}>();

const detailsData = computed(() => {
  return [
    {
      title: '呼叫类型',
      value: detailsInfo.type === 1 ? '呼入' : '呼出',
    },
    {
      title: '接听状态',
      value: detailsInfo.status || '--',
    },
    {
      title: detailsInfo.type == 1 ? '热线号码' : '外显号码',
      value: detailsInfo.hotline || '--',
    },
    {
      title: '等待时长',
      value: calculateBridgeDuration(detailsInfo.waitDuration || 0) || '--',
    },
    {
      title: '开始时间',
      value: detailsInfo.startTime
        ? dayjs(detailsInfo.startTime).format('YYYY-MM-DD HH:mm:ss')
        : '--',
    },
    {
      title: '结束时间',
      value: detailsInfo.endTime
        ? dayjs(detailsInfo.endTime).format('YYYY-MM-DD HH:mm:ss')
        : '--',
    },
    {
      title: '接通时间',
      value: detailsInfo.bridgeTime
        ? dayjs(detailsInfo.bridgeTime).format('YYYY-MM-DD HH:mm:ss')
        : '--',
    },
    {
      title: '通话时长',
      value: calculateBridgeDuration(detailsInfo.bridgeDuration || 0) || '--',
    },
    {
      title: '坐席工号',
      value: detailsInfo.cno || '--',
    },
    {
      title: '坐席电话',
      value: detailsInfo.agentPhone || '--',
    },
  ];
});
</script>
