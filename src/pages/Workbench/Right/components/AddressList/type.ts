// 搜索条件
export interface formInfo {
  keyword: string;
  callTime?: [string, string];
  startTime?: string | number;
  endTime?: string | number;
  status: number[];
  pageSize: number;
  page: number;
  patientId: number | string | undefined;
}

// 通话记录详情
export interface info {
  customerNumber: string;
  hotline: string;
  callType: string;
  status: string;
  totalDuration: string;
  startTime: string;
  bridgeTime: string;
  endTime: string;
  qno: string;
  cno: string;
  clientNumber: string;
}

export interface ListItem {
  value: string;
  label: string;
}
