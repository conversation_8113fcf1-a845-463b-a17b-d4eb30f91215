import RoleEnum from '@/constant/role';
import { setupStore } from '@/store/store';
import { VueQueryPlugin } from '@tanstack/vue-query';
import { surveyPlugin } from 'survey-vue3-ui';
import { createApp, type Directive } from 'vue';
import App from './App.vue';
import bus from './lib/bus';
import router from './router';
import routes from './router/route';
import './styles/index.css';
import './styles/tailwind.css';
const app = createApp(App);

// 自定义指令
import * as directives from '@/directives';
Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

const userRoles = JSON.parse(localStorage.getItem('userRoles') ?? '[]');
const currentRole = userRoles?.[0];
routes
  .filter(
    v => !v.meta?.role || (v.meta?.role as any)?.includes(RoleEnum[currentRole])
  )
  .forEach(route => {
    router.addRoute(route);
  });
setupStore(app);
app.use(router).use(surveyPlugin).use(VueQueryPlugin);
app.config.globalProperties.$bus = bus;

app.mount('#app');
