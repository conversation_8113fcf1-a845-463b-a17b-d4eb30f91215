/** 容联云登录类型 */
export enum RLYLoginType {
  /** 普通电话 */
  Local = 'Local',
  /** IP话机 */
  gateway = 'gateway',
  /** 软电话 */
  SIP = 'sip',
}

/** 容联云通话事件类型 */
export enum RLYAttachEventType {
  /** 未注册 */
  unregister = 'unregister',
  /** 被踢 */
  kick = 'kick',
  /** 座席状态 */
  peerstate = 'peerstate',
  /** 呼叫中 */
  dialing = 'dialing',
  /** 呼入通话 */
  innerTalking = 'innerTalking',
  /** 外呼通话 */
  dialTalking = 'dialTalking',
  /** 来电振铃 */
  belling = 'belling',
  /** 内线振铃 */
  innerBelling = 'innerBelling',
  /** 外呼转接通话 */
  dialTransfer = 'dialTransfer',
  /** 三方会话 */
  threeWayTalking = 'threeWayTalking',
  /** 通话中 */
  talking = 'talking',
}

/** 容联云坐席状态 */
export enum RLYAgentStatus {
  /** 空闲 */
  Free = '0',
  /** 忙碌 */
  Busy = '1',
}

/** 通话状态 */
export enum CallStatus {
  All = 0,
  /** 来电已接听 */
  IC_Answered = 1,
  /** 来电未接听-未读 */
  IC_NotAnswered_NR = 2,
  /** 来电未接听-已读 */
  IC_NotAnswered_R = 3,
  /** 来电由系统接听 */
  IC_AnsweredBySystem = 4,
  /** 去电已接听 */
  OC_Answered = 5,
  /** 去电未接听 */
  OC_NotAnswered = 6,
  /** 去电失败 */
  OC_Failed = 7,
}

/** 容联云状态列表 */
export const AnswerStatusList = [
  {
    value: CallStatus.IC_Answered,
    label: '来电已接听',
  },
  {
    value: CallStatus.IC_NotAnswered_NR,
    label: '来电未接听-未读',
  },
  {
    value: CallStatus.IC_NotAnswered_R,
    label: '来电未接听-已读',
  },
  // {
  //   value: CallStatus.IC_AnsweredBySystem,
  //   label: '来电系统接听',
  // },
  {
    value: CallStatus.OC_Answered,
    label: '去电已接听',
  },
  {
    value: CallStatus.OC_NotAnswered,
    label: '去电未接听',
  },
  {
    value: CallStatus.OC_Failed,
    label: '去电失败',
  },
];

/**
 * CallStatus 对应 label 映射
 * key 为 CallStatus 枚举值，value 为中文 label
 */
export const CallStatusLabelMap: Record<CallStatus, string> =
  AnswerStatusList.reduce(
    (acc, cur) => {
      acc[cur.value] = cur.label;
      return acc;
    },
    {} as Record<CallStatus, string>
  );
