import type { Directive, DirectiveBinding } from 'vue';

const checkPermission = (val: string, exclude = false) => {
  const permissionRoles = Array.isArray(val) ? val : [val];
  const userRoles = JSON.parse(localStorage.getItem('userRoles') ?? '[]');
  const currentRole = userRoles?.[0];
  const isExist = permissionRoles.includes(currentRole);
  return exclude ? !isExist : isExist;
};
/** 权限控制 */
export const permission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { val, exclude } = binding.value ?? {};
    const hasPermission = checkPermission(val, exclude);
    if (!hasPermission) {
      el.parentNode?.removeChild(el);
    }
  },
};
