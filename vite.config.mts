import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import * as path from 'path';
import AutoImport from 'unplugin-auto-import/vite';
import IconsResolver from 'unplugin-icons/resolver';
import Icons from 'unplugin-icons/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
import { defineConfig } from 'vite';
import { analyzer } from 'vite-bundle-analyzer';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  base: './',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  plugins: [
    vue(),
    vueJsx(),
    // vueDevTools({
    //   launchEditor: 'webstorm',
    // }),
    AutoImport({
      eslintrc: {
        enabled: false,
        filepath: './.eslintrc-auto-import.json',
        globalsPropValue: true,
      },
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          prefix: 'Icon',
        }),
      ],
      imports: ['vue', 'vue-router'],
    }),
    Components({
      dirs: [],
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          enabledCollections: ['ep'],
        }),
      ],
    }),
    Icons({
      autoInstall: true,
    }),
    mode === 'analyze' ? analyzer() : undefined,
  ],
  esbuild: {
    drop: mode === 'production' ? ['debugger', 'console'] : [],
  },
  build: {
    rollupOptions: {
      input: 'index.html',
      output: {
        chunkFileNames: 'static/js/[name]-[hash].js',
        entryFileNames: 'static/js/[name]-[hash].js',
        assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
        manualChunks(id) {
          if (id.includes('node_modules')) {
            const arr = id.toString().split('node_modules/');
            return arr[arr.length - 1].split('/')[0].toString();
          }
        },
      },
    },
  },
  server: {
    // host: '0.0.0.0',
    // port: 8088,
    hmr: true,
    proxy: {
      // '/api/ocr': {
      //   target: 'http://**************:9213/health-manage/',
      //   changeOrigin: true,
      //   // rewrite: (path: string) => path.replace(/^\/api/, ''),
      // },
      '/api': {
        // target: 'http://**************:8213/health-manage/',
        // target: 'http://***************:8213/health-manage/',
        // target: 'https://uat.hrttest.cn/health-manage/',
        target: 'https://assistant.scheartmed.com/health-manage/',
        changeOrigin: true,
        // rewrite: (path: string) => path.replace(/^\/api/, ''),
        // target: 'http://**************:8213/health-manage/',
        // target: 'http://***************:8213/health-manage/',
      },
      '/health-manage': {
        target: 'https://assistant.scheartmed.com/',
        changeOrigin: true,
      },
    },
  },
}));
