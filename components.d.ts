/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCalendar: typeof import('element-plus/es')['ElCalendar']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    IEpArrowDown: typeof import('~icons/ep/arrow-down')['default']
    IEpArrowDownBold: typeof import('~icons/ep/arrow-down-bold')['default']
    IEpArrowLeft: typeof import('~icons/ep/arrow-left')['default']
    IEpArrowLeftBold: typeof import('~icons/ep/arrow-left-bold')['default']
    IEpArrowRight: typeof import('~icons/ep/arrow-right')['default']
    IEpArrowRightBold: typeof import('~icons/ep/arrow-right-bold')['default']
    IEpArrowUpBold: typeof import('~icons/ep/arrow-up-bold')['default']
    IEpCaretBottom: typeof import('~icons/ep/caret-bottom')['default']
    IEpCaretRight: typeof import('~icons/ep/caret-right')['default']
    IEpCircleCloseFilled: typeof import('~icons/ep/circle-close-filled')['default']
    IEpCirclePlus: typeof import('~icons/ep/circle-plus')['default']
    IEpClose: typeof import('~icons/ep/close')['default']
    IEpDArrowLeft: typeof import('~icons/ep/d-arrow-left')['default']
    IEpDArrowRight: typeof import('~icons/ep/d-arrow-right')['default']
    IEpDelete: typeof import('~icons/ep/delete')['default']
    IEpEditPen: typeof import('~icons/ep/edit-pen')['default']
    IEpLoading: typeof import('~icons/ep/loading')['default']
    IEpRefresh: typeof import('~icons/ep/refresh')['default']
    IEpView: typeof import('~icons/ep/view')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
  export interface GlobalDirectives {
    vInfiniteScroll: typeof import('element-plus/es')['ElInfiniteScroll']
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
