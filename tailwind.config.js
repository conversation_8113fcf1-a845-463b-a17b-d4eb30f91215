/** @type {import('tailwindcss').Config} */
import { isFunction } from 'lodash-es';
function valueBy(keys = [], processor) {
  return keys.reduce((result, key) => {
    result[key] = isFunction(processor) ? processor(key) : key;
    return result;
  }, {});
}
export default {
  content: [
    './index.html',
    './src/**/*.{vue,js,ts,jsx,tsx}',
    './docs/**/*.{vue,js,ts,jsx,tsx,md}',
  ],
  corePlugins: {
    preflight: false,
  },
  theme: {
    extend: {
      colors: {
        ...valueBy(
          ['primary', 'danger', 'primary-text', 'sub-text'],
          color => `var(--color-${color})`
        ),
        blue: {
          DEFAULT: '#2E6BE6',
          400: '#2E6BE6',
        },
      },
      fontSize: valueBy(['xs', 'sm', 'base', 'lg', 'xl', '2xl'], key => [
        `var(--text-size-${key})`,
        `var(--line-height-${key})`,
      ]),
      lineHeight: {
        none: 'none',
        1: 1,
        ...valueBy(
          ['xs', 'sm', 'base', 'lg', 'xl', '2xl'],
          key => `var(--line-height-${key})`
        ),
      },
      spacing: {
        ...valueBy(
          [
            '3xs',
            '2xs',
            'xs',
            'sm',
            'md',
            'lg',
            'xl',
            '2xl',
            '3xl',
            '4xl',
            '5xl',
          ],
          spacing => `var(--spacing-${spacing})`
        ),
      },
    },
    spacing: valueBy(Array.from(new Array(1000).keys()), value => `${value}px`),
  },
  plugins: [],
};
