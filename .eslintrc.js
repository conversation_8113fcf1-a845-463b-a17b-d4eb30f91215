module.exports = {
  parser: 'vue-eslint-parser',
  parserOptions: {
    parser: '@typescript-eslint/parser',
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  extends: [
    'plugin:vue/vue3-recommended',
    'plugin:@typescript-eslint/recommended',
    'prettier',
    'plugin:prettier/recommended',
    './.eslintrc-auto-import.json',
  ],
  rules: {
    camelcase: [0, { properties: 'never' }], //变量名采用驼峰命名
    indent: ['off', 2], //2 个空格缩进
    quotes: [2, 'single'], //强制使用一致的反勾号、双引号或单引号
    'no-multi-str': 2, //禁止使用多行字符串(禁止使用斜线"\"创建多行字符串,应使用"\n")
    //禁止在变量定义之前使用它们
    'no-use-before-define': [
      2,
      {
        functions: false,
        classes: false,
        variables: false,
      },
    ],
    // 无内容标签自闭合
    'vue/html-self-closing': [
      'error',
      {
        html: {
          void: 'always',
          normal: 'never',
          component: 'always',
        },
        svg: 'always',
        math: 'always',
      },
    ],

    // // 不允许在组件内部直接改变props值，或者说只能通过this.$emit（）改变props值
    'vue/no-mutating-props': 'off',
    'vue/multi-word-component-names': 'off',
    // 组件名大驼峰命名
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    // 检查v-model绑定值是否有效
    'vue/valid-v-model': 'error',
    // 标签结束 /> 之前保留一个空格
    'vue/html-closing-bracket-spacing': [
      'error',
      {
        selfClosingTag: 'always',
      },
    ],
    'no-unused-vars': 'off',
    '@typescript-eslint/no-unused-vars': ['error'],
    '@typescript-eslint/no-explicit-any': 0,
    'prettier/prettier': 'error', //必须放在规则最后一条
  },
};
