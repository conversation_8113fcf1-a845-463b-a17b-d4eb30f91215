# ElNotification 不生效问题解决方案

## 问题描述

在使用 Element Plus 的 `ElNotification` 组件时，可能会遇到通知不显示的问题，特别是在以下场景：

1. 在 Pinia Store 中使用
2. 在某些异步回调中使用
3. 样式没有正确加载

## 解决方案

### 1. 确保正确导入

```typescript
// 方法一：手动导入（推荐）
import { ElNotification } from 'element-plus'

// 方法二：使用自动导入（需要配置）
// ElNotification 会自动可用
```

### 2. 确保样式正确加载

#### 方法一：在 main.ts 中全局引入样式

```typescript
// main.ts
import 'element-plus/dist/index.css'
```

#### 方法二：配置自动导入样式

```typescript
// vite.config.mts
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    AutoImport({
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass', // 或者 'css'
        }),
      ],
    }),
    Components({
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass', // 或者 'css'
        }),
      ],
    }),
  ],
})
```

### 3. 创建统一的通知工具函数

为了更好地管理通知，建议创建统一的工具函数：

```typescript
// src/utils/notification.ts
import { ElNotification } from 'element-plus'

export const showSuccessNotification = (message: string, title = '成功') => {
  ElNotification({
    title,
    message,
    type: 'success',
    duration: 3000,
    showClose: true,
  })
}

export const showErrorNotification = (message: string, title = '错误') => {
  ElNotification({
    title,
    message,
    type: 'error',
    duration: 4000,
    showClose: true,
  })
}
```

### 4. 在 Store 中使用

```typescript
// store/module/useCallCenter.ts
import { showSuccessNotification, showErrorNotification } from '@/utils/notification'

export const useCall = defineStore('callCenter', {
  actions: {
    someAction() {
      // 使用统一的通知函数
      showSuccessNotification('操作成功！')
      showErrorNotification('操作失败！')
    }
  }
})
```

## 常见问题排查

### 1. 检查控制台错误

打开浏览器开发者工具，查看是否有相关错误信息。

### 2. 检查 z-index

确保通知的 z-index 没有被其他元素覆盖：

```css
/* 如果需要调整 z-index */
.el-notification {
  z-index: 9999 !important;
}
```

### 3. 检查容器

确保通知有正确的挂载容器：

```typescript
ElNotification({
  title: '测试',
  message: '测试消息',
  type: 'success',
  // 可以指定挂载的容器
  appendTo: document.body
})
```

### 4. 测试组件

使用测试组件验证 ElNotification 是否正常工作：

```vue
<template>
  <div>
    <el-button @click="testNotification">测试通知</el-button>
  </div>
</template>

<script setup lang="ts">
import { ElNotification } from 'element-plus'

const testNotification = () => {
  ElNotification({
    title: '测试',
    message: '如果你看到这个消息，说明 ElNotification 工作正常',
    type: 'success'
  })
}
</script>
```

## 最佳实践

1. **统一管理**：使用统一的通知工具函数，便于维护和定制
2. **错误处理**：在通知函数中添加错误处理逻辑
3. **类型安全**：使用 TypeScript 确保参数类型正确
4. **样式一致**：保持通知样式的一致性
5. **用户体验**：合理设置通知的持续时间和位置

## 项目中的实现

在本项目中，我们已经：

1. ✅ 创建了 `src/utils/notification.ts` 工具函数
2. ✅ 更新了 `vite.config.mts` 配置自动导入样式
3. ✅ 在 `main.ts` 中引入了 Element Plus 样式
4. ✅ 更新了 `useCallCenter.ts` 使用统一的通知函数
5. ✅ 创建了测试组件 `src/components/NotificationTest.vue`

现在 `ElNotification` 应该可以正常工作了！
