<template>
  <div>
    {{ formData }}
    <TableForm
      mode="edit"
      :type="item.uiOptions?.type"
      :columns="item.columns"
      :items="item.items"
      :value="formData"
      :ui-options="item.uiOptions || {}"
      @change="val => formItemChange(item, val)"
    />
  </div>
</template>

<script setup lang="ts">
import TableForm from '@/components/FormItem/TableForm.vue';

const formData = ref();

function formItemChange(item, val) {
  formData.value = val;
}

const item = ref({
  key: 'physique_data',
  value: '体格数据',
  enSession: 'physique_data',
  sessionName: '体格数据',
  ordered: 11,
  version: 1,
  uiMethod: 'table',
  items: [
    {
      key: 'weight',
      value: '体重',
      enSession: 'body',
      sessionName: 'physical',
      rowId: 1,
      rowOrder: 0,
      necessity: true,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      enSession: 'body',
      sessionName: 'physical',
      rowId: 1,
      rowOrder: 1,
      necessity: true,
      uiDisable: false,
      uiMethod: 'inputNumber',
      version: 1,
    },
    {
      key: 'kg',
      value: 'kg',
      enSession: 'body',
      sessionName: 'physical',
      rowId: 1,
      rowOrder: 2,
      necessity: true,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      key: 'height',
      value: '身高',
      enSession: 'body',
      sessionName: 'physical',
      rowId: 2,
      rowOrder: 0,
      necessity: true,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      enSession: 'body',
      sessionName: 'physical',
      rowId: 2,
      rowOrder: 1,
      necessity: true,
      uiDisable: false,
      uiMethod: 'inputNumber',
      version: 1,
    },
    {
      key: 'cm',
      value: 'cm',
      enSession: 'body',
      sessionName: 'physical',
      rowId: 2,
      rowOrder: 2,
      necessity: true,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      key: 'systolic_pressure',
      value: '收缩压',
      enSession: 'body',
      sessionName: 'physical',
      rowId: 3,
      rowOrder: 0,
      necessity: true,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      enSession: 'body',
      sessionName: 'physical',
      rowId: 3,
      rowOrder: 1,
      necessity: true,
      uiDisable: false,
      uiMethod: 'input',
      version: 1,
    },
    {
      key: 'mmHg',
      value: 'mmHg',
      enSession: 'body',
      sessionName: 'physical',
      rowId: 3,
      rowOrder: 2,
      necessity: true,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      key: 'diastolic_pressure',
      value: '舒张压',
      enSession: 'body',
      sessionName: 'physical',
      rowId: 4,
      rowOrder: 0,
      necessity: true,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      enSession: 'body',
      sessionName: 'physical',
      rowId: 4,
      rowOrder: 1,
      necessity: true,
      uiDisable: false,
      uiMethod: 'input',
      version: 1,
    },
    {
      key: 'mmHg',
      value: 'mmHg',
      enSession: 'body',
      sessionName: 'physical',
      rowId: 4,
      rowOrder: 2,
      necessity: true,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      key: 'heart_rate',
      value: '心率',
      enSession: 'body',
      sessionName: 'physical',
      rowId: 5,
      rowOrder: 0,
      necessity: true,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      enSession: 'body',
      sessionName: 'physical',
      rowId: 5,
      rowOrder: 1,
      necessity: true,
      uiDisable: false,
      uiMethod: 'input',
      version: 1,
    },
    {
      key: 'bpm',
      value: '次/分',
      enSession: 'body',
      sessionName: 'physical',
      rowId: 5,
      rowOrder: 2,
      necessity: true,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
  ],
  columns: [
    {
      key: 'info',
      value: '信息',
      enSession: 'title',
      sessionName: 'physical',
      rowId: 0,
      rowOrder: 0,
      necessity: false,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      key: 'value',
      value: '数值',
      enSession: 'title',
      sessionName: 'physical',
      rowId: 0,
      rowOrder: 1,
      necessity: false,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
    {
      key: 'unit',
      value: '单位',
      enSession: 'title',
      sessionName: 'physical',
      rowId: 0,
      rowOrder: 2,
      necessity: false,
      uiDisable: false,
      uiMethod: 'text',
      version: 1,
    },
  ],
});
</script>

<style>
.vp-doc table {
  margin: 0;
}
</style>
