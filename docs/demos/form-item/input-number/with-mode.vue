<script setup lang="ts">
import { ref } from 'vue';
import InputNumber from '@/components/FormItem/InputNumber.vue';

const value = ref(1);
const mode = ref<'create' | 'edit' | 'view'>('create');
</script>

<template>
  <div class="flex flex-col gap-16">
    <el-radio-group v-model="mode">
      <el-radio value="create">创建</el-radio>
      <el-radio value="edit">编辑</el-radio>
      <el-radio value="view">查看</el-radio>
    </el-radio-group>
    <InputNumber
      :mode="mode"
      :value="value"
      :precision="2"
      :min="0"
      :max="100"
      unit="kg"
      @change="val => (value = val)"
    />
  </div>
</template>
