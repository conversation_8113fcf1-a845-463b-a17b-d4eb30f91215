<script setup lang="ts">
import { ref } from 'vue';
import InputNumber from '@/components/FormItem/InputNumber.vue';

const value = ref(1);
const control = ref<boolean>(true);
</script>

<template>
  <div class="flex flex-col gap-16">
    <el-radio-group v-model="control">
      <el-radio :value="true">带控制器</el-radio>
      <el-radio :value="false">不带控制器</el-radio>
    </el-radio-group>
    <InputNumber
      mode="edit"
      :value="value"
      :precision="2"
      :min="0"
      :max="100"
      :control="control"
      unit="kg"
      @change="val => (value = val)"
    />
  </div>
</template>
